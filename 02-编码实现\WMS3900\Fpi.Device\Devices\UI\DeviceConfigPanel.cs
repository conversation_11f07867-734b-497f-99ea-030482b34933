﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Fpi.Communication.Protocols;
using Fpi.Devices.Properties;
using Fpi.HB.Business.Protocols;
using Fpi.Timers.UI.PC;
using Fpi.UI.Common.PC;
using Fpi.Util.Reflection;
using Timer = Fpi.Timers.Timer;

namespace Fpi.Devices.UI
{
    public partial class DeviceConfigPanel : UserControl
    {
        #region 字段属性

        /// <summary>
        /// 当前设备
        /// </summary>
        public Device Device { get; set; }

        /// <summary>
        /// 当前配置类型
        /// </summary>
        public DeviceConfigType CurConfigType { get; set; }

        /// <summary>
        /// 通信参数对应接口（保存通信参数）
        /// </summary>
        private ICommParam _iCommParam;

        public DeviceConfigPanel()
        {
            InitializeComponent();
        }

        #endregion

        #region 构造

        public DeviceConfigPanel(Device device, DeviceConfigType curConfigType)
        {
            this.Device = device;
            this.CurConfigType = curConfigType;
        }

        private void DeviceConfigPanel_Load(object sender, EventArgs e)
        {
            FillDeviceImpClass();
            this.ShowDeviceConfig();
        }

        #endregion

        #region 事件

        private void btnTimerConfig_Click(object sender, EventArgs e)
        {
            if(this.Device != null)
            {
                if(this.Device.Timer == null)
                {
                    this.Device.Timer = new Timer
                    {
                        timespan = 15000,
                        description = this.Device.name + "数据采集定时器"
                    };
                }

                FormConfigTimerUser configTimerFrm = new FormConfigTimerUser(this.Device.Timer);
                configTimerFrm.ShowDialog();
            }
            else
            {
                FpiMessageBox.ShowError("请先选择设备类型！");
            }
        }

        private void btnBuildChl_Click(object sender, EventArgs e)
        {
            if(this.Device != null)
            {
                if(MessageBox.Show(@"重建设备通道将清空原有通道配置，是否继续？", Resources.SystemPrompt, MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    this.Device.ReBuildDataChannel((int)this.numInValueCount.Value, (int)this.numOutValueCount.Value, (int)this.numInSwitchCount.Value, (int)this.numOutSwitchCount.Value);

                    this.btnInValue.Enabled = this.Device.InValueChannels.GetCount() > 0;

                    this.btnOutValue.Enabled = this.Device.OutValueChannels.GetCount() > 0;

                    this.btnInSwitch.Enabled = this.Device.InSwitchChannels.GetCount() > 0;

                    this.btnOutSwitch.Enabled = this.Device.OutSwitchChannels.GetCount() > 0;
                }
            }
            else
            {
                MessageBox.Show(@"请先选择设备类型", Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void cmbDeviceImp_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(this.cmbDeviceImp.SelectedItem is DeviceImpInfo deviceInfo)
            {
                pnlMain.Enabled = true;
                this.GetDeviceInstance(deviceInfo.ImpFullName);
            }
            else
            {
                pnlMain.Enabled = false;
            }
        }

        private void chkIsUsed_CheckedChanged(object sender, EventArgs e)
        {
            if(this.Device != null)
            {
                this.Device.IsUsed = this.chkIsUsed.Checked;
            }
        }

        private void cmbCommType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(this.Device != null)
            {
                if(this.cmbCommType.SelectedIndex >= 0)
                {
                    this.pnlCommParam.Controls.Clear();

                    if(this.cmbCommType.Text == eDeviceCommType.COM.ToString())
                    {
                        var sParamPanel = new SerialPortParamPanel(this.Device)
                        {
                            Dock = DockStyle.Fill
                        };
                        this._iCommParam = sParamPanel;
                        this.pnlCommParam.Controls.Add(sParamPanel);
                        this.Device.DeviceCommType = eDeviceCommType.COM.ToString();
                    }
                    else if(this.cmbCommType.Text == eDeviceCommType.COM_SP.ToString())
                    {
                        var sParamPanel = new SerialPortParamPanel(this.Device)
                        {
                            Dock = DockStyle.Fill
                        };
                        this._iCommParam = sParamPanel;
                        this.pnlCommParam.Controls.Add(sParamPanel);
                        this.Device.DeviceCommType = eDeviceCommType.COM_SP.ToString();
                    }
                    else if(this.cmbCommType.Text == eDeviceCommType.NET_C.ToString())
                    {
                        var nWParamPanel = new NetWorkParamPanel(this.Device, true) { Dock = DockStyle.Fill };
                        this._iCommParam = nWParamPanel;
                        this.pnlCommParam.Controls.Add(nWParamPanel);
                        this.Device.DeviceCommType = eDeviceCommType.NET_C.ToString();
                    }
                    else if(this.cmbCommType.Text == eDeviceCommType.NET_S.ToString())
                    {
                        var nWParamPanel = new NetWorkParamPanel(this.Device, false) { Dock = DockStyle.Fill };
                        this._iCommParam = nWParamPanel;
                        this.pnlCommParam.Controls.Add(nWParamPanel);
                        this.Device.DeviceCommType = eDeviceCommType.NET_S.ToString();
                    }
                    else if(this.cmbCommType.Text == eDeviceCommType.FILE.ToString())
                    {
                        var oParamPanel = new OtherParamPanel(this.Device) { Dock = DockStyle.Fill };
                        this._iCommParam = oParamPanel;
                        this.pnlCommParam.Controls.Add(oParamPanel);
                        this.Device.DeviceCommType = eDeviceCommType.FILE.ToString();
                    }
                    else
                    {
                        var oParamPanel = new OtherParamPanel(this.Device) { Dock = DockStyle.Fill };
                        this._iCommParam = oParamPanel;
                        this.pnlCommParam.Controls.Add(oParamPanel);
                        this.Device.DeviceCommType = eDeviceCommType.OTHER.ToString();
                    }
                }
            }
            else
            {
                MessageBox.Show(@"请先选择设备类型", Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// 搜索条件变化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtSearchCondition_TextChanged(object sender, EventArgs e)
        {
            FillDeviceImpClass();
        }

        #region 通道详细配置

        private void btnInValue_Click(object sender, EventArgs e)
        {
            if(this.Device.InValueChannels.GetCount() > 0)
            {
                ChannelConfigForm chlConfigForm = new ChannelConfigForm(this.Device.InValueChannels, eDeviceChannelType.InValue);
                chlConfigForm.ShowDialog();
            }
        }

        private void btnOutValue_Click(object sender, EventArgs e)
        {
            if(this.Device.OutValueChannels.GetCount() > 0)
            {
                ChannelConfigForm chlConfigForm = new ChannelConfigForm(this.Device.OutValueChannels, eDeviceChannelType.OutValue);
                chlConfigForm.ShowDialog();
            }
        }

        private void btnInSwitch_Click(object sender, EventArgs e)
        {
            if(this.Device.InSwitchChannels.GetCount() > 0)
            {
                ChannelConfigForm chlConfigForm = new ChannelConfigForm(this.Device.InSwitchChannels, eDeviceChannelType.InSwitch);
                chlConfigForm.ShowDialog();
            }
        }

        private void btnOutSwitch_Click(object sender, EventArgs e)
        {
            if(this.Device.OutSwitchChannels.GetCount() > 0)
            {
                ChannelConfigForm chlConfigForm = new ChannelConfigForm(this.Device.OutSwitchChannels, eDeviceChannelType.OutSwitch);
                chlConfigForm.ShowDialog();
            }
        }

        #endregion

        #endregion

        #region 私有方法

        private void ShowDeviceConfig()
        {
            if(this.CurConfigType == DeviceConfigType.Edit)
            {
                this.cmbDeviceImp.Enabled = false;
                this.txtId.Enabled = false;
                this.txtSearchCondition.Enabled = false;
            }
            this.cmbCommType.Items.Clear();
            string[] commTypes = Enum.GetNames(typeof(eDeviceCommType));
            foreach(string str in commTypes)
            {
                this.cmbCommType.Items.Add(str);
            }

            ProtocolManager.InitProtocolTable();
            ArrayList proList = ProtocolManager.GetProtocolList();
            foreach(Protocol p in proList)
            {
                // 非远程输出，填充列表
                if(!(p is RemoteProtocol))
                {
                    this.cmbDeviceCommun.Items.Add(p);
                }
            }

            if(this.Device != null)
            {
                Protocol pro = ProtocolManager.GetProtocol(this.Device.ProtocolImp);
                if(pro != null)
                {
                    this.cmbDeviceCommun.SelectedItem = pro;
                }
            }

            cmbDeviceMeasureType.Items.Clear();
            commTypes = Enum.GetNames(typeof(eDeviceMeasureType));
            foreach(string str in commTypes)
            {
                this.cmbDeviceMeasureType.Items.Add(str);
            }

            if(this.Device != null)
            {
                if(this.CurConfigType == DeviceConfigType.Add)
                {
                    this.txtAddr.Text = string.IsNullOrEmpty(this.Device.Addr) ? "0" : this.Device.Addr;
                    this.txtId.Text = this.Device.id;
                    this.txtName.Text = this.Device.name;
                    this.txtDescription.Text = this.Device.Description;
                    this.txtAlarmGroup.Text = this.Device.AlarmGroupId;
                    this.chkIsUsed.Checked = this.Device.IsUsed;

                    this.cmbDeviceImp.SelectedIndex = -1;
                    this.cmbCommType.SelectedIndex = -1;
                    this.cmbDeviceMeasureType.SelectedIndex = -1;
                }
                else
                {
                    this.txtAddr.Text = string.IsNullOrEmpty(this.Device.Addr) ? "0" : this.Device.Addr;
                    this.txtId.Text = this.Device.id;
                    this.txtName.Text = this.Device.name;
                    this.txtDescription.Text = this.Device.Description;
                    this.txtAlarmGroup.Text = this.Device.AlarmGroupId;
                    this.chkIsUsed.Checked = this.Device.IsUsed;

                    this.numInValueCount.Value = this.Device.InValueChannels.GetCount();
                    this.numOutValueCount.Value = this.Device.OutValueChannels.GetCount();
                    this.numInSwitchCount.Value = this.Device.InSwitchChannels.GetCount();
                    this.numOutSwitchCount.Value = this.Device.OutSwitchChannels.GetCount();

                    if(this.Device.InValueChannels.GetCount() > 0)
                    {
                        this.btnInValue.Enabled = true;
                    }

                    if(this.Device.OutValueChannels.GetCount() > 0)
                    {
                        this.btnOutValue.Enabled = true;
                    }

                    if(this.Device.InSwitchChannels.GetCount() > 0)
                    {
                        this.btnInSwitch.Enabled = true;
                    }

                    if(this.Device.OutSwitchChannels.GetCount() > 0)
                    {
                        this.btnOutSwitch.Enabled = true;
                    }
                    // modify by xiaopeng_liu 2017-12-06 解决修改设备时若设备名称自定义过就无法在选择框中展示的问题
                    this.cmbDeviceImp.Text = ReflectionHelper.CreateInstance(this.Device.GetType().FullName).ToString();

                    if(this.Device.DeviceCommType != null)
                    {
                        this.cmbCommType.Text = this.Device.DeviceCommType;
                    }

                    if(!string.IsNullOrEmpty(this.Device.TypeDesc))
                    {
                        this.cmbDeviceMeasureType.Text = this.Device.TypeDesc;
                    }
                }

                if(!this.Device.NeedDefaultCommView)
                {
                    this.Size = new Size(this.Size.Width, this.Size.Height - 106);
                    this.gbxCommParam.Visible = false;
                }
            }
        }

        /// <summary>
        /// 填充设备实现类选择框
        /// </summary>
        private void FillDeviceImpClass()
        {
            Type[] types = ReflectionHelper.GetChildTypes(typeof(Device));

            if(types.Length > 0)
            {
                this.cmbDeviceImp.Items.Clear();

                // modify by xiaopeng_liu 2017-3-25 带排序
                var typeList = new List<Type>(types);
                typeList.Sort((x, y) => string.Compare(x.FullName, y.FullName, StringComparison.Ordinal));

                // 符合筛选条件的设备
                List<DeviceImpInfo> validDevice = new List<DeviceImpInfo>();
                int i = 0;
                foreach(Type type in typeList)
                {
                    // 判断筛选条件
                    if(ReflectionHelper.CreateInstance(type.FullName).ToString().ToLower().Contains(txtSearchCondition.Text.ToLower()))
                    {
                        validDevice.Add(new DeviceImpInfo(type.FullName, ReflectionHelper.CreateInstance(type.FullName).ToString()));
                        Console.WriteLine(i++ + ":" + type.FullName);
                    }

                }
                this.cmbDeviceImp.Items.AddRange(validDevice.ToArray());

                // 当前有设备选中
                if(this.Device != null)
                {
                    // 遍历筛选结果，判断当前选中设备是否在筛选结果中
                    foreach(DeviceImpInfo dev in validDevice)
                    {
                        // 筛选结果包含当前选中设备，不进行清除显示操作
                        if(this.Device.GetType().FullName == dev.ImpFullName)
                        {
                            return;
                        }
                    }
                    // 筛选结果不包含当前选中设备，清除当前所有显示
                    ClearAllDisplay();
                }
            }
        }

        /// <summary>
        /// 清除所有显示
        /// </summary>
        private void ClearAllDisplay()
        {
            this.txtAddr.Text = string.Empty;
            this.txtId.Text = string.Empty;
            this.txtName.Text = string.Empty;
            this.txtDescription.Text = string.Empty;
            this.txtAlarmGroup.Text = string.Empty;
            this.chkIsUsed.Checked = false;

            this.numInValueCount.Value = 0;
            this.numOutValueCount.Value = 0;
            this.numInSwitchCount.Value = 0;
            this.numOutSwitchCount.Value = 0;

            btnOutValue.Enabled = btnInSwitch.Enabled = btnOutSwitch.Enabled = btnInValue.Enabled = false;
            this.cmbCommType.SelectedIndex = -1;
            this.cmbDeviceMeasureType.SelectedIndex = -1;
        }

        private void GetDeviceInstance(string deviceImpFullName)
        {
            if(this.Device == null)
            {
                this.Device = ReflectionHelper.CreateInstance(deviceImpFullName) as Device;
                this.CurConfigType = DeviceConfigType.Modify;
                this.ShowDeviceConfig();
            }
            else
            {
                if(this.Device.GetType().FullName.Equals(deviceImpFullName))
                {
                    return;
                }

                this.Device = ReflectionHelper.CreateInstance(deviceImpFullName) as Device;
                this.ShowDeviceConfig();
            }
        }

        /// <summary>
        /// 保存配置参数
        /// </summary>
        public void SaveDeviceConfigParam()
        {
            if(this.Device != null)
            {
                Check();
                if(this._iCommParam != null)
                {
                    this._iCommParam.SaveCommParam();
                }
                this.Device.Addr = this.txtAddr.Text;
                this.Device.id = this.txtId.Text;
                this.Device.name = this.txtName.Text;
                this.Device.Description = this.txtDescription.Text;
                this.Device.AlarmGroupId = this.txtAlarmGroup.Text;
                this.Device.AlarmSourceId = this.txtId.Text;
                this.Device.TypeDesc = this.cmbDeviceMeasureType.Text;
                this.Device.IsUsed = this.chkIsUsed.Checked;

                //update timer description pan_xu 2013.6.20
                if(this.Device.Timer != null)
                {
                    this.Device.Timer.description = this.Device.name + "数据采集定时器";
                }

                if(this.CurConfigType == DeviceConfigType.Modify)
                {
                    foreach(Device dev in DeviceManager.GetInstance().GetDeviceList())
                    {
                        if(dev.id == this.Device.id)
                        {
                            throw new Exception("已存在该ID的设备，请使用其他ID保存");
                        }

                        if(dev.name == this.Device.name)
                        {
                            throw new Exception("已存在该名称的设备，请使用其他名称保存");
                        }
                    }

                    DeviceManager.GetInstance().Devices.AddNode(this.Device);
                }

                DeviceManager.GetInstance().Save();
            }
        }

        private void Check()
        {
            if(cmbDeviceMeasureType.SelectedIndex == -1)
            {
                throw new Exception("设备测量类型未配置！");
            }
            if(cmbCommType.SelectedIndex == -1)
            {
                throw new Exception("设备通信类型未配置！");
            }
        }

        #endregion

        private void cmbDeviceCommun_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(this.cmbDeviceCommun.SelectedItem is Protocol pro)
            {
                this.Device.ProtocolImp = pro.GetType().FullName;
                toolTip1.SetToolTip(cmbDeviceCommun, pro.ToString());
            }
        }
    }

    public class DeviceImpInfo
    {
        public string ImpFullName
        {
            get;
            private set;
        }

        public string ShowSelectName
        {
            set;
            private get;
        }

        public DeviceImpInfo(string impFullName, string showSelectName)
        {
            this.ImpFullName = impFullName;
            this.ShowSelectName = showSelectName;
        }

        public override string ToString()
        {
            return this.ShowSelectName;
        }
    }

    public enum DeviceConfigType : byte
    {
        /// <summary>
        /// 新增设备
        /// </summary>
        Add = 1,

        /// <summary>
        /// 编辑设备
        /// </summary>
        Edit = 2,

        /// <summary>
        /// 修改模式（从增加模式切换到修改模式）
        /// </summary>
        Modify = 3
    }
}