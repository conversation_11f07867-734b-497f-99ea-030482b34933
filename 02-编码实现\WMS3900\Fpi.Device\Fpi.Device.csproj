﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{88FEF5D2-E039-4AC0-942B-442F23755978}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.Devices</RootNamespace>
    <AssemblyName>Fpi.Device</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Channel\DataChannelFactory.cs" />
    <Compile Include="Devices\Channel\DataProcessor\Coefficient\CoeffProcessor.cs" />
    <Compile Include="Devices\Channel\DataProcessor\Coefficient\ConfigCoeffPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\Coefficient\ConfigCoeffPanel.Designer.cs">
      <DependentUpon>ConfigCoeffPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\A2BLimitProcessor.cs" />
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\A2BProcessor.cs" />
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\A2BRoundProcessor.cs" />
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\ConfigA2BPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\ConfigA2BPanel.Designer.cs">
      <DependentUpon>ConfigA2BPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\ConfigA2BRoundPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\ConvertA2B\ConfigA2BRoundPanel.Designer.cs">
      <DependentUpon>ConfigA2BRoundPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\RoundData\ConfigRoundPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\RoundData\ConfigRoundPanel.Designer.cs">
      <DependentUpon>ConfigRoundPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\RoundData\RoundProcessor.cs" />
    <Compile Include="Devices\Channel\DataProcessor\ScopeRevise\ConfigScopeRevisePanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\ScopeRevise\ConfigScopeRevisePanel.Designer.cs">
      <DependentUpon>ConfigScopeRevisePanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\Channel\DataProcessor\ScopeRevise\ScopeReviseProcessor.cs" />
    <Compile Include="Devices\Channel\ValueChannel.cs" />
    <Compile Include="Devices\DeciveEnum.cs" />
    <Compile Include="Devices\DeviceManager.cs" />
    <Compile Include="Devices\Device.cs" />
    <Compile Include="Devices\Channel\DataChannel.cs" />
    <Compile Include="Devices\Channel\DataProcessor\IParamConfig.cs" />
    <Compile Include="Devices\Channel\InValueChannel.cs" />
    <Compile Include="Devices\Channel\InSwitchChannel.cs" />
    <Compile Include="Devices\Channel\OutValueChannel.cs" />
    <Compile Include="Devices\Channel\OutSwitchChannel.cs" />
    <Compile Include="Devices\Channel\DataProcessor\Processor.cs" />
    <Compile Include="Devices\DeviceProtocols\CommonProtocol\CommonPort.cs" />
    <Compile Include="Devices\DeviceProtocols\CommonProtocol\CommonProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\CommonProtocol\CommonProtocolDesc.cs" />
    <Compile Include="Devices\DeviceProtocols\CommonProtocol\CommonSyncParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Fpi_General\FpiBoardParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Fpi_General\FpiBoardProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\Fpi_General\FpiBoardProtocolDesc.cs" />
    <Compile Include="Devices\DeviceProtocols\HJ212\HJ212Parser.cs" />
    <Compile Include="Devices\DeviceProtocols\HJ212\H212Port.cs" />
    <Compile Include="Devices\DeviceProtocols\HJ212\HJ212Protocol.cs" />
    <Compile Include="Devices\DeviceProtocols\HJ212\HJ212ProtocolDesc.cs" />
    <Compile Include="Devices\DeviceProtocols\Http\HttpParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Hub_Fpi\HubParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Hub_Fpi\HubProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\Hub_Fpi_New\NewHubParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Hub_Fpi_New\NewHubProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\Hub_Fpi_New\NewHubReceiver.cs" />
    <Compile Include="Devices\DeviceProtocols\LineProtocol\LineProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\LineProtocol\LineSyncParser.cs" />
    <Compile Include="Devices\DeviceProtocols\LineProtocol\LinePort.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModbusProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModBusTCPPYProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModbusTCPProtocolDesc.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModbusTCPPort.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModbusTCPParser.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModbusTCPProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\ModBusTCP\ModbusTCPPYPort.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusRevertParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusRevertPort.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusPort.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusParser.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusRevertProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\Modbus\ModbusProtocolDesc.cs" />
    <Compile Include="Devices\DeviceProtocols\SimpleSync\SimpleSyncPort.cs" />
    <Compile Include="Devices\DeviceProtocols\SimpleSync\SimpleSyncParser.cs" />
    <Compile Include="Devices\DeviceProtocols\SimpleSync\SimpleSyncProtocol.cs" />
    <Compile Include="Devices\DeviceProtocols\SimpleReceive\SampleReceiver.cs" />
    <Compile Include="Devices\DeviceProtocols\SimpleReceive\SimpleParser.cs" />
    <Compile Include="Devices\DeviceProtocols\SimpleReceive\SimpleReceiveProtocol.cs" />
    <Compile Include="Devices\Interface\IIODeviceOperation.cs" />
    <Compile Include="Devices\UI\DeviceConfigPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\UI\DeviceConfigPanel.Designer.cs">
      <DependentUpon>DeviceConfigPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\EditValueChannelForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Devices\UI\EditValueChannelForm.Designer.cs">
      <DependentUpon>EditValueChannelForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\EditSwitchChannelForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Devices\UI\EditSwitchChannelForm.Designer.cs">
      <DependentUpon>EditSwitchChannelForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\ChannelConfigForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Devices\UI\ChannelConfigForm.Designer.cs">
      <DependentUpon>ChannelConfigForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\SingleDeviceConfigForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Devices\UI\SingleDeviceConfigForm.Designer.cs">
      <DependentUpon>SingleDeviceConfigForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\SerialPortParamPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\UI\SerialPortParamPanel.Designer.cs">
      <DependentUpon>SerialPortParamPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\NetWorkParamPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\UI\NetWorkParamPanel.Designer.cs">
      <DependentUpon>NetWorkParamPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Devices\UI\OtherParamPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Devices\UI\OtherParamPanel.Designer.cs">
      <DependentUpon>OtherParamPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Devices\Channel\SwitchChannel.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <DependentUpon>Resources.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Alarm\Fpi.Alarm.csproj">
      <Project>{E714875C-0EC1-4C0F-8571-D0F631430C82}</Project>
      <Name>Fpi.Alarm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Instrument\Fpi.Instrument.csproj">
      <Project>{E8D1EB85-2B23-4622-8CDE-80D5F850CC74}</Project>
      <Name>Fpi.Instrument</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Devices\Channel\DataProcessor\Coefficient\ConfigCoeffPanel.resx">
      <DependentUpon>ConfigCoeffPanel.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\Channel\DataProcessor\ConvertA2B\ConfigA2BPanel.resx">
      <DependentUpon>ConfigA2BPanel.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\Channel\DataProcessor\ConvertA2B\ConfigA2BRoundPanel.resx">
      <DependentUpon>ConfigA2BRoundPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\Channel\DataProcessor\RoundData\ConfigRoundPanel.resx">
      <DependentUpon>ConfigRoundPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\Channel\DataProcessor\ScopeRevise\ConfigScopeRevisePanel.resx">
      <DependentUpon>ConfigScopeRevisePanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\DeviceConfigPanel.resx">
      <DependentUpon>DeviceConfigPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\EditValueChannelForm.en-US.resx">
      <DependentUpon>EditValueChannelForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\EditValueChannelForm.resx">
      <DependentUpon>EditValueChannelForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\EditSwitchChannelForm.en-US.resx">
      <DependentUpon>EditSwitchChannelForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\EditSwitchChannelForm.resx">
      <DependentUpon>EditSwitchChannelForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\ChannelConfigForm.en-US.resx">
      <DependentUpon>ChannelConfigForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\ChannelConfigForm.resx">
      <DependentUpon>ChannelConfigForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\SingleDeviceConfigForm.resx">
      <DependentUpon>SingleDeviceConfigForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\SerialPortParamPanel.resx">
      <DependentUpon>SerialPortParamPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\NetWorkParamPanel.resx">
      <DependentUpon>NetWorkParamPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Devices\UI\OtherParamPanel.resx">
      <DependentUpon>OtherParamPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.en-US.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>