﻿namespace Fpi.Communication.Crc
{
    /// <summary>
    /// CRC8校验类.
    /// </summary>
    public static class CRC8
    {
        private static byte[] table = new byte[256];
        private const byte poly = 0xe5;

        public static byte ComputeChecksum(params byte[] bytes)
        {
            byte crc = 0;
            if(bytes != null && bytes.Length > 0)
            {
                foreach(byte b in bytes)
                {
                    crc = table[crc ^ b];
                }
            }
            return crc;
        }

        static CRC8()
        {
            for(int i = 0; i < 256; ++i)
            {
                int temp = i;
                for(int j = 0; j < 8; ++j)
                {
                    if((temp & 0x80) != 0)
                    {
                        temp = (temp << 1) ^ poly;
                    }
                    else
                    {
                        temp <<= 1;
                    }
                }
                table[i] = (byte)temp;
            }
        }
        public static byte CRC(byte[] buffer)
        {
            byte crc = 0;
            if(buffer != null && buffer.Length > 0)
            {
                foreach(byte b in buffer)
                {
                    crc = table[crc ^ b];
                }
            }
            return crc;
        }


        public static byte CalcCrc(byte[] buffer, byte poly)
        {
            byte[] table = new byte[256];

            for(int i = 0; i < 256; ++i)
            {
                int temp = i;
                for(int j = 0; j < 8; ++j)
                {
                    if((temp & 0x80) != 0)
                    {
                        temp = (temp << 1) ^ poly;
                    }
                    else
                    {
                        temp <<= 1;
                    }
                }
                table[i] = (byte)temp;
            }
            byte crc = 0;
            if(buffer != null && buffer.Length > 0)
            {
                foreach(byte b in buffer)
                {
                    crc = table[crc ^ b];
                }
            }
            return crc;
        }
    }
}