﻿namespace Fpi.Communication.Crc
{
    /// <summary>
    /// CRC16校验类(老版).
    /// </summary>
    public class Crc16_Old
    {
        public static uint Crc16(byte[] data, int dataLength)
        {
            uint reg16 = 0xFFFF;
            byte regHi, regLow, charCheck, charOut;
            int i, j;

            for(i = 0; i < dataLength; i++)
            {
                regHi = (byte)((reg16 >> 8) & 0x00FF);
                regLow = (byte)(reg16 & 0x00FF);
                charCheck = data[i];
                reg16 = (uint)(regHi ^ charCheck);//此处我认为高位异或后赋值到高位而不是直接赋值给16位寄存器
                for(j = 0; j < 8; j++)
                {
                    charOut = (byte)(reg16 & 0x0001);
                    reg16 = reg16 >> 1;
                    if(0x0001 == charOut)
                    {
                        reg16 = reg16 ^ 0xA001;

                    }
                }
            }
            return reg16;
        }
    }
}
