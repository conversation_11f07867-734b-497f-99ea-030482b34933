using System;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Properties;

namespace Fpi.Data.UI.PC.ValueNodeViews
{
    internal partial class FormEditScope : Form
    {
        public FormEditScope()
        {
            InitializeComponent();
        }

        public FormEditScope(Scope scope)
            : this()
        {
            this.Scope = scope;
        }

        public Scope Scope { get; private set; }

        private void FormEditScope_Load(object sender, EventArgs e)
        {
            if(Scope != null)
            {
                this.txtId.Text = Scope.id;
                this.txtName.Text = Scope.name;
                this.nuMin.Value = (decimal)Scope.MinValue;
                this.nuMax.Value = (decimal)Scope.MaxValue;

                if(Scope.CalK == null)
                {
                    Scope.CalK = new CalK(1, 0);
                }

                this.nuK.Value = (decimal)Scope.CalK.k;
                this.nuZ.Value = (decimal)Scope.CalK.z;
            }
        }

        private void btnCalk_Click(object sender, EventArgs e)
        {
            FormComputeCalk form = new FormComputeCalk(Scope.CalK);
            if(form.ShowDialog() == DialogResult.OK)
            {
                this.nuK.Value = (decimal)form.Calk.k;
                this.nuZ.Value = (decimal)form.Calk.z;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtId.Text) || string.IsNullOrEmpty(this.txtName.Text))
            {
                throw new Exception(Resources.IdNameEmpty);
            }

            if(Scope == null)
            {
                Scope = new Scope();
            }

            Scope.id = this.txtId.Text;
            Scope.name = this.txtName.Text;
            Scope.MinValue = (double)this.nuMin.Value;
            Scope.MaxValue = (double)this.nuMax.Value;
            Scope.CalK.k = (double)this.nuK.Value;
            Scope.CalK.z = (double)this.nuZ.Value;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {

        }
    }
}