﻿namespace Fpi.DB.Manager
{
    /// <summary>
    /// 数据库字段类
    /// </summary>
    public class FpiColumn
    {
        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn()
        {
        }
        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn(string name, ColumnType type)
        {
            Name = name;
            Type = type;
        }
        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn(string name, ColumnType type, string indexname)
        {
            Name = name;
            Type = type;
            IndexName = indexname;
        }
        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn(string name, ColumnType type, int len)
        {
            Name = name;
            Type = type;
            Length = len;
        }
        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn(string name, ColumnType type, int len, bool primaryKey)
        {
            Name = name;
            Type = type;
            Length = len;
            PrimaryKey = primaryKey;
            if(primaryKey)
            {
                CanNull = false;
            }
        }
        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn(string name, ColumnType type, int len, string indexname)
        {
            Name = name;
            Type = type;
            Length = len;
            IndexName = indexname;
        }

        /// <summary>
        /// 构造
        /// </summary>
        public FpiColumn(string name, ColumnType type, bool autoIncrement, bool primaryKey)
        {
            Name = name;
            Type = type;
            AutoColumn = autoIncrement;
            PrimaryKey = primaryKey;
            if(primaryKey)
            {
                CanNull = false;
            }
        }

        /// <summary>
        /// 字段名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 字段类型
        /// </summary>
        public ColumnType Type { get; set; }

        /// <summary>
        /// 是否自增列
        /// </summary>
        public bool AutoColumn { get; set; }

        /// <summary>
        /// 是否主键
        /// </summary>
        public bool PrimaryKey { get; set; }

        /// <summary>
        /// 是否可空
        /// </summary>
        public bool CanNull { get; set; } = true;

        /// <summary>
        /// 索引名
        /// </summary>
        public string IndexName { get; set; } = string.Empty;

        /// <summary>
        /// 长度
        /// </summary>
        public int Length { get; set; } = 10;
    }
}
