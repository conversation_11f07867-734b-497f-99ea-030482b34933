﻿using System;
using System.Data;
using Fpi.DB.Manager;
using MySql.Data.MySqlClient;

namespace Fpi.DB
{
    /// <summary>
    /// MySQL专门驱动
    /// </summary>
    public class MySqlFactory : IDbFactory
    {
        public MySqlFactory()
        {
            ConnectString = DataBaseManager.GetInstance().GetDataBaseConStr();
        }

        public MySqlFactory(string constr)
        {
            ConnectString = constr;
        }

        #region IDbFactory 成员

        public string ConnectString { get; }

        public IDbConnection CreateConnection()
        {
            return new MySqlConnection(this.ConnectString);
        }


        public IDbDataAdapter CreateDataAdapter()
        {
            return new MySqlDataAdapter();
        }

        public IDataParameter CreateDataParameter()
        {
            return new MySqlParameter();
        }

        #region 平台以前的方法，为了兼容而保留

        /// <summary>
        /// 创建数据库。
        /// </summary>
        /// <param name="dbName"></param>
        /// <returns></returns>
        public bool CreateDb(string dbName)
        {
            string connectString = this.ConnectString;
            int index = connectString.IndexOf("DATABASE", StringComparison.OrdinalIgnoreCase);
            connectString = connectString.Substring(0, index);

            IDbConnection conn = null;
            try
            {
                conn = new MySqlConnection(connectString);
                conn.Open();

                string sql = "CREATE DATABASE IF NOT EXISTS " + dbName + " DEFAULT CHARACTER SET utf8";

                IDbCommand command = conn.CreateCommand();
                command.CommandText = sql;

                command.ExecuteNonQuery();
            }
            finally
            {
                if(null != conn)
                {
                    conn.Close();
                }
            }
            return true;
        }

        #endregion

        #endregion
    }
}
