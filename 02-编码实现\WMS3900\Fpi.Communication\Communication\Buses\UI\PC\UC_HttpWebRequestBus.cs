﻿using System;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Extensions;
using Fpi.Xml;

namespace Fpi.Communication.Buses.UI.PC
{
    public partial class UC_HttpWebRequestBus : BaseConfigureView// System.Windows.Forms.UserControl
    {
        public UC_HttpWebRequestBus()
        {
            InitializeComponent();
        }

        protected override void ShowConfig(object obj)
        {
            this.ParentForm.Text = "Http客户端配置选项";
            BaseNode configNode = configObj as BaseNode;
            this.txtUrl.Text = configNode.GetPropertyValue(HttpWebRequestBus.PropertyName_URL, string.Empty);
            this.txtHttpHeaders.Text = configNode.GetPropertyValue(HttpWebRequestBus.PropertyName_Headers, string.Empty);
            this.cmbContentTypes.Text = configNode.GetPropertyValue(HttpWebRequestBus.PropertyName_ContentType, "application/json");

            this.chkNeedSaveLog.Checked = configNode.GetPropertyValue(BaseBus.PropertyName_NeedSaveLog, string.Empty).TryValue<bool>(true);
        }

        public override void Check()
        {
            base.Check();
            if(string.IsNullOrEmpty(this.txtUrl.Text))
            {
                throw new Exception("必须配置Http服务器访问地址！");
            }
        }

        public override object Save()
        {
            BaseNode configNode = configObj as BaseNode;

            configNode.SetProperty(HttpWebRequestBus.PropertyName_URL, this.txtUrl.Text);
            configNode.SetProperty(HttpWebRequestBus.PropertyName_Headers, this.txtHttpHeaders.Text);
            configNode.SetProperty(HttpWebRequestBus.PropertyName_ContentType, this.cmbContentTypes.Text);

            configNode.SetProperty(BaseBus.PropertyName_NeedSaveLog, this.chkNeedSaveLog.Checked.ToString());

            return configNode;
        }
    }
}