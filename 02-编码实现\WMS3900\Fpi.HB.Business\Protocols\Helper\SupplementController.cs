﻿using System;
using System.Collections.Generic;
using System.Threading;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Manager;
using Fpi.Communication.Protocols;
using Fpi.HB.Business.Protocols.Interface;

namespace Fpi.HB.Business.Protocols.Helper
{
    /// <summary>
    /// 补传控制器
    /// </summary>
    public class SupplementController
    {
        #region 字段属性

        /// <summary>
        /// 任务是否执行中标志量
        /// </summary>
        public bool IsRunning { get; private set; }

        /// <summary>
        /// 补遗任务总数据量
        /// </summary>
        public int DataSum { get; private set; }

        /// <summary>
        /// 补遗任务当前补传数据量
        /// </summary>
        public int DataIndex { get; private set; }

        /// <summary>
        /// 是否需要停止当前任务标志量
        /// </summary>
        private bool _stopTask;

        /// <summary>
        /// 任务执行状态操作锁
        /// </summary>
        private readonly object _syncObject = new object();

        #region 补传任务参数

        private Pipe _supplePipe;
        private DateTime _startSuppleTime;
        private DateTime _endSuppleTime;
        private int _sendInterval;
        private int _suppleType;
        private IDateSupplementListener _suppleListener;

        #endregion

        #endregion

        #region 公共方法

        /// <summary>
        /// 异步启动补遗
        /// </summary>
        public void StartSuppleAsync(Pipe pipe, DateTime startTime, DateTime endTime, int type, IDateSupplementListener listener, int sendInterval = 2)
        {
            // 任务触发/取消，加全局锁
            lock(_syncObject)
            {
                // 检查任务状态
                if(IsRunning)
                {
                    throw new Exception("当前任务执行中，请稍后再试！");
                }

                // 置任务进行中的状态标志
                IsRunning = true;

                try
                {
                    // 通道启用，且所用协议实现了IGetSuppleData接口
                    if(!pipe.valid || pipe.Protocol == null || !(pipe.Protocol.Sender is IGetSuppleData))
                    {
                        throw new Exception("当前所选数据传输通道无效！");
                    }

                    // 时间参数检查
                    if(startTime >= endTime)
                    {
                        throw new Exception("起始时间不可晚于结束时间！");
                    }

                    _supplePipe = pipe;
                    _suppleListener = listener;
                    _suppleType = type;
                    _sendInterval = sendInterval;
                    _startSuppleTime = startTime;
                    _endSuppleTime = endTime;

                    // 清除任务被停止标志
                    _stopTask = false;

                    new Thread(DoSupple).Start();
                }
                catch(Exception e)
                {
                    // 触发异常时，取消任务进行中的状态标志
                    lock(_syncObject)
                    {
                        IsRunning = false;
                    }
                    throw e;
                }
            }
        }

        /// <summary>
        /// 触发停止补遗
        /// </summary>
        public void StopSupple()
        {
            // 任务触发/取消，加全局锁
            lock(_syncObject)
            {
                if(IsRunning)
                {
                    _stopTask = true;
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行补传任务
        /// 数据获取在业务类，数据发送在本方法。方便做过程管控
        /// </summary>
        private void DoSupple()
        {
            // 任务完成通知信息
            string taskInfo = "";
            try
            {
                // 记录任务开始时间
                var taskStarkTime = DateTime.Now;

                // 检查任务是否被停止
                CheckIsTaskCanceled();

                // 获取待补传数据
                List<IByteStream> suppleDataList = ((IGetSuppleData)_supplePipe.Protocol.Sender).GetSuppleData(_startSuppleTime, _endSuppleTime, _suppleType);
                DataSum = suppleDataList.Count;

                // 检查任务是否被停止
                CheckIsTaskCanceled();

                for(int i = 0; i < suppleDataList.Count; i++)
                {
                    DataIndex = i + 1;

                    // 发送数据
                    var suppleData = suppleDataList[i];
                    _supplePipe.Send(null, suppleData);

                    // 发送间隔
                    Thread.Sleep(_sendInterval * 1000);

                    // Info记录
                    ProtocolLogHelper.ShowSendMsg(suppleData);

                    // 任务执行通知
                    if(_suppleListener != null)
                    {
                        _suppleListener.OnDateSupplement(DataIndex, DataSum);
                    }

                    // 检查任务是否被停止
                    CheckIsTaskCanceled();
                }

                // 记录任务结束时间
                var taskEndTime = DateTime.Now;

                taskInfo = string.Format(
                    "补传任务执行完毕！开始时间：{0:yyyy-MM-dd HH:mm:ss}；结束时间：{1:yyyy-MM-dd HH:mm:ss}；执行耗时： {2} 秒；补传数据总量： {3} 条。",
                    taskStarkTime, taskEndTime, (taskEndTime - taskStarkTime).TotalSeconds, DataSum);
            }
            catch(TaskCancelException)
            {
                taskInfo = string.Format("补传被取消,当前补遗进度：{0}/{1}", DataIndex, DataSum);
            }
            catch(Exception e)
            {
                taskInfo = "补传异常：" + e.Message;
            }
            finally
            {
                lock(_syncObject)
                {
                    IsRunning = false;
                }

                // 任务完成通知
                if(_suppleListener != null)
                {
                    _suppleListener.OnDateSupplementDone(taskInfo);
                }
            }
        }

        /// <summary>
        /// 检查任务是否需要停止
        /// </summary>
        private void CheckIsTaskCanceled()
        {
            if(_stopTask)
            {
                throw new TaskCancelException();
            }
        }

        #endregion

        #region 私有资源

        /// <summary>
        /// 任务取消异常
        /// </summary>
        private class TaskCancelException : Exception
        {
        }

        #endregion
    }
}