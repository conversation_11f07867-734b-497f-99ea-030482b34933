﻿//==================================================================================================
//类名：     Processor   
//创建人:    曹旭
//创建时间:  2012-4-26 14:36:28
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

namespace Fpi.Devices.Channel
{
    public class Processor
    {
        public IParamConfig ucParamConfig;

        /// <summary>
        /// 设置处理器参数，切换处理器时使用
        /// </summary>
        /// <param name="param"></param>
        public virtual void SetParam(string param)
        {

        }

        /// <summary>
        /// 模拟量数据输入处理
        /// </summary>
        /// <param name="inputValue"></param>
        /// <returns></returns>
        public virtual double InputProcessData(double inputValue)
        {
            return inputValue;
        }

        /// <summary>
        /// 模拟量数据输出处理
        /// </summary>
        /// <param name="outputValue"></param>
        /// <returns></returns>
        public virtual double OutputProcessData(double outputValue)
        {
            return outputValue;
        }
    }
}
