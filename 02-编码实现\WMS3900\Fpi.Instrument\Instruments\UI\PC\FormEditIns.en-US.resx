﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>288, 9</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnCancel.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>191, 9</value>
  </data>
  <data name="btnOK.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="pnlFunc.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 275</value>
  </data>
  <data name="pnlFunc.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pnlFunc.Size" type="System.Drawing.Size, System.Drawing">
    <value>385, 46</value>
  </data>
  <data name="nuAddress.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 185</value>
  </data>
  <data name="nuAddress.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="nuAddress.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 21</value>
  </data>
  <data name="cmbRoute.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 146</value>
  </data>
  <data name="cmbRoute.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbRoute.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 23</value>
  </data>
  <data name="cmbType.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 104</value>
  </data>
  <data name="cmbType.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbType.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 23</value>
  </data>
  <data name="txtDesc.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 226</value>
  </data>
  <data name="txtDesc.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtDesc.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 25</value>
  </data>
  <data name="txtName.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 64</value>
  </data>
  <data name="txtName.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtName.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 21</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 150</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 15</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Route instrument</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 108</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 15</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Instrument type</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 230</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 15</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Instrument description</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 188</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>115, 15</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Instrument address</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 68</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 15</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Instrument name</value>
  </data>
  <data name="txtID.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 25</value>
  </data>
  <data name="txtID.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtID.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 21</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 29</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 15</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Instrument number</value>
  </data>
  <data name="groupBox1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>385, 275</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 15</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>385, 321</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9pt</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Instrument edit</value>
  </data>
</root>