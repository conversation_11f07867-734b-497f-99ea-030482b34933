﻿using Fpi.Devices;
using Fpi.UI.PC.DockForms;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Windows.Forms;

namespace Fpi.HB.UI
{
    public partial class FrmDeviceParam : DockView
    {
        #region 字段属性

        /// <summary>
        /// 界面缓存表
        /// </summary>
        private readonly Hashtable _formTable = new Hashtable();

        /// <summary>
        /// 无配置时对应界面
        /// </summary>
        private UC_NoParamPanel _noParamPanel = new UC_NoParamPanel() { Dock = DockStyle.Fill };

        #endregion

        #region 构造

        public FrmDeviceParam()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void DeviceParamForm_Load(object sender, EventArgs e)
        {
            this.InitDeviceTree();
        }

        private void tvDevices_AfterSelect(object sender, TreeViewEventArgs e)
        {
            try
            {
                // 获取当前选中节点
                var node = tvDevices.SelectedNode;

                if(node != null && node.Tag is Device device)
                {
                    if(device != null)
                    {
                        // 界面无效了，或缓存中不存在
                        if((!(_formTable[device.id] is UserControl uc) || uc.IsDisposed))
                        {
                            // 获取配置界面
                            uc = device.GetDeviceParamUC();
                            uc.Dock = DockStyle.Fill;
                            _formTable[device.id] = uc;
                            pnlDeviceUI.Controls.Add(uc);
                        }

                        // 无配置界面
                        if(uc == null)
                        {
                            uc = _noParamPanel;
                        }

                        // 显示在最前端
                        uc.BringToFront();
                    }
                }
            }
            catch(Exception ex)
            {
                //
            }
        }

        /// <summary>
        /// 语音控制切换界面
        /// </summary>
        public void VoiceSwitchUI(Device device)
        {
            if(device != null)
            {
                // 当前显示界面已经是想打开的界面了，不操作
                if(tvDevices.SelectedNode.Tag is Device currentDevice && currentDevice.id == device.id)
                {
                    return;
                }

                TreeNode node = FindTreeNodeByText(this.tvDevices.Nodes, device.name);
                if(node != null)
                {
                    this.tvDevices.SelectedNode = node;
                    node.EnsureVisible();
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化仪器列表
        /// </summary>
        private void InitDeviceTree()
        {
            this.tvDevices.Nodes.Clear();
            TreeNode root = new TreeNode("在线监测系统设备集合");
            root.ImageKey = "equip";
            root.SelectedImageKey = "equip";
            this.tvDevices.Nodes.Add(root);

            List<Device> tempDeviceList = DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS);
            tempDeviceList.AddRange(DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.IO));
            tempDeviceList.AddRange(DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.AUXILI));
            if(tempDeviceList.Count > 0)
            {
                foreach(Device device in tempDeviceList)
                {
                    TreeNode deviceNode = new TreeNode(device.name);
                    deviceNode.Tag = device;
                    if(device.IsUsed)
                    {
                        deviceNode.ImageKey = "equip";
                        deviceNode.SelectedImageKey = "select";
                        root.Nodes.Add(deviceNode);
                    }
                }
            }

            pnlDeviceUI.Controls.Add(_noParamPanel);

            this.tvDevices.Sort();
            this.tvDevices.ExpandAll();
            if(root.Nodes.Count > 0)
            {
                this.tvDevices.SelectedNode = root.Nodes[0];
            }
        }

        /// <summary>
        /// 根据名称查找节点
        /// </summary>
        /// <param name="nodes"></param>
        /// <param name="deviceName"></param>
        /// <returns></returns>
        private TreeNode FindTreeNodeByText(TreeNodeCollection nodes, string deviceName)
        {
            TreeNode found = null;
            foreach(TreeNode item in nodes)
            {
                if(item.Text.Equals(deviceName))
                {
                    return item;
                }

                found = FindTreeNodeByText(item.Nodes, deviceName);
            }
            return found;
        }

        #endregion
    }
}