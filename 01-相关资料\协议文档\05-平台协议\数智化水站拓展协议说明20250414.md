# 数智化水站拓展协议说明

## 一、数据上传

### 1. 常规部分

参照《国家地表水自动监测系统通信协议技术要求》文档说明。

### 2. 拓展部分

1. 五参数核查数据上传提取
使用`2062`指令。

2. 五参数水样比对数据上传提取(待定，要不要参照国家协议修改为2061)
使用`2067`指令，内容格式同`2062`指令。

3. 盲样核查数据上传提取
使用`2068`指令，内容格式同`2062`指令。（平台可忽略解析StandardValue）

4. 空白测试数据上传提取
使用`2069`指令，内容格式同`2062`指令。

5. 任意浓度标液核查数据上传提取
使用`2070`指令，内容格式同`2062`指令。

6. 多点线性核查数据上传提取
使用`2071`指令，内容如下：
A、B、C、D四点各自的核查时间，核查标准值，核查浓度。总核查时间，线性相关系数。
格式如下：

```ASCII
##0455QN=20250324094400000;ST=21;CN=2071;PW=123456;MN=88888880000001;Flag=5;CP=&&DataTime=20250324080000;w01019-ACheckTime==20250324040000,w01019-ACheckValue=11.0,w01019-AStandardValue=10,w01019-BCheckTime==20250324050000,w01019-BCheckValue=21.0,w01019-BStandardValue=20,w01019-CCheckTime==20250324060000,w01019-CCheckValue=33.0,w01019-CStandardValue=30,w01019-DCheckTime==20250324070000,w01019-DCheckValue=63.0,w01019-DStandardValue=60;w01019-Coefficient=0.9&&B041

```

## 二、平台反控部分

### 1. 单表反控

1. 反控SIA仪表测量
使用`3087`指令，内容格式同`3080`指令。

2. 反控SIA仪表空白测试
使用`3088`指令，内容格式同`3080`指令。

3. 反控SIA仪表初始化（复位排空）
使用`3089`指令，内容格式同`3080`指令。

4. 反控SIA仪表停止测试
使用`3090`指令，内容格式同`3080`指令。

5. 反控SIA仪表仪表重启
使用`3091`指令，内容格式同`3080`指令。

6. 反控SIA仪表标定
使用`3092`指令，内容格式同`3080`指令。

7. 反控SIA仪表试剂填充（试剂导入）
使用`3093`指令，内容格式同`3080`指令。

8. 反控SIA仪表系统排空
使用`3094`指令，内容格式同`3080`指令。

9. 反控SIA仪表检测室清洗
使用`3095`指令，内容格式同`3080`指令。

10. 反控SIA仪表信号调整
使用`3096`指令，内容格式同`3080`指令。

11. 反控SIA仪表一键维护
使用`3097`指令，内容格式同`3080`指令。

12. 反控SIA仪表量程校准
使用`3098`指令，内容格式同`3080`指令。

13. 反控SIA仪表关键器件自检流程
使用`3099`指令，内容格式如下：
自检流程编号OpId。1：选项阀气密性诊断；2：光路诊断；3：选项阀自诊断；4：柱塞泵自诊断；5：光路自诊断；6：温控系统自诊断；7：反应单元自诊断；8：液体检测器自诊断；9：氙灯自诊断。

格式如下：

```ASCII
##0096QN=20250324094400000;ST=21;CN=3099;PW=123456;MN=88888880000001;Flag=5;CP=&&PolId=w01019;OpId=1&&FF01

```

14. 切换五参数仪表运行模式
使用`3100`指令，内容格式同`3042`指令。内容格式如下：
0：受控模式
1：维护模式

15. 五参数仪表复位排空
使用`3101`指令，内容格式同`3044`指令。

16. 五参数仪表紧急停止
使用`3102`指令，内容格式同`3044`指令。

17. 五参数仪表标定
使用`3103`指令。一次触发1到4个电极。内容格式如下：
PolIdList：因子ID列表，用`|`隔开。
w01001 pH 值
w01009 溶解氧
w01003 浑浊度
w01014 电导率
格式如下：

```ASCII
##0106QN=20250324094400000;ST=21;CN=3103;PW=123456;MN=88888880000001;Flag=5;CP=&&PolIdList=w01009|w01003|w01014&&8C41

```

### 2. 集成系统反控

1. 反控单表多点线性核查(下端执行完成后上传测量数据)
使用`4001`指令。一次只触发一台仪表。内容格式如下：
A、B、C、D四点各自的核查标准值。
格式如下：

```ASCII
##0161QN=20250324094400000;ST=21;CN=4001;PW=123456;MN=88888880000001;Flag=5;CP=&&PolId=w01019;AStandardValue=10;BStandardValue=20;CStandardValue=30;DStandardValue=60&&4A40

```

2. 反控单表盲样测试(下端执行完成后上传测量数据)
使用`4002`指令。一次只触发一台仪表。内容格式如下：
盲样核查标准值。
格式如下：

```ASCII
##0106QN=20250324094400000;ST=21;CN=4002;PW=123456;MN=88888880000001;Flag=5;CP=&&PolId=w01019;StandardValue=10&&8C41

```

3. 反控单表任意浓度标液核查(下端执行完成后上传测量数据)
使用`4003`指令。一次只触发一台仪表。内容格式如下：
核查标准值。
格式如下：

```ASCII
##0106QN=20250324094400000;ST=21;CN=4003;PW=123456;MN=88888880000001;Flag=5;CP=&&PolId=w01019;StandardValue=10&&8C41

```

4. 反控五参数仪表测量(下端执行完成后上传测量数据)
使用`4004`指令。一次触发全部因子测量。内容格式同`3044`指令。

5. 反控五参数仪表核查(下端执行完成后上传测量数据)
使用`4005`指令。一次触发1到5个因子，不同因子量程选择不同。内容格式如下：
PolId-FlowId 因子ID-核查流程
1 w01010 水温	FlowId：0 自动判断；1 启用核查1；2 启用核查2；3 启用核查3
2 w01001 pH 值	FlowId：无选择，默认写0即可
3 w01009 溶解氧	FlowId：0 自动判断；1 无氧核查；2 饱和氧核查
4 w01003 浑浊度	FlowId：0 自动判断；1 启用核查1；2 启用核查2；3 启用核查3
5 w01014 电导率 	FlowId：0 自动判断；1 启用核查1；2 启用核查2；3 启用核查3
格式如下：

```ASCII
##0106QN=20250324094400000;ST=21;CN=4005;PW=123456;MN=88888880000001;Flag=5;CP=&&w01010-FlowId=0;w01009-FlowId=1&&8C41

```

6. 反控五参数仪表水样比对(下端执行完成后上传测量数据)
使用`4006`指令。一次触发1到5个因子，不同因子量程选择不同。内容格式同`4005`。

7. 门禁控制
使用`4021`指令。内容格式如下：
门禁状态DoorState，关闭（0）、 开启（1）。
格式如下：

```ASCII
##0088QN=20250324094400000;ST=21;CN=4021;PW=123456;MN=88888880000001;Flag=5;CP=&&DoorState=1&&4380

```

8. 空调控制
使用`4022`指令。内容格式如下：
空调状态ACStatus，关闭（0）、 开启（1）。
空调模式ACMode，送风（0）、制冷（1）、制热（2）、除霜（3）。
空调温度ACTemp，单位 ℃，1位小数，设置空调的温度。
空调状态为开时下面参数必填。为关时可不填。
格式如下：

```ASCII
##0108QN=20250324094400000;ST=21;CN=4022;PW=123456;MN=88888880000001;Flag=5;CP=&&ACStatus=1;ACMode=1;ACTemp=23.1&&6B80

```

9. 排风扇控制
使用`4023`指令。内容格式如下：
排风扇状态FanState，关闭（0）、 开启（1）。
格式如下：

```ASCII
##0087QN=20250324094400000;ST=21;CN=4023;PW=123456;MN=88888880000001;Flag=5;CP=&&FanState=1&&0CC0

```

10. 灯控制
使用`4024`指令。内容格式如下：
灯状态LampState，关闭（0）、 开启（1）。
格式如下：

```ASCII
##0088QN=20250324094400000;ST=21;CN=4024;PW=123456;MN=88888880000001;Flag=5;CP=&&LampState=1&&3740

```

### 3. 月质控流程说明

1. 实际水样比对

- 1. 平台当天提前新建工单，指定水样比对对应周期测量数据时间。
- 2. 实验室监测出结果后（可能不会是当天），在平台上实际水样数据录入界面，找到之前新建的工单，输入测试结果数据。计算比对结果。

2. 集成干预

- 1. 平台提前新建工单，指定集成干预自动流程时间、手动取样时间。
- 2. 下端站点按设定的'自动流程时间'去运行。自动流程运行结束后，在设定的`手动取样时间`之后且在下一次周期测量流程之前去手动启动测量流程，测量结束后以`2061`CN码加`hd`数据标识上传到平台。平台自动关联数据。
