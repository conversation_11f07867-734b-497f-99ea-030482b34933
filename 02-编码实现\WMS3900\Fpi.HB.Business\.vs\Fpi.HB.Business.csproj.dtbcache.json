{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business", "ProjectFileName": "Fpi.HB.Business.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "DataAndParamBackUp\\Config\\BackUpDatalManager.cs"}, {"SourceFile": "DataAndParamBackUp\\UI\\FrmParamRestore.cs"}, {"SourceFile": "DataAndParamBackUp\\UI\\FrmParamRestore.Designer.cs"}, {"SourceFile": "DataAndParamBackUp\\UI\\FrmDataAndParamBackUp.cs"}, {"SourceFile": "DataAndParamBackUp\\UI\\FrmDataAndParamBackUp.designer.cs"}, {"SourceFile": "DataAndParamBackUp\\UI\\FrmDBRestore.cs"}, {"SourceFile": "DataAndParamBackUp\\UI\\FrmDBRestore.Designer.cs"}, {"SourceFile": "DB\\DBConfig.cs"}, {"SourceFile": "DB\\MeasureTableCreator.cs"}, {"SourceFile": "DB\\DBHelper.cs"}, {"SourceFile": "DB\\DBNode.cs"}, {"SourceFile": "HisData\\ReportFormatHelper.cs"}, {"SourceFile": "HisData\\QueryNode.cs"}, {"SourceFile": "HisData\\QueryGroup.cs"}, {"SourceFile": "HisData\\ReportManager.cs"}, {"SourceFile": "Log\\PatternDBAppender.cs"}, {"SourceFile": "Log\\UI\\FrmQueryLog.cs"}, {"SourceFile": "Log\\UI\\FrmQueryLog.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Protocols\\GB\\ConfigUI\\FrmOutPutConfig.cs"}, {"SourceFile": "Protocols\\GB\\ConfigUI\\FrmOutPutConfig.Designer.cs"}, {"SourceFile": "Protocols\\GB\\ConfigUI\\GBConfigUC.cs"}, {"SourceFile": "Protocols\\GB\\ConfigUI\\GBConfigUC.Designer.cs"}, {"SourceFile": "Protocols\\GB\\ConfigUI\\OutputNode.cs"}, {"SourceFile": "Protocols\\GB\\ConfigUI\\OutputNode.Designer.cs"}, {"SourceFile": "Protocols\\GB\\Config\\Enums.cs"}, {"SourceFile": "Protocols\\GB\\Config\\GBConfig.cs"}, {"SourceFile": "Protocols\\GB\\Config\\PolNode.cs"}, {"SourceFile": "Protocols\\GB\\Config\\SingleCfg.cs"}, {"SourceFile": "Protocols\\GB\\GBDataFrame\\CommandParameter.cs"}, {"SourceFile": "Protocols\\GB\\GBDataFrame\\GBCommand.cs"}, {"SourceFile": "Protocols\\GB\\GBDataFrame\\GBHelper.cs"}, {"SourceFile": "Protocols\\GB\\GBDataFrame\\PolParameter.cs"}, {"SourceFile": "Protocols\\GB\\GBDataFrame\\ProtocalExplain.cs"}, {"SourceFile": "Protocols\\GB\\GBParser.cs"}, {"SourceFile": "Protocols\\GB\\GBProtocol.cs"}, {"SourceFile": "Protocols\\GB\\GBProtocolDesc.cs"}, {"SourceFile": "Protocols\\GB\\GBReceiver.cs"}, {"SourceFile": "Protocols\\GB\\GBSender.cs"}, {"SourceFile": "Protocols\\Helper\\Enums.cs"}, {"SourceFile": "Protocols\\Helper\\SupplementController.cs"}, {"SourceFile": "Protocols\\Interface\\IDataUpload.cs"}, {"SourceFile": "Protocols\\Interface\\IDateSupplementListener.cs"}, {"SourceFile": "Protocols\\Interface\\IGetSuppleData.cs"}, {"SourceFile": "Protocols\\UI\\FrmDataSupplement.cs"}, {"SourceFile": "Protocols\\UI\\FrmDataSupplement.designer.cs"}, {"SourceFile": "Tasks\\Config\\CustomTask.cs"}, {"SourceFile": "Tasks\\Config\\CustomTaskManager.cs"}, {"SourceFile": "Tasks\\CustomTask\\DateSimulateTask.cs"}, {"SourceFile": "Tasks\\CustomTask\\DbBackupTask.cs"}, {"SourceFile": "Tasks\\CustomTask\\MinuteDataStorageTask.cs"}, {"SourceFile": "Tasks\\CustomTask\\SecondDataStorageTask.cs"}, {"SourceFile": "Tasks\\TaskLogHelper.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\DevComponents.DotNetBar2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\Ionic.Zip.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\log4net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\NPOI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\NPOI.OOXML.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\WinFormsUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll", "OutputItemRelativePath": "Fpi.HB.Business.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}