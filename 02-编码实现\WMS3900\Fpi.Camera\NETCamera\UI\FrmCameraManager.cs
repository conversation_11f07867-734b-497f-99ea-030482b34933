﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Sunny.UI;

namespace Fpi.Camera.UI
{
    public partial class FrmCameraManager : UIForm
    {
        #region 属性、字段

        #endregion

        #region 构造

        public FrmCameraManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FormConfigCamera_Load(object sender, EventArgs e)
        {
            SetDataGridViewHead();
            InitlvCameraList();
        }

        #region 增删改

        private void btnAdd_Click(object sender, EventArgs e)
        {
            var form = new FrmCameraEdit();
            if(form.ShowDialog() == DialogResult.OK)
            {
                NETCameraManager.GetInstance().Cameras.AddNode(form.Camera);
                this.InitlvCameraList();
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is BaseNETCamera camera)
            {
                if(new FrmCameraEdit(camera).ShowDialog() == DialogResult.OK)
                {
                    this.InitlvCameraList();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择需要编辑的摄像机！");
            }
        }

        private void dgvData_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is BaseNETCamera camera)
            {
                if(new FrmCameraEdit(camera).ShowDialog() == DialogResult.OK)
                {
                    this.InitlvCameraList();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择需要编辑的摄像机！");
            }
        }

        private void btnDel_Click(object sender, EventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is BaseNETCamera camera)
            {
                if(FpiMessageBox.ShowQuestion($"是否确认删除选中的摄像机[{camera.name}]?") == DialogResult.Yes)
                {
                    try
                    {
                        camera.StopRecording();
                        camera.StartRealPlay();
                        camera.StopNoPreviewRealPlay();
                    }
                    catch
                    {
                    }

                    NETCameraManager.GetInstance().Cameras.Remove(camera);

                    this.InitlvCameraList();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择需要删除的摄像机!");
            }
        }

        #endregion

        #region 保存

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                NETCameraManager.GetInstance().Save();
                FpiMessageBox.ShowInfo("保存成功！");
                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("保存失败：" + ex.Message);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if(FpiMessageBox.ShowQuestion("确认取消编辑？") == DialogResult.Yes)
            {
                NETCameraManager.GetInstance().ReLoad();
                DialogResult = DialogResult.Cancel;
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置表头
        /// </summary>
        private void SetDataGridViewHead()
        {
            dgvData.ClearColumns();
            dgvData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            var col = dgvData.AddColumn("序号", "num");
            col.FillWeight = 50;
            col = dgvData.AddColumn("名称", "name");
            col.FillWeight = 300;
            col = dgvData.AddColumn("描述信息", "desc");
            col.FillWeight = 440;
        }

        /// <summary>
        /// 初始化摄像机列表
        /// </summary>
        private void InitlvCameraList()
        {
            dgvData.Rows.Clear();

            foreach(BaseNETCamera camera in NETCameraManager.GetInstance().GetAllCameras())
            {
                int index = dgvData.Rows.Add();
                DataGridViewRow dr = dgvData.Rows[index];
                dr.Tag = camera;
                dr.Cells[0].Value = index + 1;
                dr.Cells[1].Value = camera.name;
                dr.Cells[2].Value = camera.Description;
            }
        }

        #endregion
    }
}