﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Fpi.Instruments.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Fpi.Instruments.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Add {
            get {
                object obj = ResourceManager.GetObject("Add", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 配置已保存，是否立即生效? 的本地化字符串。
        /// </summary>
        internal static string ConfigSave {
            get {
                return ResourceManager.GetString("ConfigSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确定删除:{0}? 的本地化字符串。
        /// </summary>
        internal static string ConfirmDelete {
            get {
                return ResourceManager.GetString("ConfirmDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Edit {
            get {
                object obj = ResourceManager.GetObject("Edit", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 仪器的编号与命名不可为空! 的本地化字符串。
        /// </summary>
        internal static string InstrumentEmpty {
            get {
                return ResourceManager.GetString("InstrumentEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的仪器! 的本地化字符串。
        /// </summary>
        internal static string InstrumentExist {
            get {
                return ResourceManager.GetString("InstrumentExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 仪器类型的编号与命名不可为空! 的本地化字符串。
        /// </summary>
        internal static string InstrumentTypeEmpty {
            get {
                return ResourceManager.GetString("InstrumentTypeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的仪器类型! 的本地化字符串。
        /// </summary>
        internal static string IntrumentTypeExist {
            get {
                return ResourceManager.GetString("IntrumentTypeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 否 的本地化字符串。
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Remove {
            get {
                object obj = ResourceManager.GetObject("Remove", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 系统提示 的本地化字符串。
        /// </summary>
        internal static string SystemPrompt {
            get {
                return ResourceManager.GetString("SystemPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 是 的本地化字符串。
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
