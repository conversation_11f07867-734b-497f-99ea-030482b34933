using System;
using Fpi.Util.Exeptions;

namespace Fpi.Communication.Exceptions
{
    public class CommunicationParamException : PlatformException
    {
        public CommunicationParamException()
            : base()
        {
        }

        public CommunicationParamException(string message)
            : base(message)
        {
        }

        public CommunicationParamException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}