﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{13650425-1448-4DF5-884F-B7CD466ECB24}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.HB.Business</RootNamespace>
    <AssemblyName>Fpi.HB.Business</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevComponents.DotNetBar2, Version=*********, Culture=neutral, PublicKeyToken=c39c3242a43eee2b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zip, Version=*******, Culture=neutral, PublicKeyToken=edbe51ad942a3f5c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Ionic.Zip.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>..\FpiDLL\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataAndParamBackUp\Config\BackUpDatalManager.cs" />
    <Compile Include="DataAndParamBackUp\UI\FrmParamRestore.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DataAndParamBackUp\UI\FrmParamRestore.Designer.cs">
      <DependentUpon>FrmParamRestore.cs</DependentUpon>
    </Compile>
    <Compile Include="DataAndParamBackUp\UI\FrmDataAndParamBackUp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DataAndParamBackUp\UI\FrmDataAndParamBackUp.designer.cs">
      <DependentUpon>FrmDataAndParamBackUp.cs</DependentUpon>
    </Compile>
    <Compile Include="DataAndParamBackUp\UI\FrmDBRestore.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DataAndParamBackUp\UI\FrmDBRestore.Designer.cs">
      <DependentUpon>FrmDBRestore.cs</DependentUpon>
    </Compile>
    <Compile Include="DB\DBConfig.cs" />
    <Compile Include="DB\MeasureTableCreator.cs" />
    <Compile Include="DB\DBHelper.cs" />
    <Compile Include="DB\DBNode.cs" />
    <Compile Include="HisData\ReportFormatHelper.cs" />
    <Compile Include="HisData\QueryNode.cs" />
    <Compile Include="HisData\QueryGroup.cs" />
    <Compile Include="HisData\ReportManager.cs" />
    <Compile Include="Log\PatternDBAppender.cs" />
    <Compile Include="Log\UI\FrmQueryLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Log\UI\FrmQueryLog.Designer.cs">
      <DependentUpon>FrmQueryLog.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Protocols\GB\ConfigUI\FrmOutPutConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Protocols\GB\ConfigUI\FrmOutPutConfig.Designer.cs">
      <DependentUpon>FrmOutPutConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Protocols\GB\ConfigUI\GBConfigUC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Protocols\GB\ConfigUI\GBConfigUC.Designer.cs">
      <DependentUpon>GBConfigUC.cs</DependentUpon>
    </Compile>
    <Compile Include="Protocols\GB\ConfigUI\OutputNode.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Protocols\GB\ConfigUI\OutputNode.Designer.cs">
      <DependentUpon>OutputNode.cs</DependentUpon>
    </Compile>
    <Compile Include="Protocols\GB\Config\Enums.cs" />
    <Compile Include="Protocols\GB\Config\GBConfig.cs" />
    <Compile Include="Protocols\GB\Config\PolNode.cs" />
    <Compile Include="Protocols\GB\Config\SingleCfg.cs" />
    <Compile Include="Protocols\GB\GBDataFrame\CommandParameter.cs" />
    <Compile Include="Protocols\GB\GBDataFrame\GBCommand.cs" />
    <Compile Include="Protocols\GB\GBDataFrame\GBHelper.cs" />
    <Compile Include="Protocols\GB\GBDataFrame\PolParameter.cs" />
    <Compile Include="Protocols\GB\GBDataFrame\ProtocalExplain.cs" />
    <Compile Include="Protocols\GB\GBParser.cs" />
    <Compile Include="Protocols\GB\GBProtocol.cs" />
    <Compile Include="Protocols\GB\GBProtocolDesc.cs" />
    <Compile Include="Protocols\GB\GBReceiver.cs" />
    <Compile Include="Protocols\GB\GBSender.cs" />
    <Compile Include="Protocols\Helper\Enums.cs" />
    <Compile Include="Protocols\Helper\SupplementController.cs" />
    <Compile Include="Protocols\Interface\IDataUpload.cs" />
    <Compile Include="Protocols\Interface\IDateSupplementListener.cs" />
    <Compile Include="Protocols\Interface\IGetSuppleData.cs" />
    <Compile Include="Protocols\UI\FrmDataSupplement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Protocols\UI\FrmDataSupplement.designer.cs">
      <DependentUpon>FrmDataSupplement.cs</DependentUpon>
    </Compile>
    <Compile Include="Tasks\Config\CustomTask.cs" />
    <Compile Include="Tasks\Config\CustomTaskManager.cs" />
    <Compile Include="Tasks\CustomTask\DateSimulateTask.cs" />
    <Compile Include="Tasks\CustomTask\DbBackupTask.cs" />
    <Compile Include="Tasks\CustomTask\MinuteDataStorageTask.cs" />
    <Compile Include="Tasks\CustomTask\SecondDataStorageTask.cs" />
    <Compile Include="Tasks\TaskLogHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2D502016-B3B3-43FF-9BAE-AD1D2A18D42E}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="DataAndParamBackUp\UI\FrmParamRestore.resx">
      <DependentUpon>FrmParamRestore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DataAndParamBackUp\UI\FrmDataAndParamBackUp.resx">
      <DependentUpon>FrmDataAndParamBackUp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DataAndParamBackUp\UI\FrmDBRestore.resx">
      <DependentUpon>FrmDBRestore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Log\UI\FrmQueryLog.resx">
      <DependentUpon>FrmQueryLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Protocols\GB\ConfigUI\FrmOutPutConfig.en-US.resx">
      <DependentUpon>FrmOutPutConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Protocols\GB\ConfigUI\FrmOutPutConfig.resx">
      <DependentUpon>FrmOutPutConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Protocols\GB\ConfigUI\GBConfigUC.resx">
      <DependentUpon>GBConfigUC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Protocols\GB\ConfigUI\OutputNode.resx">
      <DependentUpon>OutputNode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Protocols\UI\FrmDataSupplement.resx">
      <DependentUpon>FrmDataSupplement.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>