using System;
using System.Windows.Forms;
using Fpi.Alarm.Properties;
using Fpi.UI.Common.PC;

namespace Fpi.Alarm.UI.PC
{
    public partial class FormEditAlarmSource : Form
    {
        public FormEditAlarmSource()
        {
            InitializeComponent();
        }
        public FormEditAlarmSource(AlarmSource source) : this()
        {
            this.AlarmSource = source;
        }

        public AlarmSource AlarmSource { get; private set; }

        private void FormEditAlarmGrade_Load(object sender, EventArgs e)
        {
            this.InitAlarmGroups();

            if(AlarmSource != null)
            {
                this.txtSourceId.Text = AlarmSource.id;
                this.txtSourceName.Text = AlarmSource.name;
                this.cboAlarmGroups.SelectedItem = AlarmManager.GetInstance().alarmGroups.FindNode(AlarmSource.alarmGroupId);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtSourceId.Text) || string.IsNullOrEmpty(this.txtSourceName.Text))
            {
                throw new Exception(Resources.AlarmGradeCodeEmpty);
            }

            if(AlarmSource == null)
            {
                AlarmSource = new AlarmSource();
            }
            AlarmSource.id = this.txtSourceId.Text;
            AlarmSource.name = this.txtSourceName.Text;
            AlarmSource.alarmGroupId = (this.cboAlarmGroups.SelectedItem as AlarmGroup).id;
        }

        private void InitAlarmGroups()
        {
            foreach(AlarmGroup group in AlarmManager.GetInstance().alarmGroups)
            {
                this.cboAlarmGroups.Items.Add(group);
            }
        }
    }
}