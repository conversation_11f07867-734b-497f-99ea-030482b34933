﻿using System;
using System.ComponentModel;

namespace Fpi.Entrance.YS.Models
{
    /// <summary>
    /// 门磁告警数据模型（4.5.5协议）
    /// </summary>
    public class DoorContactAlarm
    {
        /// <summary>
        /// 订阅者引用地址
        /// 格式：IP:Port/Subscription/Subscribers/{id}
        /// </summary>     
        [Description("订阅者引用地址")]
        public string Reference { get; set; }

        /// <summary>
        /// 状态类型标识
        /// 40-门磁状态告警（协议定义）
        /// </summary>   
        [Description("状态类型标识")]
        public string StatusID { get; set; }

        /// <summary>
        /// 状态详细信息
        /// </summary>      
        [Description("状态详细信息")]
        public StatusInformation StatusInfo { get; set; } = new StatusInformation();

        /// <summary>
        /// 状态信息包装类
        /// </summary>
        public class StatusInformation
        {
            /// <summary>
            /// 门磁状态详情
            /// </summary>         
            [Description("门磁状态详情")]
            public DoorStatusDetail DoorStatus { get; set; } = new DoorStatusDetail();
        }

        /// <summary>
        /// 门磁状态详细信息
        /// </summary>
        public class DoorStatusDetail
        {
            /// <summary>
            /// 门磁状态
            /// 0：关门告警 1：开门告警
            /// </summary>      
            [Description("门磁状态(0:关门 1:开门)")]
            public eDoorState Status { get; set; }

            /// <summary>
            /// 状态时间戳（Unix时间戳，秒级）
            /// </summary>          
            [Description("状态时间戳（Unix秒）")]
            public long TimeStamp { get; set; }

            /// <summary>
            /// 设备唯一标识
            /// </summary>         
            [Description("设备唯一标识")]
            public string DeviceID { get; set; }

            /// <summary>
            /// 转换后的日期时间（只读）
            /// </summary>         
            [Description("转换后的本地时间")]
            [Newtonsoft.Json.JsonIgnore]
            public DateTime LocalTime => DateTimeOffset.FromUnixTimeSeconds(TimeStamp).LocalDateTime;
        }
    }

    /// <summary>
    /// 门磁状态枚举
    /// </summary>
    public enum eDoorState
    {
        /// <summary>
        /// 关门状态
        /// </summary>
        [Description("关门状态")]
        Closed = 0,

        /// <summary>
        /// 开门告警
        /// </summary>
        [Description("开门告警")]
        Opened = 1,

        /// <summary>
        /// 无权限
        /// </summary>
        [Description("无权限")]
        UnRecognize = 2
    }
}