using System;

namespace Fpi.Communication.Exceptions
{
    public class ProtocolException : CommunicationException
    {
        public ProtocolException()
            : base()
        {
        }

        public ProtocolException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public ProtocolException(string message)
            : base(message)
        {
        }
    }
}