﻿/*==================================================================================================
** 类 名 称:Enums
** 创 建 人:xiaopeng_liu
** 当前版本：V1.0.0
** CLR 版本:4.0.30319.42000
** 创建时间:2018/5/4 17:09:26

** 修改人		修改时间		修改后版本		修改内容


** 功能描述：枚举定义集
 
==================================================================================================
 Copyright @2018. Focused Photonics Inc. All rights reserved.
==================================================================================================*/
using System.ComponentModel;

namespace Fpi.HB.Business.Protocols.Helper
{
    /// <summary>
    /// 上传数据类型
    /// </summary>
    public enum eUploadDataType
    {
        /// <summary>
        /// 实时数据
        /// </summary>
        [Description("实时数据")]
        实时数据,

        /// <summary>
        /// 小时数据
        /// </summary>
        [Description("小时数据")]
        小时数据,

        /// <summary>
        /// 标样核查数据
        /// </summary>
        [Description("标样核查数据")]
        标样核查数据,

        /// <summary>
        /// 加标回收数据
        /// </summary>
        [Description("加标回收数据")]
        加标回收数据,

        /// <summary>
        /// 平行样数据
        /// </summary>
        [Description("平行样数据")]
        平行样数据,

        /// <summary>
        /// 零点核查数据
        /// </summary>
        [Description("零点核查数据")]
        零点核查数据,

        /// <summary>
        /// 跨度核查数据
        /// </summary>
        [Description("跨度核查数据")]
        跨度核查数据,

        /// <summary>
        /// 分钟数据
        /// </summary>
        [Description("分钟数据")]
        分钟数据,

        /// <summary>
        /// 日数据
        /// </summary>
        [Description("日数据")]
        日数据,

        /// <summary>
        /// 五参数单独测量数据
        /// </summary>
        [Description("五参数单独测量数据")]
        五参数单独测量数据,

        /// <summary>
        /// 超标留样数据
        /// </summary>
        [Description("超标留样数据")]
        超标留样数据,

        /// <summary>
        /// 日志状态数据
        /// </summary>
        [Description("日志状态数据")]
        日志状态数据,

        /// <summary>
        /// 五参数核查数据
        /// </summary>
        [Description("五参数核查数据")]
        五参数核查数据,

        /// <summary>
        /// 五参数水样比对数据
        /// </summary>
        [Description("五参数水样比对数据")]
        五参数水样比对数据,

        /// <summary>
        /// 空白测试
        /// </summary>
        [Description("空白测试")]
        空白测试,

        /// <summary>
        /// 盲样测试
        /// </summary>
        [Description("盲样测试")]
        盲样测试,

        /// <summary>
        /// 任意浓度核查
        /// </summary>
        [Description("任意浓度核查")]
        任意浓度核查,

        /// <summary>
        /// 多点线性核查
        /// </summary>
        [Description("多点线性核查")]
        多点线性核查
    }

    /// <summary>
    /// 上传数据类型
    /// </summary>
    public enum eFpiHttpUploadDataType
    {
        /// <summary>
        /// 工况模拟数据
        /// </summary>
        [Description("工况模拟数据")]
        工况模拟数据,

        /// <summary>
        /// 智能巡检报告
        /// </summary>
        [Description("智能巡检报告")]
        智能巡检报告,
    }
}