using System;
using Fpi.Communication.Properties;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Extensions;
using Fpi.Xml;

namespace Fpi.Communication.Buses.UI.PC
{
    public partial class UC_ClientSocketBus : BaseConfigureView// System.Windows.Forms.UserControl
    {
        public UC_ClientSocketBus()
        {
            InitializeComponent();
            InitUI();
        }

        protected override void ShowConfig(object obj)
        {
            this.ParentForm.Text = Resources.TcpClientConfigOption;

            BaseNode configNode = configObj as BaseNode;
            string host = configNode.GetPropertyValue(TcpClientSocket.PropertyName_Host, string.Empty);
            string strPort = configNode.GetPropertyValue(TcpClientSocket.PropertyName_Port, string.Empty);

            int port = 0;
            int.TryParse(strPort, out port);

            this.txtHost.Text = host;
            this.nuPort.Value = port;

            this.chkNeedSaveLog.Checked = configNode.GetPropertyValue(BaseBus.PropertyName_NeedSaveLog, string.Empty).TryValue<bool>(true);
        }

        public override void Check()
        {
            base.Check();
            if(string.IsNullOrEmpty(this.txtHost.Text))
            {
                throw new Exception(Resources.ConfigTcpClient);
            }
            if(this.nuPort.Value == 0)
            {
                throw new Exception(Resources.TcpServerPortEmpty);
            }
        }

        public override object Save()
        {
            string host = this.txtHost.Text;
            string port = this.nuPort.Value.ToString();

            BaseNode configNode = configObj as BaseNode;
            configNode.SetProperty(TcpClientSocket.PropertyName_Host, host);
            configNode.SetProperty(TcpClientSocket.PropertyName_Port, port);

            configNode.SetProperty(BaseBus.PropertyName_NeedSaveLog, this.chkNeedSaveLog.Checked.ToString());

            return configNode;
        }

        private void InitUI()
        {
        }
    }
}