﻿//==================================================================================================
//类名：     Sevice   
//创建人:    hongbing_mao
//创建时间:  2013-1-11 9:32:47
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;

namespace Fpi.Data.FuncServer
{
    /// <summary>
    /// 服务信息类
    /// </summary>
    public class Service
    {
        /// <summary>
        /// 构造
        /// </summary>
        public Service()
        {
        }
        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="name"></param>
        /// <param name="des"></param>
        /// <param name="type"></param>
        public Service(string name, string des, Type type)
        {
            SrvName = name;
            SrvDescription = des;
            SupportType = type;
        }

        /// <summary>
        /// 服务名
        /// </summary>
        public string SrvName { get; set; }

        /// <summary>
        /// 服务有好别名
        /// </summary>
        public string SrvFriendlyName { get; set; }

        /// <summary>
        /// 服务描述
        /// </summary>
        public string SrvDescription { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public Type[] ParamType { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string[] ParamDescription { get; set; }

        /// <summary>
        /// 服务提供者类型
        /// </summary>
        public Type SupportType { get; set; }

        #region override
        public override string ToString()
        {
            return SrvName;
        }
        public override bool Equals(object obj)
        {
            return obj is Service ? SrvName == (obj as Service).SrvName : base.Equals(obj);
        }
        public override int GetHashCode()
        {
            return SrvName.GetHashCode();
        }
        #endregion
    }
}
