﻿namespace Fpi.Data.ExpParser
{
    using System.Collections.Generic;

    public class KeyValueList<K, T>
    {
        private Dictionary<K, T> list;

        public KeyValueList()
        {
            this.list = new Dictionary<K, T>();
        }

        public void Add(K key, T value)
        {
            this.list.Add(key, value);
        }

        public T this[K key]
        {
            get
            {
                T local;
                this.list.TryGetValue(key, out local);
                return local;
            }
        }
    }
}

