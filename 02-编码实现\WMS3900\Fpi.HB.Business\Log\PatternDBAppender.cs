﻿using System;
using Fpi.DB.Manager;
using Fpi.HB.Business.DB;
using Fpi.Log;
using Fpi.Log.Config;
using log4net.Appender;
using log4net.Core;

namespace Fpi.HB.Business
{
    /// <summary>
    /// 数据库输出附着器
    /// 为避免循环引用造成的问题，把此功能放置到Fpi.HB.Business项目中
    /// </summary>
    public class PatternDBAppender : AppenderSkeleton
    {
        #region 构造

        public PatternDBAppender()
        {
            try
            {
                CreateLogTable();
            }
            catch(Exception ex)
            {
                LogUtil.Error($"日志数据表创建出错：{ex.Message}");
            }
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 输出
        /// </summary>
        /// <param name="loggingEvent"></param>
        protected override void Append(LoggingEvent loggingEvent)
        {
            if(!LogManager.GetInstance().DBOutput(loggingEvent.Level.Name))
            {
                return;
            }

            try
            {
                var logType = string.Empty;
                DateTime dataTime = loggingEvent.TimeStamp;
                string level = loggingEvent.Level.Name;
                try
                {
                    logType = (string)loggingEvent.Properties["logtype"];
                }
                catch
                {
                    //
                }
                string massage = loggingEvent.RenderedMessage;

                // 日志中含义会影响sql语句的字符
                if(massage.Contains("\'"))
                {
                    // 跳过输出
                    return;
                }

                FpiTable table = FpiDataBase.GetInstance().FindTableByName(DBConfig.LOG_DATA);
                var row = new FpiRow();
                row.SetFieldValue("DataTime", dataTime);
                row.SetFieldValue("Level", level);
                row.SetFieldValue("Type", logType);
                row.SetFieldValue("Message", massage);
                try
                {
                    table.AddRecord(row);
                }
                catch
                {
                    //
                }
            }
            catch(Exception ex)
            {
                ErrorHandler.Error($"日志存储到数据库中出错：{ex.Message}");
            }
        }

        #endregion

        #region 数据库创建

        /// <summary>
        /// 创建日志表
        /// </summary>
        private void CreateLogTable()
        {
            var table = new FpiTable(DBConfig.LOG_DATA);
            var col = new FpiColumn("id", ColumnType.Int, true, true);
            table.AddColumn(col);
            // 日志产生时间
            col = new FpiColumn("datatime", ColumnType.Datetime, "idx_datatime");
            table.AddColumn(col);
            // 日志等级
            col = new FpiColumn("level", ColumnType.Varchar);
            table.AddColumn(col);
            // 日志类型
            col = new FpiColumn("type", ColumnType.Varchar, 50);
            table.AddColumn(col);
            // 日志内容
            col = new FpiColumn("message", ColumnType.Text);
            table.AddColumn(col);
            FpiDataBase.GetInstance().AddTable(table);
            table.CreateTable();
        }

        #endregion
    }
}