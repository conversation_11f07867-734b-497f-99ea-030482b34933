﻿/*==================================================================================================
** 类 名 称:UC_LogView
** 创 建 人:xiaopeng_liu
** 当前版本：V1.0.0
** CLR 版本:4.0.30319.42000
** 创建时间:2017/4/24 9:03:10

** 修改人		修改时间		修改后版本		修改内容


** 功能描述：
 
==================================================================================================
 Copyright @2017. Focused Photonics Inc. All rights reserved.
==================================================================================================*/
using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;

namespace Fpi.Camera.UI
{
    /// <summary>
    /// summary
    /// </summary>
    public partial class UC_LogView : UserControl
    {
        #region 构造

        /// <summary>
        /// 日志输出
        /// </summary>
        public UC_LogView()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="info"></param>
        public void Log(string info)
        {
            // 跨线程更新控件
            if(lvLog.InvokeRequired)
            {
                Action<string> addInfo = str => { lvLog.Items.Add(DateTime.Now.ToShortTimeString() + ":" + str); };
                lvLog.Invoke(addInfo, info);
                Action<string> upadteIndex = str => { lvLog.SelectedIndex = lvLog.Items.Count - 1; };
                lvLog.Invoke(upadteIndex, "");
            }
            else
            {
                lvLog.Items.Add(DateTime.Now.ToShortTimeString() + ":" + info);
                lvLog.SelectedIndex = lvLog.Items.Count - 1;
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 清空显示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmClear_Click(object sender, EventArgs e)
        {
            lvLog.Items.Clear();
        }

        private void lvLog_DoubleClick(object sender, EventArgs e)
        {
            string info = lvLog.SelectedItem as string;
            if(!string.IsNullOrEmpty(info))
            {
                var form = new FormInfoBarDetail(info);
                form.ShowDialog();
            }
        }

        #endregion
    }
}