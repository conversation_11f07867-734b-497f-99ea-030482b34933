using System;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Properties;

namespace Fpi.Data.UI.PC.ValueNodeViews
{
    internal partial class FormEditIndice : Form
    {
        public FormEditIndice()
        {
            InitializeComponent();
        }

        public FormEditIndice(Indice indice)
            : this()
        {
            this.Indice = indice;
        }

        public Indice Indice { get; private set; }

        private void FormEditScope_Load(object sender, EventArgs e)
        {
            if(Indice != null)
            {
                this.txtId.Text = Indice.id;
                this.txtName.Text = Indice.name;
                this.nuMin.Value = (decimal)Indice.minValue;
                this.nuMax.Value = (decimal)Indice.maxValue;
            }
        }

        private void btnCalk_Click(object sender, EventArgs e)
        {

        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtId.Text) || string.IsNullOrEmpty(this.txtName.Text))
            {
                throw new Exception(Resources.IdNameEmpty);
            }

            if(Indice == null)
            {
                Indice = new Indice();
            }

            Indice.id = this.txtId.Text;
            Indice.name = this.txtName.Text;
            Indice.minValue = (double)this.nuMin.Value;
            Indice.maxValue = (double)this.nuMax.Value;
        }

    }
}