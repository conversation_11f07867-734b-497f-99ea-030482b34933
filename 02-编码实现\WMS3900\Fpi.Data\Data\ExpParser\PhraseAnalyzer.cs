﻿namespace Fpi.Data.ExpParser
{
    using System;

    public class PhraseAnalyzer
    {
        private string _expression = string.Empty;
        private Link_OP _link_OP = null;

        public PhraseAnalyzer(string expression)
        {
            this._expression = expression;
        }

        private void AddToLink(int startpos, int endpos, EDFAState state)
        {
            string str = null;
            IToken token = null;
            bool flag;
            EOperatorType plus;
            if(((endpos < 0) || (startpos < 0)) || (endpos < startpos))
            {
                return;
            }
            str = this._expression.Substring(startpos, (endpos - startpos) + 1);
            str = state == EDFAState.CharStr
                ? str.Length == 2 ? string.Empty : str.Substring(1, str.Length - 1).Substring(0, str.Length - 2).Replace("\\\"", "\"")
                : str.Replace(" ", "").Replace("\r\n", "").Replace("\t", "");
            switch(state)
            {
                case EDFAState.IntStr:
                    token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<int>(EDataType.Dint, Convert.ToInt32(str)), startpos);
                    goto Label_057E;

                case EDFAState.DoubleStr:
                    token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, Convert.ToDouble(str)), startpos);
                    goto Label_057E;

                case EDFAState.CharStr:
                    token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<string>(EDataType.Dstring, str), startpos);
                    goto Label_057E;

                case EDFAState.ABCStr:
                    foreach(string str2 in Enum.GetNames(typeof(EKeyword)))
                    {
                        if(str2.ToLower().Equals(str.ToLower()))
                        {
                            token = new TOKEN<KeyWord>(ETokenType.token_keyword, Define.KeyWords[(EKeyword)Enum.Parse(typeof(EKeyword), str, true)], startpos);
                            break;
                        }
                    }
                    break;

                case EDFAState.OperatorStr:
                    flag = true;
                    plus = EOperatorType.Plus;
                    switch(str)
                    {
                        case "(":
                            plus = EOperatorType.LeftParen;
                            goto Label_055A;

                        case ")":
                            plus = EOperatorType.RightParen;
                            goto Label_055A;

                        case "+":
                        case "-":
                            plus = (this._link_OP.Tail != null) && ((this._link_OP.Tail.Token.Type == ETokenType.token_operand) || ((this._link_OP.Tail.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)this._link_OP.Tail.Token).Tag.Type == EOperatorType.RightParen)))
                                ? str == "-" ? EOperatorType.Minus : EOperatorType.Plus
                                : str == "-" ? EOperatorType.Negative : EOperatorType.Positive;
                            goto Label_055A;

                        case "*":
                            plus = EOperatorType.Multiply;
                            goto Label_055A;

                        case "/":
                            plus = EOperatorType.Divide;
                            goto Label_055A;

                        case "%":
                            plus = EOperatorType.Mod;
                            goto Label_055A;

                        case "<":
                            plus = EOperatorType.LessThan;
                            goto Label_055A;

                        case ">":
                            if((this._link_OP.Tail != null) && ((this._link_OP.Tail.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)this._link_OP.Tail.Token).Tag.Type == EOperatorType.LessThan)))
                            {
                                ((TOKEN<Operator>)this._link_OP.Tail.Token).Tag = Define.Operators[EOperatorType.NotEqual];
                                flag = false;
                            }
                            else
                            {
                                plus = EOperatorType.GreaterThan;
                            }
                            goto Label_055A;

                        case "=":
                            if((this._link_OP.Tail != null) && ((this._link_OP.Tail.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)this._link_OP.Tail.Token).Tag.Type == EOperatorType.LessThan)))
                            {
                                ((TOKEN<Operator>)this._link_OP.Tail.Token).Tag = Define.Operators[EOperatorType.LessEqual];
                                flag = false;
                            }
                            else if((this._link_OP.Tail.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)this._link_OP.Tail.Token).Tag.Type == EOperatorType.GreaterThan))
                            {
                                ((TOKEN<Operator>)this._link_OP.Tail.Token).Tag = Define.Operators[EOperatorType.GreaterEqual];
                                flag = false;
                            }
                            else
                            {
                                plus = EOperatorType.Equal;
                            }
                            goto Label_055A;
                    }
                    goto Label_055A;

                case EDFAState.Comma:
                    token = new TOKEN<Separator>(ETokenType.token_separator, new Separator(','), startpos);
                    goto Label_057E;

                default:
                    goto Label_057E;
            }
            if(token == null)
            {
                throw new Exception(string.Format("Error! 非法关键字“{0}”（索引：{1}）", str, startpos.ToString()));
            }
            goto Label_057E;
            Label_055A:
            if(flag)
            {
                token = new TOKEN<Operator>(ETokenType.token_operator, Define.Operators[plus], startpos);
            }
            Label_057E:
            if(token != null)
            {
                this._link_OP.Add(new TOKENLink(token));
            }
        }

        public Link_OP Analyze()
        {
            this._link_OP = new Link_OP();
            char[] chArray = this._expression.Trim().ToCharArray();
            int index = 0;
            int startpos = 0;
            int endpos = 0;
            EDFAState start = EDFAState.Start;
            while(index < chArray.Length)
            {
                if(start == EDFAState.CharStr)
                {
                    if((chArray[index] == '"') && (chArray[index - 1] != '\\'))
                    {
                        endpos = index;
                        this.AddToLink(startpos, endpos, start);
                        start = EDFAState.Start;
                        startpos = index + 1;
                    }
                    else if((index + 1) == chArray.Length)
                    {
                        throw new Exception("Error! 词法.缺少字符串引号（\"）");
                    }
                }
                else if(chArray[index] == '"')
                {
                    if(start != EDFAState.Start)
                    {
                        endpos = index - 1;
                        this.AddToLink(startpos, endpos, start);
                        startpos = index;
                    }
                    start = EDFAState.CharStr;
                }
                else if(char.IsLetter(chArray[index]))
                {
                    if(start == EDFAState.Start)
                    {
                        start = EDFAState.ABCStr;
                    }
                    else if(start != EDFAState.ABCStr)
                    {
                        endpos = index - 1;
                        this.AddToLink(startpos, endpos, start);
                        start = EDFAState.ABCStr;
                        startpos = index;
                    }
                }
                else if(char.IsDigit(chArray[index]))
                {
                    if(start == EDFAState.Start)
                    {
                        start = EDFAState.IntStr;
                    }
                    else if((start != EDFAState.IntStr) && (start != EDFAState.DoubleStr))
                    {
                        endpos = index - 1;
                        this.AddToLink(startpos, endpos, start);
                        start = EDFAState.IntStr;
                        startpos = index;
                    }
                }
                else if(chArray[index] == '.')
                {
                    if(start != EDFAState.IntStr)
                    {
                        throw new Exception(string.Format("Error! 词法.错误的小数点位置,索引：{0}", index.ToString()));
                    }
                    start = EDFAState.DoubleStr;
                }
                else if(chArray[index] == ',')
                {
                    endpos = index - 1;
                    this.AddToLink(startpos, endpos, start);
                    start = EDFAState.Comma;
                    startpos = index;
                }
                else if(((!char.IsWhiteSpace(chArray[index]) && (chArray[index] != '\r')) && (chArray[index] != '\n')) && (chArray[index] != '\t'))
                {
                    if((((((chArray[index] != '(') && (chArray[index] != ')')) && ((chArray[index] != '+') && (chArray[index] != '-'))) && (((chArray[index] != '*') && (chArray[index] != '/')) && ((chArray[index] != '%') && (chArray[index] != '>')))) && (chArray[index] != '<')) && (chArray[index] != '='))
                    {
                        throw new Exception(string.Format("Error! 词法.非法字符“{0}”（索引：{1}）", chArray[index].ToString(), index.ToString()));
                    }
                    if(start != EDFAState.Start)
                    {
                        endpos = index - 1;
                        this.AddToLink(startpos, endpos, start);
                        startpos = index;
                    }
                    start = EDFAState.OperatorStr;
                }
                index++;
                if(index == chArray.Length)
                {
                    endpos = index - 1;
                    if((startpos == endpos) && (chArray[endpos] == '"'))
                    {
                        throw new Exception("Error! 词法.缺少字符串引号（\"）");
                    }
                    this.AddToLink(startpos, endpos, start);
                }
            }
            return this._link_OP;
        }
    }
}

