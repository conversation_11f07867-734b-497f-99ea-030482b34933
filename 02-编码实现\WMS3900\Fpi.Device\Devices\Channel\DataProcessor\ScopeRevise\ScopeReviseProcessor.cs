﻿using System;
using System.ComponentModel;

namespace Fpi.Devices.Channel
{
    /// <summary>
    /// 范围修正器
    /// </summary>
    public class ScopeReviseProcessor : Processor
    {
        #region — 字段属性 —

        /// <summary>
        /// 触发模式
        /// </summary>
        private TriggerModel _triggerModel;

        /// <summary>
        /// 触发模式参数1
        /// </summary>
        private float _triggerParam1;

        /// <summary>
        /// 触发模式参数2
        /// </summary>
        private float _triggerParam2;

        /// <summary>
        /// 修正模式
        /// </summary>
        private ReviseModel _reviseModel;

        /// <summary>
        /// 修正模式参数1
        /// </summary>
        private float _reviseParam1;

        /// <summary>
        /// 修正模式参数2
        /// </summary>
        private float _reviseParam2;

        #endregion

        public ScopeReviseProcessor()
        {
            this.ucParamConfig = new ConfigScopeRevisePanel();
        }

        public ScopeReviseProcessor(string param)
            : this()
        {
            this.SetParam(param);
        }

        public override void SetParam(string param)
        {
            try
            {
                string[] paramStrs = param.Split(',');
                var index = 0;
                switch(paramStrs[index])
                {
                    case "0":
                        _triggerModel = TriggerModel.LowModel;
                        index++;
                        _triggerParam1 = float.Parse(paramStrs[index]);
                        break;
                    case "1":
                        _triggerModel = TriggerModel.HighModel;
                        index++;
                        _triggerParam2 = float.Parse(paramStrs[index]);
                        break;
                    case "2":
                        _triggerModel = TriggerModel.BetweenModel;
                        index++;
                        _triggerParam1 = float.Parse(paramStrs[index]);
                        index++;
                        _triggerParam2 = float.Parse(paramStrs[index]);
                        break;
                }
                index++;
                switch(paramStrs[index])
                {
                    case "0":
                        _reviseModel = ReviseModel.Ratio;
                        index++;
                        _reviseParam1 = float.Parse(paramStrs[index]);
                        break;
                    case "1":
                        _reviseModel = ReviseModel.Random;
                        index++;
                        _reviseParam1 = float.Parse(paramStrs[index]);
                        index++;
                        _reviseParam2 = float.Parse(paramStrs[index]);
                        break;
                }
            }
            catch(Exception)
            {
                // ignored
            }
        }

        /// <summary>
        /// 输入数据处理
        /// </summary>
        /// <param name="inputValue"></param>
        /// <returns></returns>
        public override double InputProcessData(double inputValue)
        {
            double dataProcessed = double.NaN;

            switch(_triggerModel)
            {
                case TriggerModel.LowModel:
                    if(!(inputValue <= this._triggerParam1))
                    {
                        return inputValue;
                    }
                    break;
                case TriggerModel.HighModel:
                    if(!(inputValue >= this._triggerParam2))
                    {
                        return inputValue;
                    }
                    break;
                case TriggerModel.BetweenModel:
                    if(!(inputValue >= this._triggerParam1 && inputValue <= this._triggerParam2))
                    {
                        return inputValue;
                    }
                    break;
            }

            switch(_reviseModel)
            {
                case ReviseModel.Ratio:
                    dataProcessed = inputValue * this._reviseParam1;
                    break;
                case ReviseModel.Random:
                    var a = new Random();
                    dataProcessed = a.NextDouble() * (this._reviseParam2 - this._reviseParam1) + this._reviseParam1;
                    break;
            }
            return dataProcessed;
        }

        /// <summary>
        /// 输出数据处理
        /// </summary>
        /// <param name="outputValue"></param>
        /// <returns></returns>
        public override double OutputProcessData(double outputValue)
        {
            return outputValue;
        }

        public override string ToString()
        {
            return "数据范围处理器";
        }
    }

    /// <summary>
    /// 触发模式
    /// </summary>
    public enum TriggerModel
    {
        /// <summary>
        /// 低于下限
        /// </summary>
        [Description("低于下限")]
        LowModel = 0,

        /// <summary>
        /// 高于上限
        /// </summary>
        [Description("高于上限")]
        HighModel = 1,

        /// <summary>
        /// 处于区间
        /// </summary>
        [Description("处于区间")]
        BetweenModel = 2
    }

    /// <summary>
    /// 修正模式
    /// </summary>
    public enum ReviseModel
    {
        /// <summary>
        /// 系数修正
        /// </summary>
        [Description("系数修正")]
        Ratio = 0,

        /// <summary>
        /// 随机修正
        /// </summary>
        [Description("随机修正")]
        Random = 1
    }
}
