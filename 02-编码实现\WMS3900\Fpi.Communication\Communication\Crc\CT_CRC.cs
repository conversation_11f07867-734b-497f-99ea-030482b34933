
namespace Fpi.Communication.Ports.FpiPorts
{
    public class CT_CRC
    {
        public static ushort Crc16(byte[] data, int dataLength)
        {
            ushort num = 0xffff;

            int num4 = 0;
            while(num4 < dataLength)
            {
                byte num6 = (byte)(num >> 8);
                num = (ushort)(data[num4] ^ num6);
                num4++;
                for(int i = 0; i < 8; i++)
                {
                    ushort num2 = (ushort)(num & 1);
                    num = (ushort)(num >> 1);
                    if(num2 == 1)
                    {
                        num = (ushort)(num ^ 0xa001);
                    }
                }
            }
            return num;
        }


    }
}
