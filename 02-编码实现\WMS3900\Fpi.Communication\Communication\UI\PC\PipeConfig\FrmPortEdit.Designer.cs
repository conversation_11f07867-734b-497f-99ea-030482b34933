﻿namespace Fpi.Communication.UI.PC.PipeConfig
{
    partial class FrmPortEdit
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmPortEdit));
            this.splitter1 = new System.Windows.Forms.Splitter();
            this.pnlBottom = new System.Windows.Forms.Panel();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.pnlPorts = new System.Windows.Forms.Panel();
            this.splitter2 = new System.Windows.Forms.Splitter();
            this.lsbPorts = new System.Windows.Forms.ListBox();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.gbReceiver = new System.Windows.Forms.GroupBox();
            this.btnReceiver = new System.Windows.Forms.Button();
            this.txtImpReceiver = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.gbSender = new System.Windows.Forms.GroupBox();
            this.btnSender = new System.Windows.Forms.Button();
            this.txtImpSender = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.pnlBottom.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.gbReceiver.SuspendLayout();
            this.gbSender.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitter1
            // 
            resources.ApplyResources(this.splitter1, "splitter1");
            this.splitter1.Name = "splitter1";
            this.splitter1.TabStop = false;
            // 
            // pnlBottom
            // 
            this.pnlBottom.Controls.Add(this.btnCancel);
            this.pnlBottom.Controls.Add(this.btnOK);
            resources.ApplyResources(this.pnlBottom, "pnlBottom");
            this.pnlBottom.Name = "pnlBottom";
            // 
            // btnCancel
            // 
            resources.ApplyResources(this.btnCancel, "btnCancel");
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            resources.ApplyResources(this.btnOK, "btnOK");
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Name = "btnOK";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage1);
            resources.ApplyResources(this.tabControl1, "tabControl1");
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.pnlPorts);
            this.tabPage2.Controls.Add(this.splitter2);
            this.tabPage2.Controls.Add(this.lsbPorts);
            resources.ApplyResources(this.tabPage2, "tabPage2");
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // pnlPorts
            // 
            resources.ApplyResources(this.pnlPorts, "pnlPorts");
            this.pnlPorts.Name = "pnlPorts";
            // 
            // splitter2
            // 
            resources.ApplyResources(this.splitter2, "splitter2");
            this.splitter2.Name = "splitter2";
            this.splitter2.TabStop = false;
            // 
            // lsbPorts
            // 
            resources.ApplyResources(this.lsbPorts, "lsbPorts");
            this.lsbPorts.FormattingEnabled = true;
            this.lsbPorts.Name = "lsbPorts";
            this.lsbPorts.SelectedIndexChanged += new System.EventHandler(this.lsbPorts_SelectedIndexChanged);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.gbReceiver);
            this.tabPage1.Controls.Add(this.gbSender);
            resources.ApplyResources(this.tabPage1, "tabPage1");
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // gbReceiver
            // 
            this.gbReceiver.Controls.Add(this.btnReceiver);
            this.gbReceiver.Controls.Add(this.txtImpReceiver);
            this.gbReceiver.Controls.Add(this.label1);
            resources.ApplyResources(this.gbReceiver, "gbReceiver");
            this.gbReceiver.Name = "gbReceiver";
            this.gbReceiver.TabStop = false;
            // 
            // btnReceiver
            // 
            resources.ApplyResources(this.btnReceiver, "btnReceiver");
            this.btnReceiver.Image = global::Fpi.Communication.Properties.Resources.Search;
            this.btnReceiver.Name = "btnReceiver";
            this.btnReceiver.UseVisualStyleBackColor = true;
            this.btnReceiver.Click += new System.EventHandler(this.btnReceiver_Click);
            // 
            // txtImpReceiver
            // 
            resources.ApplyResources(this.txtImpReceiver, "txtImpReceiver");
            this.txtImpReceiver.Name = "txtImpReceiver";
            this.txtImpReceiver.ReadOnly = true;
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // gbSender
            // 
            this.gbSender.Controls.Add(this.btnSender);
            this.gbSender.Controls.Add(this.txtImpSender);
            this.gbSender.Controls.Add(this.label3);
            resources.ApplyResources(this.gbSender, "gbSender");
            this.gbSender.Name = "gbSender";
            this.gbSender.TabStop = false;
            // 
            // btnSender
            // 
            resources.ApplyResources(this.btnSender, "btnSender");
            this.btnSender.Image = global::Fpi.Communication.Properties.Resources.Search;
            this.btnSender.Name = "btnSender";
            this.btnSender.UseVisualStyleBackColor = true;
            this.btnSender.Click += new System.EventHandler(this.btnSender_Click);
            // 
            // txtImpSender
            // 
            resources.ApplyResources(this.txtImpSender, "txtImpSender");
            this.txtImpSender.Name = "txtImpSender";
            this.txtImpSender.ReadOnly = true;
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // FormPortEdit
            // 
            this.AcceptButton = this.btnOK;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.pnlBottom);
            this.Controls.Add(this.splitter1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormPortEdit";
            this.ShowInTaskbar = false;
            this.Load += new System.EventHandler(this.FormPortEdit_Load);
            this.pnlBottom.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.gbReceiver.ResumeLayout(false);
            this.gbReceiver.PerformLayout();
            this.gbSender.ResumeLayout(false);
            this.gbSender.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Splitter splitter1;
        private System.Windows.Forms.Panel pnlBottom;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.GroupBox gbReceiver;
        private System.Windows.Forms.Button btnReceiver;
        private System.Windows.Forms.TextBox txtImpReceiver;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox gbSender;
        private System.Windows.Forms.Button btnSender;
        private System.Windows.Forms.TextBox txtImpSender;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Panel pnlPorts;
        private System.Windows.Forms.Splitter splitter2;
        private System.Windows.Forms.ListBox lsbPorts;
    }
}