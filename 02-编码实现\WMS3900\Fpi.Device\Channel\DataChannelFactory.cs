﻿namespace Fpi.Devices.Channel
{
    internal class DataChannelFactory
    {
        public static DataChannel CreateNewChannel(eDeviceChannelType channelType)
        {
            return channelType == eDeviceChannelType.InSwitch
                ? new InSwitchChannel()
                : channelType == eDeviceChannelType.OutSwitch
                    ? new OutSwitchChannel()
                    : channelType == eDeviceChannelType.InValue ? new InValueChannel() : (DataChannel)new OutValueChannel();
        }
    }
}
