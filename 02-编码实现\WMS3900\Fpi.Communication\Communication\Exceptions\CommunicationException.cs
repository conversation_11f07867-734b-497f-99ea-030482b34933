using System;
using Fpi.Util.Exeptions;

namespace Fpi.Communication.Exceptions
{
    public class CommunicationException : PlatformException
    {
        public CommunicationException()
            : base()
        {
        }

        public CommunicationException(string message)
            : base(message)
        {
        }

        public CommunicationException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}