
namespace Fpi.Data.Config
{
    public class ArrayNode : VarNode
    {
        public override eVarType VarType => eVarType.Array;

        protected override void Conformation()
        {
            base.Conformation();
            _Value = new double[0];
        }

        new public double[] GetValue()
        {
            object v = base.GetValue();
            return v == null ? (new double[0]) : (double[])v;
        }
        public void SetValue(double[] value)
        {
            base.SetValue(value);
        }
    }
}
