﻿using Fpi.UI.PC.Common;
using System.Windows.Forms;

namespace Fpi.Camera.UI
{
    partial class FrmUSBCameraPreview
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code



        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.logView = new Fpi.Camera.UI.UC_LogView();
            this.pnlPreview = new System.Windows.Forms.Panel();
            this.cmbCameraList = new Sunny.UI.UIComboBox();
            this.btnScreenShot = new Sunny.UI.UISymbolButton();
            this.btnRefresh = new Sunny.UI.UISymbolButton();
            this.btnRecord = new Sunny.UI.UISymbolButton();
            this.pnlCameraOper = new Sunny.UI.UIPanel();
            this.pnlCameraList = new Sunny.UI.UIPanel();
            this.pnlCameraCtrl = new Sunny.UI.UIPanel();
            this.videoSourcePlayer = new AForge.Controls.VideoSourcePlayer();
            this.pnlPreview.SuspendLayout();
            this.pnlCameraOper.SuspendLayout();
            this.pnlCameraList.SuspendLayout();
            this.pnlCameraCtrl.SuspendLayout();
            this.SuspendLayout();
            // 
            // logView
            // 
            this.logView.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.logView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.logView.Location = new System.Drawing.Point(1, 115);
            this.logView.Margin = new System.Windows.Forms.Padding(9, 9, 9, 9);
            this.logView.Name = "logView";
            this.logView.Size = new System.Drawing.Size(236, 521);
            this.logView.TabIndex = 3;
            // 
            // pnlPreview
            // 
            this.pnlPreview.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.pnlPreview.Controls.Add(this.videoSourcePlayer);
            this.pnlPreview.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlPreview.Location = new System.Drawing.Point(238, 0);
            this.pnlPreview.Name = "pnlPreview";
            this.pnlPreview.Size = new System.Drawing.Size(666, 637);
            this.pnlPreview.TabIndex = 1;
            // 
            // cmbCameraList
            // 
            this.cmbCameraList.DataSource = null;
            this.cmbCameraList.DropDownAutoWidth = true;
            this.cmbCameraList.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbCameraList.FillColor = System.Drawing.Color.White;
            this.cmbCameraList.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbCameraList.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbCameraList.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbCameraList.Location = new System.Drawing.Point(6, 13);
            this.cmbCameraList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbCameraList.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbCameraList.Name = "cmbCameraList";
            this.cmbCameraList.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbCameraList.Size = new System.Drawing.Size(186, 29);
            this.cmbCameraList.SymbolSize = 24;
            this.cmbCameraList.TabIndex = 0;
            this.cmbCameraList.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbCameraList.Watermark = "";
            // 
            // btnScreenShot
            // 
            this.btnScreenShot.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnScreenShot.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnScreenShot.Location = new System.Drawing.Point(14, 11);
            this.btnScreenShot.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnScreenShot.Name = "btnScreenShot";
            this.btnScreenShot.Size = new System.Drawing.Size(100, 35);
            this.btnScreenShot.Symbol = 61488;
            this.btnScreenShot.TabIndex = 0;
            this.btnScreenShot.Text = "截屏";
            this.btnScreenShot.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnScreenShot.Click += new System.EventHandler(this.btnScreenShot_Click);
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.Transparent;
            this.btnRefresh.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRefresh.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRefresh.IsCircle = true;
            this.btnRefresh.Location = new System.Drawing.Point(199, 11);
            this.btnRefresh.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(35, 35);
            this.btnRefresh.Symbol = 61473;
            this.btnRefresh.TabIndex = 1;
            this.btnRefresh.TipsFont = new System.Drawing.Font("微软雅黑", 11F);
            this.btnRefresh.TipsText = "刷新";
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // btnRecord
            // 
            this.btnRecord.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRecord.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRecord.Location = new System.Drawing.Point(127, 11);
            this.btnRecord.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnRecord.Name = "btnRecord";
            this.btnRecord.Size = new System.Drawing.Size(100, 35);
            this.btnRecord.Symbol = 61501;
            this.btnRecord.TabIndex = 3;
            this.btnRecord.Text = "开始录像";
            this.btnRecord.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRecord.Click += new System.EventHandler(this.btnRecord_Click);
            // 
            // pnlCameraOper
            // 
            this.pnlCameraOper.Controls.Add(this.btnRecord);
            this.pnlCameraOper.Controls.Add(this.btnScreenShot);
            this.pnlCameraOper.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlCameraOper.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlCameraOper.Location = new System.Drawing.Point(1, 58);
            this.pnlCameraOper.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlCameraOper.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlCameraOper.Name = "pnlCameraOper";
            this.pnlCameraOper.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlCameraOper.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom;
            this.pnlCameraOper.Size = new System.Drawing.Size(236, 57);
            this.pnlCameraOper.TabIndex = 0;
            this.pnlCameraOper.Text = null;
            this.pnlCameraOper.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlCameraList
            // 
            this.pnlCameraList.Controls.Add(this.btnRefresh);
            this.pnlCameraList.Controls.Add(this.cmbCameraList);
            this.pnlCameraList.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlCameraList.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlCameraList.Location = new System.Drawing.Point(1, 1);
            this.pnlCameraList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlCameraList.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlCameraList.Name = "pnlCameraList";
            this.pnlCameraList.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlCameraList.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Bottom;
            this.pnlCameraList.Size = new System.Drawing.Size(236, 57);
            this.pnlCameraList.TabIndex = 23;
            this.pnlCameraList.Text = null;
            this.pnlCameraList.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlCameraCtrl
            // 
            this.pnlCameraCtrl.Controls.Add(this.logView);
            this.pnlCameraCtrl.Controls.Add(this.pnlCameraOper);
            this.pnlCameraCtrl.Controls.Add(this.pnlCameraList);
            this.pnlCameraCtrl.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlCameraCtrl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlCameraCtrl.Location = new System.Drawing.Point(0, 0);
            this.pnlCameraCtrl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlCameraCtrl.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlCameraCtrl.Name = "pnlCameraCtrl";
            this.pnlCameraCtrl.Padding = new System.Windows.Forms.Padding(1);
            this.pnlCameraCtrl.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlCameraCtrl.Size = new System.Drawing.Size(238, 637);
            this.pnlCameraCtrl.TabIndex = 5;
            this.pnlCameraCtrl.Text = null;
            this.pnlCameraCtrl.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // videoSourcePlayer
            // 
            this.videoSourcePlayer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.videoSourcePlayer.Location = new System.Drawing.Point(0, 0);
            this.videoSourcePlayer.Name = "videoSourcePlayer";
            this.videoSourcePlayer.Size = new System.Drawing.Size(666, 637);
            this.videoSourcePlayer.TabIndex = 0;
            this.videoSourcePlayer.VideoSource = null;
            // 
            // USBPreviewForm
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(904, 637);
            this.Controls.Add(this.pnlPreview);
            this.Controls.Add(this.pnlCameraCtrl);
            this.DoubleBuffered = true;
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(3, 3, 3, 3);
            this.Name = "USBPreviewForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "视频监控";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.MainPreviewForm_FormClosed);
            this.Load += new System.EventHandler(this.FrmUSBCameraPreview_Load);
            this.pnlPreview.ResumeLayout(false);
            this.pnlCameraOper.ResumeLayout(false);
            this.pnlCameraList.ResumeLayout(false);
            this.pnlCameraCtrl.ResumeLayout(false);
            this.ResumeLayout(false);

        }


        #endregion
        private System.Windows.Forms.Panel pnlPreview;
        private Fpi.Camera.UI.UC_LogView logView;
        private Sunny.UI.UIComboBox cmbCameraList;
        private Sunny.UI.UISymbolButton btnScreenShot;
        private Sunny.UI.UISymbolButton btnRefresh;
        private Sunny.UI.UISymbolButton btnRecord;
        private Sunny.UI.UIPanel pnlCameraOper;
        private Sunny.UI.UIPanel pnlCameraList;
        private Sunny.UI.UIPanel pnlCameraCtrl;
        private AForge.Controls.VideoSourcePlayer videoSourcePlayer;
    }
}