﻿using System.Windows.Forms;

namespace Fpi.Devices.Channel
{
    public partial class ConfigCoeffPanel : UserControl, IParamConfig
    {
        public ConfigCoeffPanel()
        {
            InitializeComponent();
        }

        #region IParamConfig 成员

        public string SaveConfig()
        {
            return this.txtCoeff.Text;
        }

        public bool CheakParam()
        {
            float coeff;

            return float.TryParse(this.txtCoeff.Text, out coeff);
        }

        public void ShowParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length > 0)
            {
                this.txtCoeff.Text = paramStrs[0];
            }
        }

        #endregion
    }
}
