{"version": "2.0.0", "tasks": [{"label": "restore", "command": "D:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "process", "args": ["${workspaceFolder}/WMS3900.sln", "/t:restore"], "problemMatcher": "$msCompile"}, {"label": "build", "command": "D:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "process", "args": ["${workspaceFolder}/WMS3900.sln", "/p:Configuration=Debug", "/p:Platform=Any CPU", "/t:build", "/m", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}, "dependsOn": ["restore"]}, {"label": "rebuild", "command": "D:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "process", "args": ["${workspaceFolder}/WMS3900.sln", "/p:Configuration=Debug", "/p:Platform=Any CPU", "/t:rebuild", "/m", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}]}