﻿//==================================================================================================
//类名：     DeviceManager   
//创建人:    xu_cao
//创建时间:  2012-9-26 16:57:00
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using System;
using System.Collections.Generic;
using Fpi.Alarm;
using Fpi.Log;
using Fpi.Util.Interfaces.Initialize;
using Fpi.Util.Reflection;
using Fpi.Xml;

namespace Fpi.Devices
{
    public class DeviceManager : BaseNode, IInitialization
    {
        #region 字段属性

        public NodeList Devices = new NodeList();

        #endregion

        #region 单例

        private DeviceManager()
        {
            loadXml();
        }

        private static readonly object syncObj = new object();
        private static DeviceManager _instance = null;
        public static DeviceManager GetInstance()
        {
            lock(syncObj)
            {
                if(_instance == null)
                {
                    _instance = new DeviceManager();
                }
            }
            return _instance;
        }

        #endregion

        #region 公共方法

        #region 查找方法

        /// <summary>
        /// 通过设备ID查找设备
        /// </summary>
        /// <param name="deviceId"></param>
        /// <returns></returns>
        public Device GetDeviceById(string deviceId)
        {
            return Devices.FindNode(deviceId) as Device;
        }

        /// <summary>
        /// 通过设备名称查找设备
        /// </summary>
        /// <param name="deviceName"></param>
        /// <returns></returns>
        public Device GetDeviceByName(string deviceName)
        {
            return Devices.FindNodeByName(deviceName) as Device;
        }

        /// <summary>
        /// 查找正在使用的设备集
        /// </summary>
        /// <returns></returns>
        public List<Device> GetDeviceListUsed()
        {
            var deviceList = new List<Device>();
            foreach(Device device in Devices)
            {
                if(device.IsUsed)
                {
                    deviceList.Add(device);
                }
            }
            deviceList.Sort((x, y) => string.Compare(x.name, y.name, StringComparison.Ordinal));
            return deviceList;
        }

        /// <summary>
        /// 获取设备集列表
        /// </summary>
        /// <returns></returns>
        public List<Device> GetDeviceList()
        {
            var deviceList = new List<Device>();
            foreach(Device device in Devices)
            {
                deviceList.Add(device);
            }
            deviceList.Sort((x, y) => string.Compare(x.name, y.name, StringComparison.Ordinal));
            return deviceList;
        }

        /// <summary>
        /// 通过设备类型找到对应的设备集
        /// </summary>
        /// <param name="deciveType"></param>
        /// <returns></returns>
        public List<Device> GetDeviceListUsedByType(eDeviceType deciveType)
        {
            var deviceList = new List<Device>();
            foreach(Device device in Devices)
            {
                if(device.IsUsed && device.DeviceType == deciveType)
                {
                    deviceList.Add(device);
                }
            }
            deviceList.Sort((x, y) => string.Compare(x.name, y.name, StringComparison.Ordinal));
            return deviceList;
        }

        #endregion

        /// <summary>
        /// 重置设备相关报警配置
        /// </summary>
        public void InitDeviceAlarmConfig()
        {
            this.InitDevicesAlarmList();

            // 初始化报警等级设置
            if(AlarmManager.GetInstance().alarmGrades.GetCount() == 0)
            {
                var grade1 = new AlarmGrade("1")
                {
                    level = 1,
                    name = "故障"
                };

                var grade2 = new AlarmGrade("2")
                {
                    level = 2,
                    name = "报警"
                };

                AlarmManager.GetInstance().alarmGrades.AddNode(grade1);
                AlarmManager.GetInstance().alarmGrades.AddNode(grade2);
            }

            // 清除设备原报警信息
            foreach(Device device in GetInstance().GetDeviceList())
            {
                AlarmManager.GetInstance().alarmGroups.Remove(device.AlarmGroupId);
                AlarmManager.GetInstance().alarmSources.Remove(device.AlarmSourceId);
            }

            // 重新设置设备报警源，报警组信息
            foreach(Device device in GetInstance().GetDeviceListUsed())
            {
                if(!AlarmManager.GetInstance().alarmGroups.Contains(device.AlarmGroupId))
                {
                    var group = new AlarmGroup(device.AlarmGroupId);
                    // 修正报警组名命名规则
                    group.name = "(报警组)" + ReflectionHelper.CreateInstance(device.GetType().FullName);

                    foreach(string alarmId in device.DeviceAlarmList.Keys)
                    {
                        var alarmCode = new AlarmCode
                        {
                            id = alarmId,
                            name = device.DeviceAlarmList[alarmId],
                            description = device.DeviceAlarmList[alarmId]
                        };

                        if(AlarmManager.GetInstance().alarmGrades.GetCount() > 0)
                        {
                            alarmCode.alarmGradeId = ((AlarmGrade)AlarmManager.GetInstance().alarmGrades[0]).id;
                        }

                        group.alarmCodes.AddNode(alarmCode);
                    }

                    AlarmManager.GetInstance().alarmGroups.AddNode(group);
                }

                if(!AlarmManager.GetInstance().alarmSources.Contains(device.AlarmSourceId))
                {
                    var source = new AlarmSource(device.AlarmSourceId)
                    {
                        name = device.name,
                        alarmGroupId = device.AlarmGroupId
                    };
                    AlarmManager.GetInstance().alarmSources.AddNode(source);
                }
            }
            AlarmManager.GetInstance().Save();
        }

        #endregion

        #region IInitialization 成员

        public void Initialize()
        {
            this.InitDevicesStateAndAlarmList();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化所有设备报警列表
        /// </summary>
        private void InitDevicesAlarmList()
        {
            foreach(Device device in Devices)
            {
                if(device.IsUsed)
                {
                    device.SetDeviceAlarmList();
                }
            }
        }

        /// <summary>
        /// 初始化设备状态及报警列表
        /// </summary>
        private void InitDevicesStateAndAlarmList()
        {
            foreach(Device device in Devices)
            {
                if(device.IsUsed)
                {
                    try
                    {
                        device.SetDeviceAlarmList();
                        device.InitDeviceState();
                    }
                    catch(Exception ex)
                    {
                        LogUtil.Error("exception", $"[{device.name}]设备初始化出错：{ex.Message}");
                    }
                }
            }
        }

        #endregion
    }
}
