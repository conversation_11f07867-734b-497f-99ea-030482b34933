﻿using Fpi.UI.PC.Common;
using System.Windows.Forms;

namespace Fpi.Camera.UI
{
    partial class FrmNETCameraPreview
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code



        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlCameraCtrl = new System.Windows.Forms.Panel();
            this.logView = new Fpi.Camera.UI.UC_LogView();
            this.gbManualControl = new System.Windows.Forms.Panel();
            this.trkBarSpeed = new Sunny.UI.UITrackBar();
            this.gbYunTaiControl = new System.Windows.Forms.GroupBox();
            this.btnWiperOpen = new System.Windows.Forms.Button();
            this.btnDown = new System.Windows.Forms.Button();
            this.btnUp = new System.Windows.Forms.Button();
            this.btnLeft = new System.Windows.Forms.Button();
            this.btnRight = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.gbShowCtrl = new System.Windows.Forms.Panel();
            this.label5 = new System.Windows.Forms.Label();
            this.btnScreenShot = new System.Windows.Forms.Button();
            this.btnMax = new System.Windows.Forms.Button();
            this.btnConAndBreak = new System.Windows.Forms.Button();
            this.btnRecord = new System.Windows.Forms.Button();
            this.panel2 = new System.Windows.Forms.Panel();
            this.PreviewPanel = new System.Windows.Forms.Panel();
            this.pnlCameraCtrl.SuspendLayout();
            this.gbManualControl.SuspendLayout();
            this.gbYunTaiControl.SuspendLayout();
            this.gbShowCtrl.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlCameraCtrl
            // 
            this.pnlCameraCtrl.Controls.Add(this.logView);
            this.pnlCameraCtrl.Controls.Add(this.gbManualControl);
            this.pnlCameraCtrl.Controls.Add(this.gbShowCtrl);
            this.pnlCameraCtrl.Controls.Add(this.panel2);
            this.pnlCameraCtrl.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlCameraCtrl.Location = new System.Drawing.Point(0, 0);
            this.pnlCameraCtrl.Name = "pnlCameraCtrl";
            this.pnlCameraCtrl.Size = new System.Drawing.Size(240, 637);
            this.pnlCameraCtrl.TabIndex = 0;
            // 
            // logView
            // 
            this.logView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.logView.Location = new System.Drawing.Point(0, 377);
            this.logView.Name = "logView";
            this.logView.Size = new System.Drawing.Size(240, 260);
            this.logView.TabIndex = 3;
            // 
            // gbManualControl
            // 
            this.gbManualControl.Controls.Add(this.trkBarSpeed);
            this.gbManualControl.Controls.Add(this.gbYunTaiControl);
            this.gbManualControl.Controls.Add(this.label9);
            this.gbManualControl.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbManualControl.Location = new System.Drawing.Point(0, 176);
            this.gbManualControl.Name = "gbManualControl";
            this.gbManualControl.Size = new System.Drawing.Size(240, 201);
            this.gbManualControl.TabIndex = 1;
            this.gbManualControl.Text = "手动控制";
            // 
            // trkBarSpeed
            // 
            this.trkBarSpeed.BackColor = System.Drawing.Color.Transparent;
            this.trkBarSpeed.FillColor = System.Drawing.SystemColors.Control;
            this.trkBarSpeed.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.trkBarSpeed.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.trkBarSpeed.Location = new System.Drawing.Point(4, 5);
            this.trkBarSpeed.Maximum = 7;
            this.trkBarSpeed.Minimum = 1;
            this.trkBarSpeed.MinimumSize = new System.Drawing.Size(1, 1);
            this.trkBarSpeed.Name = "trkBarSpeed";
            this.trkBarSpeed.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.trkBarSpeed.Size = new System.Drawing.Size(188, 33);
            this.trkBarSpeed.Style = Sunny.UI.UIStyle.Custom;
            this.trkBarSpeed.TabIndex = 0;
            this.trkBarSpeed.Value = 1;
            // 
            // gbYunTaiControl
            // 
            this.gbYunTaiControl.Controls.Add(this.btnWiperOpen);
            this.gbYunTaiControl.Controls.Add(this.btnDown);
            this.gbYunTaiControl.Controls.Add(this.btnUp);
            this.gbYunTaiControl.Controls.Add(this.btnLeft);
            this.gbYunTaiControl.Controls.Add(this.btnRight);
            this.gbYunTaiControl.Location = new System.Drawing.Point(3, 50);
            this.gbYunTaiControl.Name = "gbYunTaiControl";
            this.gbYunTaiControl.Size = new System.Drawing.Size(230, 141);
            this.gbYunTaiControl.TabIndex = 1;
            this.gbYunTaiControl.TabStop = false;
            // 
            // btnWiperOpen
            // 
            this.btnWiperOpen.BackColor = System.Drawing.Color.Transparent;
            this.btnWiperOpen.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__7_;
            this.btnWiperOpen.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnWiperOpen.FlatAppearance.BorderSize = 0;
            this.btnWiperOpen.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnWiperOpen.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnWiperOpen.ForeColor = System.Drawing.Color.White;
            this.btnWiperOpen.Location = new System.Drawing.Point(85, 39);
            this.btnWiperOpen.Margin = new System.Windows.Forms.Padding(0);
            this.btnWiperOpen.Name = "btnWiperOpen";
            this.btnWiperOpen.Size = new System.Drawing.Size(60, 60);
            this.btnWiperOpen.TabIndex = 5;
            this.btnWiperOpen.Text = "雨刷";
            this.btnWiperOpen.UseVisualStyleBackColor = false;
            this.btnWiperOpen.Click += new System.EventHandler(this.btnWiperOpen_Click);
            this.btnWiperOpen.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            // 
            // btnDown
            // 
            this.btnDown.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__10_;
            this.btnDown.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnDown.FlatAppearance.BorderSize = 0;
            this.btnDown.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDown.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDown.ForeColor = System.Drawing.Color.White;
            this.btnDown.Location = new System.Drawing.Point(4, 91);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(221, 50);
            this.btnDown.TabIndex = 3;
            this.btnDown.Text = "下";
            this.btnDown.UseVisualStyleBackColor = true;
            this.btnDown.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            this.btnDown.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btnDown_MouseDown);
            this.btnDown.MouseUp += new System.Windows.Forms.MouseEventHandler(this.btnDown_MouseUp);
            // 
            // btnUp
            // 
            this.btnUp.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__8_;
            this.btnUp.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnUp.FlatAppearance.BorderSize = 0;
            this.btnUp.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnUp.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnUp.ForeColor = System.Drawing.Color.White;
            this.btnUp.Location = new System.Drawing.Point(4, -3);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(221, 50);
            this.btnUp.TabIndex = 0;
            this.btnUp.Text = "上";
            this.btnUp.UseVisualStyleBackColor = true;
            this.btnUp.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            this.btnUp.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btnUp_MouseDown);
            this.btnUp.MouseUp += new System.Windows.Forms.MouseEventHandler(this.btnUp_MouseUp);
            // 
            // btnLeft
            // 
            this.btnLeft.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__11_;
            this.btnLeft.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnLeft.FlatAppearance.BorderSize = 0;
            this.btnLeft.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLeft.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnLeft.ForeColor = System.Drawing.Color.White;
            this.btnLeft.Location = new System.Drawing.Point(-3, -2);
            this.btnLeft.Name = "btnLeft";
            this.btnLeft.Size = new System.Drawing.Size(85, 143);
            this.btnLeft.TabIndex = 1;
            this.btnLeft.Text = "左";
            this.btnLeft.UseVisualStyleBackColor = true;
            this.btnLeft.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            this.btnLeft.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btnLeft_MouseDown);
            this.btnLeft.MouseUp += new System.Windows.Forms.MouseEventHandler(this.btnLeft_MouseUp);
            // 
            // btnRight
            // 
            this.btnRight.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__9_;
            this.btnRight.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnRight.FlatAppearance.BorderSize = 0;
            this.btnRight.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRight.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRight.ForeColor = System.Drawing.Color.White;
            this.btnRight.Location = new System.Drawing.Point(147, -2);
            this.btnRight.Name = "btnRight";
            this.btnRight.Size = new System.Drawing.Size(85, 143);
            this.btnRight.TabIndex = 2;
            this.btnRight.Text = "右";
            this.btnRight.UseVisualStyleBackColor = true;
            this.btnRight.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            this.btnRight.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btnRight_MouseDown);
            this.btnRight.MouseUp += new System.Windows.Forms.MouseEventHandler(this.btnRight_MouseUp);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label9.Location = new System.Drawing.Point(194, 12);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(42, 21);
            this.label9.TabIndex = 28;
            this.label9.Text = "速度";
            // 
            // gbShowCtrl
            // 
            this.gbShowCtrl.BackColor = System.Drawing.SystemColors.ControlLight;
            this.gbShowCtrl.Controls.Add(this.label5);
            this.gbShowCtrl.Controls.Add(this.btnScreenShot);
            this.gbShowCtrl.Controls.Add(this.btnMax);
            this.gbShowCtrl.Controls.Add(this.btnConAndBreak);
            this.gbShowCtrl.Controls.Add(this.btnRecord);
            this.gbShowCtrl.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbShowCtrl.ForeColor = System.Drawing.SystemColors.ControlText;
            this.gbShowCtrl.Location = new System.Drawing.Point(0, 14);
            this.gbShowCtrl.Name = "gbShowCtrl";
            this.gbShowCtrl.Size = new System.Drawing.Size(240, 162);
            this.gbShowCtrl.TabIndex = 0;
            this.gbShowCtrl.Text = "显示控制";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.BackColor = System.Drawing.Color.Transparent;
            this.label5.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label5.Location = new System.Drawing.Point(88, 72);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(74, 21);
            this.label5.TabIndex = 21;
            this.label5.Text = "显示控制";
            // 
            // btnScreenShot
            // 
            this.btnScreenShot.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__5_;
            this.btnScreenShot.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnScreenShot.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnScreenShot.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnScreenShot.ForeColor = System.Drawing.Color.White;
            this.btnScreenShot.Location = new System.Drawing.Point(5, 82);
            this.btnScreenShot.Name = "btnScreenShot";
            this.btnScreenShot.Size = new System.Drawing.Size(120, 80);
            this.btnScreenShot.TabIndex = 1;
            this.btnScreenShot.Text = "截屏";
            this.btnScreenShot.UseVisualStyleBackColor = false;
            this.btnScreenShot.Click += new System.EventHandler(this.btnScreenShot_Click);
            this.btnScreenShot.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            // 
            // btnMax
            // 
            this.btnMax.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__3_;
            this.btnMax.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnMax.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnMax.FlatAppearance.BorderSize = 0;
            this.btnMax.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnMax.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnMax.ForeColor = System.Drawing.Color.White;
            this.btnMax.Location = new System.Drawing.Point(125, 0);
            this.btnMax.Name = "btnMax";
            this.btnMax.Size = new System.Drawing.Size(120, 80);
            this.btnMax.TabIndex = 1;
            this.btnMax.Text = "最大化";
            this.btnMax.UseVisualStyleBackColor = false;
            this.btnMax.Click += new System.EventHandler(this.btnMax_Click);
            this.btnMax.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            // 
            // btnConAndBreak
            // 
            this.btnConAndBreak.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__2_1;
            this.btnConAndBreak.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnConAndBreak.FlatAppearance.BorderSize = 0;
            this.btnConAndBreak.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnConAndBreak.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnConAndBreak.ForeColor = System.Drawing.Color.White;
            this.btnConAndBreak.Location = new System.Drawing.Point(4, 0);
            this.btnConAndBreak.Name = "btnConAndBreak";
            this.btnConAndBreak.Size = new System.Drawing.Size(120, 80);
            this.btnConAndBreak.TabIndex = 22;
            this.btnConAndBreak.Text = "连接";
            this.btnConAndBreak.Click += new System.EventHandler(this.btnConAndBreak_Click);
            this.btnConAndBreak.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            // 
            // btnRecord
            // 
            this.btnRecord.BackgroundImage = global::Fpi.Camera.Properties.Resources.监控__4_;
            this.btnRecord.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.btnRecord.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRecord.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRecord.ForeColor = System.Drawing.Color.White;
            this.btnRecord.Location = new System.Drawing.Point(125, 81);
            this.btnRecord.Name = "btnRecord";
            this.btnRecord.Size = new System.Drawing.Size(120, 80);
            this.btnRecord.TabIndex = 2;
            this.btnRecord.Text = "开始录像";
            this.btnRecord.Click += new System.EventHandler(this.btnRecord_Click);
            this.btnRecord.Paint += new System.Windows.Forms.PaintEventHandler(this.button_Paint);
            // 
            // panel2
            // 
            this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2.Location = new System.Drawing.Point(0, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(240, 14);
            this.panel2.TabIndex = 22;
            // 
            // PreviewPanel
            // 
            this.PreviewPanel.BackColor = System.Drawing.SystemColors.Control;
            this.PreviewPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.PreviewPanel.Location = new System.Drawing.Point(240, 0);
            this.PreviewPanel.Name = "PreviewPanel";
            this.PreviewPanel.Size = new System.Drawing.Size(664, 637);
            this.PreviewPanel.TabIndex = 1;
            // 
            // MainPreviewForm
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(904, 637);
            this.Controls.Add(this.PreviewPanel);
            this.Controls.Add(this.pnlCameraCtrl);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(3, 3, 3, 3);
            this.Name = "MainPreviewForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "视频监控";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.FrmNETCameraPreview_FormClosed);
            this.Load += new System.EventHandler(this.FrmNETCameraPreview_Load);
            this.pnlCameraCtrl.ResumeLayout(false);
            this.gbManualControl.ResumeLayout(false);
            this.gbManualControl.PerformLayout();
            this.gbYunTaiControl.ResumeLayout(false);
            this.gbShowCtrl.ResumeLayout(false);
            this.gbShowCtrl.PerformLayout();
            this.ResumeLayout(false);

        }


        #endregion

        private System.Windows.Forms.Panel pnlCameraCtrl;
        private System.Windows.Forms.Panel PreviewPanel;
        private System.Windows.Forms.Panel gbManualControl;
        private System.Windows.Forms.GroupBox gbYunTaiControl;
        private System.Windows.Forms.Button btnWiperOpen;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Button btnRight;
        private System.Windows.Forms.Button btnUp;
        private System.Windows.Forms.Button btnLeft;
        private System.Windows.Forms.Button btnDown;
        private System.Windows.Forms.Button btnRecord;
        private System.Windows.Forms.Button btnScreenShot;
        private System.Windows.Forms.Button btnMax;
        private System.Windows.Forms.Button btnConAndBreak;
        private Fpi.Camera.UI.UC_LogView logView;
        private System.Windows.Forms.Panel gbShowCtrl;
        private Label label5;
        private Panel panel2;
        private Sunny.UI.UITrackBar trkBarSpeed;
    }
}