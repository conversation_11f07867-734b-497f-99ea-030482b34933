﻿using System.IO;
using Fpi.Xml;
using Newtonsoft.Json;

namespace Fpi.Json
{
    /// <summary>
    /// JSON帮助类
    /// </summary>
    public static class FpiJsonHelper
    {
        /// <summary>
        /// 序列化参数控制
        /// </summary>
        private static JsonSerializerSettings setting = new JsonSerializerSettings
        {
            //NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.Indented,
            TypeNameHandling = TypeNameHandling.Auto,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            //DateFormatString = "yyyy-MM-dd HH:mm:ss"
        };

        /// <summary>
        /// 将Model转换成JOSN字符串
        /// </summary>
        /// <param name="value">Model对象</param>
        /// <returns></returns>
        public static string ModelToJson(object value)
        {
            string strRet = "";
            try
            {
                strRet = JsonConvert.SerializeObject(value, setting);
            }
            catch
            {
                strRet = "";
            }
            return strRet;
        }

        /// <summary>
        /// 将String字符串转换成Model
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static T JsonToModel<T>(string value)
        {
            return JsonConvert.DeserializeObject<T>(value, setting);
        }

        #region 存储、提取

        private static readonly object syncObj = new object();

        /// <summary>
        /// 数据保存到磁盘中
        /// </summary>
        /// <param name="value">Model对象</param>
        /// <returns></returns>
        public static bool SaveDataToDisk(object value)
        {
            bool flag = false;
            try
            {
                lock(syncObj)
                {
                    var filename = Path.Combine(ConstConfig.XmlPath, value.GetType().Name) + ".json";

                    if(File.Exists(filename))
                    {
                        FileInfo fi = new FileInfo(filename);
                        if(fi.IsReadOnly)
                        {
                            fi.IsReadOnly = false;
                        }

                        File.Delete(filename);
                    }

                    string strResult = JsonConvert.SerializeObject(value, setting);

                    using(TextWriter textWriter = File.CreateText(filename))
                    {
                        textWriter.Write(strResult);
                        textWriter.Flush();
                    }

                    flag = true;
                }
            }
            catch
            {
            }
            return flag;
        }

        /// <summary>
        /// 从磁盘中提取序列化的数据，赋值给传入对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static void LoadDataFromDisk(object value)
        {
            try
            {
                JsonConvert.PopulateObject(LoadJsonStrFromDisk(value.GetType().Name), value, setting);
            }
            catch
            {
            }
        }

        /// <summary>
        /// 从磁盘中提取序列化的数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static T LoadDataFromDisk<T>()
        {
            T result = default;

            try
            {
                result = JsonToModel<T>(LoadJsonStrFromDisk(typeof(T).Name));
            }
            catch
            {
            }

            return result;
        }

        /// <summary>
        /// 从磁盘中提取序列化的数据字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string LoadJsonStrFromDisk(string typeName)
        {
            lock(syncObj)
            {
                string result = string.Empty;
                try
                {
                    var filename = Path.Combine(ConstConfig.XmlPath, typeName) + ".json";

                    if(File.Exists(filename))
                    {
                        result = File.ReadAllText(filename);
                    }
                }
                catch
                {
                }
                return result;
            }
        }

        #endregion
    }
}
