﻿using System;
using Fpi.Communication.Interfaces;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Extensions;
using Fpi.Util.Interfaces;
using Fpi.Xml;

namespace Fpi.Communication.Buses
{
    public abstract class BaseBus : IBus, IDisposable, INaming, IConfigView
    {
        public static readonly string PropertyName_NeedSaveLog = "needsaveLog";//wxb20240624新增：是否保存日志

        protected BaseNode config;

        public BaseBus()
        {
        }

        public string GetProperty(string id)
        {
            return GetProperty(id, null);
        }

        public string GetProperty(string id, string defaultValue)
        {
            return config == null ? defaultValue : config.GetPropertyValue(id, defaultValue);
        }

        public override string ToString()
        {
            return !string.IsNullOrEmpty(FriendlyName) ? FriendlyName : GetType().Name;
        }

        #region IBus 成员

        public virtual void Init(BaseNode config)
        {
            this.config = config;
            this._needSaveLog = config.GetPropertyValue(PropertyName_NeedSaveLog).TryValue<bool>(true);
        }

        public abstract bool Write(byte[] buf);

        public abstract bool Read(byte[] buf, int count, ref int bytesread);


        #endregion

        #region IConnector 成员

        public abstract bool Open();

        public abstract bool Close();

        protected bool connected;

        public virtual bool Connected => connected;

        #endregion

        #region IDisposable 成员

        virtual public void Dispose()
        {
            if(connected)
            {
                Close();
            }
        }

        #endregion

        #region IConfigView 成员

        private BaseConfigureView view;
        public BaseConfigureView ConfigView
        {
            get
            {
                if(view == null)
                {
                    view = CreateConfigureView();
                }
                return view;
            }
        }

        protected virtual BaseConfigureView CreateConfigureView()
        {
            return null;
        }

        #endregion

        #region INaming 成员

        virtual public string FriendlyName => GetType().Name;
        virtual public string InstanceName => string.Empty;

        #endregion

        #region 日志输出控制

        protected bool _needSaveLog = true;
        public virtual bool NeedSaveLog
        {//wxb20240624新增：为了打印通信日志，在BusPort中统一处理，且可配置是否打印，从而实现：只打印某一个通道的通信日志
            get { return _needSaveLog; }
        }

        #endregion
    }
}