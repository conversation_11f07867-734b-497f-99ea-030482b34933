﻿using System;
using System.Collections;
using Fpi.Data.Config;
using Fpi.DB.Manager;
using Fpi.Util.Interfaces.Initialize;

namespace Fpi.HB.Business.DB
{
    /// <summary>
    /// 测量数据表创建帮助类
    /// </summary>
    public class MeasureTableCreator : IInitialization
    {
        #region IInitialization 成员

        public void Initialize()
        {
            try
            {
                this.CreateDataTable();
            }
            catch(Exception ex)
            {
                Log.LogUtil.Error(ex.Message);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建数据表
        /// </summary>
        private void CreateDataTable()
        {
            // 直接包含数据因子时，自动创建对应数据库
            if(DataManager.GetInstance().VarNodes != null && DataManager.GetInstance().VarNodes.GetCount() > 0)
            {
                CreateDataTable(DBConfig.REAL_DATA_TABLE, VarNodeTemplate.GetInstance().VarNodes.List);
            }
        }

        /// <summary>
        /// 创建数据表
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="nodes"></param>
        private void CreateDataTable(string tableName, ArrayList nodes)
        {
            FpiTable table = new FpiTable(tableName);
            FpiColumn col = new FpiColumn("id", ColumnType.Int, true, true);
            table.AddColumn(col);
            col = new FpiColumn("datatime", ColumnType.Datetime, "idx_datatime");
            table.AddColumn(col);
            foreach(VarNode varNode in nodes)
            {
                if(varNode is ValueNode)
                {
                    col = new FpiColumn { Name = DBConfig.PREFIX_F + varNode.id, Type = ColumnType.Double };
                    table.AddColumn(col);
                    col = new FpiColumn(DBConfig.PREFIX_F + varNode.id + DBConfig.POSTFIX, ColumnType.Int, 2);
                    table.AddColumn(col);
                }
            }
            FpiDataBase.GetInstance().AddTable(table);
            table.CreateTable();
        }

        #endregion
    }
}
