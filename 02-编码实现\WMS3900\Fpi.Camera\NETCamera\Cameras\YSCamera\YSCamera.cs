﻿using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using Fpi.ExternalSDK.YS;

namespace Fpi.Camera.YS
{
    /// <summary>
    /// 宇视摄像机
    /// </summary>
    public class YSCamera : BaseNETCamera
    {
        #region 字段属性

        /// <summary>
        /// SDK初始化完成参数标志位
        /// </summary>
        public static bool SdkInitOver { get; set; }

        #endregion

        #region 构造

        public YSCamera()
        {
            if(string.IsNullOrEmpty(Description))
            {
                Description = "宇视摄像机";
            }

            if(id.Contains("DefaultID"))
            {
                id = "YSCamera";
            }

            if(name.Contains("DefaultName"))
            {
                name = "宇视摄像机";
            }

            //ControlTypePath = typeof(FrmYSCameraControl).FullName;
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(name) || name.Contains("DefaultName") ? "宇视摄像机" : name;
        }

        #region 初始化

        /// <summary>
        /// 初始化摄像机SDK
        /// </summary>
        public override void InitSDK()
        {
            // 软件只注册一次即可
            // 避免重复注册
            if(!SdkInitOver)
            {
                //初始化参数
                try
                {
                    SdkInitOver = YSNetSDK.NETDEV_Init() == YSNetSDK.TRUE;

                    // 常规报警
                    YSNetSDK.NETDEV_AlarmMessCallBack_PF alarmCB = new YSNetSDK.NETDEV_AlarmMessCallBack_PF(alarmMessCallBack);
                    // 图片报警
                    YSNetSDK.NETDEV_PicAlarmMessCallBack_PF pictureCB = new YSNetSDK.NETDEV_PicAlarmMessCallBack_PF(PictureCallBack);
                    // 抽烟报警
                    YSNetSDK.NETDEV_SmokeAlarmMessCallBack_PF smokeAlarmCB = new YSNetSDK.NETDEV_SmokeAlarmMessCallBack_PF(SmokeAlarmCallBack);
                }
                catch(Exception)
                {
                    throw new Exception(name + ":--初始化SDK失败,error code= " + GetLastError());
                }

                if(!SdkInitOver)
                {
                    throw new Exception(name + ":--初始化SDK失败,error code= " + GetLastError());
                }
            }
        }

        /// <summary>
        /// 释放摄像机SDK
        /// </summary>
        public override void CleanSDK()
        {
            if(SdkInitOver)
            {
                try
                {
                    SdkInitOver = YSNetSDK.NETDEV_Cleanup() != YSNetSDK.TRUE;
                }
                catch(Exception)
                {
                    throw new Exception(name + ":--释放SDK失败,error code= " + GetLastError());
                }

                if(!SdkInitOver)
                {
                    throw new Exception(name + ":--释放SDK失败,error code= " + GetLastError());
                }
            }
        }

        #endregion

        #region 登录注销

        /// <summary>
        /// 登录
        /// </summary>
        public override void Login()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                NETDEV_DEVICE_LOGIN_INFO_S pstDevLoginInfo = new NETDEV_DEVICE_LOGIN_INFO_S();
                NETDEV_SELOG_INFO_S pstSELogInfo = new NETDEV_SELOG_INFO_S();
                pstDevLoginInfo.szIPAddr = Ip;
                pstDevLoginInfo.dwPort = int.Parse(Port);
                pstDevLoginInfo.szUserName = User;
                pstDevLoginInfo.szPassword = Pwd;
                if(IsDS)
                {
                    pstDevLoginInfo.dwLoginProto = (int)NETDEV_LOGIN_PROTO_E.NETDEV_LOGIN_PROTO_PRIVATE;
                }
                else
                {
                    pstDevLoginInfo.dwLoginProto = (int)NETDEV_LOGIN_PROTO_E.NETDEV_LOGIN_PROTO_ONVIF;
                }
                m_LoginID = YSNetSDK.NETDEV_Login_V30(ref pstDevLoginInfo, ref pstSELogInfo);
                if(IntPtr.Zero == m_LoginID)
                {
                    throw new Exception(name + "登录失败：error code=" + GetLastError());
                }
                PrevierChannel = 0;

                // 刻录机，计算预览通道号
                if(IsDS)
                {
                    PrevierChannel = DsPort - 1;
                }
            }
        }

        /// <summary>
        /// 注销
        /// </summary>
        public override void Logout()
        {
            if(IntPtr.Zero != m_LoginID)
            {
                if(YSNetSDK.FALSE == YSNetSDK.NETDEV_Logout(m_LoginID))
                {
                    throw new Exception(name + ":--注销失败: error code=" + GetLastError());
                }
                m_LoginID = IntPtr.Zero;
            }
        }

        #endregion

        #region 视频流相关

        /// <summary>
        /// 开始获取视频流
        /// </summary>
        /// <returns></returns>
        public override void StartRealPlay()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                throw new Exception(name + "开始获取视频流失败:设备还没注册。");
            }

            // TODO:测试
            if(m_NoPreviewRealPlayHandle != IntPtr.Zero)
            {
                throw new Exception(name + "开始获取视频流失败:后台正在获取视频流，不可重复获取！");
            }

            if(m_PreviewRealPlayHandle == IntPtr.Zero)
            {
                NETDEV_PREVIEWINFO_S stPreviewInfo = new NETDEV_PREVIEWINFO_S();
                stPreviewInfo.dwChannelID = DsPort;
                stPreviewInfo.dwLinkMode = (int)NETDEV_PROTOCAL_E.NETDEV_TRANSPROTOCAL_RTPTCP;
                stPreviewInfo.dwStreamType = (int)NETDEV_LIVE_STREAM_INDEX_E.NETDEV_LIVE_STREAM_INDEX_MAIN;
                stPreviewInfo.hPlayWnd = GetPreviewInterface().GetPicHandle();
                m_PreviewRealPlayHandle = YSNetSDK.NETDEV_RealPlay(m_LoginID, ref stPreviewInfo, IntPtr.Zero, IntPtr.Zero);
                if(m_PreviewRealPlayHandle == IntPtr.Zero)
                {
                    throw new Exception(name + "开始获取视频流失败：error code=" + GetLastError());
                }

                // 开启IVA(显示自己化的线)
                //YSNetSDK.NETDEV_SetIVAEnable(m_PreviewRealPlayHandle, 1);
            }
        }

        /// <summary>
        /// 停止获取视频流
        /// </summary>
        public override void StopRealPlay()
        {
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                if(YSNetSDK.FALSE == YSNetSDK.NETDEV_StopRealPlay(m_PreviewRealPlayHandle))
                {
                    throw new Exception(name + ":--停止获取视频流失败: error code=" + GetLastError());
                }
                m_PreviewRealPlayHandle = IntPtr.Zero;
            }
        }

        /// <summary>
        /// 开始获取视频流(不绑定预览界面)
        /// </summary>
        /// <returns></returns>
        public override void StartNoPreviewRealPlay()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                throw new Exception(name + "开始获取视频流失败:设备还没注册。");
            }

            // 前台正在预览
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                throw new Exception(name + "开始获取视频流失败:前台正在获取视频流，不可重复获取！");
            }

            if(m_NoPreviewRealPlayHandle == IntPtr.Zero)
            {
                NETDEV_PREVIEWINFO_S stPreviewInfo = new NETDEV_PREVIEWINFO_S();
                stPreviewInfo.dwChannelID = DsPort;
                stPreviewInfo.dwLinkMode = (int)NETDEV_PROTOCAL_E.NETDEV_TRANSPROTOCAL_RTPTCP;
                stPreviewInfo.dwStreamType = (int)NETDEV_LIVE_STREAM_INDEX_E.NETDEV_LIVE_STREAM_INDEX_MAIN;
                stPreviewInfo.hPlayWnd = IntPtr.Zero;
                m_NoPreviewRealPlayHandle = YSNetSDK.NETDEV_RealPlay(m_LoginID, ref stPreviewInfo, IntPtr.Zero, IntPtr.Zero);
                if(m_NoPreviewRealPlayHandle == IntPtr.Zero)
                {
                    throw new Exception(name + "开始获取视频流失败：error code=" + GetLastError());
                }
            }
        }

        /// <summary>
        /// 停止获取视频流(不绑定预览界面)
        /// </summary>
        /// <returns></returns>
        public override void StopNoPreviewRealPlay()
        {
            if(m_NoPreviewRealPlayHandle != IntPtr.Zero)
            {
                if(YSNetSDK.FALSE == YSNetSDK.NETDEV_StopRealPlay(m_NoPreviewRealPlayHandle))
                {
                    throw new Exception(name + ":--停止获取视频流失败: error code=" + GetLastError());
                }
                m_NoPreviewRealPlayHandle = IntPtr.Zero;
            }
        }

        #endregion

        #region 抓拍

        /// <summary>
        /// 抓拍图像到指定文件
        /// </summary>
        /// <param name="picFileName"></param>
        /// <returns></returns>
        public override bool ScreenShot(string picFileName)
        {
            // 初始化
            InitSDK();

            // 未登录，先登录
            if(IntPtr.Zero == m_LoginID)
            {
                Login();
            }

            bool flag = false;

            byte[] picSavePath;
            GetUTF8Buffer(picFileName, YSNetSDK.NETDEV_LEN_260, out picSavePath);

            // 预览抓图
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                flag = YSNetSDK.NETDEV_CapturePicture(m_PreviewRealPlayHandle, picSavePath, (int)NETDEV_PICTURE_FORMAT_E.NETDEV_PICTURE_JPG) == YSNetSDK.TRUE;
            }
            // 非预览抓图
            else
            {
                Thread.Sleep(100);

                StartNoPreviewRealPlay();

                Thread.Sleep(1000);

                flag = YSNetSDK.NETDEV_CapturePicture(m_NoPreviewRealPlayHandle, picSavePath, (int)NETDEV_PICTURE_FORMAT_E.NETDEV_PICTURE_JPG) == YSNetSDK.TRUE;

                StopNoPreviewRealPlay();
            }

            string str = flag
          ? name + ":--截图成功，文件：" + picFileName
          : name + "截图失败, error code= " + GetLastError();
            //CameraLogHelper.Info(str);
            return flag;
        }

        #endregion

        #region 录像

        /// <summary>
        /// 开始录像
        /// </summary>
        public override bool StartRecording(string videoFileName)
        {
            // 初始化
            InitSDK();

            // 未登录，先登录
            if(IntPtr.Zero == m_LoginID)
            {
                Login();
            }

            IntPtr handle = m_PreviewRealPlayHandle;

            // 如果当前没开始获取视频流，先开始获取视频流(不绑定预览界面)
            if(m_PreviewRealPlayHandle == IntPtr.Zero && m_NoPreviewRealPlayHandle == IntPtr.Zero)
            {
                StartNoPreviewRealPlay();
                handle = m_NoPreviewRealPlayHandle;
            }

            if(IsRecording)
            {
                return false;
            }

            //录像保存路径和文件名
            SVideoFileName = videoFileName;

            byte[] localRecordPath;
            GetUTF8Buffer(SVideoFileName, YSNetSDK.NETDEV_LEN_260, out localRecordPath);
            int iRet = YSNetSDK.NETDEV_SaveRealData(handle, localRecordPath, (int)NETDEV_MEDIA_FILE_FORMAT_E.NETDEV_MEDIA_FILE_MP4);

            if(YSNetSDK.TRUE == iRet)
            {
                IsRecording = true;
                string str = $"{name}:--开始录像成功!";
                //CameraLogHelper.Info(str);

                return true;
            }
            else
            {
                string str = $"{name}:--开始录像失败, error code= {GetLastError()}";
                //CameraLogHelper.Info(str);
                throw new Exception(str);
            }
        }

        /// <summary>
        /// 停止录像
        /// </summary>
        /// <returns></returns>
        public override bool StopRecording()
        {
            if(!IsRecording)
            {
                return false;
            }

            int iRet = YSNetSDK.FALSE;

            if(IntPtr.Zero != m_NoPreviewRealPlayHandle)
            {
                iRet = YSNetSDK.NETDEV_StopSaveRealData(m_NoPreviewRealPlayHandle);
                StopNoPreviewRealPlay();
            }
            else
            {
                iRet = YSNetSDK.NETDEV_StopSaveRealData(m_PreviewRealPlayHandle);
            }

            if(YSNetSDK.TRUE == iRet)
            {
                IsRecording = false;
                string str = name + ":--保存录像成功，录像文件名： " + SVideoFileName;
                CameraLogHelper.Info(str);

                return true;
            }
            else
            {
                string str = $"{name}:--停止录像失败, error code= {GetLastError()}";
                //CameraLogHelper.Info(str);
                throw new Exception(str);
            }
        }

        #endregion

        #region 云台控制

        /// <summary>
        /// 云台控制
        /// </summary>
        /// <param name="dwPTZCommand">控制方向类型</param>
        /// <param name="dwStop">是否停止(mouseup 表示停止传入1，down传入0)</param>
        /// <param name="dwSpeed">速度</param>
        public override void PTZControlWithSpeed(ControlType dwPTZCommand, int dwStop, int dwSpeed)
        {
            if(dwStop == 1)
            {
                YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_ALLSTOP, dwSpeed);
                return;
            }

            switch(dwPTZCommand)
            {
                case ControlType.UP:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_TILTUP, dwSpeed);
                    break;
                case ControlType.DOWN:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_TILTDOWN, dwSpeed);
                    break;
                case ControlType.LEFT:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_PANLEFT, dwSpeed);
                    break;
                case ControlType.RIGHT:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_PANRIGHT, dwSpeed);
                    break;
                case ControlType.LEFTTOP:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_LEFTUP, dwSpeed);
                    break;
                case ControlType.RIGHTTOP:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_RIGHTUP, dwSpeed);
                    break;
                case ControlType.LEFTDOWN:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_LEFTDOWN, dwSpeed);
                    break;
                case ControlType.RIGHTDOWN:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_RIGHTDOWN, dwSpeed);
                    break;
                case ControlType.WIPER:
                    YSNetSDK.NETDEV_PTZControl(m_PreviewRealPlayHandle, (int)NETDEV_PTZ_E.NETDEV_PTZ_BRUSHON, dwSpeed);
                    break;
            }
        }

        #endregion

        #region 时间校准

        /// <summary>
        /// 时间校准
        /// </summary>
        /// <returns></returns>
        public override void SetTime()
        {
            try
            {
                var now = DateTime.Now;
                NETDEV_TIME_CFG_S stTimeCfg = new NETDEV_TIME_CFG_S();
                stTimeCfg.stTime.dwYear = now.Year;
                stTimeCfg.stTime.dwMonth = now.Month;
                stTimeCfg.stTime.dwDay = now.Day;
                stTimeCfg.stTime.dwHour = now.Hour;
                stTimeCfg.stTime.dwMinute = now.Minute;
                stTimeCfg.stTime.dwSecond = now.Second;
                stTimeCfg.dwTimeZone = NETDEV_TIME_ZONE_E.NETDEV_TIME_ZONE_E0800;

                if(YSNetSDK.TRUE != YSNetSDK.NETDEV_SetSystemTimeCfg(m_LoginID, ref stTimeCfg))
                {
                    throw new Exception($"error code={GetLastError()}");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"{name}:--校准设备时间失败: {ex.Message}");
            }
        }

        #endregion

        #region 获取错误日志

        /// <summary>
        /// 获取错误日志
        /// </summary>
        /// <returns></returns>
        public override string GetLastError()
        {
            return YSNetSDK.NETDEV_GetLastError().ToString();
        }

        #endregion

        #endregion

        #region 私有方法

        public void GetUTF8Buffer(string inputString, int bufferLen, out byte[] utf8Buffer)
        {
            utf8Buffer = new byte[bufferLen];
            byte[] tempBuffer = System.Text.Encoding.UTF8.GetBytes(inputString);
            for(int i = 0; i < tempBuffer.Length; ++i)
            {
                utf8Buffer[i] = tempBuffer[i];
            }
        }

        #region 报警回调

        // NETDEV_ALARM_OBJECTS_INSIDE                                   = 1004,          /* 区域入侵  Objects inside */
        // NETDEV_ALARM_SMOKING                                          = 1428,         /* 吸烟告警 */
        // NETDEV_ALARM_NOT_WORN_WORKCLOTHES                             = 1424,         /* 未穿戴工作服报警 */

        /// <summary>
        /// 常规报警信息回调
        /// </summary>
        /// <param name="lpUserID"></param>
        /// <param name="dwChannelID"></param>
        /// <param name="stAlarmInfo"></param>
        /// <param name="lpBuf"></param>
        /// <param name="dwBufLen"></param>
        /// <param name="lpUserData"></param>
        public void alarmMessCallBack(IntPtr lpUserID, Int32 dwChannelID, NETDEV_ALARM_INFO_S stAlarmInfo, IntPtr lpBuf, Int32 dwBufLen, IntPtr lpUserData)
        {
            // 报警时间
            string strAlarmTime = TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(1970, 1, 1)).AddSeconds(stAlarmInfo.tAlarmTime).ToString("yyyy/MM/dd HH:mm:ss");
            //// 获取设备ip或设备ID,自己实现
            //string strDeviceIP = getDeviceIP(lpUserID);
            //// 获取报警详情
            //string strAlarmInfo = getAlarmInfo(stAlarmInfo.dwAlarmType);
        }

        /// <summary>
        /// 图片报警信息回调
        /// </summary>
        /// <param name="lpHandle"></param>
        /// <param name="pstAlarmPicData"></param>
        /// <param name="lpUserData"></param>
        private void PictureCallBack(IntPtr lpHandle, ref NETDEV_ALARM_PIC_DATA_S pstAlarmPicData, IntPtr lpUserData)
        {
            String strNowTime = DateTime.Now.ToString("HH-mm-ss-ms");
            String strFileName = strNowTime + "_" + "Pic_alarm.jpg";
            for(int i = 0; i < pstAlarmPicData.udwImageNum; i++)
            {
                NETDEV_STRUCT_IMAGE_INFO_S stImageInfo = (NETDEV_STRUCT_IMAGE_INFO_S)Marshal.PtrToStructure(pstAlarmPicData.pstImageInfo, typeof(NETDEV_STRUCT_IMAGE_INFO_S));
                byte[] array = new byte[stImageInfo.udwSize];
                YSNetSDK.MemCopy(array, stImageInfo.pszData, (Int32)stImageInfo.udwSize);

                FileStream fs = new FileStream(strFileName, FileMode.Create);
                fs.Write(array, 0, array.Length);
                fs.Close();
            }
        }

        /// <summary>
        /// 抽烟报警
        /// </summary>
        /// <param name="lpHandle"></param>
        /// <param name="pstAlarmInfo"></param>
        /// <param name="lpUserData"></param>
        private void SmokeAlarmCallBack(IntPtr lpHandle, ref NETDEV_SMOKE_DETC_S pstAlarmInfo, IntPtr lpUserData)
        {
            for(int i = 0; i < pstAlarmInfo.udwChannelNum; i++)
            {
                NETDEV_SMOKE_DETC_CHANNEL_S stSmokeDetcChnInfo = (NETDEV_SMOKE_DETC_CHANNEL_S)Marshal.PtrToStructure(pstAlarmInfo.pstSmokeDetcChannel, typeof(NETDEV_SMOKE_DETC_CHANNEL_S));
                for(int j = 0; j < stSmokeDetcChnInfo.udwImageNum; j++)
                {
                    NETDEV_STRUCT_IMAGE_INFO_S stImageInfo = (NETDEV_STRUCT_IMAGE_INFO_S)Marshal.PtrToStructure(stSmokeDetcChnInfo.pstImageList, typeof(NETDEV_STRUCT_IMAGE_INFO_S));
                    byte[] array = new byte[stImageInfo.udwSize];
                    YSNetSDK.MemCopy(array, stImageInfo.pszData, (Int32)stImageInfo.udwSize);

                    String strNowTime = DateTime.Now.ToString("HH-mm-ss-ms");
                    String strFileName = strNowTime + "_" + i + "_" + "smoke.jpg";

                    FileStream fs = new FileStream(strFileName, FileMode.Create);
                    fs.Write(array, 0, array.Length);
                    fs.Close();
                }
            }
        }

        #endregion

        #endregion
    }
}