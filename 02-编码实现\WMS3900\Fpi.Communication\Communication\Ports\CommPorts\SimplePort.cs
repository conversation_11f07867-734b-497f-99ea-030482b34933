using Fpi.Communication.Interfaces;

namespace Fpi.Communication.Ports.CommPorts
{
    public class SimplePort : BasePort
    {
        public override void Receive(object source, IByteStream data)
        {
            IPortOwner owner = PortOwner;
            if(owner != null)
            {
                //PortLogHelper.TracePortRecvMsg(this.GetType().Name, data.GetBytes());
                owner.Receive(source, data);
            }
        }

        public override object Send(object dest, IByteStream data)
        {
            IPort port = LowerPort;
            if(port != null)
            {
                //PortLogHelper.TracePortSendMsg(this.GetType().Name, data.GetBytes());
                return port.Send(dest, data);
            }
            return null;
        }
    }
}