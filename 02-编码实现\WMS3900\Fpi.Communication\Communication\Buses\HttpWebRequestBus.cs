﻿using System.ComponentModel;
using Fpi.Communication.Buses.UI.PC;
using Fpi.Communication.Exceptions;
using Fpi.UI.Common.PC.Configure;
using Fpi.Xml;

namespace Fpi.Communication.Buses
{
    [Description("HTTP通讯")]
    public class HttpWebRequestBus : BaseBus
    {
        public static readonly string PropertyName_URL = "url";
        public static readonly string PropertyName_Headers = "headers";
        public static readonly string PropertyName_ContentType = "contentType";

        public override string FriendlyName => "Http客户端";

        #region IBus 成员

        public override void Init(BaseNode config)
        {
            if(config == null)
                throw new CommunicationParamException("HttpWebRequest远程服务器参数未配置。");

            base.Init(config);
        }

        public override bool Write(byte[] buf)
        {
            return false;
        }

        public override bool Read(byte[] buf, int count, ref int bytesread)
        {
            return false;
        }

        #endregion

        #region IConnector 成员

        public override bool Open()
        {
            bool flag = true;
            connected = flag;
            return flag;
        }

        public override bool Close()
        {
            bool flag = true;
            connected = !flag;
            return flag;
        }

        #endregion

        protected override BaseConfigureView CreateConfigureView()
        {
            return new UC_HttpWebRequestBus();
        }
    }
}