<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="columnHeader3.Text" xml:space="preserve">
    <value>是否启用</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="columnHeader3.Width" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>通道编号</value>
  </data>
  <data name="columnHeader1.Width" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>通道命名</value>
  </data>
  <data name="columnHeader2.Width" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="columnHeader6.Text" xml:space="preserve">
    <value>对端设备</value>
  </data>
  <data name="columnHeader6.Width" type="System.Int32, mscorlib">
    <value>91</value>
  </data>
  <data name="columnHeader4.Text" xml:space="preserve">
    <value>链路类型</value>
  </data>
  <data name="columnHeader4.Width" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="columnHeader5.Text" xml:space="preserve">
    <value>通讯协议</value>
  </data>
  <data name="columnHeader5.Width" type="System.Int32, mscorlib">
    <value>297</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lsvPipes.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lsvPipes.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 0</value>
  </data>
  <data name="lsvPipes.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="lsvPipes.Size" type="System.Drawing.Size, System.Drawing">
    <value>797, 465</value>
  </data>
  <data name="lsvPipes.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lsvPipes.Name" xml:space="preserve">
    <value>lsvPipes</value>
  </data>
  <data name="&gt;&gt;lsvPipes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lsvPipes.Parent" xml:space="preserve">
    <value>uiPanel4</value>
  </data>
  <data name="&gt;&gt;lsvPipes.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="uiPanel4.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="uiPanel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="uiPanel4.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 35</value>
  </data>
  <data name="uiPanel4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="uiPanel4.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="uiPanel4.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 0, 0, 0</value>
  </data>
  <data name="uiPanel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>798, 465</value>
  </data>
  <data name="uiPanel4.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="uiPanel4.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;uiPanel4.Name" xml:space="preserve">
    <value>uiPanel4</value>
  </data>
  <data name="&gt;&gt;uiPanel4.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiPanel4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;uiPanel4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnCancel.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>692, 7</value>
  </data>
  <data name="btnCancel.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 35</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnOK.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>575, 7</value>
  </data>
  <data name="btnOK.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 35</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="uiPanel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="uiPanel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="uiPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 552</value>
  </data>
  <data name="uiPanel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="uiPanel1.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="uiPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>798, 47</value>
  </data>
  <data name="uiPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="uiPanel1.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;uiPanel1.Name" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;uiPanel1.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiPanel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;uiPanel1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnDel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDel.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnDel.Location" type="System.Drawing.Point, System.Drawing">
    <value>692, 12</value>
  </data>
  <data name="btnDel.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnDel.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 30</value>
  </data>
  <data name="btnDel.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="btnDel.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDel.Name" xml:space="preserve">
    <value>btnDel</value>
  </data>
  <data name="&gt;&gt;btnDel.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnDel.Parent" xml:space="preserve">
    <value>uiPanel3</value>
  </data>
  <data name="&gt;&gt;btnDel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnUpdate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnUpdate.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnUpdate.Location" type="System.Drawing.Point, System.Drawing">
    <value>575, 12</value>
  </data>
  <data name="btnUpdate.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnUpdate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 30</value>
  </data>
  <data name="btnUpdate.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="btnUpdate.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnUpdate.Name" xml:space="preserve">
    <value>btnUpdate</value>
  </data>
  <data name="&gt;&gt;btnUpdate.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnUpdate.Parent" xml:space="preserve">
    <value>uiPanel3</value>
  </data>
  <data name="&gt;&gt;btnUpdate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnAdd.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAdd.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>458, 12</value>
  </data>
  <data name="btnAdd.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 30</value>
  </data>
  <data name="btnAdd.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="btnAdd.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAdd.Name" xml:space="preserve">
    <value>btnAdd</value>
  </data>
  <data name="&gt;&gt;btnAdd.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnAdd.Parent" xml:space="preserve">
    <value>uiPanel3</value>
  </data>
  <data name="&gt;&gt;btnAdd.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="uiPanel3.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="uiPanel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="uiPanel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 500</value>
  </data>
  <data name="uiPanel3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="uiPanel3.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="uiPanel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>798, 52</value>
  </data>
  <data name="uiPanel3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="uiPanel3.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;uiPanel3.Name" xml:space="preserve">
    <value>uiPanel3</value>
  </data>
  <data name="&gt;&gt;uiPanel3.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiPanel3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;uiPanel3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>800, 600</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 35, 1, 1</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>通讯配置</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Name" xml:space="preserve">
    <value>columnHeader3</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Name" xml:space="preserve">
    <value>columnHeader1</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Name" xml:space="preserve">
    <value>columnHeader2</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Name" xml:space="preserve">
    <value>columnHeader6</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Name" xml:space="preserve">
    <value>columnHeader4</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Name" xml:space="preserve">
    <value>columnHeader5</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FrmPipeManager</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>Sunny.UI.UIForm, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
</root>