﻿namespace Fpi.Devices.Channel
{
    public class CoeffProcessor : Processor
    {
        #region — 属性 —
        /// <summary> 数据转换系数</summary>
        private double coefficient;

        public double Coefficient
        {
            get => coefficient;
            set => coefficient = value;
        }

        #endregion

        public CoeffProcessor()
        {
            this.ucParamConfig = new ConfigCoeffPanel();
        }

        public CoeffProcessor(string param) : this()
        {
            this.SetParam(param);
        }

        public override void SetParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length > 0)
            {
                double.TryParse(paramStrs[0], out coefficient);
            }
        }

        /// <summary>
        /// 输入数据处理
        /// </summary>
        /// <param name="inputValue"></param>
        /// <returns></returns>
        public override double InputProcessData(double inputValue)
        {
            return inputValue * this.coefficient;
        }

        /// <summary>
        /// 输出数据处理
        /// </summary>
        /// <param name="outputValue"></param>
        /// <returns></returns>
        public override double OutputProcessData(double outputValue)
        {
            double dataProcessed = double.NaN;

            dataProcessed = this.coefficient != 0 ? outputValue / this.coefficient : outputValue;

            return dataProcessed;
        }

        public override string ToString()
        {
            return "系数转换处理器(数值=采样值*系数)";
        }
    }
}
