﻿namespace Fpi.Data.ExpParser
{
    using System;
    using System.Collections.Generic;

    public class GrammarAnalyzer
    {
        private SyntaxAnalyzer _analyze = null;

        public GrammarAnalyzer(SyntaxAnalyzer analyze)
        {
            this._analyze = analyze;
        }

        public void Key_analyze(TOKENLink startLink, out TOKENLink endLink, out List<TOKENLink> commaList)
        {
            if((startLink.Next == null) || ((startLink.Next.Token.Type != ETokenType.token_operator) || (((TOKEN<Operator>)startLink.Next.Token).Tag.Type != EOperatorType.LeftParen)))
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）附近有语法错误", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            int num = 1;
            TOKENLink next = startLink.Next;
            commaList = new List<TOKENLink>();
            do
            {
                next = next.Next;
                if(next.Token.Type == ETokenType.token_operator)
                {
                    if(((TOKEN<Operator>)next.Token).Tag.Type == EOperatorType.LeftParen)
                    {
                        num++;
                    }
                    else if(((TOKEN<Operator>)next.Token).Tag.Type == EOperatorType.RightParen)
                    {
                        num--;
                    }
                }
                else if((num == 1) && (next.Token.Type == ETokenType.token_separator))
                {
                    if((next.Prev.Token.Type != ETokenType.token_operand) && ((next.Prev.Token.Type != ETokenType.token_operator) || (((TOKEN<Operator>)next.Prev.Token).Tag.Type != EOperatorType.RightParen)))
                    {
                        throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）的分隔符“,”有语法错误", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
                    }
                    commaList.Add(next);
                }
            }
            while(num != 0);
            endLink = next;
            if(endLink.Prev.Token.Type == ETokenType.token_separator)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）的分隔符“,”有语法错误", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            if(startLink.Next.Next == endLink)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）缺少表达式", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
        }

        public IOperand Key_ANDOR_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            IOperand operand = null;
            List<TOKENLink> list;
            int num = 1;
            this.Key_analyze(startLink, out endLink, out list);
            TOKENLink next = startLink.Next.Next;
            foreach(TOKENLink link2 in list)
            {
                if(this._analyze.Analyze(next, link2.Prev).Type != EDataType.Dbool)
                {
                    throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）的第{2}逻辑表达式无法转换为“bool”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString(), num.ToString()));
                }
                next = link2.Next;
                num++;
            }
            operand = this._analyze.Analyze(next, endLink.Prev);
            return operand.Type != EDataType.Dbool
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）的第{2}逻辑表达式无法转换为“bool”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString(), num.ToString()))
                : operand;
        }

        public IOperand Key_False_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            if(((((startLink.Next == null) || (startLink.Next.Token.Type != ETokenType.token_operator)) || ((((TOKEN<Operator>)startLink.Next.Token).Tag.Type != EOperatorType.LeftParen) || (startLink.Next.Next == null))) || (startLink.Next.Next.Token.Type != ETokenType.token_operator)) || (((TOKEN<Operator>)startLink.Next.Next.Token).Tag.Type != EOperatorType.RightParen))
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）附近有语法错误", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            endLink = startLink.Next.Next;
            return new Operand<bool>(EDataType.Dbool, false);
        }

        public IOperand Key_IF_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count < 2)
            {
                throw new Exception(string.Format("Error! 关键字“if”（索引:{0}）缺少表达式", startLink.Token.Index.ToString()));
            }
            if(list.Count > 2)
            {
                throw new Exception(string.Format("Error! 关键字“if”（索引:{0}）表达式过多", startLink.Token.Index.ToString()));
            }
            if(this._analyze.Analyze(startLink.Next.Next, list[0].Prev).Type != EDataType.Dbool && this._analyze.Analyze(startLink.Next.Next, list[0].Prev).Type != EDataType.Dunknown)
            {
                throw new Exception(string.Format("Error! 关键字“if”（索引:{0}）的逻辑表达式无法转换为“bool”", startLink.Token.Index.ToString()));
            }
            this._analyze.Analyze(list[0].Next, list[1].Prev);
            this._analyze.Analyze(list[1].Next, endLink.Prev);
            return new Operand<Unknown>(EDataType.Dunknown, new Unknown());
        }

        public IOperand Key_Len_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            return new Operand<int>(EDataType.Dint, 0);
        }

        public IOperand Key_Not_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            return operand.Type != EDataType.Dbool
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）的逻辑表达式无法转换为“bool”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : operand;
        }

        public IOperand Key_NowDate_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            if(((((startLink.Next == null) || (startLink.Next.Token.Type != ETokenType.token_operator)) || ((((TOKEN<Operator>)startLink.Next.Token).Tag.Type != EOperatorType.LeftParen) || (startLink.Next.Next == null))) || (startLink.Next.Next.Token.Type != ETokenType.token_operator)) || (((TOKEN<Operator>)startLink.Next.Next.Token).Tag.Type != EOperatorType.RightParen))
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）附近有语法错误", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            endLink = startLink.Next.Next;
            return new Operand<DateTime>(EDataType.Ddatetime, DateTime.Now);
        }

        public IOperand Key_ToDateTime_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            try
            {
                Convert.ToDateTime(operand.Value);
            }
            catch
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）无法将表达式转换为“datetime”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            return new Operand<DateTime>(EDataType.Ddatetime, Convert.ToDateTime(operand.Value));
        }

        public IOperand Key_ToDouble_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            try
            {
                Convert.ToDouble(operand.Value);
            }
            catch
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）无法将表达式转换为“double”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            return new Operand<double>(EDataType.Ddouble, 0.0);
        }

        public IOperand Key_ToInt_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            try
            {
                Convert.ToInt32(operand.Value);
            }
            catch
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）无法将表达式转换为“int”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            return new Operand<int>(EDataType.Dint, 0);
        }

        public IOperand Key_ToString_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count == 0)
            {
                this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            }
            else
            {
                if(list.Count != 1)
                {
                    throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
                }
                IOperand operand = this._analyze.Analyze(startLink.Next.Next, list[0].Prev);
                //if (operand.Type != EDataType.Ddatetime || operand.Type != EDataType.Dstring)
                //{
                //    throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}） 第一表达式无法转换为“datatime”", ((TOKEN<KeyWord>) startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
                //}
                IOperand operand2 = this._analyze.Analyze(list[0].Next, endLink.Prev);
                if(operand2.Type != EDataType.Dstring)
                {
                    throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）第二参数有误应为字符串!", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString(), operand2.ToString()));
                }
                if(operand.Type == EDataType.Ddatetime)
                {
                    try
                    {
                        string s = DateTime.Now.ToString(operand2.ToString());
                    }
                    catch
                    {
                        throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}） 日期转换格式有误“{2}”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString(), operand2.ToString()));
                    }
                    Operand<DateTime> operand4 = (Operand<DateTime>)operand;
                    return new Operand<string>(EDataType.Dstring, operand4.TValue.ToString(((TOKEN<IOperand>)list[0].Next.Token).Tag.ToString()));
                }
            }
            return new Operand<string>(EDataType.Dstring, "");
        }

        public IOperand Key_True_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            if(((((startLink.Next == null) || (startLink.Next.Token.Type != ETokenType.token_operator)) || ((((TOKEN<Operator>)startLink.Next.Token).Tag.Type != EOperatorType.LeftParen) || (startLink.Next.Next == null))) || (startLink.Next.Next.Token.Type != ETokenType.token_operator)) || (((TOKEN<Operator>)startLink.Next.Next.Token).Tag.Type != EOperatorType.RightParen))
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）附近有语法错误", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            endLink = startLink.Next.Next;
            return new Operand<bool>(EDataType.Dbool, true);
        }

        public IOperand Key_GetNodeValue_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            return list.Count > 1
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : (IOperand)new Operand<double>(EDataType.Ddouble, 0);
        }

        public IOperand Key_GetNodeIntValue_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            return list.Count > 1
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : (IOperand)new Operand<int>(EDataType.Dint, 0);
        }

        public IOperand Key_GetNodeBoolValue_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            return list.Count > 1
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : (IOperand)new Operand<bool>(EDataType.Dbool, false);
        }

        public IOperand Key_DoubleNaN_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);

            return operand.Type != EDataType.Ddouble
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）无法将表达式转换为“Double”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : (IOperand)new Operand<bool>(EDataType.Dbool, false);
        }

        public IOperand Key_IsAlarmNode_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count > 0)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            IOperand operand = this._analyze.Analyze(startLink.Next.Next, endLink.Prev);
            return operand.Type != EDataType.Dstring
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）无法将表达式转换为“string”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : (IOperand)new Operand<bool>(EDataType.Dbool, false);
        }

        public IOperand Key_CASE_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            int num = 1;
            this.Key_analyze(startLink, out endLink, out list);
            if(list.Count < 1)
            {
                throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过少", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()));
            }
            if(list.Count % 2 == 0)
            {
                throw new Exception("表达式个数不对！");
            }

            TOKENLink next = startLink.Next.Next;
            foreach(TOKENLink link2 in list)
            {
                if(num % 2 == 1 && this._analyze.Analyze(next, link2.Prev).Type != EDataType.Dbool)
                {
                    throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）的第{2}逻辑表达式无法转换为“bool”", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString(), num.ToString()));
                }
                next = link2.Next;
                num++;
            }
            return new Operand<Unknown>(EDataType.Dunknown, new Unknown());
        }

        public IOperand Key_Invoke_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            return (((TOKEN<IOperand>)(startLink.Next.Next.Token)).Tag).Type != EDataType.Dstring
                ? throw new Exception("第一个参数不是字符串，请核对!")
                : (IOperand)new Operand<Unknown>(EDataType.Dunknown, new Unknown());
        }

        public IOperand Key_GetAlarmNodeBoolValue_Analyze(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_analyze(startLink, out endLink, out list);
            return list.Count > 1
                ? throw new Exception(string.Format("Error! 关键字“{0}”（索引:{1}）表达式过多", ((TOKEN<KeyWord>)startLink.Token).Tag.Value, startLink.Token.Index.ToString()))
                : (IOperand)new Operand<bool>(EDataType.Dbool, false);
        }
    }
}

