﻿using System.Windows.Forms;
namespace Fpi.Communication.Ports.UI.PC
{
    partial class UC_FpiRouterPort
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.nuAddress = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.nuAddress)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(86, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "本端地址";
            // 
            // nuAddress
            // 
            this.nuAddress.Location = new System.Drawing.Point(151, 25);
            this.nuAddress.Maximum = new decimal(new int[] {
            255,
            0,
            0,
            0});
            this.nuAddress.Name = "nuAddress";
            this.nuAddress.Size = new System.Drawing.Size(158, 21);
            this.nuAddress.TabIndex = 1;
            // 
            // UC_FpiRouterPort
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.nuAddress);
            this.Controls.Add(this.label1);
            this.Name = "UC_FpiRouterPort";
            this.Size = new System.Drawing.Size(395, 71);
            ((System.ComponentModel.ISupportInitialize)(this.nuAddress)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private NumericUpDown nuAddress;
    }
}
