﻿using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Extensions;
using Fpi.Xml;

namespace Fpi.Communication.Buses.UI.PC
{
    public partial class UC_UdpBus : BaseConfigureView// System.Windows.Forms.UserControl
    {
        public UC_UdpBus()
        {
            InitializeComponent();
        }

        protected override void ShowConfig(object obj)
        {
            if(!(configObj is BaseNode configNode))
            {
                return;
            }

            if(configNode.GetProperty(UdpBus.PropertyName_Host) != null)
            {
                this.txtUdpIp.Text = configNode.GetPropertyValue(UdpBus.PropertyName_Host);
            }
            if(configNode.GetProperty(UdpBus.PropertyName_RecPort) != null)
            {
                this.txtLocalPort.Text = configNode.GetPropertyValue(UdpBus.PropertyName_RecPort);
            }
            if(configNode.GetProperty(UdpBus.PropertyName_SendPort) != null)
            {
                this.txtDesPort.Text = configNode.GetPropertyValue(UdpBus.PropertyName_SendPort);
            }

            this.chkNeedSaveLog.Checked = configNode.GetPropertyValue(BaseBus.PropertyName_NeedSaveLog, string.Empty).TryValue<bool>(true);
        }

        public override object Save()
        {
            BaseNode configNode = configObj as BaseNode;
            configNode.SetProperty(UdpBus.PropertyName_Host, this.txtUdpIp.Text);
            configNode.SetProperty(UdpBus.PropertyName_RecPort, this.txtLocalPort.Text);
            configNode.SetProperty(UdpBus.PropertyName_SendPort, this.txtDesPort.Text);
            configNode.SetProperty(BaseBus.PropertyName_NeedSaveLog, this.chkNeedSaveLog.Checked.ToString());
            return configNode;
        }
    }
}
