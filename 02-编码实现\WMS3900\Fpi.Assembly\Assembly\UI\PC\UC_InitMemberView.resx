<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="gbContent.Size" type="System.Drawing.Size, System.Drawing">
    <value>355, 277</value>
  </data>
  <data name="&gt;&gt;txtID.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 12</value>
  </data>
  <data name="chkActive.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 16</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="chkActive.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="&gt;&gt;gbContent.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;txtDLL.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;txtDescription.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 232</value>
  </data>
  <data name="&gt;&gt;chkActive.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtDescription.Location" type="System.Drawing.Point, System.Drawing">
    <value>76, 140</value>
  </data>
  <data name="radFatal.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="chkActive.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 25</value>
  </data>
  <data name="txtID.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="label6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 140</value>
  </data>
  <data name="radNotFatal.Text" xml:space="preserve">
    <value>非致命</value>
  </data>
  <data name="&gt;&gt;gbContent.Name" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="&gt;&gt;radNotFatal.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>异常</value>
  </data>
  <data name="&gt;&gt;txtDLL.Name" xml:space="preserve">
    <value>txtDLL</value>
  </data>
  <data name="radNotFatal.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;txtDLL.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="&gt;&gt;radNotFatal.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="radFatal.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 21</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtName.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtName.ScrollBars" type="System.Windows.Forms.ScrollBars, System.Windows.Forms">
    <value>Vertical</value>
  </data>
  <data name="&gt;&gt;txtName.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtID.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 21</value>
  </data>
  <data name="txtDLL.Location" type="System.Drawing.Point, System.Drawing">
    <value>76, 104</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>程序集</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDescription.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 60</value>
  </data>
  <data name="&gt;&gt;txtDescription.Name" xml:space="preserve">
    <value>txtDescription</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 31</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>UC_InitMemberView</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>76, 208</value>
  </data>
  <data name="gbContent.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="radNotFatal.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 16</value>
  </data>
  <data name="txtDescription.ScrollBars" type="System.Windows.Forms.ScrollBars, System.Windows.Forms">
    <value>Vertical</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="radFatal.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 16</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;gbContent.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 107</value>
  </data>
  <data name="txtName.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 21</value>
  </data>
  <data name="radNotFatal.Location" type="System.Drawing.Point, System.Drawing">
    <value>81, 21</value>
  </data>
  <data name="&gt;&gt;chkActive.Name" xml:space="preserve">
    <value>chkActive</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 350</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtDLL.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 21</value>
  </data>
  <data name="txtDescription.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;txtName.Name" xml:space="preserve">
    <value>txtName</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="chkActive.Text" xml:space="preserve">
    <value>启用该初始化接口</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtDescription.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="txtID.Location" type="System.Drawing.Point, System.Drawing">
    <value>76, 28</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;radNotFatal.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtName.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="label5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="txtName.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 66</value>
  </data>
  <data name="&gt;&gt;txtID.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="gbContent.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 52</value>
  </data>
  <data name="txtDescription.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radFatal.Text" xml:space="preserve">
    <value>致命</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="&gt;&gt;txtID.Name" xml:space="preserve">
    <value>txtID</value>
  </data>
  <data name="&gt;&gt;radNotFatal.Name" xml:space="preserve">
    <value>radNotFatal</value>
  </data>
  <data name="txtDLL.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>41, 69</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;radFatal.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;radFatal.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;chkActive.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>gbContent</value>
  </data>
  <data name="radNotFatal.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gbContent.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;radFatal.Name" xml:space="preserve">
    <value>radFatal</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkActive.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radFatal.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtDLL.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtDescription.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 51</value>
  </data>
  <data name="&gt;&gt;radFatal.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="chkActive.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>