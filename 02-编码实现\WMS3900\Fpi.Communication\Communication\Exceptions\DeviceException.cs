using System;

namespace Fpi.Communication.Exceptions
{
    /// <summary>
    /// 
    /// </summary>
    public class DeviceException : CommunicationException
    {
        public DeviceException()
            : base()
        {
        }

        public DeviceException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public DeviceException(string message)
            : base(message)
        {
        }
    }
}