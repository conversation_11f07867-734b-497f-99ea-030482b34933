﻿//==================================================================================================
//类名：     OutSwitchDataChannel   
//创建人:    曹旭
//创建时间:  2012-9-26 15:14:04
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using Fpi.Data.Config;
using Fpi.Xml;

namespace Fpi.Devices.Channel
{
    public class OutSwitchChannel : SwitchChannel
    {
        public OutSwitchChannel()
        {
            this.ChannelType = eDeviceChannelType.OutSwitch;
        }

        public override BaseNode Init(System.Xml.XmlNode node)
        {
            base.Init(node);

            if(this.StateNode != null)
            {
                this.StateNode.ValueChangedEvent += new ValueChangeHandler(SetOutValue);
            }

            return this;
        }

        /// <summary>
        /// 设置开关量数据输出（将开关量值输出到通道值上）
        /// </summary>
        /// <param name="varNode"></param>
        /// <param name="newValue"></param>
        public override void SetOutValue(VarNode varNode, object newValue)
        {
            this.ChannelValue = newValue;
        }
    }
}
