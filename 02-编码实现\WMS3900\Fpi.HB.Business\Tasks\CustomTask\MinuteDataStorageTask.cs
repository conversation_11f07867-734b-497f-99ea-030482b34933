﻿using System;
using Fpi.HB.Business.DB;

namespace Fpi.HB.Business.Tasks
{
    /// <summary>
    /// 分钟数据存储任务
    /// </summary>
    public class MinuteDataStorageTask : CustomTask
    {
        #region 构造

        public MinuteDataStorageTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "分钟数据存储任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "MinuteDataStorageTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "分钟数据存储任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "分钟数据存储任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            DateTime now = DateTime.Now;
            // 整分存储
            DBHelper.WriteRealDataToDb(now.AddSeconds(-now.Second));
        }

        #endregion
    }
}
