﻿namespace Fpi.Data.ExpParser
{
    using System.Runtime.InteropServices;

    [StructLayout(LayoutKind.Sequential)]
    public struct Operator
    {
        public int Dimension;
        public int PRI;
        public EOperatorType Type;
        public string Value;
        public Operator(int dimension, int pri, EOperatorType type, string value)
        {
            this.Dimension = dimension;
            this.PRI = pri;
            this.Type = type;
            this.Value = value;
        }
    }
}

