﻿//==================================================================================================
//类名：     DataChannel   
//创建人:    曹旭
//创建时间:  2012-9-24 15:19:29
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using Fpi.Data.Config;
using Fpi.Xml;

namespace Fpi.Devices.Channel
{
    public class DataChannel : IdNameNode
    {
        public string VarNodeId;

        public object ChannelValue { get; set; }

        public eDeviceChannelType ChannelType { get; protected set; }

        public VarNode GetVarNode()
        {
            return !string.IsNullOrEmpty(VarNodeId) ? DataManager.GetInstance().GetVarNodeById(VarNodeId) : null;
        }

        /// <summary>
        /// 设置开关量输入通道当前状态（将采集到的开关量状态设置到变量上）
        /// </summary>
        public virtual void SetInState()
        {

        }

        /// <summary>
        /// 设置数据通道实时数据（将采集到得数据设置到变量上）
        /// </summary>
        public virtual void SetRealValue()
        {

        }

        /// <summary>
        /// 设置开关量/模拟量输出通道值（将开关量状态和模拟量值输出到通道上）
        /// </summary>
        public virtual void SetOutValue(VarNode varNode, object newValue)
        {

        }
    }
}
