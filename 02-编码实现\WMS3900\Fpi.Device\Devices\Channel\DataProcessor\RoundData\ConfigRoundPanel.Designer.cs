﻿namespace Fpi.Devices.Channel
{
    partial class ConfigRoundPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label5 = new System.Windows.Forms.Label();
            this.txtRound = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.rdoBtn1 = new System.Windows.Forms.RadioButton();
            this.rdoBtn2 = new System.Windows.Forms.RadioButton();
            this.SuspendLayout();
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label5.Location = new System.Drawing.Point(37, 12);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 15;
            this.label5.Text = "保留小数位数";
            // 
            // txtRound
            // 
            this.txtRound.CanEmpty = false;
            this.txtRound.DigitLength = 0;
            this.txtRound.InputType = Fpi.UI.Common.PC.Controls.TextInputType.UInt;
            this.txtRound.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtRound.IsValidCheck = true;
            this.txtRound.Label = "保留小数位数";
            this.txtRound.Location = new System.Drawing.Point(123, 8);
            this.txtRound.MaxValue = null;
            this.txtRound.MinValue = null;
            this.txtRound.Name = "txtRound";
            this.txtRound.Size = new System.Drawing.Size(202, 21);
            this.txtRound.TabIndex = 0;
            this.txtRound.Text = "1";
            // 
            // rdoBtn1
            // 
            this.rdoBtn1.AutoSize = true;
            this.rdoBtn1.Location = new System.Drawing.Point(123, 41);
            this.rdoBtn1.Name = "rdoBtn1";
            this.rdoBtn1.Size = new System.Drawing.Size(71, 16);
            this.rdoBtn1.TabIndex = 16;
            this.rdoBtn1.TabStop = true;
            this.rdoBtn1.Text = "四舍五入";
            this.rdoBtn1.UseVisualStyleBackColor = true;
            // 
            // rdoBtn2
            // 
            this.rdoBtn2.AutoSize = true;
            this.rdoBtn2.Location = new System.Drawing.Point(240, 40);
            this.rdoBtn2.Name = "rdoBtn2";
            this.rdoBtn2.Size = new System.Drawing.Size(71, 16);
            this.rdoBtn2.TabIndex = 17;
            this.rdoBtn2.TabStop = true;
            this.rdoBtn2.Text = "直接截断";
            this.rdoBtn2.UseVisualStyleBackColor = true;
            // 
            // ConfigRoundPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.rdoBtn2);
            this.Controls.Add(this.rdoBtn1);
            this.Controls.Add(this.txtRound);
            this.Controls.Add(this.label5);
            this.Name = "ConfigRoundPanel";
            this.Size = new System.Drawing.Size(398, 64);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label5;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtRound;
        private System.Windows.Forms.RadioButton rdoBtn1;
        private System.Windows.Forms.RadioButton rdoBtn2;
    }
}
