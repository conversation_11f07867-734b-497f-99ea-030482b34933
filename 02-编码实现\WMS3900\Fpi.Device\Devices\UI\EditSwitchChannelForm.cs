using System;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Data.UI.PC;
using Fpi.Devices.Channel;

namespace Fpi.Devices.UI
{
    internal partial class EditSwitchChannelForm : Form
    {
        private EditSwitchChannelForm()
        {
            InitializeComponent();
        }

        public EditSwitchChannelForm(SwitchChannel channel)
            : this()
        {
            this.channel = channel;
        }

        private SwitchChannel channel;

        private void FormEditSwitchChannel_Load(object sender, EventArgs e)
        {
            this.txtId.Text = channel.id;
            this.txtName.Text = channel.name;
            ShowVar(channel.GetVarNode());
        }

        private void ShowVar(VarNode var)
        {
            this.txtNode.Tag = var;
            this.txtNode.Text = (var != null ? var.FullName : string.Empty);
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            FormVarExplore form = new FormVarExplore(eVarType.State);
            if(form.ShowDialog() == DialogResult.OK)
            {
                ShowVar(form.VarNode);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(this.txtNode.Tag is VarNode var)
            {
                channel.StateNode = var as StateNode;
                channel.VarNodeId = var.FullId;
            }
            else
            {
                channel.StateNode = null;
                channel.VarNodeId = string.Empty;
            }

        }
    }
}