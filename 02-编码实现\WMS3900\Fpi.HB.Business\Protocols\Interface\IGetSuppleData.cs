﻿using System;
using System.Collections.Generic;
using Fpi.Communication.Interfaces;

namespace Fpi.HB.Business.Protocols.Interface
{
    /// <summary>
    /// 数据补传业务实现接口
    /// </summary>
    public interface IGetSuppleData
    {
        /// <summary>
        /// 获取待补传数据
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        List<IByteStream> GetSuppleData(DateTime startTime, DateTime endTime, int type);
    }
}
