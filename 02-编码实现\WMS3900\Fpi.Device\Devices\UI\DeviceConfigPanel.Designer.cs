﻿namespace Fpi.Devices.UI
{
    partial class DeviceConfigPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnInValue = new System.Windows.Forms.Button();
            this.gbxCommParam = new System.Windows.Forms.GroupBox();
            this.txtAddr = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.chkIsUsed = new System.Windows.Forms.CheckBox();
            this.pnlCommParam = new System.Windows.Forms.Panel();
            this.cmbCommType = new System.Windows.Forms.ComboBox();
            this.label9 = new System.Windows.Forms.Label();
            this.btnOutValue = new System.Windows.Forms.Button();
            this.btnBuildChl = new System.Windows.Forms.Button();
            this.btnInSwitch = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.cmbDeviceCommun = new Sunny.UI.UIComboBox();
            this.label13 = new System.Windows.Forms.Label();
            this.cmbDeviceMeasureType = new System.Windows.Forms.ComboBox();
            this.label11 = new System.Windows.Forms.Label();
            this.txtAlarmGroup = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.btnOutSwitch = new System.Windows.Forms.Button();
            this.btnTimerConfig = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.gbDescription = new System.Windows.Forms.GroupBox();
            this.txtDescription = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.gbDeviceChlConfig = new System.Windows.Forms.GroupBox();
            this.numOutSwitchCount = new System.Windows.Forms.NumericUpDown();
            this.numInSwitchCount = new System.Windows.Forms.NumericUpDown();
            this.numOutValueCount = new System.Windows.Forms.NumericUpDown();
            this.numInValueCount = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.gbBasicInfo = new System.Windows.Forms.GroupBox();
            this.cmbDeviceImp = new Sunny.UI.UIComboBox();
            this.label12 = new System.Windows.Forms.Label();
            this.txtSearchCondition = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.txtName = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.txtId = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.lblDeviceImp = new System.Windows.Forms.Label();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlMain = new System.Windows.Forms.Panel();
            this.gbxCommParam.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.gbDescription.SuspendLayout();
            this.gbDeviceChlConfig.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOutSwitchCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numInSwitchCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOutValueCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numInValueCount)).BeginInit();
            this.gbBasicInfo.SuspendLayout();
            this.pnlMain.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnInValue
            // 
            this.btnInValue.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnInValue.Enabled = false;
            this.btnInValue.Location = new System.Drawing.Point(6, 19);
            this.btnInValue.Name = "btnInValue";
            this.btnInValue.Size = new System.Drawing.Size(115, 36);
            this.btnInValue.TabIndex = 5;
            this.btnInValue.Text = "数值输入通道";
            this.btnInValue.UseVisualStyleBackColor = true;
            this.btnInValue.Click += new System.EventHandler(this.btnInValue_Click);
            // 
            // gbxCommParam
            // 
            this.gbxCommParam.Controls.Add(this.txtAddr);
            this.gbxCommParam.Controls.Add(this.label10);
            this.gbxCommParam.Controls.Add(this.chkIsUsed);
            this.gbxCommParam.Controls.Add(this.pnlCommParam);
            this.gbxCommParam.Controls.Add(this.cmbCommType);
            this.gbxCommParam.Controls.Add(this.label9);
            this.gbxCommParam.Location = new System.Drawing.Point(0, 241);
            this.gbxCommParam.Name = "gbxCommParam";
            this.gbxCommParam.Size = new System.Drawing.Size(478, 96);
            this.gbxCommParam.TabIndex = 20;
            this.gbxCommParam.TabStop = false;
            this.gbxCommParam.Text = "设备通信默认参数配置";
            // 
            // txtAddr
            // 
            this.txtAddr.BackColor = System.Drawing.SystemColors.Window;
            this.txtAddr.CanEmpty = true;
            this.txtAddr.DigitLength = 2;
            this.txtAddr.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtAddr.InvalidBackColor = System.Drawing.Color.Red;
            this.txtAddr.IsValidCheck = false;
            this.txtAddr.Label = "";
            this.txtAddr.Location = new System.Drawing.Point(323, 16);
            this.txtAddr.MaxValue = "";
            this.txtAddr.MinValue = "";
            this.txtAddr.Name = "txtAddr";
            this.txtAddr.Size = new System.Drawing.Size(35, 21);
            this.txtAddr.TabIndex = 11;
            this.txtAddr.Text = "0";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(240, 20);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(83, 12);
            this.label10.TabIndex = 10;
            this.label10.Text = "设备地址(HEX)";
            // 
            // chkIsUsed
            // 
            this.chkIsUsed.AutoSize = true;
            this.chkIsUsed.Location = new System.Drawing.Point(371, 19);
            this.chkIsUsed.Name = "chkIsUsed";
            this.chkIsUsed.Size = new System.Drawing.Size(96, 16);
            this.chkIsUsed.TabIndex = 9;
            this.chkIsUsed.Text = "是否启用设备";
            this.chkIsUsed.UseVisualStyleBackColor = true;
            this.chkIsUsed.CheckedChanged += new System.EventHandler(this.chkIsUsed_CheckedChanged);
            // 
            // pnlCommParam
            // 
            this.pnlCommParam.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlCommParam.Location = new System.Drawing.Point(3, 43);
            this.pnlCommParam.Name = "pnlCommParam";
            this.pnlCommParam.Size = new System.Drawing.Size(472, 50);
            this.pnlCommParam.TabIndex = 8;
            // 
            // cmbCommType
            // 
            this.cmbCommType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCommType.FormattingEnabled = true;
            this.cmbCommType.Items.AddRange(new object[] {
            "串口通信",
            "其他方式",
            "数据文件",
            "以太网服务端",
            "以太网客户端"});
            this.cmbCommType.Location = new System.Drawing.Point(113, 17);
            this.cmbCommType.Name = "cmbCommType";
            this.cmbCommType.Size = new System.Drawing.Size(111, 20);
            this.cmbCommType.Sorted = true;
            this.cmbCommType.TabIndex = 7;
            this.cmbCommType.SelectedIndexChanged += new System.EventHandler(this.cmbCommType_SelectedIndexChanged);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(30, 20);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(77, 12);
            this.label9.TabIndex = 6;
            this.label9.Text = "通信类型选择";
            // 
            // btnOutValue
            // 
            this.btnOutValue.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOutValue.Enabled = false;
            this.btnOutValue.Location = new System.Drawing.Point(123, 19);
            this.btnOutValue.Name = "btnOutValue";
            this.btnOutValue.Size = new System.Drawing.Size(115, 36);
            this.btnOutValue.TabIndex = 6;
            this.btnOutValue.Text = "数值输出通道";
            this.btnOutValue.UseVisualStyleBackColor = true;
            this.btnOutValue.Click += new System.EventHandler(this.btnOutValue_Click);
            // 
            // btnBuildChl
            // 
            this.btnBuildChl.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBuildChl.Location = new System.Drawing.Point(149, 144);
            this.btnBuildChl.Name = "btnBuildChl";
            this.btnBuildChl.Size = new System.Drawing.Size(75, 23);
            this.btnBuildChl.TabIndex = 10;
            this.btnBuildChl.Text = "重建通道";
            this.btnBuildChl.UseVisualStyleBackColor = true;
            this.btnBuildChl.Click += new System.EventHandler(this.btnBuildChl_Click);
            // 
            // btnInSwitch
            // 
            this.btnInSwitch.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnInSwitch.Enabled = false;
            this.btnInSwitch.Location = new System.Drawing.Point(240, 19);
            this.btnInSwitch.Name = "btnInSwitch";
            this.btnInSwitch.Size = new System.Drawing.Size(115, 36);
            this.btnInSwitch.TabIndex = 7;
            this.btnInSwitch.Text = "开关量输入通道";
            this.btnInSwitch.UseVisualStyleBackColor = true;
            this.btnInSwitch.Click += new System.EventHandler(this.btnInSwitch_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.cmbDeviceCommun);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.cmbDeviceMeasureType);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.txtAlarmGroup);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Location = new System.Drawing.Point(249, 5);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(224, 131);
            this.groupBox2.TabIndex = 19;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "设备报警相关配置";
            // 
            // cmbDeviceCommun
            // 
            this.cmbDeviceCommun.DataSource = null;
            this.cmbDeviceCommun.DropDownAutoWidth = true;
            this.cmbDeviceCommun.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbDeviceCommun.FillColor = System.Drawing.Color.White;
            this.cmbDeviceCommun.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.cmbDeviceCommun.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbDeviceCommun.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbDeviceCommun.Location = new System.Drawing.Point(65, 93);
            this.cmbDeviceCommun.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbDeviceCommun.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbDeviceCommun.Name = "cmbDeviceCommun";
            this.cmbDeviceCommun.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbDeviceCommun.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(130)))), ((int)(((byte)(135)))), ((int)(((byte)(144)))));
            this.cmbDeviceCommun.Size = new System.Drawing.Size(152, 20);
            this.cmbDeviceCommun.SymbolSize = 24;
            this.cmbDeviceCommun.TabIndex = 31;
            this.cmbDeviceCommun.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbDeviceCommun.Watermark = "";
            this.cmbDeviceCommun.SelectedIndexChanged += new System.EventHandler(this.cmbDeviceCommun_SelectedIndexChanged);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(6, 97);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(53, 12);
            this.label13.TabIndex = 22;
            this.label13.Text = "通讯协议";
            // 
            // cmbDeviceMeasureType
            // 
            this.cmbDeviceMeasureType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDeviceMeasureType.FormattingEnabled = true;
            this.cmbDeviceMeasureType.Items.AddRange(new object[] {
            "串口通信",
            "其他方式",
            "数据文件",
            "以太网服务端",
            "以太网客户端"});
            this.cmbDeviceMeasureType.Location = new System.Drawing.Point(95, 60);
            this.cmbDeviceMeasureType.Name = "cmbDeviceMeasureType";
            this.cmbDeviceMeasureType.Size = new System.Drawing.Size(122, 20);
            this.cmbDeviceMeasureType.Sorted = true;
            this.cmbDeviceMeasureType.TabIndex = 21;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(18, 63);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(77, 12);
            this.label11.TabIndex = 10;
            this.label11.Text = "设备测量类型";
            // 
            // txtAlarmGroup
            // 
            this.txtAlarmGroup.BackColor = System.Drawing.SystemColors.Window;
            this.txtAlarmGroup.CanEmpty = false;
            this.txtAlarmGroup.DigitLength = 2;
            this.txtAlarmGroup.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtAlarmGroup.InvalidBackColor = System.Drawing.Color.Red;
            this.txtAlarmGroup.IsValidCheck = true;
            this.txtAlarmGroup.Label = "";
            this.txtAlarmGroup.Location = new System.Drawing.Point(95, 27);
            this.txtAlarmGroup.MaxValue = "";
            this.txtAlarmGroup.MinValue = "";
            this.txtAlarmGroup.Name = "txtAlarmGroup";
            this.txtAlarmGroup.ReadOnly = true;
            this.txtAlarmGroup.Size = new System.Drawing.Size(123, 21);
            this.txtAlarmGroup.TabIndex = 7;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(6, 32);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(89, 12);
            this.label7.TabIndex = 1;
            this.label7.Text = "设备报警组编号";
            // 
            // btnOutSwitch
            // 
            this.btnOutSwitch.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOutSwitch.Enabled = false;
            this.btnOutSwitch.Location = new System.Drawing.Point(357, 19);
            this.btnOutSwitch.Name = "btnOutSwitch";
            this.btnOutSwitch.Size = new System.Drawing.Size(115, 36);
            this.btnOutSwitch.TabIndex = 8;
            this.btnOutSwitch.Text = "开关量输出通道";
            this.btnOutSwitch.UseVisualStyleBackColor = true;
            this.btnOutSwitch.Click += new System.EventHandler(this.btnOutSwitch_Click);
            // 
            // btnTimerConfig
            // 
            this.btnTimerConfig.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnTimerConfig.Location = new System.Drawing.Point(357, 143);
            this.btnTimerConfig.Name = "btnTimerConfig";
            this.btnTimerConfig.Size = new System.Drawing.Size(109, 28);
            this.btnTimerConfig.TabIndex = 9;
            this.btnTimerConfig.Text = "设备定时器配置";
            this.btnTimerConfig.UseVisualStyleBackColor = true;
            this.btnTimerConfig.Click += new System.EventHandler(this.btnTimerConfig_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnOutSwitch);
            this.groupBox1.Controls.Add(this.btnInSwitch);
            this.groupBox1.Controls.Add(this.btnOutValue);
            this.groupBox1.Controls.Add(this.btnInValue);
            this.groupBox1.Location = new System.Drawing.Point(0, 174);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(478, 63);
            this.groupBox1.TabIndex = 18;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "设备通道详细配置";
            // 
            // gbDescription
            // 
            this.gbDescription.Controls.Add(this.txtDescription);
            this.gbDescription.Location = new System.Drawing.Point(0, 343);
            this.gbDescription.Name = "gbDescription";
            this.gbDescription.Size = new System.Drawing.Size(478, 80);
            this.gbDescription.TabIndex = 12;
            this.gbDescription.TabStop = false;
            this.gbDescription.Text = "设备描述信息";
            // 
            // txtDescription
            // 
            this.txtDescription.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtDescription.Location = new System.Drawing.Point(3, 17);
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Size = new System.Drawing.Size(472, 60);
            this.txtDescription.TabIndex = 0;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(248, 73);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 6;
            this.label6.Text = "设备名称";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(54, 74);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 5;
            this.label5.Text = "设备编号";
            // 
            // gbDeviceChlConfig
            // 
            this.gbDeviceChlConfig.Controls.Add(this.numOutSwitchCount);
            this.gbDeviceChlConfig.Controls.Add(this.numInSwitchCount);
            this.gbDeviceChlConfig.Controls.Add(this.numOutValueCount);
            this.gbDeviceChlConfig.Controls.Add(this.numInValueCount);
            this.gbDeviceChlConfig.Controls.Add(this.btnBuildChl);
            this.gbDeviceChlConfig.Controls.Add(this.label4);
            this.gbDeviceChlConfig.Controls.Add(this.label3);
            this.gbDeviceChlConfig.Controls.Add(this.label2);
            this.gbDeviceChlConfig.Controls.Add(this.label1);
            this.gbDeviceChlConfig.Location = new System.Drawing.Point(3, 5);
            this.gbDeviceChlConfig.Name = "gbDeviceChlConfig";
            this.gbDeviceChlConfig.Size = new System.Drawing.Size(236, 169);
            this.gbDeviceChlConfig.TabIndex = 16;
            this.gbDeviceChlConfig.TabStop = false;
            this.gbDeviceChlConfig.Text = "设备通道配置";
            // 
            // numOutSwitchCount
            // 
            this.numOutSwitchCount.Location = new System.Drawing.Point(113, 117);
            this.numOutSwitchCount.Name = "numOutSwitchCount";
            this.numOutSwitchCount.Size = new System.Drawing.Size(111, 21);
            this.numOutSwitchCount.TabIndex = 14;
            // 
            // numInSwitchCount
            // 
            this.numInSwitchCount.Location = new System.Drawing.Point(113, 87);
            this.numInSwitchCount.Name = "numInSwitchCount";
            this.numInSwitchCount.Size = new System.Drawing.Size(111, 21);
            this.numInSwitchCount.TabIndex = 13;
            // 
            // numOutValueCount
            // 
            this.numOutValueCount.Location = new System.Drawing.Point(113, 54);
            this.numOutValueCount.Name = "numOutValueCount";
            this.numOutValueCount.Size = new System.Drawing.Size(111, 21);
            this.numOutValueCount.TabIndex = 12;
            // 
            // numInValueCount
            // 
            this.numInValueCount.Location = new System.Drawing.Point(113, 22);
            this.numInValueCount.Name = "numInValueCount";
            this.numInValueCount.Size = new System.Drawing.Size(111, 21);
            this.numInValueCount.TabIndex = 11;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(18, 119);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 3;
            this.label4.Text = "开关量输出通道";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(18, 89);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "开关量输入通道";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(30, 56);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "数值输出通道";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(30, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "数值输入通道";
            // 
            // gbBasicInfo
            // 
            this.gbBasicInfo.Controls.Add(this.cmbDeviceImp);
            this.gbBasicInfo.Controls.Add(this.label12);
            this.gbBasicInfo.Controls.Add(this.txtSearchCondition);
            this.gbBasicInfo.Controls.Add(this.label6);
            this.gbBasicInfo.Controls.Add(this.label5);
            this.gbBasicInfo.Controls.Add(this.txtName);
            this.gbBasicInfo.Controls.Add(this.txtId);
            this.gbBasicInfo.Controls.Add(this.lblDeviceImp);
            this.gbBasicInfo.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbBasicInfo.Location = new System.Drawing.Point(0, 0);
            this.gbBasicInfo.Name = "gbBasicInfo";
            this.gbBasicInfo.Size = new System.Drawing.Size(478, 97);
            this.gbBasicInfo.TabIndex = 15;
            this.gbBasicInfo.TabStop = false;
            this.gbBasicInfo.Text = "设备基本信息";
            // 
            // cmbDeviceImp
            // 
            this.cmbDeviceImp.DataSource = null;
            this.cmbDeviceImp.DropDownAutoWidth = true;
            this.cmbDeviceImp.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbDeviceImp.FillColor = System.Drawing.Color.White;
            this.cmbDeviceImp.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.cmbDeviceImp.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbDeviceImp.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbDeviceImp.Location = new System.Drawing.Point(113, 41);
            this.cmbDeviceImp.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbDeviceImp.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbDeviceImp.Name = "cmbDeviceImp";
            this.cmbDeviceImp.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbDeviceImp.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(130)))), ((int)(((byte)(135)))), ((int)(((byte)(144)))));
            this.cmbDeviceImp.Size = new System.Drawing.Size(353, 22);
            this.cmbDeviceImp.SymbolSize = 24;
            this.cmbDeviceImp.TabIndex = 30;
            this.cmbDeviceImp.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbDeviceImp.Watermark = "";
            this.cmbDeviceImp.SelectedIndexChanged += new System.EventHandler(this.cmbDeviceImp_SelectedIndexChanged);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(54, 18);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(53, 12);
            this.label12.TabIndex = 8;
            this.label12.Text = "名称筛选";
            // 
            // txtSearchCondition
            // 
            this.txtSearchCondition.BackColor = System.Drawing.SystemColors.Window;
            this.txtSearchCondition.CanEmpty = true;
            this.txtSearchCondition.DigitLength = 2;
            this.txtSearchCondition.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtSearchCondition.InvalidBackColor = System.Drawing.Color.Red;
            this.txtSearchCondition.IsValidCheck = true;
            this.txtSearchCondition.Label = "设备编号";
            this.txtSearchCondition.Location = new System.Drawing.Point(113, 15);
            this.txtSearchCondition.MaxValue = "";
            this.txtSearchCondition.MinValue = "";
            this.txtSearchCondition.Name = "txtSearchCondition";
            this.txtSearchCondition.Size = new System.Drawing.Size(353, 21);
            this.txtSearchCondition.TabIndex = 7;
            this.txtSearchCondition.TextChanged += new System.EventHandler(this.txtSearchCondition_TextChanged);
            // 
            // txtName
            // 
            this.txtName.BackColor = System.Drawing.SystemColors.Window;
            this.txtName.CanEmpty = false;
            this.txtName.DigitLength = 2;
            this.txtName.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtName.InvalidBackColor = System.Drawing.Color.Red;
            this.txtName.IsValidCheck = true;
            this.txtName.Label = "设备名称";
            this.txtName.Location = new System.Drawing.Point(307, 69);
            this.txtName.MaxValue = "";
            this.txtName.MinValue = "";
            this.txtName.Name = "txtName";
            this.txtName.Size = new System.Drawing.Size(159, 21);
            this.txtName.TabIndex = 5;
            // 
            // txtId
            // 
            this.txtId.BackColor = System.Drawing.SystemColors.Window;
            this.txtId.CanEmpty = false;
            this.txtId.DigitLength = 2;
            this.txtId.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtId.InvalidBackColor = System.Drawing.Color.Red;
            this.txtId.IsValidCheck = true;
            this.txtId.Label = "设备编号";
            this.txtId.Location = new System.Drawing.Point(113, 70);
            this.txtId.MaxValue = "";
            this.txtId.MinValue = "";
            this.txtId.Name = "txtId";
            this.txtId.Size = new System.Drawing.Size(111, 21);
            this.txtId.TabIndex = 5;
            // 
            // lblDeviceImp
            // 
            this.lblDeviceImp.AutoSize = true;
            this.lblDeviceImp.Location = new System.Drawing.Point(18, 46);
            this.lblDeviceImp.Name = "lblDeviceImp";
            this.lblDeviceImp.Size = new System.Drawing.Size(89, 12);
            this.lblDeviceImp.TabIndex = 1;
            this.lblDeviceImp.Text = "设备实现类选择";
            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.btnTimerConfig);
            this.pnlMain.Controls.Add(this.groupBox1);
            this.pnlMain.Controls.Add(this.gbxCommParam);
            this.pnlMain.Controls.Add(this.gbDescription);
            this.pnlMain.Controls.Add(this.groupBox2);
            this.pnlMain.Controls.Add(this.gbDeviceChlConfig);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Enabled = false;
            this.pnlMain.Location = new System.Drawing.Point(0, 97);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new System.Drawing.Size(478, 423);
            this.pnlMain.TabIndex = 12;
            // 
            // DeviceConfigPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.gbBasicInfo);
            this.Name = "DeviceConfigPanel";
            this.Size = new System.Drawing.Size(478, 520);
            this.Load += new System.EventHandler(this.DeviceConfigPanel_Load);
            this.gbxCommParam.ResumeLayout(false);
            this.gbxCommParam.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.gbDescription.ResumeLayout(false);
            this.gbDescription.PerformLayout();
            this.gbDeviceChlConfig.ResumeLayout(false);
            this.gbDeviceChlConfig.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numOutSwitchCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numInSwitchCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numOutValueCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numInValueCount)).EndInit();
            this.gbBasicInfo.ResumeLayout(false);
            this.gbBasicInfo.PerformLayout();
            this.pnlMain.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnInValue;
        private System.Windows.Forms.GroupBox gbxCommParam;
        private System.Windows.Forms.Panel pnlCommParam;
        private System.Windows.Forms.ComboBox cmbCommType;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Button btnOutValue;
        private System.Windows.Forms.Button btnBuildChl;
        private System.Windows.Forms.Button btnInSwitch;
        private System.Windows.Forms.GroupBox groupBox2;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtAlarmGroup;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Button btnOutSwitch;
        private System.Windows.Forms.Button btnTimerConfig;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox gbDescription;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.GroupBox gbDeviceChlConfig;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtName;
        private System.Windows.Forms.GroupBox gbBasicInfo;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtId;
        private System.Windows.Forms.Label lblDeviceImp;
        private System.Windows.Forms.CheckBox chkIsUsed;
        private System.Windows.Forms.NumericUpDown numOutSwitchCount;
        private System.Windows.Forms.NumericUpDown numInSwitchCount;
        private System.Windows.Forms.NumericUpDown numOutValueCount;
        private System.Windows.Forms.NumericUpDown numInValueCount;
        private System.Windows.Forms.Label label10;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtAddr;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtSearchCondition;
        private System.Windows.Forms.ComboBox cmbDeviceMeasureType;
        private System.Windows.Forms.Label label13;
        private Sunny.UI.UIComboBox cmbDeviceImp;
        private Sunny.UI.UIComboBox cmbDeviceCommun;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Panel pnlMain;
    }
}
