﻿using System;

namespace Fpi.HB.Business.HisData
{
    public static class ReportFormatHelper
    {
        /// <summary>
        ///  根据报表配置中配置的小数位数修约数据
        /// </summary>
        /// <param name="queryNodeId"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static double ToTransFormat(string queryNodeId, double value)
        {
            if(value == double.NaN)
            {
                return value;
            }

            QueryNode node = ReportManager.GetInstance().GetQueryNodeByNodeId(queryNodeId);
            return node != null ? Math.Round(value, node.Dec) : value;
        }
    }
}