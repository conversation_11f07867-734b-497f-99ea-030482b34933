POST /LAPI/V1.0/System/Event/Subscription HTTP/1.1
cache-control: no-cache
Postman-Token: bc22aa8f-3926-4946-ba17-176d915b1ff1
Content-Type: application/json
User-Agent: PostmanRuntime/6.4.1
Accept: */*
Host: *************
accept-encoding: gzip, deflate
content-length: 201
Connection: keep-alive

{
"AddressType":	0,						   
"IPAddress":	"*************",				
"Port":	6666,								
"Duration": 200,						
"Type":	1024,	
"SubscribePersonCondition":{
	"LibIDNum":65535,
	"LibIDList":[]
	}
}HTTP/1.1 200 Ok
Content-Length: 407
Content-Type: text/plain
Connection: close
X-Frame-Options: SAMEORIGIN

{
"Response": {
	"ResponseURL": "/LAPI/V1.0/System/Event/Subscription/1000",
	"CreatedID": 1000, 
	"ResponseCode": 0,
 	"SubResponseCode": 0,
 	"ResponseString": "Succeed",
	"StatusCode": 0,
	"StatusString": "Succeed",
	"Data": {
	    "ID": 1000,
	    "Reference": "*************:80/Subscription/Subscribers/1000",
	    "CurrentTime": 1667458185,
	    "TerminationTime": 1667458385
	}
	}
}
