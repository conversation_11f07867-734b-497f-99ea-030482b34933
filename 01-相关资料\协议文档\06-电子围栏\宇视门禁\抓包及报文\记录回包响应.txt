POST /LAPI/V1.0/System/Event/Notification/PersonVerification HTTP/1.1
Host: *************:5597
Content-Length: 844
Content-Type: text/plain
Connection: keep-alive

{"Reference":"","Seq":3,"DeviceCode":"210235C6F33225000084","Timestamp":1667982393,"NotificationType":0,"FaceInfoNum":1,"FaceInfoList":[{"ID":3,"Timestamp":1667982392,"CapSrc":1,"FeatureNum":0,"FeatureList":[{"FeatureVersion":"","Feature":""},{"FeatureVersion":"","Feature":""}],"Temperature":0.0,"MaskFlag":0,"PanoImage":{"Name":"1667982392_1_0.jpg","Size":0,"Data":"","URL":""},"FaceImage":{"Name":"1667982392_2_0.jpg","Size":0,"Data":"","URL":""},"FaceArea":{"LeftTopX":0,"LeftTopY":0,"RightBottomX":0,"RightBottomY":0}}],"CardInfoNum":0,"CardInfoList":[],"GateInfoNum":0,"GateInfoList":[],"LibMatInfoNum":1,"LibMatInfoList":[{"ID":3,"LibID":3,"LibType":3,"MatchStatus":1,"MatchConfidence":99,"MatchPersonID":4026531841,"MatchFaceID":4026531841,"MatchPersonInfo":{"PersonCode":"","PersonName":"456","Gender":0,"CardID":"","IdentityNo":""}}]}
HTTP/1.1 200 OK
Content-Length: 169
Content-Type: application/json
Connection: close
X-Frame-Options: SAMEORIGIN

{"Response": {"ResponseURL":"/LAPI/V1.0/PACS/Controller/Event/Notifications","StatusCode":0,"StatusString":"Succeed","Data":{"RecordID":3,"Time":"2022-11-09 16:26:16"}}}