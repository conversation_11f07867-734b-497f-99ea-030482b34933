﻿using System;
using System.Windows.Forms;
using Fpi.Camera.UI;
using Fpi.UI.Common.PC;

namespace Fpi.Camera.HIK.UI
{
    /// <summary>
    /// 摄像机控制界面
    /// </summary>
    public partial class FrmHIKCameraControl : FrmBaseControl
    {
        #region 字段属性

        /// <summary>
        /// 对应摄像机
        /// </summary>
        public HikvisonCamera Camera { get; set; }

        #endregion

        #region 构造

        public FrmHIKCameraControl()
        {
            InitializeComponent();
        }

        public FrmHIKCameraControl(HikvisonCamera camera)
            : this()
        {
            this.Camera = camera;
        }

        #endregion

        #region 事件

        #region 界面

        /// <summary>
        /// 加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmHIKCameraControl_Load(object sender, EventArgs e)
        {
            #region 水印设置

            txtCameraOSD.Text = Camera.OSDString;
            txtCameraOSDX.Text = Camera.OSDx.ToString();
            txtCameraOSDY.Text = Camera.OSDy.ToString();
            chkCameraOSD.Checked = Camera.OSDflag;

            #endregion

            //巡航路线
            string[] nums = new string[257];
            for(int i = 0; i < nums.Length; i++)
            {
                nums[i] = i.ToString();
            }
            cbbSpeedCruise.Items.AddRange(nums);
            cbbTimeCruise.Items.AddRange(nums);
            cbbSpeedCruise.SelectedIndex = 0;
            cbbTimeCruise.SelectedIndex = 0;
            cbbPresetCruise.SelectedIndex = 0;
            cbbPreset.SelectedIndex = 0;
        }

        #endregion

        #region 控制

        #region 归位点设置

        /// <summary>
        /// 归位点设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCallHome_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.PTZPreset(PTZPresetCmd.GotoPreset, Camera.HomePoint);
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("调用归位点错误。\r\n" + ex.Message);
            }
        }

        /// <summary>
        /// 归位点设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetHome_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion("确定要设置归位点吗？") == DialogResult.Yes)
                {
                    Camera.PTZPreset(PTZPresetCmd.SetPreset, Camera.HomePoint);
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("设置归位点错误。\r\n" + ex.Message);
            }
        }

        #endregion

        #region 预置位设置

        /// <summary>
        /// 调用预置位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCallPreset_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.PTZPreset(PTZPresetCmd.GotoPreset, (uint)(cbbPreset.SelectedIndex + 1));
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 设置预置位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetPreset_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion("确定要设置预置位吗？") == DialogResult.Yes)
                {
                    Camera.PTZPreset(PTZPresetCmd.SetPreset, (uint)(cbbPreset.SelectedIndex + 1));
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 清除预置位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnClePreset_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion("确定要清除预置位吗？") == DialogResult.Yes)
                {
                    Camera.PTZPreset(PTZPresetCmd.ClePreset, (uint)(cbbPreset.SelectedIndex + 1));
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 巡航路线

        private void btnRunPTZCruise_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.PTZCruiseStart();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnStopPTZCruise_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.PTZCruiseStop();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnAddCruise_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.PTZCruiseAddPreset((ushort)(cbbPresetCruise.SelectedIndex + 1), (ushort)cbbSpeedCruise.SelectedIndex, (ushort)cbbTimeCruise.SelectedIndex);
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnDeleteCruise_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.PTZCruiseDeletePreset((ushort)(cbbPresetCruise.SelectedIndex + 1));
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 云台水平角度控制

        /// <summary>
        /// 获取当前云台水平角度 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnGetPTZ_P_Click(object sender, EventArgs e)
        {
            try
            {
                nudPTZ_P.Value = Camera.GetPTZPOS_P();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 设置云台当前水平角度
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetPTZ_P_Click(object sender, EventArgs e)
        {
            try
            {
                if(nudPTZ_P.Value > 360 || nudPTZ_P.Value < 0)
                {
                    throw new Exception("设置云台当前水平角度错误:请设置在河里范围内[0,360]度。");
                }
                else
                {
                    Camera.SetPTZPOS_P((ushort)nudPTZ_P.Value);
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 时间校准

        /// <summary>
        /// 时间校准
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCalTime_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.SetTime();
                FpiMessageBox.ShowInfo("时间校准成功！");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 水印

        /// <summary>
        /// 设置水印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetOSD_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.SetOSD(chkCameraOSD.Checked, txtCameraOSD.Text, (ushort)txtCameraOSD.Text.Length, ushort.Parse(txtCameraOSDX.Text), ushort.Parse(txtCameraOSDY.Text));

                Camera.OSDflag = chkCameraOSD.Checked;
                Camera.OSDString = txtCameraOSD.Text;
                Camera.OSDx = ushort.Parse(txtCameraOSDX.Text);
                Camera.OSDy = ushort.Parse(txtCameraOSDY.Text);

                FpiMessageBox.ShowInfo("设置水印成功。");

                NETCameraManager.GetInstance().Save();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("设置水印失败。\r\n" + ex.Message);
            }
        }

        #endregion

        #endregion

        #endregion
    }
}