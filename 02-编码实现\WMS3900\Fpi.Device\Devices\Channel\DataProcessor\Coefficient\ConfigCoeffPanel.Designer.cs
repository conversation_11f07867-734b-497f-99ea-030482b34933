﻿namespace Fpi.Devices.Channel
{
    partial class ConfigCoeffPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label5 = new System.Windows.Forms.Label();
            this.txtCoeff = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.SuspendLayout();
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label5.Location = new System.Drawing.Point(48, 7);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 15;
            this.label5.Text = "转换系数";
            // 
            // txtCoeff
            // 
            this.txtCoeff.CanEmpty = true;
            this.txtCoeff.DigitLength = 3;
            this.txtCoeff.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtCoeff.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtCoeff.IsValidCheck = false;
            this.txtCoeff.Label = "转换系数";
            this.txtCoeff.Location = new System.Drawing.Point(107, 3);
            this.txtCoeff.MaxValue = null;
            this.txtCoeff.MinValue = null;
            this.txtCoeff.Name = "txtCoeff";
            this.txtCoeff.Size = new System.Drawing.Size(229, 21);
            this.txtCoeff.TabIndex = 0;
            this.txtCoeff.Text = "1.000";
            // 
            // ConfigCoeffPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.txtCoeff);
            this.Controls.Add(this.label5);
            this.Name = "ConfigCoeffPanel";
            this.Size = new System.Drawing.Size(398, 64);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label5;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtCoeff;
    }
}
