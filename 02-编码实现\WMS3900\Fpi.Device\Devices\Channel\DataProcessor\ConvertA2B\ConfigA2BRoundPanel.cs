﻿using System.Windows.Forms;

namespace Fpi.Devices.Channel
{
    public partial class ConfigA2BRoundPanel : UserControl, IParamConfig
    {
        public ConfigA2BRoundPanel()
        {
            InitializeComponent();
        }

        #region IParamConfig 成员

        public string SaveConfig()
        {

            return nuMinSignal.Value.ToString() + "," +
                    nuMaxSignal.Value.ToString() + "," +
                    nuMinDigital.Value.ToString() + "," +
                    nuMaxDigital.Value.ToString() + "," +
                    this.txtRound.Text;

        }

        public bool CheakParam()
        {
            int coeff;
            return nuMaxSignal.Value > nuMinSignal.Value &&
                nuMaxDigital.Value > nuMinDigital.Value
&& int.TryParse(this.txtRound.Text, out coeff);
        }

        public void ShowParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length == 4)
            {
                this.nuMinSignal.Value = decimal.Parse(paramStrs[0]);
                this.nuMaxSignal.Value = decimal.Parse(paramStrs[1]);

                this.nuMinDigital.Value = decimal.Parse(paramStrs[2]);
                this.nuMaxDigital.Value = decimal.Parse(paramStrs[3]);
            }
            if(paramStrs.Length == 5)
            {
                this.nuMinSignal.Value = decimal.Parse(paramStrs[0]);
                this.nuMaxSignal.Value = decimal.Parse(paramStrs[1]);

                this.nuMinDigital.Value = decimal.Parse(paramStrs[2]);
                this.nuMaxDigital.Value = decimal.Parse(paramStrs[3]);

                this.txtRound.Text = paramStrs[4];
            }
        }

        #endregion
    }
}
