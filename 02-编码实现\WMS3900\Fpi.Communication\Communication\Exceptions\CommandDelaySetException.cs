using System;

namespace Fpi.Communication.Exceptions
{
    public class CommandDelaySetException : ApplicationException
    {
        public CommandDelaySetException()
            : base()
        {
        }

        public CommandDelaySetException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public CommandDelaySetException(string message)
            : base(message)
        {
        }
    }
}