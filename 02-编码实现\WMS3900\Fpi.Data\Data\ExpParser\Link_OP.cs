﻿namespace Fpi.Data.ExpParser
{
    public class Link_OP
    {
        public TOKENLink Head;
        public TOKENLink Tail;

        public void Add(TOKENLink token)
        {
            if(token != null)
            {
                if(this.Tail != null)
                {
                    this.Tail.Next = token;
                    token.Prev = this.Tail;
                    this.Tail = token;
                }
                else
                {
                    this.Head = token;
                    this.Tail = token;
                }
            }
        }
    }
}

