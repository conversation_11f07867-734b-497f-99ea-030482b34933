﻿using Fpi.Communication.Protocols;

namespace Fpi.Devices.DeviceProtocols.ModBusTCP
{
    public class ModbusProtocol : Protocol
    {
        public override string FriendlyName => "ModBus通讯协议";

        protected override Parser ConstructParser()
        {
            return new ModbusParser();
        }

        protected override ProtocolDesc ConstructProtocolDesc()
        {
            return new ModbusProtocolDesc();
        }
    }
}
