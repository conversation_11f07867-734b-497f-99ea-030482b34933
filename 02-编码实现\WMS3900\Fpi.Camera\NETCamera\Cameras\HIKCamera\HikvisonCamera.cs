﻿using System;
using System.Runtime.InteropServices;
using System.Threading;
using Fpi.Camera.HIK.UI;
using Fpi.ExternalSDK.HK;
using Fpi.Util.EnumRelated;

namespace Fpi.Camera.HIK
{
    /// <summary>
    /// 海康威视摄像机
    /// </summary>
    public class HikvisonCamera : BaseNETCamera
    {
        #region 字段属性

        /// <summary>
        /// SDK初始化完成参数标志位
        /// </summary>
        public static bool SdkInitOver { get; set; }

        /// <summary>
        /// 设备登录用户号（默认-1，登录成功后变0）海康用
        /// </summary>
        public int MlUserId { get; set; } = -1;

        /// <summary>
        /// 归位点，也就是预置点255
        /// </summary>
        public uint HomePoint { get; set; } = 255;

        /// <summary>
        /// 实时预览标志
        /// </summary>
        public int MlRealHandle { get; set; } = -1;

        #endregion

        #region 构造

        public HikvisonCamera()
        {
            if(string.IsNullOrEmpty(Description))
            {
                Description = "海康威视摄像机";
            }

            if(id.Contains("DefaultID"))
            {
                id = "HikvisonCamera";
            }

            if(name.Contains("DefaultName"))
            {
                name = "海康威视摄像机";
            }

            ControlTypePath = typeof(FrmHIKCameraControl).FullName;
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(name) || name.Contains("DefaultName") ? "海康威视摄像机" : name;
        }

        #region 初始化

        /// <summary>
        /// 初始化参数
        /// </summary>
        public override void InitSDK()
        {
            // 软件只注册一次即可
            // 避免重复注册
            if(!SdkInitOver)
            {
                //初始化参数
                try
                {
                    SdkInitOver = HKNetSDK.NET_DVR_Init();
                }
                catch(Exception)
                {
                    throw new Exception($"{name}:--初始化SDK失败,error code= {GetLastError()}");
                }

                if(!SdkInitOver)
                {
                    throw new Exception($"{name}:--初始化SDK失败,error code= {GetLastError()}");
                }
            }
        }

        /// <summary>
        /// 释放摄像机SDK
        /// </summary>
        public override void CleanSDK()
        {
            if(SdkInitOver)
            {
                try
                {
                    SdkInitOver = !HKNetSDK.NET_DVR_Cleanup();
                }
                catch(Exception)
                {
                    throw new Exception($"{name}:--释放SDK失败,error code= {GetLastError()}");
                }
            }
        }

        #endregion

        #region 登录注销

        /// <summary>
        /// 登录
        /// </summary>
        public override void Login()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                HKNetSDK.NET_DVR_DEVICEINFO_V30 deviceInfo = new HKNetSDK.NET_DVR_DEVICEINFO_V30();
                //登录设备
                MlUserId = HKNetSDK.NET_DVR_Login_V30(Ip, short.Parse(Port), User, Pwd, ref deviceInfo);

                if(MlUserId < 0)
                {
                    throw new Exception(name + ":--注册失败,error code= " + GetLastError());
                }
                else
                {
                    // 赋值标识登录成功
                    m_LoginID = (IntPtr)1;

                    // 刻录机，计算预览通道号
                    if(IsDS)
                    {
                        base.PrevierChannel = deviceInfo.byStartDChan + DsPort - 1;
                        if(base.PrevierChannel == 0)
                        {
                            base.PrevierChannel = 1;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 注销
        /// </summary>
        public override void Logout()
        {
            //注销登录
            if(IntPtr.Zero != m_LoginID)
            {
                if(!HKNetSDK.NET_DVR_Logout(MlUserId))
                {
                    throw new Exception(name + ":--注销失败,error code= " + GetLastError());
                }
                MlUserId = -1;
                m_LoginID = IntPtr.Zero;
            }
        }

        #endregion

        #region 视频流相关

        /// <summary>
        /// 开始获取视频流
        /// </summary>
        /// <returns></returns>
        public override void StartRealPlay()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                throw new Exception(name + "开始获取视频流失败:设备还没注册。");
            }

            if(m_NoPreviewRealPlayHandle != IntPtr.Zero)
            {
                throw new Exception(name + "开始获取视频流失败:后台正在获取视频流，不可重复获取！");
            }

            if(m_PreviewRealPlayHandle == IntPtr.Zero)
            {
                var lpPreviewInfo = new HKNetSDK.NET_DVR_PREVIEWINFO
                {
                    hPlayWnd = GetPreviewInterface().GetPicHandle(), //预览窗口
                    lChannel = base.PrevierChannel, //预te览的设备通道
                    dwStreamType = 0, //码流类型：0-主码流，1-子码流，2-码流3，3-码流4，以此类推
                    dwLinkMode = 0, //连接方式：0- TCP方式，1- UDP方式，2- 多播方式，3- RTP方式，4-RTP/RTSP，5-RSTP/HTTP 
                    bBlocked = true, //0- 非阻塞取流，1- 阻塞取流
                    dwDisplayBufNum = 15, //播放库播放缓冲区最大缓冲帧数
                    byProtoType = 0,
                    byPreviewMode = 0,
                };

                MlRealHandle = HKNetSDK.NET_DVR_RealPlay_V40(MlUserId, ref lpPreviewInfo, null, new IntPtr());
                //预览失败，输出错误号
                if(MlRealHandle < 0)
                {
                    throw new Exception(name + ":--开始获取视频流失败: error code=" + GetLastError());
                }

                // 赋值标识开始获取视频流成功
                m_PreviewRealPlayHandle = (IntPtr)1;
            }
        }

        /// <summary>
        /// 停止获取视频流
        /// </summary>
        public override void StopRealPlay()
        {
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                if(!HKNetSDK.NET_DVR_StopRealPlay(MlRealHandle))
                {
                    throw new Exception(name + ":--停止获取视频流失败: error code=" + GetLastError());
                }
                MlRealHandle = -1;
                m_PreviewRealPlayHandle = IntPtr.Zero;
            }
        }

        /// <summary>
        /// 开始获取视频流(不绑定预览界面)
        /// </summary>
        /// <returns></returns>
        public override void StartNoPreviewRealPlay()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                throw new Exception(name + "开始获取视频流失败:设备还没注册。");
            }

            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                throw new Exception(name + "开始获取视频流失败:后台正在获取视频流，不可重复获取！");
            }

            if(m_NoPreviewRealPlayHandle == IntPtr.Zero)
            {
                var lpPreviewInfo = new HKNetSDK.NET_DVR_PREVIEWINFO
                {
                    hPlayWnd = IntPtr.Zero, //预览窗口
                    lChannel = base.PrevierChannel, //预te览的设备通道
                    dwStreamType = 0, //码流类型：0-主码流，1-子码流，2-码流3，3-码流4，以此类推
                    dwLinkMode = 0, //连接方式：0- TCP方式，1- UDP方式，2- 多播方式，3- RTP方式，4-RTP/RTSP，5-RSTP/HTTP 
                    bBlocked = true, //0- 非阻塞取流，1- 阻塞取流
                    dwDisplayBufNum = 15, //播放库播放缓冲区最大缓冲帧数
                    byProtoType = 0,
                    byPreviewMode = 0,
                };

                MlRealHandle = HKNetSDK.NET_DVR_RealPlay_V40(MlUserId, ref lpPreviewInfo, null, new IntPtr());
                //预览失败，输出错误号
                if(MlRealHandle < 0)
                {
                    throw new Exception(name + ":--开始获取视频流失败: error code=" + GetLastError());
                }

                // 赋值标识开始获取视频流成功
                m_NoPreviewRealPlayHandle = (IntPtr)1;
            }
        }

        /// <summary>
        /// 停止获取视频流(不绑定预览界面)
        /// </summary>
        public override void StopNoPreviewRealPlay()
        {
            if(m_NoPreviewRealPlayHandle != IntPtr.Zero)
            {
                if(!HKNetSDK.NET_DVR_StopRealPlay(MlRealHandle))
                {
                    throw new Exception(name + ":--停止获取视频流失败: error code=" + GetLastError());
                }
                MlRealHandle = -1;
                m_NoPreviewRealPlayHandle = IntPtr.Zero;
            }
        }

        #endregion

        #region 抓拍

        /// <summary>
        /// 抓拍图像到指定文件
        /// </summary>
        /// <param name="picFileName"></param>
        /// <returns></returns>
        public override bool ScreenShot(string picFileName)
        {
            // 初始化
            InitSDK();

            // 未登录，先登录
            if(IntPtr.Zero == m_LoginID)
            {
                Login();
            }

            bool flag = false;

            // 预览抓图
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                var lpJpegPara = new HKNetSDK.NET_DVR_JPEGPARA
                {
                    //图像质量 
                    wPicQuality = 0,
                    //抓图分辨率 Picture size: 2- 4CIF，0xff- Auto(使用当前码流分辨率)，抓图分辨率需要设备支持，更多取值请参考SDK文档
                    wPicSize = 0xff
                };
                //JPEG抓图
                flag = HKNetSDK.NET_DVR_CaptureJPEGPicture(MlUserId, base.PrevierChannel, ref lpJpegPara, picFileName);
            }
            // 非预览抓图
            else
            {
                Thread.Sleep(100);

                StartNoPreviewRealPlay();

                Thread.Sleep(1000);

                var lpJpegPara = new HKNetSDK.NET_DVR_JPEGPARA
                {
                    //图像质量 
                    wPicQuality = 0,
                    //抓图分辨率 Picture size: 2- 4CIF，0xff- Auto(使用当前码流分辨率)，抓图分辨率需要设备支持，更多取值请参考SDK文档
                    wPicSize = 0xff
                };
                //JPEG抓图
                flag = HKNetSDK.NET_DVR_CaptureJPEGPicture(MlUserId, base.PrevierChannel, ref lpJpegPara, picFileName);

                StopNoPreviewRealPlay();
            }

            string str = flag
                ? name + ":--截图成功，文件：" + picFileName
                : name + "截图失败, error code= " + GetLastError();
            //CameraLogHelper.Info(str);
            return flag;
        }

        #endregion

        #region 录像

        /// <summary>
        /// 开始录像
        /// </summary>
        public override bool StartRecording(string videoFileName)
        {
            // 初始化
            InitSDK();

            // 未登录，先登录
            if(IntPtr.Zero == m_LoginID)
            {
                Login();
            }

            IntPtr handle = m_PreviewRealPlayHandle;

            // 如果当前没开始获取视频流，先开始获取视频流(不绑定预览界面)
            if(m_PreviewRealPlayHandle == IntPtr.Zero && m_NoPreviewRealPlayHandle == IntPtr.Zero)
            {
                StartNoPreviewRealPlay();
                handle = m_NoPreviewRealPlayHandle;
            }

            if(IsRecording)
            {
                return false;
            }

            //录像保存路径和文件名
            SVideoFileName = videoFileName;

            //强制I帧
            HKNetSDK.NET_DVR_MakeKeyFrame(MlUserId, base.PrevierChannel);

            //开始录像
            if(HKNetSDK.NET_DVR_SaveRealData(MlRealHandle, SVideoFileName))
            {
                IsRecording = true;
                string str = name + ":--开始录像成功!";
                //CameraLogHelper.Info(str);

                return true;
            }
            else
            {
                string str = $"{name}:--开始录像失败, error code= {GetLastError()}";
                //CameraLogHelper.Info(str);
                throw new Exception(str);
            }
        }

        /// <summary>
        /// 停止录像
        /// </summary>
        public override bool StopRecording()
        {
            if(!IsRecording)
            {
                return false;
            }

            bool ret = false;

            if(IntPtr.Zero != m_NoPreviewRealPlayHandle)
            {
                ret = HKNetSDK.NET_DVR_StopSaveRealData(MlRealHandle);
                StopNoPreviewRealPlay();
            }
            else
            {
                ret = HKNetSDK.NET_DVR_StopSaveRealData(MlRealHandle);
            }

            //停止录像
            if(ret)
            {
                IsRecording = false;
                string str = name + ":--保存录像成功，录像文件名： " + SVideoFileName;
                CameraLogHelper.Info(str);

                return true;
            }
            else
            {
                string str = $"{name}:--停止录像失败, error code= {GetLastError()}";
                //CameraLogHelper.Info(str);
                throw new Exception(str);
            }
        }

        #endregion

        #region 云台控制

        /// <summary>
        /// 云台控制
        /// </summary>
        /// <param name="dwPTZCommand">控制方向类型</param>
        /// <param name="dwStop">是否停止(mouseup 表示停止传入1，down传入0)</param>
        /// <param name="dwSpeed">速度</param>
        public override void PTZControlWithSpeed(ControlType dwPTZCommand, int dwStop, int dwSpeed)
        {
            if(MlRealHandle >= 0)
            {
                switch(dwPTZCommand)
                {
                    case ControlType.RIGHT:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed(MlRealHandle, HKNetSDK.PAN_RIGHT, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.LEFT:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed(MlRealHandle, HKNetSDK.PAN_LEFT, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.UP:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed(MlRealHandle, HKNetSDK.TILT_UP, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.DOWN:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed(MlRealHandle, HKNetSDK.TILT_DOWN, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.WIPER:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed(MlRealHandle, HKNetSDK.WIPER_PWRON, (uint)dwStop, (uint)dwSpeed);
                        break;
                }
            }
            else
            {
                switch(dwPTZCommand)
                {
                    case ControlType.RIGHT:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed_Other(MlUserId, PrevierChannel, HKNetSDK.PAN_RIGHT, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.LEFT:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed_Other(MlUserId, PrevierChannel, HKNetSDK.PAN_LEFT, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.UP:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed_Other(MlUserId, PrevierChannel, HKNetSDK.TILT_UP, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.DOWN:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed_Other(MlUserId, PrevierChannel, HKNetSDK.TILT_DOWN, (uint)dwStop, (uint)dwSpeed);
                        break;
                    case ControlType.WIPER:
                        HKNetSDK.NET_DVR_PTZControlWithSpeed_Other(MlUserId, PrevierChannel, HKNetSDK.WIPER_PWRON, (uint)dwStop, (uint)dwSpeed);
                        break;
                    default:
                        break;
                }
            }
        }

        #endregion

        #region 校准设备时间

        /// <summary>
        /// 校准设备时间
        /// </summary>
        /// <returns></returns>
        public override void SetTime()
        {
            //设备时间结构体
            var dvrTime = new HKNetSDK.NET_DVR_TIME();
            DateTime nowTime = DateTime.Now;
            dvrTime.dwYear = (uint)nowTime.Year;
            dvrTime.dwMonth = (uint)nowTime.Month;
            dvrTime.dwDay = (uint)nowTime.Day;
            dvrTime.dwHour = (uint)nowTime.Hour;
            dvrTime.dwMinute = (uint)nowTime.Minute;
            dvrTime.dwSecond = (uint)nowTime.Second;

            var nSize = Marshal.SizeOf(dvrTime);
            IntPtr ptrTimeCfg = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(dvrTime, ptrTimeCfg, false);
            try
            {
                if(!HKNetSDK.NET_DVR_SetDVRConfig(MlUserId, HKNetSDK.NET_DVR_SET_TIMECFG, -1, ptrTimeCfg,
                    (uint)nSize))
                {
                    throw new Exception($"error code={GetLastError()}");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"error code={GetLastError()}");
            }
            finally
            {
                //释放资源
                Marshal.FreeHGlobal(ptrTimeCfg);
            }
        }

        #endregion

        #region 获取错误日志

        /// <summary>
        /// 获取错误日志
        /// </summary>
        /// <returns></returns>
        public override string GetLastError()
        {
            string reslut = null;
            uint error = HKNetSDK.NET_DVR_GetLastError();
            if(0 != error)
            {
                try
                {
                    reslut = EnumOperate.GetEnumDesc((E_ErrorCode)error);
                }
                catch(Exception)
                {
                    reslut = error.ToString();
                }
            }
            else
            {
                reslut = error.ToString();
            }

            return reslut;
        }

        #endregion

        #endregion

        #region 公共方法

        #region 预置位

        /// <summary>
        /// 预置位操作
        /// </summary>
        /// <param name="presetCmd">操作云台预置点命令</param>
        /// <param name="presetIndex">预置点的序号（从1 开始），最多支持255 个预置点</param>
        /// <returns></returns>
        public void PTZPreset(PTZPresetCmd presetCmd, uint presetIndex)
        {
            if(!HKNetSDK.NET_DVR_PTZPreset(MlRealHandle, (uint)presetCmd, presetIndex))
            {
                throw new Exception($"{name}:--预置位操作失败，命令{presetCmd}，error code={GetLastError()}");
            }
        }

        #endregion

        #region 云台水平旋转角度

        /// <summary>
        /// 获取云台当前水平旋转角度
        /// </summary>
        /// <returns></returns>
        public int GetPTZPOS_P()
        {
            int p = 0;
            var ptzpos = new HKNetSDK.NET_DVR_PTZPOS { wAction = 1 };
            uint dwOutBufferSize = (uint)Marshal.SizeOf(ptzpos);
            IntPtr intPtr = Marshal.AllocHGlobal((int)dwOutBufferSize);
            try
            {
                Marshal.StructureToPtr(ptzpos, intPtr, false);
                uint lpBytesReturned = 0;
                if(!HKNetSDK.NET_DVR_GetDVRConfig(MlRealHandle, HKNetSDK.NET_DVR_GET_PTZPOS, PrevierChannel, intPtr, dwOutBufferSize, ref lpBytesReturned))
                {
                    throw new Exception($"{name}:--获取云台当前水平旋转角度失败,error code={GetLastError()}");
                }
                else
                {
                    ptzpos = (HKNetSDK.NET_DVR_PTZPOS)Marshal.PtrToStructure(intPtr, typeof(HKNetSDK.NET_DVR_PTZPOS));
                    p = (ushort)Convert.ToInt16(ptzpos.wPanPos.ToString("x4"), 10);
                    p /= 10;
                    return p;
                }
            }
            finally
            {
                //释放资源
                Marshal.FreeHGlobal(intPtr);
            }
        }

        /// <summary>
        /// 设置云台当前水平旋转角度
        /// </summary>
        /// <param name="p">水平设置角度</param>
        public void SetPTZPOS_P(ushort p)
        {
            p *= 10;
            p = (ushort)Convert.ToInt16(p.ToString(), 16);

            var ptzpos = new HKNetSDK.NET_DVR_PTZPOS
            {
                wAction = 1,
                wPanPos = p
            };

            uint dwInBufferSize = (uint)Marshal.SizeOf(ptzpos);

            IntPtr intPtr = Marshal.AllocHGlobal((int)dwInBufferSize);
            try
            {
                Marshal.StructureToPtr(ptzpos, intPtr, false);
                if(!HKNetSDK.NET_DVR_SetDVRConfig(MlUserId, HKNetSDK.NET_DVR_SET_PTZPOS, PrevierChannel, intPtr, dwInBufferSize))
                {
                    throw new Exception($"{name}:--设置云台当前水平旋转角度失败,error code={GetLastError()}");
                }
            }
            finally
            {
                //释放资源
                Marshal.FreeHGlobal(intPtr);
            }
        }

        #endregion

        #region 巡航

        /// <summary>
        /// 设置巡航点
        /// </summary>
        /// <param name="byCruisePoint">预置点的序号（从1 开始），最多支持255 个预置点</param>
        /// <param name="speed">巡航速度</param>
        /// <param name="dwell">巡航点停顿时间</param>
        /// <returns></returns>
        public void PTZCruiseAddPreset(ushort byCruisePoint, ushort speed, ushort dwell)
        {
            if(!HKNetSDK.NET_DVR_PTZCruise(MlRealHandle, HKNetSDK.FILL_PRE_SEQ, 0x01, (byte)byCruisePoint, byCruisePoint)
                ||
                !HKNetSDK.NET_DVR_PTZCruise(MlRealHandle, HKNetSDK.SET_SEQ_SPEED, 0x01, (byte)byCruisePoint, speed)
                ||
                !HKNetSDK.NET_DVR_PTZCruise(MlRealHandle, HKNetSDK.SET_SEQ_DWELL, 0x01, (byte)byCruisePoint, dwell))
            {
                throw new Exception($"{name}:--设置巡航点操作失败,error code={GetLastError()}");
            }
        }

        /// <summary>
        /// 将预置点从巡航序列中删除
        /// </summary>
        /// <param name="byCruisePoint">预置点的序号（从1 开始），最多支持255 个预置点</param>
        /// <returns></returns>
        public void PTZCruiseDeletePreset(ushort byCruisePoint)
        {
            if(!HKNetSDK.NET_DVR_PTZCruise(MlRealHandle, HKNetSDK.CLE_PRE_SEQ, 0x01, (byte)byCruisePoint,
                byCruisePoint))
            {
                throw new Exception($"{name}:--将预置点从巡航序列中删除失败,error code={GetLastError()}");
            }
        }

        /// <summary>
        /// 开始巡航
        /// </summary>
        /// <returns></returns>
        public override void PTZCruiseStart()
        {
            if(!HKNetSDK.NET_DVR_PTZCruise(MlRealHandle, HKNetSDK.RUN_SEQ, 0x01, 0x00, 0))
            {
                throw new Exception($"{name}:--开始巡航操作失败,error code={GetLastError()}");
            }
        }

        /// <summary>
        /// 结束巡航
        /// </summary>
        public override void PTZCruiseStop()
        {
            if(!HKNetSDK.NET_DVR_PTZCruise(MlRealHandle, HKNetSDK.STOP_SEQ, 0x01, 0x00, 0))
            {
                throw new Exception($"{name}:--结束巡航操作失败,error code={GetLastError()}");
            }
        }

        #endregion

        #region 水印

        /// <summary>
        /// 设置水印
        /// </summary>
        /// <param name="flag">是否叠加字符</param>
        /// <param name="sString">字符</param>
        /// <param name="len">字符长度</param>
        /// <param name="x">x坐标</param>
        /// <param name="y">y坐标</param>
        /// <returns></returns>
        public void SetOSD(bool flag, string sString, ushort len, ushort x, ushort y)
        {
            //字符叠加结构体
            var showStrCfg = new HKNetSDK.NET_DVR_SHOWSTRING_V30
            {
                struStringInfo = new HKNetSDK.NET_DVR_SHOWSTRINGINFO[8]
            };

            showStrCfg.struStringInfo[0].wShowString = flag ? (ushort)1 : (ushort)0;
            showStrCfg.struStringInfo[0].sString = sString;
            showStrCfg.struStringInfo[0].wStringSize = (ushort)(len * 2);
            showStrCfg.struStringInfo[0].wShowStringTopLeftX = x;
            showStrCfg.struStringInfo[0].wShowStringTopLeftY = y;

            int nSize = Marshal.SizeOf(showStrCfg);
            showStrCfg.dwSize = (uint)nSize;
            IntPtr ptrShowStrCfg = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(showStrCfg, ptrShowStrCfg, false);

            try
            {
                if(!HKNetSDK.NET_DVR_SetDVRConfig(MlUserId, HKNetSDK.NET_DVR_SET_SHOWSTRING_V30, PrevierChannel,
                    ptrShowStrCfg, (uint)nSize))
                {
                    throw new Exception($"error code={GetLastError()}");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"{name}:--设置字符叠加失败:{e.Message}");
            }
            finally
            {
                //释放资源
                Marshal.FreeHGlobal(ptrShowStrCfg);
            }
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// 操作云台预置点命令
    /// </summary>
    public enum PTZPresetCmd
    {
        /// <summary>
        /// 设置预置点
        /// </summary>
        SetPreset = 8,
        /// <summary>
        /// 清除预置点
        /// </summary>
        ClePreset = 9,
        /// <summary>
        /// 转到预置点
        /// </summary>
        GotoPreset = 39
    }
}