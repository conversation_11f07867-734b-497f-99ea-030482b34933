   .winmd.dll.exe    SE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Log\bin\Debug\Fpi.Log.dllUE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Util\bin\Debug\Fpi.Util.dllSE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Xml\bin\Debug\Fpi.Xml.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dllKE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\FpiDLL\MySql.Data.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dllSE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\FpiDLL\System.Data.SQLite.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dllkC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}GE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.DB\bin\Debug\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}pE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.DB\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         