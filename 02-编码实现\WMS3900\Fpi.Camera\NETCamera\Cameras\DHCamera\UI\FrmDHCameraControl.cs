﻿using System;
using System.Windows.Forms;
using Fpi.Camera.UI;
using Fpi.ExternalSDK.DH;
using Fpi.UI.Common.PC;

namespace Fpi.Camera.DH.UI
{
    /// <summary>
    /// 摄像机控制界面
    /// </summary>
    public partial class FrmDHCameraControl : FrmBaseControl
    {
        #region 字段属性

        /// <summary>
        /// 对应摄像机
        /// </summary>
        public DHIPCCamera Camera { get; set; }

        #endregion

        #region 构造
        private const int MaxSpeed = 8;
        private const int MinSpeed = 1;
        public FrmDHCameraControl()
        {
            InitializeComponent();
            step_comboBox.Items.Clear();
            for(int i = MinSpeed; i <= MaxSpeed; i++)
            {
                step_comboBox.Items.Add(i);
            }
            step_comboBox.SelectedIndex = SpeedValue - 1;

            this.comboBox_customalign.Items.Clear();
            this.comboBox_customalign.Items.Add("Invalid(无效的对齐)");
            this.comboBox_customalign.Items.Add("Left(左对齐)");
            this.comboBox_customalign.Items.Add("Xcenter(X坐标中对齐)");
            this.comboBox_customalign.Items.Add("Ycenter(Y坐标中对齐)");
            this.comboBox_customalign.Items.Add("Center(居中)");
            this.comboBox_customalign.Items.Add("Right(右对齐)");
            this.comboBox_customalign.Items.Add("Top(顶部对齐)");
            this.comboBox_customalign.Items.Add("Bottom(底部对齐)");
            this.comboBox_customalign.Items.Add("LeftTop(左上角对齐)");
            this.comboBox_customalign.Items.Add("ChangeLine(换行对齐)");

        }

        public FrmDHCameraControl(DHIPCCamera camera)
            : this()
        {
            this.Camera = camera;
        }

        #endregion

        #region 界面

        /// <summary>
        /// 加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmDHCameraControl_Load(object sender, EventArgs e)
        {
            #region 水印设置

            #endregion

            //巡航路线
            string[] nums = new string[257];
            for(int i = 0; i < nums.Length; i++)
            {
                nums[i] = i.ToString();
            }

            string str = "";
            int index = -1;
            Camera.GetOSD(ref str, ref index);
            this.txtCameraOSD.Text = str;

            this.comboBox_customalign.SelectedIndex = index;
        }

        #endregion

        #region 控制

        #region 零方位角

        /// <summary>
        /// 设置零方位角
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBenchmarking_Click(object sender, EventArgs e)
        {
            try
            {

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 调用零方位角
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCallBenchmarking_Click(object sender, EventArgs e)
        {
            try
            {

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 预置位设置

        /// <summary>
        /// 调用预置位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCallPreset_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.MoveControl(int.Parse(txtPreset.Text));
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 设置预置位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetPreset_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion("确定要设置预置位吗？") == DialogResult.Yes)
                {
                    Camera.SetControl(int.Parse(txtPreset.Text));
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 清除预置位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnClePreset_Click(object sender, EventArgs e)
        {
            try
            {
                if(FpiMessageBox.ShowQuestion("确定要清除预置位吗？") == DialogResult.Yes)
                {
                    Camera.DelControl(int.Parse(txtPreset.Text));
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 时间校准

        /// <summary>
        /// 时间校准
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCalTime_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.SetTime();
                FpiMessageBox.ShowInfo("时间校准成功!");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 水印

        /// <summary>
        /// 设置水印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetOSD_Click(object sender, EventArgs e)
        {
            try
            {
                Camera.SetOSD((EM_TITLE_TEXT_ALIGNTYPE)this.comboBox_customalign.SelectedIndex, this.txtCameraOSD.Text, chkCameraOSD.Checked);
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region PTZ Control 云台控制

        private int SpeedValue = 4;
        private void step_comboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            SpeedValue = step_comboBox.SelectedIndex + 1;
        }

        private void topleft_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.LEFTTOP, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void topleft_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.LEFTTOP, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void top_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.UP, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void top_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.UP, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void topright_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.RIGHTTOP, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void topright_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.RIGHTTOP, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void left_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.LEFT, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void left_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.LEFT, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void right_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.RIGHT, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void right_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.RIGHT, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void bottomleft_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.LEFTDOWN, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void bottomleft_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.LEFTDOWN, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void bottom_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.DOWN, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void bottom_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.DOWN, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void bottomright_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.RIGHTDOWN, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void bottomright_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.PTZControlWithSpeed(ControlType.RIGHTDOWN, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void zoomadd_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.ZOOM_ADD_CONTROL, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void zoomadd_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.ZOOM_ADD_CONTROL, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void zoomdec_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.ZOOM_DEC_CONTROL, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void zoomdec_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.ZOOM_DEC_CONTROL, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void focusadd_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.FOCUS_ADD_CONTROL, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void focusadd_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.FOCUS_ADD_CONTROL, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void focusdec_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.FOCUS_DEC_CONTROL, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void focusdec_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.FOCUS_DEC_CONTROL, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void apertureadd_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.APERTURE_ADD_CONTROL, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void apertureadd_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.APERTURE_ADD_CONTROL, 1, SpeedValue);
            }
            catch
            {
            }
        }

        private void aperturedec_button_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.APERTURE_DEC_CONTROL, 0, SpeedValue);
            }
            catch
            {
            }
        }

        private void aperturedec_button_MouseUp(object sender, MouseEventArgs e)
        {
            try
            {
                Camera.DHPTZControl(DHPTZControlType.APERTURE_DEC_CONTROL, 1, SpeedValue);
            }
            catch
            {
            }
        }

        #endregion

        #endregion
    }
}