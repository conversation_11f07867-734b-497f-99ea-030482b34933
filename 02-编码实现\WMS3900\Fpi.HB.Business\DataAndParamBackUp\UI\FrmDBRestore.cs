﻿using System;
using System.ComponentModel;
using System.Windows.Forms;
using Fpi.DB.Manager;
using Fpi.UI.Common.PC;

namespace Fpi.HB.Business
{
    public partial class FrmDBRestore : Form
    {
        public FrmDBRestore()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FormRestore_Load(object sender, EventArgs e)
        {
            FpiDataBase.GetInstance().OnRestoring += new FpiDataBase.RestoreReportHander(FormRestore_RestoreReportEvent);
            FpiDataBase.GetInstance().OnRestoreCompleted += new FpiDataBase.RestoreCompletedHander(FormRestore_RestoreCompletedEvent);
            FpiDataBase.GetInstance().ErrorEvent += new FpiDataBase.ErrorHander(FormRestore_ErrorEvent);
        }

        private delegate void ErrorHander(Exception ex);
        /// <summary>
        /// 出错
        /// </summary>
        /// <param name="ex"></param>
        private void FormRestore_ErrorEvent(Exception ex)
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new ErrorHander(FormRestore_ErrorEvent), new object[] { ex });
            }
            else
            {
                this.btnRestore.Enabled = true;
                this.pbStep.Value = 0;
                this.lbInfo.Text = "";
                FpiMessageBox.ShowError("恢复出错:" + ex.Message);
            }
        }

        private delegate void RestoreCompleteHander();
        /// <summary>
        /// 恢复完成事件
        /// </summary>
        private void FormRestore_RestoreCompletedEvent()
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new RestoreCompleteHander(FormRestore_RestoreCompletedEvent));
            }
            else
            {
                this.btnRestore.Enabled = true;
                this.pbStep.Value = 0;
                this.lbInfo.Text = "";
                FpiMessageBox.ShowInfo("恢复完成!");
            }
        }

        private delegate void RestoreHander(int index, int count, string info);
        /// <summary>
        /// 恢复进度报告
        /// </summary>
        /// <param name="index"></param>
        /// <param name="count"></param>
        /// <param name="info"></param>
        private void FormRestore_RestoreReportEvent(int index, int count, string info)
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new RestoreHander(FormRestore_RestoreReportEvent), new object[] { index, count, info });
            }
            else
            {
                this.lbInfo.Text = info;
                this.pbStep.Maximum = count;
                this.pbStep.Value = index;
            }
        }

        /// <summary>
        /// 执行恢复
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                txtBackFile.Check();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
                return;
            }
            if(DialogResult.Yes != FpiMessageBox.ShowQuestion("恢复数据将丢失备份之后的数据,确认继续吗？"))
            {
                return;
            }

            this.btnRestore.Enabled = false;
            FpiDataBase.GetInstance().StartRestore(txtBackFile.Text);
        }

        /// <summary>
        /// 选择文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSel_Click(object sender, EventArgs e)
        {
            if(DialogResult.OK == ofDialog.ShowDialog())
            {
                this.txtBackFile.Text = ofDialog.FileName;
            }
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="e"></param>
        protected override void OnClosing(CancelEventArgs e)
        {
            if(!btnRestore.Enabled)
            {
                FpiMessageBox.ShowInfo("正在恢复数据不能关闭窗口!");
                e.Cancel = true;
                return;
            }
            FpiDataBase.GetInstance().OnRestoring -= FormRestore_RestoreReportEvent;
            FpiDataBase.GetInstance().OnRestoreCompleted -= FormRestore_RestoreCompletedEvent;
            FpiDataBase.GetInstance().ErrorEvent -= FormRestore_ErrorEvent;
            base.OnClosing(e);
        }
    }
}