using System;
using System.Windows.Forms;
using Fpi.Instruments.Properties;

namespace Fpi.Instruments.UI.PC
{
    public partial class FormEditType : Form
    {
        public FormEditType()
        {
            InitializeComponent();
        }
        public FormEditType(InstrumentType type)
            : this()
        {
            this.InstrumentType = type;
        }

        public InstrumentType InstrumentType { get; private set; }

        private void FormEditType_Load(object sender, EventArgs e)
        {
            if(InstrumentType == null)
            {
                InstrumentType = new InstrumentType();
            }

            this.txtID.Text = InstrumentType.id;
            this.txtName.Text = InstrumentType.name;
            this.chkDirect.Checked = InstrumentType.directLink;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            }

        }

        private void Save()
        {
            string id = this.txtID.Text;
            string name = this.txtName.Text;
            if(string.IsNullOrEmpty(id) || string.IsNullOrEmpty(name))
            {
                throw new Exception(Resources.InstrumentTypeEmpty);
            }

            InstrumentType.id = id;
            InstrumentType.name = name;
            InstrumentType.directLink = this.chkDirect.Checked;
        }


    }
}