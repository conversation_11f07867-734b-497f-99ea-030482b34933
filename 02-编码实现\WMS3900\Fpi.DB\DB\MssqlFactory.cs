﻿using System;
using System.Data;
using System.Data.SqlClient;
using Fpi.DB.Manager;

namespace Fpi.DB
{
    /// <summary>
    /// Sqlserver专门驱动
    /// </summary>
    public class MssqlFactory : IDbFactory
    {
        public MssqlFactory()
        {
            ConnectString = DataBaseManager.GetInstance().GetDataBaseConStr();
        }

        public MssqlFactory(string constr)
        {
            ConnectString = constr;
        }

        #region IDbFactory 成员

        public string ConnectString { get; }

        public IDbConnection CreateConnection()
        {
            return new SqlConnection(this.ConnectString);
        }


        public IDbDataAdapter CreateDataAdapter()
        {
            return new SqlDataAdapter();
        }

        public IDataParameter CreateDataParameter()
        {
            return new SqlParameter();
        }

        #region 平台以前的方法，为了兼容而保留

        /// <summary>
        /// 创建数据库。
        /// </summary>
        /// <param name="dbName"></param>
        /// <returns></returns>
        public bool CreateDb(string dbName)
        {
            string connectString = this.ConnectString;
            int index = connectString.IndexOf("Initial Catalog", StringComparison.OrdinalIgnoreCase);
            connectString = connectString.Substring(0, index);

            IDbConnection conn = null;
            try
            {
                conn = new SqlConnection(connectString);
                conn.Open();
                string existsql = "select count(1) from sysdatabases where name = '" + dbName + "'";
                IDbCommand command = conn.CreateCommand();
                command.CommandText = existsql;
                object obj = command.ExecuteScalar();
                if((int)obj < 1)
                {
                    string sql = "CREATE DATABASE " + dbName;

                    command = conn.CreateCommand();
                    command.CommandText = sql;

                    command.ExecuteNonQuery();
                }
            }
            finally
            {
                if(null != conn)
                {
                    conn.Close();
                }
            }

            return true;
        }

        #endregion

        #endregion
    }
}
