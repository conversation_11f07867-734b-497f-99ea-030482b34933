﻿using System;
using System.Collections.Generic;
using Fpi.HB.Business.Protocols.Helper;

namespace Fpi.HB.Business.Protocols.Interface
{
    /// <summary>
    /// 数据补传业务实现接口
    /// </summary>
    public interface IDataSupplement
    {
        /// <summary>
        ///  数据补传实现接口
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="type"></param>
        /// <param name="listener"></param>
        /// <returns></returns>
        string Supplement(DateTime startTime, DateTime endTime, eUploadDataType type, IDateSupplementListener listener);
    }

}
