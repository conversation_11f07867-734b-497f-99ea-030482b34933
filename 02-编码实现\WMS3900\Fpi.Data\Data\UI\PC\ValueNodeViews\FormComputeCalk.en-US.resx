﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 9</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnCancel.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Cancel(&amp;C)</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>314, 9</value>
  </data>
  <data name="btnOK.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK(&amp;E)</value>
  </data>
  <data name="pnlFunc.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 270</value>
  </data>
  <data name="pnlFunc.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pnlFunc.Size" type="System.Drawing.Size, System.Drawing">
    <value>510, 46</value>
  </data>
  <data name="groupBox1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>560, 325</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 18</value>
  </data>
  <data name="panel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 303</value>
  </data>
  <data name="btnAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>238, 44</value>
  </data>
  <data name="btnAdd.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 29</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 125</value>
  </data>
  <data name="groupBox3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox3.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>210, 109</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Calibrated coefficient</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 35</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 15</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>k</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 69</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 15</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>b</value>
  </data>
  <data name="txtZ.Location" type="System.Drawing.Point, System.Drawing">
    <value>102, 64</value>
  </data>
  <data name="txtZ.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtZ.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 21</value>
  </data>
  <data name="txtK.Location" type="System.Drawing.Point, System.Drawing">
    <value>102, 30</value>
  </data>
  <data name="txtK.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtK.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 21</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 4</value>
  </data>
  <data name="groupBox4.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox4.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>210, 109</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>Calibrated data</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 34</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 15</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Measure value</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 68</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 15</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Correct value</value>
  </data>
  <data name="txtAdjust.Location" type="System.Drawing.Point, System.Drawing">
    <value>102, 65</value>
  </data>
  <data name="txtAdjust.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtAdjust.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 21</value>
  </data>
  <data name="txtRaw.Location" type="System.Drawing.Point, System.Drawing">
    <value>102, 31</value>
  </data>
  <data name="txtRaw.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtRaw.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 21</value>
  </data>
  <data name="btnCompute.Location" type="System.Drawing.Point, System.Drawing">
    <value>238, 173</value>
  </data>
  <data name="btnCompute.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnCompute.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 29</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 4</value>
  </data>
  <data name="groupBox2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox2.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>196, 238</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Calibrted list</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>Measure value</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>Correct Value</value>
  </data>
  <data name="menuDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 22</value>
  </data>
  <data name="menuDelete.Text" xml:space="preserve">
    <value>Delete(&amp;D)</value>
  </data>
  <data name="ctxMenu.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 26</value>
  </data>
  <data name="lvArray.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 18</value>
  </data>
  <data name="lvArray.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lvArray.Size" type="System.Drawing.Size, System.Drawing">
    <value>190, 216</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 15</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>510, 316</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Calibrated cofficient calculating</value>
  </data>
</root>