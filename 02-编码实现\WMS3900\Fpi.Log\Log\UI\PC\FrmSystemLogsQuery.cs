﻿using System;
using System.IO;
using System.Windows.Forms;
using Fpi.UI.Common.PC;

namespace Fpi.Log.UI.PC
{
    public partial class FrmSystemLogsQuery : Form
    {
        public FrmSystemLogsQuery()
        {
            InitializeComponent();
            this.lblCount.Text = string.Empty;
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            this.lblCount.Text = string.Empty;
            FillListView(GetOpenFilePath());
        }

        private string GetOpenFilePath()
        {
            try
            {
                openFileDialog1.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
                openFileDialog1.InitialDirectory = Application.StartupPath + @"\Log";

                if(openFileDialog1.ShowDialog() == DialogResult.OK)
                {
                    string fileName = openFileDialog1.FileName;
                    if(File.Exists(fileName))
                    {
                        return fileName;
                    }
                }
            }
            catch(Exception)
            {
            }
            return null;
        }

        public void FillListView(string fileName)
        {
            try
            {
                if(null == fileName)
                {
                    return;
                }

                using(StreamReader sr = new StreamReader(fileName))
                {
                    this.lvContent.Items.Clear();

                    string str;
                    while(null != (str = sr.ReadLine()))
                    {
                        int index = str.IndexOf(']');
                        string time;
                        string content;
                        SingleLog singleLog = new SingleLog();
                        if(index > 0)
                        {
                            time = str.Substring(1, index - 1);
                            content = str.Substring(index + 2);

                            singleLog.LogTime = time;
                            singleLog.LogInfo = content;
                        }
                        else
                        {
                            time = "";
                            content = str;
                        }

                        ListViewItem item = this.lvContent.Items.Add(time);
                        item.SubItems.Add(content);
                        item.Tag = singleLog;
                    }
                    this.lblCount.Text = "共计日志条目数: " + this.lvContent.Items.Count;
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.StackTrace, "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Stop);
            }
        }

        private void lvContent_DoubleClick(object sender, EventArgs e)
        {
            if((lvContent.SelectedItems.Count > 0) && (lvContent.SelectedItems[0].Tag is SingleLog))
            {
                SingleLog singleLog = lvContent.SelectedItems[0].Tag as SingleLog;
                string time = singleLog.LogTime;
                string info = singleLog.LogInfo;
                FormInfoBarDetail form = new FormInfoBarDetail(time, info);
                form.ShowDialog();
            }
        }
    }

    internal class SingleLog
    {
        public string LogTime
        {
            set;
            get;
        }

        public string LogInfo
        {
            set;
            get;
        }
    }
}
