using Fpi.Xml;

namespace Fpi.Data.Config
{
    public class Unit : IdNameNode
    {
        private const string SELF = "self";

        public string Trans = SELF;
        public string ReverseTrans;

        public Unit() : base()
        {
        }

        public Unit(string id) : base(id)
        {
        }

        public Unit(string id, string name) : base(id, name)
        {
        }

        public bool IsCurrentUnit()
        {
            return (this.Trans == null) || (this.Trans == string.Empty) || this.Trans.Equals(SELF);
        }
    }
}