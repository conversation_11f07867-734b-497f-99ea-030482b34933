﻿using System;
using System.IO;
using System.Threading;
using Fpi.Util.Security;
using Fpi.Xml;

namespace Fpi.DB.Manager
{
    /// <summary>
    /// 数据库字段类
    /// </summary>
    public class DataBaseManager : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 数据库类型
        /// </summary>
        public string DataBaseType;

        /// <summary>
        /// 数据库名称
        /// </summary>
        public string DataBaseName;

        /// <summary>
        /// 数据库IP
        /// </summary>
        public string DataBaseIP;

        /// <summary>
        /// 数据库端口
        /// </summary>
        public string DataBasePort;

        /// <summary>
        /// 数据库用户名
        /// </summary>
        public string DataBaseUser;

        /// <summary>
        /// 数据库密码
        /// </summary>
        public string DataBasePassword;

        /// <summary>
        /// 是否成功创建了数据库
        /// </summary>
        public bool HasCreateDB { get; set; }

        #endregion

        #region 单例

        private static readonly object syncObj = new object();
        private static DataBaseManager _instance = null;

        public static DataBaseManager GetInstance()
        {
            lock(syncObj)
            {
                if(_instance == null)
                {
                    _instance = new DataBaseManager();
                }
            }

            return _instance;
        }

        /// <summary>
        /// 构造
        /// </summary>
        public DataBaseManager()
        {
            loadXml();
        }

        #endregion

        #region 事件

        public delegate void DbChangeHandler();

        public event DbChangeHandler DbChangeEvent;

        public void OnDbChangeEvent()
        {
            if(DbChangeEvent != null)
            {
                DbChangeEvent();
            }

            InitDataBase();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        public string GetDataBaseConStr()
        {
            //根据数据库类型组装连接字符串
            string psd = RsaCryptHelper.GetInstance().Decrypt(DataBasePassword);
            DBType dbType = (DBType)Enum.Parse(typeof(DBType), GetInstance().DataBaseType, true);

            return GetDataBaseConStr(dbType, DataBaseName, DataBaseIP, DataBasePort, DataBaseUser, psd);
        }

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        public string GetDataBaseConStr(DBType dbType, string dataBaseName, string dataBaseIp,
            string dataBasePort, string dataBaseUser, string dataBasePassWord)
        {
            string dataBaseConStr = string.Empty;
            switch(dbType)
            {
                case DBType.mysql:
                    //连接串样例: "Server=localhost;Port=3306;Uid=root;Pwd=***;Database=CEMS2000;"
                    dataBaseConStr = $"Server={dataBaseIp};Port={dataBasePort};Uid={dataBaseUser};Pwd={dataBasePassWord};Database={dataBaseName};charset='utf8';";
                    break;
                case DBType.mssql:
                    //连接串样例: "Server=localhost,5600;Uid=root;Password=***;Database=CEMS2000;MultipleActiveResultSets=True"
                    dataBaseConStr = $"Server={dataBaseIp},{dataBasePort};Uid={dataBaseUser};Password={dataBasePassWord};Database={dataBaseName};MultipleActiveResultSets=True;";
                    break;
                case DBType.odbc:
                    //连接串样例: "Driver={MySQL ODBC 3.51 Driver};server=localhost;port=3308;user=root;database=aqms_plus"
                    dataBaseConStr = $@"DRIVER={{MySQL ODBC 8.0 Unicode Driver}};SERVER={dataBaseIp};PORT={dataBasePort};OPTION=3;UID={dataBaseUser};PWD={dataBasePassWord};DATABASE={dataBaseName};CHARSET=utf8mb4;";
                    //dataBaseConStr = $@"DRIVER={{MySQL ODBC 5.1 Driver}};SERVER={dataBaseIp};PORT={dataBasePort};OPTION=3;UID={dataBaseUser};PWD={dataBasePassWord};DATABASE={dataBaseName};CHARSET='utf8';";
                    break;
                case DBType.sqlite:
                    //连接串样例: "Data Source=DB\smartcems.db;Version=3;Password=***;"
                    if(!dataBaseName.EndsWith(".db"))
                    {
                        dataBaseName += ".db";
                    }
                    // 使用密码
                    //dataBaseConStr = @"Data Source=" + SQLiteFactory.SQLiteFileDir + @"\" + dataBaseName + ";Password=" + dataBasePassWord + ";";
                    // 不使用密码
                    dataBaseConStr = $"Data Source={Path.Combine(SQLiteFactory.SQLiteFileDir, dataBaseName)};";
                    break;
            }

            return dataBaseConStr;
        }

        #endregion

        #region IInitialization 成员

        /// <summary>
        /// 初始化创建数据库
        /// </summary>
        public bool InitDataBase()
        {
            string DB_NAME = GetInstance().DataBaseName;
            // 未配置数据库名称
            if(string.IsNullOrEmpty(GetInstance().DataBaseName))
            {
                Log.LogUtil.Error("数据库初始化创建失败：未配置数据库名称！");
                return false;
            }

            int count = 0;
            while(true)
            {
                try
                {
                    DbFactory.GetInstance().CreateDb(DB_NAME);
                    return true;
                }
                catch(Exception ex)
                {
                    Thread.Sleep(2000);
                    count++;
                    if(count > 10)
                    {
                        Log.LogUtil.Error(ex.Message);
                        break;
                    }
                }
            }

            return false;
        }

        #endregion
    }
}