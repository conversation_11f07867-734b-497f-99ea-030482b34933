﻿using System;
using System.Threading;
using Fpi.Communication;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Ports;
using Fpi.Util.WinApiUtil;


namespace Fpi.Devices.DeviceProtocols.ModBusTCP
{
    public class ModbusTCPPYPort : BasePort
    {
        private byte[] readBuffer;
        //数据发送线程
        private Thread dataReceiveThread = null;
        //数据接收触发事件
        private IntPtr receiveEvent = WinApiWrapper.CreateEvent(false, false, "dcon_receive_event");

        public ModbusTCPPYPort()
            : base()
        {
            readBuffer = new byte[MAX_FRAME_SIZE * 2];
        }

        override public object Send(object dest, IByteStream data)
        {
            IPort port = LowerPort;
            IByteStream newData = PackData(dest, data);

            IByteStream bs = new ByteArrayWrap(data.GetBytes());
            return port.Send(dest, bs);
        }

        public override void Receive(object source, IByteStream data)
        {
            byte[] receiveData = data.GetBytes();

            readedBytes = receiveData.Length;

            if(recevicedDataSize + receiveData.Length > readBuffer.Length)
            {
                recevicedDataSize = 0;
                headIndex = -1;
            }
            GetDataReceiveThread();
            Buffer.BlockCopy(receiveData, 0, readBuffer, recevicedDataSize, receiveData.Length);
            recevicedDataSize += readedBytes;
            WinApiWrapper.SetEvent(receiveEvent);
        }



        private void DataReceiveThreadFunc()
        {
            //modbus 帧间隔 400ms，由于该协议无明显帧头帧尾
            uint waitTime = 400;
            while(this.connected)
            {
                uint waitValue = (uint)WinApiWrapper.WaitForSingleObject(receiveEvent, waitTime);

                //Event触发(有新数据)
                if(waitValue == (uint)APIConstants.WAIT_OBJECT_0)
                {
                    continue;
                }

                //timeout发生
                else if(waitValue == (uint)APIConstants.WAIT_TIMEOUT)
                {
                    IPortOwner portOwner = PortOwner;

                    if((portOwner != null) && (recevicedDataSize > 0))
                    {
                        byte[] data;
                        lock(this)
                        {
                            try
                            {
                                //data = new byte[recevicedDataSize];
                                //Buffer.BlockCopy(readBuffer, 0, data, 0, recevicedDataSize);

                                data = new byte[recevicedDataSize];
                                Buffer.BlockCopy(readBuffer, 0, data, 0, recevicedDataSize);
                                recevicedDataSize = 0;

                            }
                            catch(Exception ex)
                            {
                                recevicedDataSize = 0;
                                continue;
                            }
                            finally
                            {
                                recevicedDataSize = 0;
                            }
                        }
                        if(data.Length < 2)
                        {
                            continue;
                        }

                        portOwner.Receive(this, new ByteArrayWrap(data));
                    }
                }
                else
                {
                    //其它错误
                    continue;
                }
            }
            dataReceiveThread = null;
        }

        private void GetDataReceiveThread()
        {
            if(dataReceiveThread == null)
            {
                dataReceiveThread = new Thread(new ThreadStart(DataReceiveThreadFunc));
                dataReceiveThread.Name = "Modbus 协议解析线程";
                dataReceiveThread.Start();
            }
        }
    }
}
