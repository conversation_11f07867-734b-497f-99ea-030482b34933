<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pageBase.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pageBase.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="pageBase.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pageBase.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="pageBase.Text" xml:space="preserve">
    <value>基本项</value>
  </data>
  <data name="&gt;&gt;pageBase.Name" xml:space="preserve">
    <value>pageBase</value>
  </data>
  <data name="&gt;&gt;pageBase.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageBase.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageBase.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtDetectionLimit.Label" xml:space="preserve">
    <value />
  </data>
  <data name="txtDetectionLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 185</value>
  </data>
  <data name="txtDetectionLimit.MaxLength" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="txtDetectionLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="txtDetectionLimit.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;txtDetectionLimit.Name" xml:space="preserve">
    <value>txtDetectionLimit</value>
  </data>
  <data name="&gt;&gt;txtDetectionLimit.Type" xml:space="preserve">
    <value>Fpi.UI.Common.PC.Controls.FpiTextBox, Fpi.UI.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;txtDetectionLimit.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;txtDetectionLimit.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 189</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 12</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>检出限</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtQualiyClass.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 240</value>
  </data>
  <data name="txtQualiyClass.MaxLength" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="txtQualiyClass.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="txtQualiyClass.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;txtQualiyClass.Name" xml:space="preserve">
    <value>txtQualiyClass</value>
  </data>
  <data name="&gt;&gt;txtQualiyClass.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtQualiyClass.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;txtQualiyClass.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 244</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>测试标准</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtGBCode.Label" xml:space="preserve">
    <value />
  </data>
  <data name="txtGBCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 213</value>
  </data>
  <data name="txtGBCode.MaxLength" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="txtGBCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="txtGBCode.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txtGBCode.Name" xml:space="preserve">
    <value>txtGBCode</value>
  </data>
  <data name="&gt;&gt;txtGBCode.Type" xml:space="preserve">
    <value>Fpi.UI.Common.PC.Controls.FpiTextBox, Fpi.UI.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;txtGBCode.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;txtGBCode.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtMolwt.Label" xml:space="preserve">
    <value />
  </data>
  <data name="txtMolwt.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 157</value>
  </data>
  <data name="txtMolwt.MaxLength" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="txtMolwt.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="txtMolwt.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txtMolwt.Name" xml:space="preserve">
    <value>txtMolwt</value>
  </data>
  <data name="&gt;&gt;txtMolwt.Type" xml:space="preserve">
    <value>Fpi.UI.Common.PC.Controls.FpiTextBox, Fpi.UI.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;txtMolwt.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;txtMolwt.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 217</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>国标编码</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>97, 161</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 12</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>分子量</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnFormula.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnFormula.Location" type="System.Drawing.Point, System.Drawing">
    <value>355, 128</value>
  </data>
  <data name="btnFormula.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 23</value>
  </data>
  <data name="btnFormula.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="btnFormula.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;btnFormula.Name" xml:space="preserve">
    <value>btnFormula</value>
  </data>
  <data name="&gt;&gt;btnFormula.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnFormula.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;btnFormula.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cmbLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 85</value>
  </data>
  <data name="cmbLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 20</value>
  </data>
  <data name="cmbLimit.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;cmbLimit.Name" xml:space="preserve">
    <value>cmbLimit</value>
  </data>
  <data name="&gt;&gt;cmbLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cmbLimit.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;cmbLimit.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="cmbScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 57</value>
  </data>
  <data name="cmbScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 20</value>
  </data>
  <data name="cmbScope.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;cmbScope.Name" xml:space="preserve">
    <value>cmbScope</value>
  </data>
  <data name="&gt;&gt;cmbScope.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cmbScope.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;cmbScope.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="nuSmooth.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 30</value>
  </data>
  <data name="nuSmooth.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="nuSmooth.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="nuSmooth.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;nuSmooth.Name" xml:space="preserve">
    <value>nuSmooth</value>
  </data>
  <data name="&gt;&gt;nuSmooth.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nuSmooth.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;nuSmooth.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label9.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label9.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>73, 91</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 12</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>默认报警限</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label8.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 62</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>默认量程</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txtFormula.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 111</value>
  </data>
  <data name="txtFormula.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtFormula.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 40</value>
  </data>
  <data name="txtFormula.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txtFormula.Name" xml:space="preserve">
    <value>txtFormula</value>
  </data>
  <data name="&gt;&gt;txtFormula.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtFormula.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;txtFormula.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 34</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>平滑次数</value>
  </data>
  <data name="label5.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 125</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>计算公式</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="pageLocal.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="pageLocal.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="pageLocal.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <data name="pageLocal.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="pageLocal.Text" xml:space="preserve">
    <value>扩展项</value>
  </data>
  <data name="&gt;&gt;pageLocal.Name" xml:space="preserve">
    <value>pageLocal</value>
  </data>
  <data name="&gt;&gt;pageLocal.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageLocal.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageLocal.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="columnHeader6.Text" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="columnHeader6.Width" type="System.Int32, mscorlib">
    <value>400</value>
  </data>
  <data name="lvUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="lvUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 281</value>
  </data>
  <data name="lvUnit.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lvUnit.Name" xml:space="preserve">
    <value>lvUnit</value>
  </data>
  <data name="&gt;&gt;lvUnit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvUnit.Parent" xml:space="preserve">
    <value>pageUnit</value>
  </data>
  <data name="&gt;&gt;lvUnit.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cmbSelfUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>70, 5</value>
  </data>
  <data name="cmbSelfUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="cmbSelfUnit.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;cmbSelfUnit.Name" xml:space="preserve">
    <value>cmbSelfUnit</value>
  </data>
  <data name="&gt;&gt;cmbSelfUnit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cmbSelfUnit.Parent" xml:space="preserve">
    <value>panel8</value>
  </data>
  <data name="&gt;&gt;cmbSelfUnit.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 9</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>默认单位</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>panel8</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnAddUnit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddUnit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddUnit.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAddUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>337, 5</value>
  </data>
  <data name="btnAddUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddUnit.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddUnit.Text" xml:space="preserve">
    <value>选择</value>
  </data>
  <data name="&gt;&gt;btnAddUnit.Name" xml:space="preserve">
    <value>btnAddUnit</value>
  </data>
  <data name="&gt;&gt;btnAddUnit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddUnit.Parent" xml:space="preserve">
    <value>panel8</value>
  </data>
  <data name="&gt;&gt;btnAddUnit.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel8.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="panel8.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 284</value>
  </data>
  <data name="panel8.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 37</value>
  </data>
  <data name="panel8.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;panel8.Name" xml:space="preserve">
    <value>panel8</value>
  </data>
  <data name="&gt;&gt;panel8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel8.Parent" xml:space="preserve">
    <value>pageUnit</value>
  </data>
  <data name="&gt;&gt;panel8.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pageUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="pageUnit.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="pageUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <data name="pageUnit.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="pageUnit.Text" xml:space="preserve">
    <value>单位集</value>
  </data>
  <data name="&gt;&gt;pageUnit.Name" xml:space="preserve">
    <value>pageUnit</value>
  </data>
  <data name="&gt;&gt;pageUnit.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageUnit.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageUnit.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lvScope.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="columnHeader1.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="columnHeader2.Width" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="columnHeader3.Text" xml:space="preserve">
    <value>最小值</value>
  </data>
  <data name="columnHeader3.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="columnHeader4.Text" xml:space="preserve">
    <value>最大值</value>
  </data>
  <data name="columnHeader4.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="lvScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="lvScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 283</value>
  </data>
  <data name="lvScope.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lvScope.Name" xml:space="preserve">
    <value>lvScope</value>
  </data>
  <data name="&gt;&gt;lvScope.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvScope.Parent" xml:space="preserve">
    <value>pageScope</value>
  </data>
  <data name="&gt;&gt;lvScope.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnDelScope.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelScope.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelScope.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnDelScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>337, 7</value>
  </data>
  <data name="btnDelScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelScope.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelScope.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelScope.Name" xml:space="preserve">
    <value>btnDelScope</value>
  </data>
  <data name="&gt;&gt;btnDelScope.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelScope.Parent" xml:space="preserve">
    <value>panel9</value>
  </data>
  <data name="&gt;&gt;btnDelScope.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddScope.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddScope.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddScope.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAddScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 7</value>
  </data>
  <data name="btnAddScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddScope.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddScope.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddScope.Name" xml:space="preserve">
    <value>btnAddScope</value>
  </data>
  <data name="&gt;&gt;btnAddScope.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddScope.Parent" xml:space="preserve">
    <value>panel9</value>
  </data>
  <data name="&gt;&gt;btnAddScope.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnUpdateScope.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnUpdateScope.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnUpdateScope.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnUpdateScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>254, 7</value>
  </data>
  <data name="btnUpdateScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnUpdateScope.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnUpdateScope.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnUpdateScope.Name" xml:space="preserve">
    <value>btnUpdateScope</value>
  </data>
  <data name="&gt;&gt;btnUpdateScope.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUpdateScope.Parent" xml:space="preserve">
    <value>panel9</value>
  </data>
  <data name="&gt;&gt;btnUpdateScope.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel9.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="panel9.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 284</value>
  </data>
  <data name="panel9.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 37</value>
  </data>
  <data name="panel9.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;panel9.Name" xml:space="preserve">
    <value>panel9</value>
  </data>
  <data name="&gt;&gt;panel9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel9.Parent" xml:space="preserve">
    <value>pageScope</value>
  </data>
  <data name="&gt;&gt;panel9.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pageScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="pageScope.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="pageScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <data name="pageScope.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="pageScope.Text" xml:space="preserve">
    <value>量程集</value>
  </data>
  <data name="&gt;&gt;pageScope.Name" xml:space="preserve">
    <value>pageScope</value>
  </data>
  <data name="&gt;&gt;pageScope.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageScope.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageScope.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="columnHeader9.Text" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="columnHeader9.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="columnHeader10.Text" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="columnHeader10.Width" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="columnHeader11.Text" xml:space="preserve">
    <value>报警下限值</value>
  </data>
  <data name="columnHeader11.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="columnHeader12.Text" xml:space="preserve">
    <value>报警上限值</value>
  </data>
  <data name="columnHeader12.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="lvLimit.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="lvLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 281</value>
  </data>
  <data name="lvLimit.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;lvLimit.Name" xml:space="preserve">
    <value>lvLimit</value>
  </data>
  <data name="&gt;&gt;lvLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvLimit.Parent" xml:space="preserve">
    <value>pageLimit</value>
  </data>
  <data name="&gt;&gt;lvLimit.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnDelLimit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelLimit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelLimit.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnDelLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>337, 7</value>
  </data>
  <data name="btnDelLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelLimit.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelLimit.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelLimit.Name" xml:space="preserve">
    <value>btnDelLimit</value>
  </data>
  <data name="&gt;&gt;btnDelLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelLimit.Parent" xml:space="preserve">
    <value>panel10</value>
  </data>
  <data name="&gt;&gt;btnDelLimit.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddLimit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddLimit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddLimit.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAddLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 7</value>
  </data>
  <data name="btnAddLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddLimit.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddLimit.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddLimit.Name" xml:space="preserve">
    <value>btnAddLimit</value>
  </data>
  <data name="&gt;&gt;btnAddLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddLimit.Parent" xml:space="preserve">
    <value>panel10</value>
  </data>
  <data name="&gt;&gt;btnAddLimit.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnUpdateLimit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnUpdateLimit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnUpdateLimit.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnUpdateLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>254, 7</value>
  </data>
  <data name="btnUpdateLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnUpdateLimit.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnUpdateLimit.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnUpdateLimit.Name" xml:space="preserve">
    <value>btnUpdateLimit</value>
  </data>
  <data name="&gt;&gt;btnUpdateLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUpdateLimit.Parent" xml:space="preserve">
    <value>panel10</value>
  </data>
  <data name="&gt;&gt;btnUpdateLimit.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel10.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="panel10.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 284</value>
  </data>
  <data name="panel10.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 37</value>
  </data>
  <data name="panel10.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;panel10.Name" xml:space="preserve">
    <value>panel10</value>
  </data>
  <data name="&gt;&gt;panel10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel10.Parent" xml:space="preserve">
    <value>pageLimit</value>
  </data>
  <data name="&gt;&gt;panel10.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pageLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="pageLimit.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="pageLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <data name="pageLimit.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="pageLimit.Text" xml:space="preserve">
    <value>报警限集</value>
  </data>
  <data name="&gt;&gt;pageLimit.Name" xml:space="preserve">
    <value>pageLimit</value>
  </data>
  <data name="&gt;&gt;pageLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageLimit.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageLimit.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label12.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label12.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>140, 36</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>245, 12</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>当该变量配备了计算公式时，不可启用固定值</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>pageFixed</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="nuFixedValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>110, 29</value>
  </data>
  <data name="nuFixedValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="nuFixedValue.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;nuFixedValue.Name" xml:space="preserve">
    <value>nuFixedValue</value>
  </data>
  <data name="&gt;&gt;nuFixedValue.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nuFixedValue.Parent" xml:space="preserve">
    <value>gbFixed</value>
  </data>
  <data name="&gt;&gt;nuFixedValue.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="nuFixedRange.Location" type="System.Drawing.Point, System.Drawing">
    <value>110, 59</value>
  </data>
  <data name="nuFixedRange.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="nuFixedRange.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;nuFixedRange.Name" xml:space="preserve">
    <value>nuFixedRange</value>
  </data>
  <data name="&gt;&gt;nuFixedRange.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nuFixedRange.Parent" xml:space="preserve">
    <value>gbFixed</value>
  </data>
  <data name="&gt;&gt;nuFixedRange.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label11.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 63</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>波动范围</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>gbFixed</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label10.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 33</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 12</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>固定中心值</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>gbFixed</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="gbFixed.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gbFixed.Location" type="System.Drawing.Point, System.Drawing">
    <value>39, 66</value>
  </data>
  <data name="gbFixed.Size" type="System.Drawing.Size, System.Drawing">
    <value>346, 109</value>
  </data>
  <data name="gbFixed.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="gbFixed.Text" xml:space="preserve">
    <value>固定值参数</value>
  </data>
  <data name="&gt;&gt;gbFixed.Name" xml:space="preserve">
    <value>gbFixed</value>
  </data>
  <data name="&gt;&gt;gbFixed.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gbFixed.Parent" xml:space="preserve">
    <value>pageFixed</value>
  </data>
  <data name="&gt;&gt;gbFixed.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="chkFixed.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chkFixed.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt, style=Bold</value>
  </data>
  <data name="chkFixed.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="chkFixed.Location" type="System.Drawing.Point, System.Drawing">
    <value>39, 34</value>
  </data>
  <data name="chkFixed.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 16</value>
  </data>
  <data name="chkFixed.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chkFixed.Text" xml:space="preserve">
    <value>启用固定值</value>
  </data>
  <data name="&gt;&gt;chkFixed.Name" xml:space="preserve">
    <value>chkFixed</value>
  </data>
  <data name="&gt;&gt;chkFixed.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkFixed.Parent" xml:space="preserve">
    <value>pageFixed</value>
  </data>
  <data name="&gt;&gt;chkFixed.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pageFixed.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="pageFixed.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="pageFixed.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="pageFixed.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <data name="pageFixed.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="pageFixed.Text" xml:space="preserve">
    <value>固定值</value>
  </data>
  <data name="&gt;&gt;pageFixed.Name" xml:space="preserve">
    <value>pageFixed</value>
  </data>
  <data name="&gt;&gt;pageFixed.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageFixed.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageFixed.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="columnHeader5.Text" xml:space="preserve">
    <value>编号</value>
  </data>
  <data name="columnHeader5.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="columnHeader7.Text" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="columnHeader7.Width" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="columnHeader8.Text" xml:space="preserve">
    <value>最小值</value>
  </data>
  <data name="columnHeader8.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="columnHeader13.Text" xml:space="preserve">
    <value>最大值</value>
  </data>
  <data name="columnHeader13.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="lvIndice.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvIndice.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lvIndice.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 287</value>
  </data>
  <data name="lvIndice.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;lvIndice.Name" xml:space="preserve">
    <value>lvIndice</value>
  </data>
  <data name="&gt;&gt;lvIndice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvIndice.Parent" xml:space="preserve">
    <value>pageIndice</value>
  </data>
  <data name="&gt;&gt;lvIndice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnDelIndice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelIndice.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelIndice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnDelIndice.Location" type="System.Drawing.Point, System.Drawing">
    <value>343, 7</value>
  </data>
  <data name="btnDelIndice.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelIndice.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelIndice.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelIndice.Name" xml:space="preserve">
    <value>btnDelIndice</value>
  </data>
  <data name="&gt;&gt;btnDelIndice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelIndice.Parent" xml:space="preserve">
    <value>pnlIndice</value>
  </data>
  <data name="&gt;&gt;btnDelIndice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddIndice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddIndice.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddIndice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAddIndice.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 7</value>
  </data>
  <data name="btnAddIndice.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddIndice.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddIndice.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddIndice.Name" xml:space="preserve">
    <value>btnAddIndice</value>
  </data>
  <data name="&gt;&gt;btnAddIndice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddIndice.Parent" xml:space="preserve">
    <value>pnlIndice</value>
  </data>
  <data name="&gt;&gt;btnAddIndice.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnEditIndice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnEditIndice.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnEditIndice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEditIndice.Location" type="System.Drawing.Point, System.Drawing">
    <value>260, 7</value>
  </data>
  <data name="btnEditIndice.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnEditIndice.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnEditIndice.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnEditIndice.Name" xml:space="preserve">
    <value>btnEditIndice</value>
  </data>
  <data name="&gt;&gt;btnEditIndice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEditIndice.Parent" xml:space="preserve">
    <value>pnlIndice</value>
  </data>
  <data name="&gt;&gt;btnEditIndice.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pnlIndice.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="pnlIndice.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 287</value>
  </data>
  <data name="pnlIndice.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 37</value>
  </data>
  <data name="pnlIndice.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;pnlIndice.Name" xml:space="preserve">
    <value>pnlIndice</value>
  </data>
  <data name="&gt;&gt;pnlIndice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlIndice.Parent" xml:space="preserve">
    <value>pageIndice</value>
  </data>
  <data name="&gt;&gt;pnlIndice.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pageIndice.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="pageIndice.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 324</value>
  </data>
  <data name="pageIndice.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="pageIndice.Text" xml:space="preserve">
    <value>标准集</value>
  </data>
  <data name="&gt;&gt;pageIndice.Name" xml:space="preserve">
    <value>pageIndice</value>
  </data>
  <data name="&gt;&gt;pageIndice.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pageIndice.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;pageIndice.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="tabControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="tabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>432, 350</value>
  </data>
  <data name="tabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Name" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>432, 350</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Name" xml:space="preserve">
    <value>columnHeader6</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Name" xml:space="preserve">
    <value>columnHeader1</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Name" xml:space="preserve">
    <value>columnHeader2</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Name" xml:space="preserve">
    <value>columnHeader3</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Name" xml:space="preserve">
    <value>columnHeader4</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader9.Name" xml:space="preserve">
    <value>columnHeader9</value>
  </data>
  <data name="&gt;&gt;columnHeader9.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader10.Name" xml:space="preserve">
    <value>columnHeader10</value>
  </data>
  <data name="&gt;&gt;columnHeader10.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader11.Name" xml:space="preserve">
    <value>columnHeader11</value>
  </data>
  <data name="&gt;&gt;columnHeader11.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader12.Name" xml:space="preserve">
    <value>columnHeader12</value>
  </data>
  <data name="&gt;&gt;columnHeader12.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Name" xml:space="preserve">
    <value>columnHeader5</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader7.Name" xml:space="preserve">
    <value>columnHeader7</value>
  </data>
  <data name="&gt;&gt;columnHeader7.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader8.Name" xml:space="preserve">
    <value>columnHeader8</value>
  </data>
  <data name="&gt;&gt;columnHeader8.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader13.Name" xml:space="preserve">
    <value>columnHeader13</value>
  </data>
  <data name="&gt;&gt;columnHeader13.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>UC_ValueNodeView</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>