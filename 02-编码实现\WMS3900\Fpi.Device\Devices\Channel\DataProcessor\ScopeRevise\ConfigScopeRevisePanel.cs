﻿using System;
using System.Text;
using System.Windows.Forms;

namespace Fpi.Devices.Channel
{
    public partial class ConfigScopeRevisePanel : UserControl, IParamConfig
    {
        public ConfigScopeRevisePanel()
        {
            InitializeComponent();
        }

        #region IParamConfig 成员

        public string SaveConfig()
        {
            var sb = new StringBuilder();
            if(radLowModel.Checked)
            {
                sb.Append("0").Append(",");
                sb.Append(txtLowModelValue.Text).Append(",");
            }
            if(radHighModel.Checked)
            {
                sb.Append("1,");
                sb.Append(txtHighModelValue.Text).Append(",");
            }
            if(radBetweenModel.Checked)
            {
                sb.Append("2,");
                sb.Append(txtBetweenModelLowValue.Text).Append(",");
                sb.Append(txtBetweenModelHighValue.Text).Append(",");
            }
            if(radRatioRevise.Checked)
            {
                sb.Append("0,");
                sb.Append(txtRatio.Text).Append(",");
            }
            if(radRandomRevise.Checked)
            {
                sb.Append("1,");
                sb.Append(txtRandomLowValue.Text).Append(",");
                sb.Append(txtRandomHighValue.Text).Append(",");
            }
            return sb.ToString().TrimEnd(',');
        }

        public bool CheakParam()
        {
            // 低于下限模式
            return (!radLowModel.Checked || !string.IsNullOrEmpty(txtLowModelValue.Text)) &&
                // 高于上限模式
                (!
                // 高于上限模式
                radHighModel.Checked || !string.IsNullOrEmpty(txtHighModelValue.Text)) &&
                // 处于区间模式
                (!
                // 处于区间模式
                radBetweenModel.Checked || !string.IsNullOrEmpty(txtBetweenModelLowValue.Text) && !string.IsNullOrEmpty(txtBetweenModelHighValue.Text)) &&
                // 下限大于上限
                float.Parse(txtBetweenModelLowValue.Text) <= float.Parse(txtBetweenModelHighValue.Text) &&
            // 系数修正模式
            (!
            // 系数修正模式
            radRatioRevise.Checked || !string.IsNullOrEmpty(txtRatio.Text)) &&
                // 随机修正模式
                (!
                // 随机修正模式
                radRandomRevise.Checked || !string.IsNullOrEmpty(txtRandomLowValue.Text) && !string.IsNullOrEmpty(txtRandomHighValue.Text)) &&
                // 下限大于上限
                float.Parse(txtRandomLowValue.Text) <= float.Parse(txtRandomHighValue.Text) &&
                // 未选择触发模式
                (radLowModel.Checked || radHighModel.Checked || radBetweenModel.Checked) &&
                // 未选择修正模式
                (radRatioRevise.Checked || radRandomRevise.Checked);
        }

        public void ShowParam(string param)
        {
            try
            {
                string[] paramStrs = param.Split(',');
                var index = 0;
                switch(paramStrs[index])
                {
                    case "0":
                        radLowModel.Checked = true;
                        index++;
                        txtLowModelValue.Text = paramStrs[index];
                        break;
                    case "1":
                        radHighModel.Checked = true;
                        index++;
                        txtHighModelValue.Text = paramStrs[index];
                        break;
                    case "2":
                        radBetweenModel.Checked = true;
                        index++;
                        txtBetweenModelLowValue.Text = paramStrs[index];
                        index++;
                        txtBetweenModelHighValue.Text = paramStrs[index];
                        break;
                }
                index++;
                switch(paramStrs[index])
                {
                    case "0":
                        radRatioRevise.Checked = true;
                        index++;
                        txtRatio.Text = paramStrs[index];
                        break;
                    case "1":
                        radRandomRevise.Checked = true;
                        index++;
                        txtRandomLowValue.Text = paramStrs[index];
                        index++;
                        txtRandomHighValue.Text = paramStrs[index];
                        break;
                }
            }
            catch(Exception)
            {
                // ignored
            }
        }

        #endregion
    }

}
