using Fpi.Communication.Ports.Grouping;
using Fpi.UI.Common.PC.Configure;
using Fpi.Xml;

namespace Fpi.Communication.Ports.UI.PC
{
    public partial class UC_GroupingPort : BaseConfigureView//UserControl
    {
        public UC_GroupingPort()
        {
            InitializeComponent();

            BindUI();
        }

        private void BindUI()
        {
        }

        protected override void ShowConfig(object obj)
        {
            BaseNode configNode = configObj as BaseNode;

            string winSize = configNode.GetPropertyValue(GroupingPort.PropertyName_WindowSize, GroupingPort.DEFAULT_WINDOW_SIZE.ToString());
            string frmSize = configNode.GetPropertyValue(GroupingPort.PropertyName_FrameSize, GroupingPort.DEFAULT_FRAME_SIZE.ToString());

            this.nuWinSize.Value = int.Parse(winSize);
            this.nuFrmSize.Value = int.Parse(frmSize);
        }

        public override object Save()
        {
            BaseNode configNode = configObj as BaseNode;

            string winSize = this.nuWinSize.Value.ToString();
            string frmSize = this.nuFrmSize.Value.ToString();

            configNode.SetProperty(GroupingPort.PropertyName_WindowSize, winSize);
            configNode.SetProperty(GroupingPort.PropertyName_FrameSize, frmSize);

            return configObj;
        }
    }
}
