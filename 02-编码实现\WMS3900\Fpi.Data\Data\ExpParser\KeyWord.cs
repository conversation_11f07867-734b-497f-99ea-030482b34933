﻿namespace Fpi.Data.ExpParser
{
    using System.Runtime.InteropServices;

    [StructLayout(LayoutKind.Sequential)]
    public struct KeyWord
    {
        public EKeyword Type;
        public EDataType ReturnType;
        public string Value;
        public KeyWord(EKeyword type, EDataType returnType, string value)
        {
            this.Type = type;
            this.ReturnType = returnType;
            this.Value = value;
        }
    }
}

