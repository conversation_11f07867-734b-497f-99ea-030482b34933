﻿namespace Fpi.Devices.Channel
{
    partial class ConfigScopeRevisePanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.radLowModel = new System.Windows.Forms.RadioButton();
            this.label2 = new System.Windows.Forms.Label();
            this.txtLowModelValue = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.gbTriggerModel = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txtHighModelValue = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.radHighModel = new System.Windows.Forms.RadioButton();
            this.label3 = new System.Windows.Forms.Label();
            this.txtBetweenModelLowValue = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.radBetweenModel = new System.Windows.Forms.RadioButton();
            this.label4 = new System.Windows.Forms.Label();
            this.txtBetweenModelHighValue = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.gbReviseModel = new System.Windows.Forms.GroupBox();
            this.label5 = new System.Windows.Forms.Label();
            this.txtRandomHighValue = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.txtRandomLowValue = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.radRandomRevise = new System.Windows.Forms.RadioButton();
            this.label7 = new System.Windows.Forms.Label();
            this.txtRatio = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.radRatioRevise = new System.Windows.Forms.RadioButton();
            this.gbTriggerModel.SuspendLayout();
            this.gbReviseModel.SuspendLayout();
            this.SuspendLayout();
            // 
            // radLowModel
            // 
            this.radLowModel.AutoSize = true;
            this.radLowModel.Location = new System.Drawing.Point(18, 16);
            this.radLowModel.Name = "radLowModel";
            this.radLowModel.Size = new System.Drawing.Size(71, 16);
            this.radLowModel.TabIndex = 19;
            this.radLowModel.TabStop = true;
            this.radLowModel.Text = "低于下限";
            this.radLowModel.UseVisualStyleBackColor = true;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(97, 19);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 20;
            this.label2.Text = "下限值：";
            // 
            // txtLowModelValue
            // 
            this.txtLowModelValue.CanEmpty = true;
            this.txtLowModelValue.DigitLength = 4;
            this.txtLowModelValue.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtLowModelValue.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtLowModelValue.IsValidCheck = false;
            this.txtLowModelValue.Label = "";
            this.txtLowModelValue.Location = new System.Drawing.Point(157, 15);
            this.txtLowModelValue.MaxValue = null;
            this.txtLowModelValue.MinValue = null;
            this.txtLowModelValue.Name = "txtLowModelValue";
            this.txtLowModelValue.Size = new System.Drawing.Size(100, 21);
            this.txtLowModelValue.TabIndex = 21;
            // 
            // gbTriggerModel
            // 
            this.gbTriggerModel.Controls.Add(this.label4);
            this.gbTriggerModel.Controls.Add(this.txtBetweenModelHighValue);
            this.gbTriggerModel.Controls.Add(this.label3);
            this.gbTriggerModel.Controls.Add(this.txtBetweenModelLowValue);
            this.gbTriggerModel.Controls.Add(this.radBetweenModel);
            this.gbTriggerModel.Controls.Add(this.label1);
            this.gbTriggerModel.Controls.Add(this.txtHighModelValue);
            this.gbTriggerModel.Controls.Add(this.radHighModel);
            this.gbTriggerModel.Controls.Add(this.label2);
            this.gbTriggerModel.Controls.Add(this.txtLowModelValue);
            this.gbTriggerModel.Controls.Add(this.radLowModel);
            this.gbTriggerModel.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbTriggerModel.Location = new System.Drawing.Point(0, 0);
            this.gbTriggerModel.Name = "gbTriggerModel";
            this.gbTriggerModel.Size = new System.Drawing.Size(451, 121);
            this.gbTriggerModel.TabIndex = 22;
            this.gbTriggerModel.TabStop = false;
            this.gbTriggerModel.Text = "触发模式：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(97, 55);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 23;
            this.label1.Text = "上限值：";
            // 
            // txtHighModelValue
            // 
            this.txtHighModelValue.CanEmpty = true;
            this.txtHighModelValue.DigitLength = 4;
            this.txtHighModelValue.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtHighModelValue.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtHighModelValue.IsValidCheck = false;
            this.txtHighModelValue.Label = "";
            this.txtHighModelValue.Location = new System.Drawing.Point(157, 52);
            this.txtHighModelValue.MaxValue = null;
            this.txtHighModelValue.MinValue = null;
            this.txtHighModelValue.Name = "txtHighModelValue";
            this.txtHighModelValue.Size = new System.Drawing.Size(100, 21);
            this.txtHighModelValue.TabIndex = 24;
            // 
            // radHighModel
            // 
            this.radHighModel.AutoSize = true;
            this.radHighModel.Location = new System.Drawing.Point(18, 53);
            this.radHighModel.Name = "radHighModel";
            this.radHighModel.Size = new System.Drawing.Size(71, 16);
            this.radHighModel.TabIndex = 22;
            this.radHighModel.TabStop = true;
            this.radHighModel.Text = "高于上限";
            this.radHighModel.UseVisualStyleBackColor = true;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(97, 92);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 26;
            this.label3.Text = "下限值：";
            // 
            // txtBetweenModelLowValue
            // 
            this.txtBetweenModelLowValue.CanEmpty = true;
            this.txtBetweenModelLowValue.DigitLength = 4;
            this.txtBetweenModelLowValue.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtBetweenModelLowValue.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtBetweenModelLowValue.IsValidCheck = false;
            this.txtBetweenModelLowValue.Label = "";
            this.txtBetweenModelLowValue.Location = new System.Drawing.Point(157, 88);
            this.txtBetweenModelLowValue.MaxValue = null;
            this.txtBetweenModelLowValue.MinValue = null;
            this.txtBetweenModelLowValue.Name = "txtBetweenModelLowValue";
            this.txtBetweenModelLowValue.Size = new System.Drawing.Size(100, 21);
            this.txtBetweenModelLowValue.TabIndex = 27;
            // 
            // radBetweenModel
            // 
            this.radBetweenModel.AutoSize = true;
            this.radBetweenModel.Location = new System.Drawing.Point(18, 90);
            this.radBetweenModel.Name = "radBetweenModel";
            this.radBetweenModel.Size = new System.Drawing.Size(71, 16);
            this.radBetweenModel.TabIndex = 25;
            this.radBetweenModel.TabStop = true;
            this.radBetweenModel.Text = "处于区间";
            this.radBetweenModel.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(275, 92);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 28;
            this.label4.Text = "上限值：";
            // 
            // txtBetweenModelHighValue
            // 
            this.txtBetweenModelHighValue.CanEmpty = true;
            this.txtBetweenModelHighValue.DigitLength = 4;
            this.txtBetweenModelHighValue.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtBetweenModelHighValue.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtBetweenModelHighValue.IsValidCheck = false;
            this.txtBetweenModelHighValue.Label = "";
            this.txtBetweenModelHighValue.Location = new System.Drawing.Point(335, 88);
            this.txtBetweenModelHighValue.MaxValue = null;
            this.txtBetweenModelHighValue.MinValue = null;
            this.txtBetweenModelHighValue.Name = "txtBetweenModelHighValue";
            this.txtBetweenModelHighValue.Size = new System.Drawing.Size(100, 21);
            this.txtBetweenModelHighValue.TabIndex = 29;
            // 
            // gbReviseModel
            // 
            this.gbReviseModel.Controls.Add(this.label5);
            this.gbReviseModel.Controls.Add(this.txtRandomHighValue);
            this.gbReviseModel.Controls.Add(this.label6);
            this.gbReviseModel.Controls.Add(this.txtRandomLowValue);
            this.gbReviseModel.Controls.Add(this.radRandomRevise);
            this.gbReviseModel.Controls.Add(this.label7);
            this.gbReviseModel.Controls.Add(this.txtRatio);
            this.gbReviseModel.Controls.Add(this.radRatioRevise);
            this.gbReviseModel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbReviseModel.Location = new System.Drawing.Point(0, 121);
            this.gbReviseModel.Name = "gbReviseModel";
            this.gbReviseModel.Size = new System.Drawing.Size(451, 91);
            this.gbReviseModel.TabIndex = 23;
            this.gbReviseModel.TabStop = false;
            this.gbReviseModel.Text = "修正模式：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(275, 62);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 28;
            this.label5.Text = "上限值：";
            // 
            // txtRandomHighValue
            // 
            this.txtRandomHighValue.CanEmpty = true;
            this.txtRandomHighValue.DigitLength = 4;
            this.txtRandomHighValue.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtRandomHighValue.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtRandomHighValue.IsValidCheck = false;
            this.txtRandomHighValue.Label = "";
            this.txtRandomHighValue.Location = new System.Drawing.Point(335, 58);
            this.txtRandomHighValue.MaxValue = null;
            this.txtRandomHighValue.MinValue = null;
            this.txtRandomHighValue.Name = "txtRandomHighValue";
            this.txtRandomHighValue.Size = new System.Drawing.Size(100, 21);
            this.txtRandomHighValue.TabIndex = 29;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(97, 62);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 26;
            this.label6.Text = "下限值：";
            // 
            // txtRandomLowValue
            // 
            this.txtRandomLowValue.CanEmpty = true;
            this.txtRandomLowValue.DigitLength = 4;
            this.txtRandomLowValue.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtRandomLowValue.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtRandomLowValue.IsValidCheck = false;
            this.txtRandomLowValue.Label = "";
            this.txtRandomLowValue.Location = new System.Drawing.Point(157, 58);
            this.txtRandomLowValue.MaxValue = null;
            this.txtRandomLowValue.MinValue = null;
            this.txtRandomLowValue.Name = "txtRandomLowValue";
            this.txtRandomLowValue.Size = new System.Drawing.Size(100, 21);
            this.txtRandomLowValue.TabIndex = 27;
            // 
            // radRandomRevise
            // 
            this.radRandomRevise.AutoSize = true;
            this.radRandomRevise.Location = new System.Drawing.Point(18, 60);
            this.radRandomRevise.Name = "radRandomRevise";
            this.radRandomRevise.Size = new System.Drawing.Size(71, 16);
            this.radRandomRevise.TabIndex = 25;
            this.radRandomRevise.TabStop = true;
            this.radRandomRevise.Text = "随机修正";
            this.radRandomRevise.UseVisualStyleBackColor = true;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(97, 25);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(53, 12);
            this.label7.TabIndex = 23;
            this.label7.Text = "系数值：";
            // 
            // txtRatio
            // 
            this.txtRatio.CanEmpty = true;
            this.txtRatio.DigitLength = 4;
            this.txtRatio.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtRatio.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtRatio.IsValidCheck = false;
            this.txtRatio.Label = "";
            this.txtRatio.Location = new System.Drawing.Point(157, 22);
            this.txtRatio.MaxValue = null;
            this.txtRatio.MinValue = null;
            this.txtRatio.Name = "txtRatio";
            this.txtRatio.Size = new System.Drawing.Size(100, 21);
            this.txtRatio.TabIndex = 24;
            // 
            // radRatioRevise
            // 
            this.radRatioRevise.AutoSize = true;
            this.radRatioRevise.Location = new System.Drawing.Point(18, 23);
            this.radRatioRevise.Name = "radRatioRevise";
            this.radRatioRevise.Size = new System.Drawing.Size(71, 16);
            this.radRatioRevise.TabIndex = 22;
            this.radRatioRevise.TabStop = true;
            this.radRatioRevise.Text = "系数修正";
            this.radRatioRevise.UseVisualStyleBackColor = true;
            // 
            // ConfigScopeRevisePanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.gbReviseModel);
            this.Controls.Add(this.gbTriggerModel);
            this.Name = "ConfigScopeRevisePanel";
            this.Size = new System.Drawing.Size(451, 212);
            this.gbTriggerModel.ResumeLayout(false);
            this.gbTriggerModel.PerformLayout();
            this.gbReviseModel.ResumeLayout(false);
            this.gbReviseModel.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.RadioButton radLowModel;
        private System.Windows.Forms.Label label2;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtLowModelValue;
        private System.Windows.Forms.GroupBox gbTriggerModel;
        private System.Windows.Forms.Label label4;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtBetweenModelHighValue;
        private System.Windows.Forms.Label label3;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtBetweenModelLowValue;
        private System.Windows.Forms.RadioButton radBetweenModel;
        private System.Windows.Forms.Label label1;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtHighModelValue;
        private System.Windows.Forms.RadioButton radHighModel;
        private System.Windows.Forms.GroupBox gbReviseModel;
        private System.Windows.Forms.Label label5;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtRandomHighValue;
        private System.Windows.Forms.Label label6;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtRandomLowValue;
        private System.Windows.Forms.RadioButton radRandomRevise;
        private System.Windows.Forms.Label label7;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtRatio;
        private System.Windows.Forms.RadioButton radRatioRevise;


    }
}
