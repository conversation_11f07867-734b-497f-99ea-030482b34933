{"version": "0.2.0", "configurations": [{"name": "WMS3900（不构建）", "type": "clr", "request": "launch", "program": "${workspaceFolder}\\..\\Product\\Debug\\WMS3900.exe", "args": [], "cwd": "${workspaceFolder}\\..\\Product\\Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}, {"name": "WMS3900（重新构建）", "type": "clr", "request": "launch", "preLaunchTask": "rebuild", "program": "${workspaceFolder}\\..\\Product\\Debug\\WMS3900.exe", "args": [], "cwd": "${workspaceFolder}\\..\\Product\\Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}, {"name": "配置向导（不构建）", "type": "clr", "request": "launch", "program": "${workspaceFolder}\\..\\Product\\Debug\\WMS3900配置向导.exe", "args": [], "cwd": "${workspaceFolder}\\..\\Product\\Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}, {"name": "配置向导（重新构建）", "type": "clr", "request": "launch", "preLaunchTask": "rebuild", "program": "${workspaceFolder}\\..\\Product\\Debug\\WMS3900配置向导.exe", "args": [], "cwd": "${workspaceFolder}\\..\\Product\\Debug", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "justMyCode": true}], "compounds": []}