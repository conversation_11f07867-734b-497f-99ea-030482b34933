﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="imageList.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imageList.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACw
        CgAAAk1TRnQBSQFMAgEBAgEAAXwBAQF8AQEBEAEAARABAAT/ASEBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEgBgABEBoAAwIBAwNdAcwDgQH/A4EB/wOBAf8DZgH/A10ByiAABAIDCQEMAxEBFgMT
        ARoDEwEaAxMBGgMTARoDEwEaAxMBGgMTARoDEwEaAxIBFwMJAQwEAowAAxABFQOSAf8B1QLTAf8B4gHg
        Ad8B/wHfAdwB2wH/AeEC3wH/A2UB9CAAAwMBBAMSARcDHgErAywBQwNHAYABVAFWAVQBqwNbAcQDWwHE
        AVQBVgFUAasDRwGAAywBQwMgAS0DEgEYAwMBBJAAA0ABbwNaAb0BxAHCAcEB/wHUAc8BzgH/A2IB9gNR
        AZwDXgHSA4EB/wNmAf8DYQH/A10B/wNZAf8DXgHXDAADMQFNA1oBvwNgAeMDWgH1AUABqAFAAf0BQAGo
        AUAB/QNaAfUDYAHjA1oBvwMxAU2MAANLAY0DXQHMA10BzANdAcwDYQHeA54B/wOaAf8DXAH4A4QB/wKH
        AYYB/wGHAYYBhQH/AaIBoAGfAf8B0wHOAc0B/wHTAc4BzQH/AegC5QH/A1oB/wgAAzEBTQFbAVwBWwHN
        AUgBYgFIAfYBFQHRAQQB/wEUAbYBAwH/ARQB0QEDAf8BFAHRAQMB/wEUAdEBAwH/ARQB0QEDAf8BSAFi
        AUgB9gFbAVwBWwHNAzEBTYgAA64B/wHkAuIB/wHXAtUB/wHVAdMB0gH/AdEBzgHNAf8BygHEAcMB/wHI
        AcMBwgH/Ac0ByQHIAf8BzALKAf8BzAHKAckB/wHYAtYB/wOBAf8BuQGxAa4B/wG3Aa8BrgH/AdMBzgHN
        Af8DXgH/BAADEwEaA1oBvwFJAWIBSAH2ARUByAEEAf8BFAGyAQMB/wPmAf8BFAGyAQMB/wEUAcgBAwH/
        ARQByAEDAf8BFAHIAQMB/wEUAcgBAwH/AUgBYgFIAfYDWgG/AxMBGoQAA7QB/wHgAt0B/wGnAYEBQwH/
        AacBgQFDAf8BpwGBAUMB/wGnAYEBQwH/AacBgQFDAf8BpwGBAUMB/wGnAYEBQwH/AacBgQFDAf8B2gHV
        AdQB/wOBAf8BugGyAbEB/wG5AbEBrwH/AdQBzwHOAf8DYwH/BAADPwFsA2AB4wEYAcABBwH/ARQBrQED
        Af8D3gH/A+IB/wPmAf8BFAGtAQMB/wEUAb4BAwH/ARQBvgEDAf8BFAG+AQMB/wEUAb4BAwH/A2AB4wM/
        AWyEAAO6Af8B3gLbAf8BtQGBAVAB/wHOAZgBZgH/AdgBrgGRAf8B2QGvAZEB/wHZAa8BkQH/AdoBrwGR
        Af8B1gGgAYEB/wGnAYEBQwH/AdcB0wHRAf8DgQH/AbsBtAGzAf8BuwGzAbEB/wHUAdABzwH/A4EB/wQA
        A1MBpwNaAfUBFQGuAQQB/wPVAf8D2gH/A94B/wPiAf8D5gH/ARQBqAEDAf8BFAG0AQMB/wEUAbQBAwH/
        ARQBtAEDAf8DWgH1A1MBp4QAA8AB/wHfAtwB/wG0AYEBTwH/AcsBlQFkAf8BzQGXAWUB/wHPAZkBgAH/
        AdEBmwGBAf8B0gGcAYEB/wHUAZ4BgQH/AacBgQFDAf8B1wHUAdMB/wOEAf8BMQGhATgB/wEtAZQBLQH/
        AdYB0QHQAf8DgQH/BAADWwHEAUABqAFAAf0BqQHXAaIB/wPVAf8D6wH/ARQBpQEDAf8D3gH/A+IB/wPm
        Af8BFAGjAQMB/wEUAaoBAwH/ARQBqgEDAf8BQAGoAUAB/QNbAcSEAAPFAf8B4QHeAdwB/wGzAYEBTgH/
        AccBkQFhAf8ByQGTAWMB/wHLAZUBZAH/Ac0BlwFmAf8BzwGZAYAB/wHRAZsBgQH/AacBgQFDAf8B2QHV
        AdQB/wOLAf8BoAHIAaQB/wFGAakBTAH/AdcB0wHRAf8DgQH/BAADWwHEAUABqAFAAf0BLgGzAR0B/wP4
        Af8BHwGoAQ4B/wEVAaIBBAH/ARQBnwEDAf8D3gH/A+IB/wPmAf8BFAGeAQMB/wEUAaEBAwH/AUABqAFA
        Af0DWwHEhAADygH/AeEB3wHeAf8BsQGBAU0B/wHEAY4BXgH/AcYBkAFgAf8ByAGSAWEB/wHKAZQBYwH/
        AcwBlgFlAf8BzgGYAWYB/wGnAYEBQwH/AdsC1gH/A5EB/wHBAbsBuQH/AcABuQG4Af8B1wLTAf8DhAH/
        BAADUwGnA1oB9QE5Ab4BKAH/ATABtQEfAf8BOQG+ASgB/wEzAbkBIgH/ASkBrwEYAf8BHwGkAQ4B/wPi
        Af8D4wH/A+cB/wEYAZ4BBwH/A1oB9QNTAaeEAAPOAf8B4gLfAf8BsAGBAUwB/wGxAYEBTAH/AbEBgQFN
        Af8BsgGBAU4B/wGzAYEBTgH/AbQBgQFPAf8BtQGBAVAB/wGnAYEBQwH/AdwB2AHXAf8DmAH/A40B/wOK
        Af8B2QHVAdQB/wOLAf8EAAM/AWwDYAHjAUYBywE1Af8BQAHFAS8B/wFAAcUBLwH/AUABxQEvAf8BQAHF
        AS8B/wFAAcUBLwH/ATYBuwElBf8BpwHiAZ4B/wFEAckBMwH/A2AB4wM/AWyEAAPTAf8B8QLvAf8B4gLf
        Af8B4gLfAf8B4QHfAd4B/wHhAd4B3QH/AeAB3QHcAf8B3wHcAdsB/wHeAtsB/wHeAdsB2QH/Ae0B7AHr
        Af8DngH/AcUBvwG+Af8BwwG9AbsB/wHaAdYB1QH/A5EB/wQAAxMBGgNaAb8CYgFgAfYBSgHPATkB/wFJ
        Ac4BOAH/AUkBzgE4Af8BSQHOATgB/wFJAc4BOAH/AUkBzgE4Af8BPAHBASsB/wFEAckBMwH/AmIBSQH2
        A1oBvwMTARqEAANqAfkD0wH/A9AB/wPMAf8DyAH/A8MB/wO/Af8DugH/A7UB/wOwAf8DqgH/A6UB/wGU
        ApMB/wGSApEB/wHbAdcB1gH/A5gB/wgAAzEBTQFbAVwBWwHNAmIBYAH2AVUB2gFEAf8BUQHWAUAB/wFR
        AdYBQAH/AVEB1gFAAf8BUQHWAUAB/wFUAdkBQwH/AmIBYAH2AVsBXAFbAc0DMQFNnAADLgFIA2EB5APf
        Af8D6gH/Ac8BygHJAf8BywHGAcUB/wHKAcQBwwH/AcgBwwHBAf8BxwLBAf8B3AHZAdgB/wOeAf8MAAMx
        AU0DWgG/A2AB4wNaAfUBQAGpAUAB/QFAAakBQAH9A1oB9QNgAeMDWgG/AzEBTaQAAwwBDwNWAasD3AH/
        AeYC4wH/AeEB3gHcAf8B3wLcAf8B3wHcAdsB/wHeAtsB/wHuAuwB/wOlAf8QAAMTARoDPwFsAVMBVAFT
        AaYDWwHEA1sBxAFTAVQBUwGmAz8BbAMTARqwAANRAZ8DXwH7A8QB/wPAAf8DuwH/A7YB/wOwAf8DUwGn
        wAABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEAAQEFAAGAFwAD/wEAAcABfwGAAQEEAAHAAX8BgAEB
        BAAB4AEAAeABBwYAAcABAwYAAYABAQYAAYABAQYAAYABAQYAAYABAQYAAYABAQYAAYABAQYAAYABAQYA
        AYABAQYAAcABAwQAAfgBAAHgAQcEAAH8AQAB8AEPBAAB/wEAAv8EAAs=
</value>
  </data>
</root>