using System;

namespace Fpi.Communication.Exceptions
{
    /// <summary>
    /// 
    /// </summary>
    public class CommandException : CommunicationException
    {
        public CommandException()
            : base()
        {
        }

        public CommandException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public CommandException(string message)
            : base(message)
        {
        }
    }
}