﻿namespace Fpi.Data.ExpParser
{
    using System;

    public class SyntaxAnalyzer
    {
        private GrammarAnalyzer _gaz;
        private KeyValueList<IToken, string> _operandSource;
        private ToolBox _toolBox;

        public SyntaxAnalyzer()
        {
            this._gaz = new GrammarAnalyzer(this);
            this._toolBox = new ToolBox();
            this._operandSource = new KeyValueList<IToken, string>();
        }

        public IOperand Analyze(TOKENLink startLink, TOKENLink endLink)
        {
            TOKENLink link2;
            IOperand operand;
            TOKENLink link3;
            TOKENLink next = startLink;
            goto Label_022F;
            Label_0141:
            link3 = new TOKENLink(new TOKEN<IOperand>(ETokenType.token_operand, operand, next.Token.Index));
            this._operandSource.Add(link3.Token, ((TOKEN<KeyWord>)next.Token).Tag.Value);
            if(link2 != null)
            {
                if(next.Prev != null)
                {
                    link3.Prev = next.Prev;
                    next.Prev.Next = link3;
                }
                if(link2.Next != null)
                {
                    link3.Next = link2.Next;
                    link2.Next.Prev = link3;
                }
                if(next == startLink)
                {
                    startLink = link3;
                }
                if(link2 == endLink)
                {
                    endLink = link3;
                }
                next = link3;
            }
            Label_0209:
            if((next == endLink) || (next.Next == null))
            {
                if(startLink == endLink)
                {
                    if(startLink.Token.Type == ETokenType.token_operand)
                    {
                        return ((TOKEN<IOperand>)next.Token).Tag;
                    }
                    string message = string.Empty;
                    message = startLink.Token.Type == ETokenType.token_operator
                        ? string.Format("Error! 操作符“{0}”附近有语法错误（索引：{1}）", ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString())
                        : startLink.Token.Type == ETokenType.token_separator
                            ? string.Format("Error! 分隔符“{0}”附近有语法错误（索引：{1}）", ((TOKEN<Separator>)next.Token).Tag.Value.ToString(), next.Token.Index.ToString())
                            : string.Format("Error! 索引{0}附近有语法错误", next.Token.Index.ToString());
                    throw new Exception(message);
                }
                return this.OperatorEvalAnalyze(startLink, endLink);
            }
            next = next.Next;
            Label_022F:
            if(next.Token.Type != ETokenType.token_keyword)
            {
                goto Label_0209;
            }
            link2 = null;
            operand = null;
            switch(((TOKEN<KeyWord>)next.Token).Tag.Type)
            {
                case EKeyword.IF:
                    operand = this._gaz.Key_IF_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.CASE:
                    operand = this._gaz.Key_CASE_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.AND:
                case EKeyword.OR:
                    operand = this._gaz.Key_ANDOR_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.NOT:
                    operand = this._gaz.Key_Not_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.TRUE:
                    operand = this._gaz.Key_True_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.FALSE:
                    operand = this._gaz.Key_False_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.ToString:
                    operand = this._gaz.Key_ToString_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.ToDateTime:
                    operand = this._gaz.Key_ToDateTime_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.ToInt:
                    operand = this._gaz.Key_ToInt_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.ToDouble:
                    operand = this._gaz.Key_ToDouble_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.Len:
                    operand = this._gaz.Key_Len_Analyze(next, out link2);
                    goto Label_0141;

                case EKeyword.NowDate:
                    operand = this._gaz.Key_NowDate_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.GetNodeValue:
                    operand = this._gaz.Key_GetNodeValue_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.GetNodeIntValue:
                    operand = this._gaz.Key_GetNodeIntValue_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.GetNodeBoolValue:
                    operand = this._gaz.Key_GetNodeBoolValue_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.GetAllNodeBoolValueOR:
                    operand = this._gaz.Key_IsAlarmNode_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.GetAlarmNodeBoolValue:
                    operand = this._gaz.Key_GetAlarmNodeBoolValue_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.DoubleNaN:
                    operand = this._gaz.Key_DoubleNaN_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.IsAlarmNode:
                    operand = this._gaz.Key_IsAlarmNode_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.IsNodeCycleValueAlarm:
                    operand = this._gaz.Key_IsAlarmNode_Analyze(next, out link2);
                    goto Label_0141;
                case EKeyword.Invoke:
                    operand = this._gaz.Key_Invoke_Analyze(next, out link2);
                    goto Label_0141;
            }
            goto Label_0141;
        }

        private void CheckParen(TOKENLink startLink, TOKENLink endLink)
        {
            TOKENLink next = startLink;
            int num = 0;
            while(true)
            {
                if(next.Token.Type == ETokenType.token_operator)
                {
                    if(((TOKEN<Operator>)next.Token).Tag.Type == EOperatorType.LeftParen)
                    {
                        num++;
                    }
                    else if(((TOKEN<Operator>)next.Token).Tag.Type == EOperatorType.RightParen)
                    {
                        num--;
                    }
                    if(num < 0)
                    {
                        throw new Exception(string.Format("Error! 缺少左括弧（索引：{0}）", next.Token.Index.ToString()));
                    }
                }
                if(next == endLink)
                {
                    if(num > 0)
                    {
                        throw new Exception(string.Format("Error! 缺少“{0}”个右括弧", num.ToString()));
                    }
                    return;
                }
                next = next.Next;
            }
        }

        public EDataType Execute(TOKENLink startLink, TOKENLink endLink)
        {
            this.CheckParen(startLink, endLink);
            return this.Analyze(startLink, endLink).Type;
        }

        private IOperand OperatorEvalAnalyze(TOKENLink startLink, TOKENLink endLink)
        {
            this.TokenEnvirAnalyze(startLink, endLink);
            TOKENLink next = this._toolBox.InfixToPostfix(startLink, endLink);
            TOKENLink link2 = null;
            IToken token = null;
            while(next.Next != null)
            {
                next = next.Next;
                if(next.Token.Type != ETokenType.token_operator)
                {
                    goto Label_06CF;
                }
                link2 = null;
                token = null;
                EOperatorType type = ((TOKEN<Operator>)next.Token).Tag.Type;
                switch(type)
                {
                    case EOperatorType.Plus:
                    case EOperatorType.Minus:
                    case EOperatorType.Multiply:
                    case EOperatorType.Divide:
                    case EOperatorType.Mod:
                        if(((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Dstring) && (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Dstring)) || ((type != EOperatorType.Plus) && (type != EOperatorType.Minus)))
                        {
                            break;
                        }
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<string>(EDataType.Dstring, ""), next.Token.Index);
                        goto Label_05C0;

                    case EOperatorType.Positive:
                    case EOperatorType.Negative:
                        {
                            IOperand tag = ((TOKEN<IOperand>)next.Prev.Token).Tag;
                            if((tag.Type != EDataType.Ddouble) && (tag.Type != EDataType.Dint))
                            {
                                throw new Exception(string.Format("Error! 运算符“{0}”无法应用于“{2}”类型的操作数（索引:{1}）", ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString(), tag.Type.ToString()));
                            }
                            token = next.Prev.Token;
                            goto Label_05C0;
                        }
                    case EOperatorType.LessThan:
                    case EOperatorType.GreaterThan:
                    case EOperatorType.Equal:
                    case EOperatorType.NotEqual:
                    case EOperatorType.LessEqual:
                    case EOperatorType.GreaterEqual:
                        if(((((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Ddouble) && (((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Dint)) || ((((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Ddouble) && (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Dint))) && ((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Ddatetime) || (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Ddatetime))) && ((((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Dstring) || (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Dstring)) && ((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Dbool) || (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Dbool))) || ((type != EOperatorType.Equal) && (type != EOperatorType.NotEqual))))
                        {
                            goto Label_051B;
                        }
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<bool>(EDataType.Dbool, true), next.Token.Index);
                        goto Label_05C0;

                    default:
                        goto Label_05C0;
                }
                if(((((TOKEN<IOperand>)next.Prev.Token).Tag.Type == EDataType.Ddouble) || (((TOKEN<IOperand>)next.Prev.Token).Tag.Type == EDataType.Dint)) && ((((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type == EDataType.Ddouble) || (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type == EDataType.Dint)))
                {
                    token = ((((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type == EDataType.Ddouble) || (((TOKEN<IOperand>)next.Prev.Token).Tag.Type == EDataType.Ddouble)) || ((type == EOperatorType.Divide) || (type == EOperatorType.Mod))
                        ? new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, 0.0), next.Token.Index)
                        : (IToken)new TOKEN<IOperand>(ETokenType.token_operand, new Operand<int>(EDataType.Dint, 0), next.Token.Index);
                    goto Label_05C0;
                }
                throw new Exception(string.Format("Error! 运算符“{0}”无法应用于“{2}”和“{3}”类型的操作数（索引:{1}）", new object[] { ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString(), ((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type.ToString(), ((TOKEN<IOperand>)next.Prev.Token).Tag.Type.ToString() }));
                Label_051B:;
                throw new Exception(string.Format("Error! 运算符“{0}”无法应用于“{2}”和“{3}”类型的操作数（索引:{1}）", new object[] { ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString(), ((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type.ToString(), ((TOKEN<IOperand>)next.Prev.Token).Tag.Type.ToString() }));
                Label_05C0:
                if(token != null)
                {
                    link2 = new TOKENLink(token);
                    link2.Next = next.Next;
                    if(next.Next != null)
                    {
                        next.Next.Prev = link2;
                    }
                    if(((TOKEN<Operator>)next.Token).Tag.Dimension == 1)
                    {
                        if(next.Prev.Prev != null)
                        {
                            link2.Prev = next.Prev.Prev;
                            next.Prev.Prev.Next = link2;
                        }
                    }
                    else if((((TOKEN<Operator>)next.Token).Tag.Dimension == 2) && (next.Prev.Prev.Prev != null))
                    {
                        link2.Prev = next.Prev.Prev.Prev;
                        next.Prev.Prev.Prev.Next = link2;
                    }
                    next = link2;
                }
                Label_06CF:;
            }
            return ((TOKEN<IOperand>)next.Token).Tag;
        }

        private void TokenEnvirAnalyze(TOKENLink startLink, TOKENLink endLink)
        {
            TOKENLink next = startLink;
            goto Label_04DF;
            Label_04C5:
            if(next == endLink)
            {
                return;
            }
            next = next.Next;
            Label_04DF:
            switch(next.Token.Type)
            {
                case ETokenType.token_operand:
                    if((next.Prev != null) && ((next.Prev.Token.Type == ETokenType.token_operand) || ((next.Prev.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)next.Prev.Token).Tag.Type == EOperatorType.RightParen))))
                    {
                        string message = this._operandSource[next.Token] != null
                            ? string.Format("Error! 关键字“{0}”附近有语法错误（索引：{1}）", this._operandSource[next.Token], next.Token.Index.ToString())
                            : string.Format("Error! 操作数“{0}”附近有语法错误（索引：{1}）", ((TOKEN<IOperand>)next.Token).Tag.ToString(), next.Token.Index.ToString());
                        throw new Exception(message);
                    }
                    goto Label_04C5;

                case ETokenType.token_operator:
                    switch(((TOKEN<Operator>)next.Token).Tag.Type)
                    {
                        case EOperatorType.LeftParen:
                            if((next.Prev != null) && ((next.Prev.Token.Type == ETokenType.token_operand) || ((next.Prev.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)next.Prev.Token).Tag.Type == EOperatorType.RightParen))))
                            {
                                throw new Exception(string.Format("Error! 左括弧“{0}”附近有语法错误（索引：{1}）", ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString()));
                            }
                            goto Label_04C5;

                        case EOperatorType.RightParen:
                            if((next.Prev == null) || ((next.Prev.Token.Type == ETokenType.token_operator) && (((TOKEN<Operator>)next.Prev.Token).Tag.Type == EOperatorType.LeftParen)))
                            {
                                throw new Exception(string.Format("Error! 右括弧“{0}”附近有语法错误（索引：{1}）", ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString()));
                            }
                            goto Label_04C5;

                        case EOperatorType.Positive:
                        case EOperatorType.Negative:
                            if((next.Next == null) || ((next.Next.Token.Type != ETokenType.token_operand) && ((next.Next.Token.Type != ETokenType.token_operator) || (((TOKEN<Operator>)next.Next.Token).Tag.Type != EOperatorType.LeftParen))))
                            {
                                throw new Exception(string.Format("Error! 一元操作符“{0}”附近有语法错误（索引：{1}）", ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString()));
                            }
                            goto Label_04C5;
                    }
                    if(((next.Prev == null) || ((next.Prev.Token.Type != ETokenType.token_operand) && ((next.Prev.Token.Type != ETokenType.token_operator) || (((TOKEN<Operator>)next.Prev.Token).Tag.Type != EOperatorType.RightParen)))) || ((next.Next == null) || ((next.Next.Token.Type != ETokenType.token_operand) && ((next.Next.Token.Type != ETokenType.token_operator) || (((((TOKEN<Operator>)next.Next.Token).Tag.Type != EOperatorType.LeftParen) && (((TOKEN<Operator>)next.Next.Token).Tag.Type != EOperatorType.Negative)) && (((TOKEN<Operator>)next.Next.Token).Tag.Type != EOperatorType.Positive))))))
                    {
                        throw new Exception(string.Format("Error! 二元操作符“{0}”附近有语法错误（索引：{1}）", ((TOKEN<Operator>)next.Token).Tag.Value, next.Token.Index.ToString()));
                    }
                    goto Label_04C5;

                case ETokenType.token_keyword:
                    throw new Exception(string.Format("Error! 关键字“{0}”未解析（索引：{1}）", ((TOKEN<KeyWord>)next.Token).Tag.Value, next.Token.Index.ToString()));

                case ETokenType.token_separator:
                    throw new Exception(string.Format("Error! 分隔符“{0}”只能用于关键字（索引：{1}）", ((TOKEN<Separator>)next.Token).Tag.Value, next.Token.Index.ToString()));
            }
            goto Label_04C5;
        }
    }
}

