﻿namespace Fpi.Data.UI.PC
{
    partial class UC_VarNodeView
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(UC_VarNodeView));
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.chkOutputValid = new System.Windows.Forms.CheckBox();
            this.btnOutput = new System.Windows.Forms.Button();
            this.txtImpOutput = new System.Windows.Forms.TextBox();
            this.gbCustom = new System.Windows.Forms.GroupBox();
            this.chkInputValid = new System.Windows.Forms.CheckBox();
            this.btnInput = new System.Windows.Forms.Button();
            this.txtImpInput = new System.Windows.Forms.TextBox();
            this.nuValidTime = new System.Windows.Forms.NumericUpDown();
            this.txtNodeName = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.txtNodeId = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox9.SuspendLayout();
            this.gbCustom.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuValidTime)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox9
            // 
            this.groupBox9.AccessibleDescription = null;
            this.groupBox9.AccessibleName = null;
            resources.ApplyResources(this.groupBox9, "groupBox9");
            this.groupBox9.BackgroundImage = null;
            this.groupBox9.Controls.Add(this.chkOutputValid);
            this.groupBox9.Controls.Add(this.btnOutput);
            this.groupBox9.Controls.Add(this.txtImpOutput);
            this.groupBox9.Font = null;
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.TabStop = false;
            // 
            // chkOutputValid
            // 
            this.chkOutputValid.AccessibleDescription = null;
            this.chkOutputValid.AccessibleName = null;
            resources.ApplyResources(this.chkOutputValid, "chkOutputValid");
            this.chkOutputValid.BackgroundImage = null;
            this.chkOutputValid.Font = null;
            this.chkOutputValid.Name = "chkOutputValid";
            this.chkOutputValid.UseVisualStyleBackColor = true;
            this.chkOutputValid.CheckedChanged += new System.EventHandler(this.chkOutputValid_CheckedChanged);
            // 
            // btnOutput
            // 
            this.btnOutput.AccessibleDescription = null;
            this.btnOutput.AccessibleName = null;
            resources.ApplyResources(this.btnOutput, "btnOutput");
            this.btnOutput.BackgroundImage = null;
            this.btnOutput.Font = null;
            this.btnOutput.Image = global::Fpi.Properties.Resources.Search;
            this.btnOutput.Name = "btnOutput";
            this.btnOutput.UseVisualStyleBackColor = true;
            this.btnOutput.Click += new System.EventHandler(this.btnOutput_Click);
            // 
            // txtImpOutput
            // 
            this.txtImpOutput.AccessibleDescription = null;
            this.txtImpOutput.AccessibleName = null;
            resources.ApplyResources(this.txtImpOutput, "txtImpOutput");
            this.txtImpOutput.BackgroundImage = null;
            this.txtImpOutput.Font = null;
            this.txtImpOutput.Name = "txtImpOutput";
            this.txtImpOutput.ReadOnly = true;
            // 
            // gbCustom
            // 
            this.gbCustom.AccessibleDescription = null;
            this.gbCustom.AccessibleName = null;
            resources.ApplyResources(this.gbCustom, "gbCustom");
            this.gbCustom.BackgroundImage = null;
            this.gbCustom.Controls.Add(this.chkInputValid);
            this.gbCustom.Controls.Add(this.btnInput);
            this.gbCustom.Controls.Add(this.txtImpInput);
            this.gbCustom.Font = null;
            this.gbCustom.Name = "gbCustom";
            this.gbCustom.TabStop = false;
            // 
            // chkInputValid
            // 
            this.chkInputValid.AccessibleDescription = null;
            this.chkInputValid.AccessibleName = null;
            resources.ApplyResources(this.chkInputValid, "chkInputValid");
            this.chkInputValid.BackgroundImage = null;
            this.chkInputValid.Font = null;
            this.chkInputValid.Name = "chkInputValid";
            this.chkInputValid.UseVisualStyleBackColor = true;
            this.chkInputValid.CheckedChanged += new System.EventHandler(this.chkInputValid_CheckedChanged);
            // 
            // btnInput
            // 
            this.btnInput.AccessibleDescription = null;
            this.btnInput.AccessibleName = null;
            resources.ApplyResources(this.btnInput, "btnInput");
            this.btnInput.BackgroundImage = null;
            this.btnInput.Font = null;
            this.btnInput.Image = global::Fpi.Properties.Resources.Search;
            this.btnInput.Name = "btnInput";
            this.btnInput.UseVisualStyleBackColor = true;
            this.btnInput.Click += new System.EventHandler(this.btnInput_Click);
            // 
            // txtImpInput
            // 
            this.txtImpInput.AccessibleDescription = null;
            this.txtImpInput.AccessibleName = null;
            resources.ApplyResources(this.txtImpInput, "txtImpInput");
            this.txtImpInput.BackgroundImage = null;
            this.txtImpInput.Font = null;
            this.txtImpInput.Name = "txtImpInput";
            this.txtImpInput.ReadOnly = true;
            // 
            // nuValidTime
            // 
            this.nuValidTime.AccessibleDescription = null;
            this.nuValidTime.AccessibleName = null;
            resources.ApplyResources(this.nuValidTime, "nuValidTime");
            this.nuValidTime.Font = null;
            this.nuValidTime.Maximum = new decimal(new int[] {
            1410065407,
            2,
            0,
            0});
            this.nuValidTime.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nuValidTime.Name = "nuValidTime";
            this.nuValidTime.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // txtNodeName
            // 
            this.txtNodeName.AccessibleDescription = null;
            this.txtNodeName.AccessibleName = null;
            resources.ApplyResources(this.txtNodeName, "txtNodeName");
            this.txtNodeName.BackgroundImage = null;
            this.txtNodeName.Font = null;
            this.txtNodeName.Name = "txtNodeName";
            this.txtNodeName.TextChanged += new System.EventHandler(this.txtNodeName_TextChanged);
            // 
            // label3
            // 
            this.label3.AccessibleDescription = null;
            this.label3.AccessibleName = null;
            resources.ApplyResources(this.label3, "label3");
            this.label3.Font = null;
            this.label3.Name = "label3";
            // 
            // label2
            // 
            this.label2.AccessibleDescription = null;
            this.label2.AccessibleName = null;
            resources.ApplyResources(this.label2, "label2");
            this.label2.Font = null;
            this.label2.Name = "label2";
            // 
            // txtNodeId
            // 
            this.txtNodeId.AccessibleDescription = null;
            this.txtNodeId.AccessibleName = null;
            resources.ApplyResources(this.txtNodeId, "txtNodeId");
            this.txtNodeId.BackgroundImage = null;
            this.txtNodeId.Font = null;
            this.txtNodeId.Name = "txtNodeId";
            // 
            // label1
            // 
            this.label1.AccessibleDescription = null;
            this.label1.AccessibleName = null;
            resources.ApplyResources(this.label1, "label1");
            this.label1.Font = null;
            this.label1.Name = "label1";
            // 
            // UC_VarNodeView
            // 
            this.AccessibleDescription = null;
            this.AccessibleName = null;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImage = null;
            this.Controls.Add(this.groupBox9);
            this.Controls.Add(this.gbCustom);
            this.Controls.Add(this.nuValidTime);
            this.Controls.Add(this.txtNodeName);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.txtNodeId);
            this.Controls.Add(this.label1);
            this.Name = "UC_VarNodeView";
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.gbCustom.ResumeLayout(false);
            this.gbCustom.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuValidTime)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.CheckBox chkOutputValid;
        private System.Windows.Forms.Button btnOutput;
        private System.Windows.Forms.TextBox txtImpOutput;
        private System.Windows.Forms.GroupBox gbCustom;
        private System.Windows.Forms.CheckBox chkInputValid;
        private System.Windows.Forms.Button btnInput;
        private System.Windows.Forms.TextBox txtImpInput;
        private System.Windows.Forms.NumericUpDown nuValidTime;
        private System.Windows.Forms.TextBox txtNodeName;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtNodeId;
        private System.Windows.Forms.Label label1;
    }
}
