﻿//==================================================================================================
//类名：     ServiceManager   
//创建人:    hongbing_mao
//创建时间:  2013-1-11 9:13:04
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;
using System.Collections.Generic;
using System.Reflection;

namespace Fpi.Data.FuncServer
{
    /// <summary>
    /// 服务管理类
    /// </summary>
    public class ServiceManager
    {
        /// <summary>
        /// 服务列表
        /// </summary>
        public List<Service> Services { get; } = new List<Service>();
        private static readonly object syncObj = new object();
        private static ServiceManager _instance = null;
        private ServiceManager()
        {
        }
        public static ServiceManager GetInstance()
        {
            lock(syncObj)
            {
                if(_instance == null)
                {
                    _instance = new ServiceManager();
                }
            }
            return _instance;
        }
        /// <summary>
        /// 注册服务
        /// </summary>
        public void RegisterService(Service srv)
        {
            if(FindServiceByName(srv.SrvName) == null)
            {
                Services.Add(srv);
            }
        }

        /// <summary>
        /// 调用服务
        /// </summary>
        /// <param name="srvName"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public object Invoke(string srvName, object[] parameters)
        {
            Service srv = FindServiceByName(srvName);
            if(srv == null)
            {
                throw new Exception("未提供此服务方法!");
                return null;
            }
            else
            {
                Type _type = srv.SupportType;
                MethodInfo method = _type.GetMethod(srvName, BindingFlags.FlattenHierarchy | BindingFlags.Static | BindingFlags.Public);
                object result = method.Invoke(null, parameters);
                return result;
            }

        }
        /// <summary>
        /// 通过服务名查找服务
        /// </summary>
        /// <param name="srvName"></param>
        /// <returns></returns>
        public Service FindServiceByName(string srvName)
        {
            foreach(Service srv in Services)
            {
                if(srv.SrvName == srvName)
                {
                    return srv;
                }
            }
            return null;
        }
    }
}
