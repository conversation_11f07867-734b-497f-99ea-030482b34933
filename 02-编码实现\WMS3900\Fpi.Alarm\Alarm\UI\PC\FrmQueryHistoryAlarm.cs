﻿using System;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.UI.PC.DockForms;
using Fpi.Util;
using Fpi.Util.EnumRelated;
using Fpi.Util.StatusForm;
using Sunny.UI;

namespace Fpi.Alarm.UI.PC
{
    /// <summary>
    /// 报警数据查询
    /// </summary>
    public partial class FrmQueryHistoryAlarm : BaseWindow
    {
        #region 字段属性

        /// <summary>
        /// 查询时报警源
        /// </summary>
        private string _alarmSource = string.Empty;

        /// <summary>
        /// 查询时报警等级
        /// </summary>
        private string _alarmGrade = string.Empty;

        /// <summary>
        /// 添加时间起始
        /// </summary>
        private DateTime? _addTimeBegin = null;

        /// <summary>
        /// 添加时间结束
        /// </summary>
        private DateTime? _addTimeEnd = null;

        /// <summary>
        /// 移除时间起始
        /// </summary>
        private DateTime? _removeTimeBegin = null;

        /// <summary>
        /// 移除时间结束
        /// </summary>
        private DateTime? _removeTimeEnd = null;

        /// <summary>
        /// 时间显示格式
        /// </summary>
        private const string DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

        #endregion

        #region 构造

        public FrmQueryHistoryAlarm()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmQueryHistoryAlarm_Load(object sender, EventArgs e)
        {
            InitCombox();
            InitialDateTimePicker();
            SetDataGridViewHead();
        }

        /// <summary>
        /// 开始查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                this.lblPage.Text = @"?/?";

                this.pagination.PageChanged -= new UIPagination.OnPageChangeEventHandler(this.pagination_PageChanged);

                // 清空界面数据
                dgvAlarmInfo.Rows.Clear();

                pagination.TotalCount = 0;

                // 检查查询条件
                Check();

                // 查询数据数量
                QueryDataCount();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                this.pagination.PageChanged += new UIPagination.OnPageChangeEventHandler(this.pagination_PageChanged);

                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 导出数据至excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvAlarmInfo.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Application.StartupPath + "\\query\\";
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName =
                    $"{filePath}报警数据_{dtpStartTime.Value:yyyy-MM-dd HH-mm-ss}_{dtpEndTime.Value:yyyy-MM-dd HH-mm-ss}.xlsx";

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvAlarmInfo, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 点击翻页
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="pagingSource"></param>
        /// <param name="curPage">第几页，和界面对应，从1开始，取数据要用pageIndex - 1</param>
        /// <param name="onePageCount">单页数据量，也就是PageSize值</param>
        private void pagination_PageChanged(object sender, object pagingSource, int curPage, int onePageCount)
        {
            try
            {
                this.lblPage.Text = $@"{curPage}/{pagination.PageCount}";

                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this, "数据加载中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvAlarmInfo.Rows.Clear();

                // 查询
                var packetList = AlarmManager.GetInstance().QueryHistoryAlarm(_alarmSource, _alarmGrade, string.Empty, _addTimeBegin, _addTimeEnd, _removeTimeBegin, _removeTimeEnd, onePageCount, (curPage - 1) * onePageCount);

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this);

                // 打开进度条界面
                FpiStatusFormService.ShowStatusForm(packetList.Count, "数据渲染中，请稍候...");

                // 渲染
                int i = 1;

                int rowIndex = (curPage - 1) * onePageCount + 1;
                foreach(AlarmInfo info in packetList)
                {
                    // 取消操作
                    if(FpiStatusFormService.StatusFormServiceClose)
                    {
                        throw new Exception("取消操作！");
                    }

                    AddNewRows(info, rowIndex++);
                    FpiStatusFormService.SetDescription($"数据渲染中[{i++}/{packetList.Count}]......");
                    FpiStatusFormService.StepIt();
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏进度条界面
                FpiStatusFormService.HideStatusForm();
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this);
            }
        }

        #endregion

        #region 私有方法

        #region 数据查询

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(dtpStartTime.Value > dtpEndTime.Value)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            if(string.IsNullOrEmpty(this.txtRecordCount.Text))
            {
                throw new Exception("每页记录数输入不可为空！");
            }

            if(!int.TryParse(this.txtRecordCount.Text, out int onePageCount))
            {
                throw new Exception("每页记录数输入异常，应为大于1的整数！");
            }

            if(onePageCount < 1)
            {
                throw new Exception("每页记录数值不可小于1！");
            }

            pagination.PageSize = onePageCount;

            if(this.cmbSource.SelectedIndex > 0)
            {
                _alarmSource = (this.cmbSource.SelectedItem as AlarmSource).id;
            }
            else
            {
                _alarmSource = string.Empty;
            }
            if(this.cmbGrade.SelectedIndex > 0)
            {
                _alarmGrade = (this.cmbGrade.SelectedItem as AlarmGrade).id;
            }
            else
            {
                _alarmGrade = string.Empty;
            }

            eTimeType timeType = eTimeType.起始时间;

            if(cmbDateType.SelectedIndex > -1)
            {
                timeType = (eTimeType)cmbDateType.SelectedValue;
            }

            if(timeType == eTimeType.起始时间)
            {
                _addTimeBegin = this.dtpStartTime.Value;
                _addTimeEnd = this.dtpEndTime.Value;
                _removeTimeBegin = null;
                _removeTimeEnd = null;
            }
            else
            {
                _removeTimeBegin = this.dtpStartTime.Value;
                _removeTimeEnd = this.dtpEndTime.Value;
                _addTimeBegin = null;
                _addTimeEnd = null;
            }
        }

        /// <summary>
        /// 更新数据内容
        /// </summary>
        private void QueryDataCount()
        {
            int recordCount = 0;
            try
            {
                recordCount = AlarmManager.GetInstance().QueryAlarmCount(_alarmSource, _alarmGrade, string.Empty, _addTimeBegin, _addTimeEnd, _removeTimeBegin, _removeTimeEnd);
            }
            catch
            {
                throw new Exception("数据库连接异常！");
            }

            if(recordCount > 0)
            {
                pagination.TotalCount = recordCount;
            }
            else
            {
                throw new Exception("无当前条件下的查询数据!");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            if(pagination.ActivePage != 1)
            {
                pagination.ActivePage = 1;
            }
            else
            {
                pagination_PageChanged(null, null, 1, pagination.PageSize);
            }
        }

        /// <summary>
        /// 添加新行显示
        /// </summary>
        /// <param name="info"></param>
        /// <param name="rowIndex"></param>
        private void AddNewRows(AlarmInfo info, int rowIndex)
        {
            if(info != null)
            {
                int index = dgvAlarmInfo.Rows.Add();
                DataGridViewRow dr = dgvAlarmInfo.Rows[index];
                dr.Tag = info;
                dr.Cells[0].Value = rowIndex;
                dr.Cells[1].Value = info.AlarmCode?.id;
                dr.Cells[2].Value = info.AlarmSource?.name;
                dr.Cells[3].Value = info.AlarmCode?.GetGrade().name;

                dr.Cells[4].Value = info.AlarmCode?.name;
                dr.Cells[4].ToolTipText = info.AlarmCode?.name;
                dr.Cells[4].Style.Alignment = DataGridViewContentAlignment.MiddleLeft;

                dr.Cells[5].Value = info.AddTime.ToString(DATE_FORMAT);
                if(info.RemoveTime != DateTime.MinValue)
                {
                    dr.Cells[6].Value = info.RemoveTime.ToString(DATE_FORMAT);
                }
            }
        }

        #endregion

        #region 控件

        /// <summary>
        /// 初始化下拉控件显示
        /// </summary>
        private void InitCombox()
        {
            this.cmbSource.Items.Clear();
            this.cmbSource.Items.Add(string.Empty);
            this.cmbSource.Items.AddRange(AlarmManager.GetInstance().alarmSources.GetItems());

            this.cmbGrade.Items.Clear();
            this.cmbGrade.Items.Add(string.Empty);
            this.cmbGrade.Items.AddRange(AlarmManager.GetInstance().alarmGrades.GetItems());

            EnumOperate.BandEnumToCmb(cmbDateType, typeof(eTimeType));

            this.cmbGrade.SelectedIndex = 0;
            this.cmbSource.SelectedIndex = 0;
            this.cmbDateType.SelectedValue = (int)eTimeType.起始时间;
        }

        /// <summary>
        /// 初始化表头显示
        /// </summary>
        private void SetDataGridViewHead()
        {
            dgvAlarmInfo.ClearColumns();

            var col = dgvAlarmInfo.AddColumn("序号", "num");
            col.FillWeight = 50;
            col = dgvAlarmInfo.AddColumn("报警码", "code");
            col.FillWeight = 100;
            col = dgvAlarmInfo.AddColumn("报警源", "endTime");
            col.FillWeight = 200;
            col = dgvAlarmInfo.AddColumn("报警等级", "endTime");
            col.FillWeight = 100;
            col = dgvAlarmInfo.AddColumn("报警内容", "name");
            col.FillWeight = 500;
            col = dgvAlarmInfo.AddColumn("产生时间", "startTime");
            col.FillWeight = 200;
            col = dgvAlarmInfo.AddColumn("消除时间", "endTime");
            col.FillWeight = 200;
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            DateTime now = DateTime.Now;
            var startTime = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0);
            dtpStartTime.Value = startTime;
            dtpEndTime.Value = startTime.AddDays(1);
        }

        #endregion

        #endregion

        /// <summary>
        /// 查询时间类型
        /// </summary>
        private enum eTimeType
        {
            /// <summary>
            /// 起始时间
            /// </summary>
            [Description("起始时间")]
            起始时间,

            /// <summary>
            /// 结束时间
            /// </summary>
            [Description("结束时间")]
            结束时间
        }
    }
}
