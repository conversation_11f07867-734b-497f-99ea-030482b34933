﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>Fpi.Data</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>Fpi</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>pdbonly</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SunnyUI">
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\AlarmLimit.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\ArrayNode.cs" />
    <Compile Include="Data\Config\CalK.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\DataDealer.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\DataHelper.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\DataManager.cs" />
    <Compile Include="Data\Config\Enums.cs" />
    <Compile Include="Data\Config\Fixup.cs" />
    <Compile Include="Data\Config\Scope.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\StateNode.cs" />
    <Compile Include="Data\Config\UnitGroup.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\UnitManager.cs" />
    <Compile Include="Data\Config\ValueNode.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\VarNode.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\Config\VarNodeTemplate.cs" />
    <Compile Include="Data\Config\Unit.cs" />
    <Compile Include="Data\Config\Indice.cs" />
    <Compile Include="Data\Exceptions\DataException.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Data\ExpParser\Define.cs" />
    <Compile Include="Data\ExpParser\EDataType.cs" />
    <Compile Include="Data\ExpParser\EDFAState.cs" />
    <Compile Include="Data\ExpParser\EKeyword.cs" />
    <Compile Include="Data\ExpParser\EOperatorType.cs" />
    <Compile Include="Data\ExpParser\ETokenType.cs" />
    <Compile Include="Data\ExpParser\Evaluator.cs" />
    <Compile Include="Data\ExpParser\ExpressionParse.cs" />
    <Compile Include="Data\FuncServer\BaseServer.cs" />
    <Compile Include="Data\FuncServer\ServiceAttribute.cs" />
    <Compile Include="Data\FuncServer\ServiceManager.cs" />
    <Compile Include="Data\FuncServer\Service.cs" />
    <Compile Include="Data\ExpParser\Grammar.cs" />
    <Compile Include="Data\ExpParser\GrammarAnalyzer.cs" />
    <Compile Include="Data\ExpParser\IOperand.cs" />
    <Compile Include="Data\ExpParser\IToken.cs" />
    <Compile Include="Data\ExpParser\KeyValueList.cs" />
    <Compile Include="Data\ExpParser\KeyWord.cs" />
    <Compile Include="Data\ExpParser\Link_OP.cs" />
    <Compile Include="Data\ExpParser\Operand.cs" />
    <Compile Include="Data\ExpParser\Operator.cs" />
    <Compile Include="Data\ExpParser\PhraseAnalyzer.cs" />
    <Compile Include="Data\ExpParser\Separator.cs" />
    <Compile Include="Data\ExpParser\SyntaxAnalyzer.cs" />
    <Compile Include="Data\ExpParser\TOKEN.cs" />
    <Compile Include="Data\ExpParser\TOKENLink.cs" />
    <Compile Include="Data\ExpParser\ToolBox.cs" />
    <Compile Include="Data\ExpParser\Unknown.cs" />
    <Compile Include="Data\Interfaces\IDataDealer.cs" />
    <Compile Include="Data\Interfaces\IDataFilter.cs" />
    <Compile Include="Data\Interfaces\IExtendFunction.cs" />
    <Compile Include="Data\Interfaces\INameChanged.cs" />
    <Compile Include="Data\Interfaces\INodeConfigView.cs" />
    <Compile Include="Data\Interfaces\ISimuData.cs" />
    <Compile Include="Data\Interfaces\IDataProvider.cs" />
    <Compile Include="Data\UI\PC\ExpressEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ExpressEditorForm.Designer.cs">
      <DependentUpon>ExpressEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ExpressesEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ExpressesEditorForm.Designer.cs">
      <DependentUpon>ExpressesEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\FormConfigSingleNode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\FormConfigSingleNode.Designer.cs">
      <DependentUpon>FormConfigSingleNode.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\FormVarExplore.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\FormVarExplore.Designer.cs">
      <DependentUpon>FormVarExplore.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ServiceEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ServiceEditForm.Designer.cs">
      <DependentUpon>ServiceEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\UC_StateNodeView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\UC_StateNodeView.Designer.cs">
      <DependentUpon>UC_StateNodeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\UC_ValueNodeView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\UC_ValueNodeView.Designer.cs">
      <DependentUpon>UC_ValueNodeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\UC_VarNodeView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\UC_VarNodeView.Designer.cs">
      <DependentUpon>UC_VarNodeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\UnitManagerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\UnitManagerForm.Designer.cs">
      <DependentUpon>UnitManagerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditUnit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditUnit.Designer.cs">
      <DependentUpon>FormEditUnit.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditScope.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditScope.Designer.cs">
      <DependentUpon>FormEditScope.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditAlarmLimit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditAlarmLimit.Designer.cs">
      <DependentUpon>FormEditAlarmLimit.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormComputeCalk.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormComputeCalk.Designer.cs">
      <DependentUpon>FormComputeCalk.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormSelectUnit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormSelectUnit.Designer.cs">
      <DependentUpon>FormSelectUnit.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditUnitType.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditUnitType.Designer.cs">
      <DependentUpon>FormEditUnitType.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditIndice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\ValueNodeViews\FormEditIndice.Designer.cs">
      <DependentUpon>FormEditIndice.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\AlarmNodeEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\AlarmNodeEditorForm.Designer.cs">
      <DependentUpon>AlarmNodeEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\VarNodeEditorForm2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\VarNodeEditorForm2.Designer.cs">
      <DependentUpon>VarNodeEditorForm2.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\VarNodeEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\VarNodeEditorForm.Designer.cs">
      <DependentUpon>VarNodeEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\VarNodeInfo.cs" />
    <Compile Include="Data\UI\PC\TemplateExpEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\TemplateExpEditorForm.Designer.cs">
      <DependentUpon>TemplateExpEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Data\UI\PC\TemplateVarNodeEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Data\UI\PC\TemplateVarNodeEditorForm.Designer.cs">
      <DependentUpon>TemplateVarNodeEditorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Data\UI\PC\ExpressEditorForm.resx">
      <DependentUpon>ExpressEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ExpressesEditorForm.resx">
      <DependentUpon>ExpressesEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\FormConfigSingleNode.en-US.resx">
      <DependentUpon>FormConfigSingleNode.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\FormConfigSingleNode.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormConfigSingleNode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\FormVarExplore.en-US.resx">
      <DependentUpon>FormVarExplore.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\FormVarExplore.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormVarExplore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ServiceEditForm.resx">
      <DependentUpon>ServiceEditForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UC_StateNodeView.en-US.resx">
      <DependentUpon>UC_StateNodeView.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UC_StateNodeView.resx">
      <SubType>Designer</SubType>
      <DependentUpon>UC_StateNodeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UC_ValueNodeView.en-US.resx">
      <DependentUpon>UC_ValueNodeView.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UC_ValueNodeView.resx">
      <SubType>Designer</SubType>
      <DependentUpon>UC_ValueNodeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UC_VarNodeView.en-US.resx">
      <DependentUpon>UC_VarNodeView.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UC_VarNodeView.resx">
      <SubType>Designer</SubType>
      <DependentUpon>UC_VarNodeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\UnitManagerForm.resx">
      <DependentUpon>UnitManagerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormComputeCalk.en-US.resx">
      <DependentUpon>FormComputeCalk.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditAlarmLimit.en-US.resx">
      <DependentUpon>FormEditAlarmLimit.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditScope.en-US.resx">
      <DependentUpon>FormEditScope.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditUnit.en-US.resx">
      <DependentUpon>FormEditUnit.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditUnit.resx">
      <DependentUpon>FormEditUnit.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditScope.resx">
      <DependentUpon>FormEditScope.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditAlarmLimit.resx">
      <DependentUpon>FormEditAlarmLimit.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormComputeCalk.resx">
      <DependentUpon>FormComputeCalk.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormSelectUnit.en-US.resx">
      <DependentUpon>FormSelectUnit.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormSelectUnit.resx">
      <DependentUpon>FormSelectUnit.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditUnitType.en-US.resx">
      <DependentUpon>FormEditUnitType.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditUnitType.resx">
      <DependentUpon>FormEditUnitType.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditIndice.en-US.resx">
      <DependentUpon>FormEditIndice.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\ValueNodeViews\FormEditIndice.resx">
      <DependentUpon>FormEditIndice.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\AlarmNodeEditorForm.en-US.resx">
      <DependentUpon>AlarmNodeEditorForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\AlarmNodeEditorForm.resx">
      <DependentUpon>AlarmNodeEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\VarNodeEditorForm2.en-US.resx">
      <DependentUpon>VarNodeEditorForm2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\VarNodeEditorForm2.resx">
      <DependentUpon>VarNodeEditorForm2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\VarNodeEditorForm.en-US.resx">
      <DependentUpon>VarNodeEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\VarNodeEditorForm.resx">
      <DependentUpon>VarNodeEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\TemplateExpEditorForm.resx">
      <DependentUpon>TemplateExpEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\TemplateVarNodeEditorForm.en-US.resx">
      <DependentUpon>TemplateVarNodeEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Data\UI\PC\TemplateVarNodeEditorForm.resx">
      <DependentUpon>TemplateVarNodeEditorForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.en-US.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <SubType>Designer</SubType>
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Search.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Compute.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Remove.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Edit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Node_Del.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Group.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Group_Del.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Node.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Alarm\Fpi.Alarm.csproj">
      <Project>{e714875c-0ec1-4c0f-8571-d0f631430c82}</Project>
      <Name>Fpi.Alarm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>