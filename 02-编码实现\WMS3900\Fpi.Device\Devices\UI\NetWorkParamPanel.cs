﻿using System;
using System.Windows.Forms;
using Fpi.Xml;

namespace Fpi.Devices.UI
{
    public partial class NetWorkParamPanel : UserControl, ICommParam
    {
        private readonly Device _device;
        private bool _isClient;

        public NetWorkParamPanel()
        {
            InitializeComponent();
        }

        public NetWorkParamPanel(Device device, bool isClient)
            : this()
        {
            this._device = device;
            _isClient = isClient;
        }

        private void SerialPortParamPanel_Load(object sender, EventArgs e)
        {
            if(_isClient)
            {
                txtHost.Visible = true;
                this.label1.Visible = true;
            }
            else
            {
                txtHost.Visible = false;
                this.label1.Visible = false;
            }
            if(this._device != null)
            {
                Property proCommParam = this._device.GetProperty("NetWorkParam");
                if(proCommParam != null)
                {
                    this.txtHost.Text = proCommParam.GetPropertyValue("host", "127.0.0.1");
                    this.nuPort.Value = int.Parse(proCommParam.GetPropertyValue("port", "10000"));
                }
            }
        }

        #region ICommParam 成员

        public void SaveCommParam()
        {
            if(this._device != null)
            {
                Property proCommParam = this._device.GetProperty("NetWorkParam");

                if(proCommParam == null)
                {
                    proCommParam = new Property("NetWorkParam", "网络通信参数");
                    this._device.AddProperty(proCommParam);
                }

                proCommParam.SetProperty("host", this.txtHost.Text);
                proCommParam.SetProperty("port", this.nuPort.Value.ToString());
            }
        }

        #endregion
    }

    /// <summary>
    /// 用于保存参数的接口
    /// </summary>
    public interface ICommParam
    {
        void SaveCommParam();
    }
}
