PUT /LAPI/V1.0/NetWork/HttpAuth HTTP/1.1
Content-Type: application/json; charset=UTF-8
Accept: application/json, text/javascript, */*; q=0.01
Authorization: Digest username="admin", realm="6cf17ec29107", nonce="51546ba489a03a9d1ce5e16481e26397", algorithm="MD5", uri="/LAPI/V1.0/NetWork/HttpAuth", response="2e0680f69f3670b64259523b241d6e4b", qop="auth", nc="00001613", cnonce="d41d8cd98f00b204e9800998ecf8427e"
X-Requested-With: XMLHttpRequest
Referer: http://*************/page/common/frame.5e20ccdf.htm
Accept-Language: zh-CN
Accept-Encoding: gzip, deflate
User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko
Host: *************
Content-Length: 12
Connection: Keep-Alive
Cache-Control: no-cache
Cookie: WebLoginHandle=1719540573703

{"Mode":2}
HTTP/1.1 200 Ok
Content-Length: 234
Content-Type: text/plain
Connection: close
X-Frame-Options: SAMEORIGIN

{
"Response": {
	"ResponseURL": "/LAPI/V1.0/NetWork/HttpAuth",
	"CreatedID": -1, 
	"ResponseCode": 0,
 	"SubResponseCode": 0,
 	"ResponseString": "Succeed",
	"StatusCode": 0,
	"StatusString": "Succeed",
	"Data": null
	}
}
