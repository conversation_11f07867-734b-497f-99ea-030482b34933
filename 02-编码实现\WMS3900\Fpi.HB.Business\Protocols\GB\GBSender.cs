﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Manager;
using Fpi.Communication.Protocols;
using Fpi.HB.Business.Protocols.GB.Config;
using Fpi.HB.Business.Protocols.GB.GBDataFrame;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;

namespace Fpi.HB.Business.Protocols.GB
{
    public class GBSender : Sender, IDataUpload, IGetSuppleData
    {
        #region 字段属性

        private Timer _timerReportCount = null;
        private readonly object _reportLockObj = new object();

        /// <summary>
        /// 当前协议描述
        /// </summary>
        private GBProtocolDesc _desc;

        #endregion

        #region 公共方法（重写）

        /// <summary>
        /// 管道生效事件方法
        /// </summary>
        /// <param name="pipe"></param>
        protected override void ActionPipe(Pipe pipe)
        {
            base.ActionPipe(pipe);
            _desc = pipe.Protocol.ProtocolDesc as GBProtocolDesc;
            _timerReportCount = new Timer(ReportCountFunc, null, 1000, 10000);
        }

        /// <summary>
        /// //上传实时数据
        /// </summary>
        /// <param name="pipe"></param>
        public override void SendData(Pipe pipe)
        {
            try
            {
                if(_desc != null)
                {
                    SingleCfg singleCfg = _desc.SingleCfg;
                    if(singleCfg != null && singleCfg.polNodes.GetCount() > 0)
                    {
                        var gbCmd = new GBCommand
                        {
                            MN = _desc.MN,
                            ST = _desc.ST,
                            CN = "2011",
                            PW = _desc.PW,
                            CP = GBHelper.BuildRealDataFrames(_desc)
                        };
                        pipe.Send("", gbCmd);
                        ProtocolLogHelper.ShowSendMsg(gbCmd.CombineData());
                    }
                }
            }
            catch(Exception ex)
            {
                ProtocolLogHelper.ShowMsg("上传实时数据出错:" + ex.Message);
            }
        }

        #endregion

        #region 上传统计数据

        /// <summary>
        /// //上传统计数据
        /// </summary>
        /// <param name="obj"></param>
        private void ReportCountFunc(object obj)
        {
            // 通道关闭，不传输
            if(CurrentPipe == null || !CurrentPipe.valid)
            {
                return;
            }

            try
            {
                if(_desc != null)
                {
                    SingleCfg singleCfg = _desc.SingleCfg;

                    if(_desc.SingleCfg.polNodes.GetCount() < 1)
                    {
                        return;
                    }

                    DateTime now = DateTime.Now.AddHours(2);
                    DateTime beginMinute, endMinute, beginHour, endHour, beginDay, endDay;
                    bool minuteTrigger, hourTrigger, dayTrigger;
                    lock(_reportLockObj)
                    {
                        minuteTrigger = singleCfg.IsTriggerMinutePLan(now, out beginMinute, out endMinute);
                        hourTrigger = singleCfg.IsTriggerHourPLan(now, out beginHour, out endHour);
                        dayTrigger = singleCfg.IsTriggerDayPLan(now, out beginDay, out endDay);
                    }

                    //统计数据 2051：分钟统计 2061：小时统计 2031：日统计
                    GBCommand gbCmdTotal = null;
                    if(minuteTrigger)
                    {
                        gbCmdTotal = GBHelper.BuildTotalDataFrame(_desc, "2051", _desc.MN, beginMinute, endMinute);
                        CurrentPipe.Send("", gbCmdTotal);
                        ProtocolLogHelper.ShowSendMsg(gbCmdTotal.CombineData());
                    }
                    if(hourTrigger)
                    {
                        gbCmdTotal = GBHelper.BuildTotalDataFrame(_desc, "2061", _desc.MN, beginHour, endHour);
                        _desc.SendDataWithAddendum(gbCmdTotal);
                        ProtocolLogHelper.ShowSendMsg(gbCmdTotal.CombineData());
                    }
                    if(dayTrigger)
                    {
                        gbCmdTotal = GBHelper.BuildTotalDataFrame(_desc, "2031", _desc.MN, beginDay, endDay);
                        CurrentPipe.Send("", gbCmdTotal);
                        ProtocolLogHelper.ShowSendMsg(gbCmdTotal.CombineData());
                    }
                }
            }
            catch(Exception ex)
            {
                ProtocolLogHelper.ShowMsg("上传统计数据出错:" + ex.Message);
            }
        }

        #endregion

        #region IDisposable 成员

        public override void Dispose()
        {
            base.Dispose();
            if(_timerReportCount != null)
            {
                _timerReportCount.Change(Timeout.Infinite, Timeout.Infinite);
                _timerReportCount.Dispose();
                _timerReportCount = null;
            }
        }

        #endregion

        #region IDataUpload

        /// <summary>
        /// 上传数据类型
        /// </summary>
        public Type UploadDataType { get => typeof(eUploadDataType); }

        public void UploadData(int type)
        {
            if(_desc != null)
            {
                try
                {
                    if(!Enum.IsDefined(UploadDataType, type))
                    {
                        throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
                    }
                    eUploadDataType uploadType = (eUploadDataType)type;

                    // 待发送数据表
                    var cmdList = new List<GBCommand>();
                    switch(uploadType)
                    {
                        case eUploadDataType.实时数据:
                            SingleCfg singleCfg = _desc.SingleCfg;
                            if(singleCfg != null && singleCfg.polNodes.GetCount() > 0)
                            {
                                GBCommand gbCmd = new GBCommand
                                {
                                    MN = _desc.MN,
                                    ST = _desc.ST,
                                    CN = "2011",
                                    PW = _desc.PW,
                                    CP = GBHelper.BuildRealDataFrames(_desc)
                                };
                                cmdList.Add(gbCmd);
                            }
                            break;

                        case eUploadDataType.小时数据:
                            cmdList.Add(GBHelper.BuildTotalDataFrame(_desc, "2061", _desc.MN, DateTime.Now.AddDays(-1), DateTime.Now));
                            break;

                        default:
                            throw new ArgumentOutOfRangeException("type", uploadType, null);
                    }

                    // 发送数据
                    foreach(GBCommand cmd in cmdList)
                    {
                        _desc.SendDataFrame(cmd);
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{(eUploadDataType)type}类型数据出错:{e.Message}");
                }
            }
            else
            {
                throw new Exception("协议描述器为空！");
            }
        }

        #endregion

        #region IGetSuppleData

        public List<IByteStream> GetSuppleData(DateTime startTime, DateTime endTime, int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eUploadDataType uploadType = (eUploadDataType)type;

            List<GBCommand> cmdLists;
            switch(uploadType)
            {
                case eUploadDataType.小时数据:
                    cmdLists = GBHelper.DataSupplement(startTime, endTime, _desc);
                    break;
                default:
                    throw new Exception($"当前协议不支持补传 {uploadType} 类型数据！");
            }

            return cmdLists?.Cast<IByteStream>().ToList();
        }

        #endregion
    }
}
