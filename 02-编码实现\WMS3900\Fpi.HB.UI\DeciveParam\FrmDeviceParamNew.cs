﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.Devices;
using Sunny.UI;

namespace Fpi.HB.UI
{
    public partial class FrmDeviceParamNew : UIForm
    {
        #region 构造

        public FrmDeviceParamNew()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void DeviceParamForm_Load(object sender, EventArgs e)
        {
            this.InitDeviceTree();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化仪器列表
        /// </summary>
        private void InitDeviceTree()
        {
            // 设置初始页面索引（关联页面，唯一且不可重复）
            int pageIndex = 1000;

            TreeNode parent = mainMenu.CreateNode("在线监测系统设备集合", pageIndex);

            List<Device> tempDeviceList = DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS);
            tempDeviceList.AddRange(DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.IO));
            tempDeviceList.AddRange(DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.AUXILI));
            if(tempDeviceList.Count > 0)
            {
                foreach(Device device in tempDeviceList)
                {
                    // 获取配置界面
                    var uc = device.GetDeviceParamUC();

                    // 无配置界面
                    if(uc == null)
                    {
                        uc = new UC_NoParamPanel();
                    }

                    var page = new UIPage();
                    page.Text = device.name;
                    page.Controls.Add(uc);

                    //通过设置PageIndex关联，节点文字、图标由相应的Page的Text、Symbol提供
                    mainMenu.CreateChildNode(parent, AddPage(page, ++pageIndex));
                }
            }

            mainMenu.ExpandAll();
        }

        #endregion
    }
}