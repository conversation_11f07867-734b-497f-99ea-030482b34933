﻿using System;
using System.Collections;
using System.ComponentModel;
using Fpi.Communication.Interfaces;

namespace Fpi.Communication
{
    //优先级
    public struct Pqitem
    {
        public int priority;
        public IByteStream value;
    }

    public enum ePriority : int
    {
        /// <summary>
        /// 高级别
        /// </summary>
        [Description("高级别")]
        高级别 = 1,
        /// <summary>
        /// 中等级别
        /// </summary>
        [Description("中等级别")]
        中等级别 = 2,
        /// <summary>
        /// 低级别
        /// </summary>
        [Description("低级别")]
        低级别 = 3,
    }

    //构造的队列
    public class CQueue
    {
        private ArrayList pqueue;
        private ArrayList squeue;
        //构造器
        public CQueue()
        {
            pqueue = new ArrayList();
            squeue = new ArrayList();
        }

        //加入队列
        public void EnQueue(object item)
        {
            pqueue.Add(item);
        }

        //删除队列
        public Pqitem DeQueue()
        {
            try
            {
                int min;
                int index = 0;
                if(pqueue.Count > 0 && pqueue[0] != null)
                {
                    min = ((Pqitem)pqueue[0]).priority;
                    for(int i = pqueue.Count - 1; i >= 0; i--)
                    {
                        if(pqueue[i] != null && ((Pqitem)pqueue[i]).priority < min)
                        {
                            min = ((Pqitem)pqueue[i]).priority;
                        }
                        squeue.Clear();
                        for(int x = pqueue.Count - 1; x >= 0; x--)
                        {
                            if(pqueue[x] != null && ((Pqitem)pqueue[x]).priority == min &&
                                ((Pqitem)pqueue[x]).value != null)
                            {
                                squeue.Add(pqueue[x]);
                                index = x;
                            }
                        }
                    }
                }
                if(squeue.Count > 0 && squeue[0] != null)
                {
                    Pqitem obj = (Pqitem)squeue[0];
                    pqueue.RemoveAt(index);
                    if(pqueue.Count > 300)
                    {
                        pqueue.Clear();
                    }
                    squeue.RemoveAt(0);
                    return obj;
                }
            }
            catch(Exception ex)
            {
                throw new Exception("设置优先级出错!" + ex.Message);
            }
            return new Pqitem();
        }

        //读取顶
        public object Peek()
        {
            return pqueue[0];
        }

        //清空队列
        public void ClearQueue()
        {
            pqueue.Clear();
        }

        //队列的长度
        public int Count()
        {
            return pqueue.Count;
        }
    }
}