GET /LAPI/V1.0/PeopleLibraries/BasicInfo HTTP/1.1
cache-control: no-cache
Postman-Token: f4332e53-33e7-4ac4-b05c-5fb35958488b
User-Agent: PostmanRuntime/6.4.1
Accept: */*
Host: *************
accept-encoding: gzip, deflate
Connection: keep-alive

HTTP/1.1 200 Ok
Content-Length: 602
Content-Type: text/plain
Connection: close
X-Frame-Options: SAMEORIGIN

{
"Response": {
	"ResponseURL": "/LAPI/V1.0/PeopleLibraries/BasicInfo",
	"CreatedID": -1, 
	"ResponseCode": 0,
 	"SubResponseCode": 0,
 	"ResponseString": "Succeed",
	"StatusCode": 0,
	"StatusString": "Succeed",
	"Data": {
	"Num":	2,
	"LibList":	[{
			"ID":	3,
			"Type":	3,
			"PersonNum":	1,
			"MemberNum":	1,
			"FaceNum":	1,
			"LastChange":	1666861282,
			"Name":	"...............",
			"BelongIndex":	""
		}, {
			"ID":	4,
			"Type":	4,
			"PersonNum":	1,
			"MemberNum":	1,
			"FaceNum":	0,
			"LastChange":	1667273789,
			"Name":	"...............",
			"BelongIndex":	""
		}]
}
	}
}
