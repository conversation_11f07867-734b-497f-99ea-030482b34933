﻿using Fpi.Xml;

namespace Fpi.HB.Business.HisData
{
    /// <summary>
    /// 历史和报表查询配置保存类（报表部分待扩展）
    /// </summary>
    public class ReportManager : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 查询项
        /// </summary>
        public NodeList QueryGroups = new NodeList();

        /// <summary>
        /// 测量数据表Id
        /// </summary>
        public const string MeasureGroupId = "0";

        #endregion

        #region 单例

        private static readonly object syncObj = new object();
        private static ReportManager _instance;
        public static ReportManager GetInstance()
        {
            lock(syncObj)
            {
                if(_instance == null)
                {
                    _instance = new ReportManager();
                }
            }
            return _instance;
        }

        private ReportManager()
        {
            loadXml();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 根据报表组id查找报表组配置
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public QueryGroup GetQueryGroupByGroupId(string groupId)
        {
            foreach(QueryGroup queryGroup in QueryGroups)
            {
                if(queryGroup.id == groupId)
                {
                    return queryGroup;
                }
            }

            return null;
        }

        /// <summary>
        /// 根据报表组id查找报表组配置，找不到时返回第一个报表组
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public QueryGroup GetQueryGroupByGroupIdOrFirst(string groupId)
        {
            foreach(QueryGroup queryGroup in QueryGroups)
            {
                if(queryGroup.id == groupId)
                {
                    return queryGroup;
                }
            }

            return GetFirstQueryGroup();
        }

        /// <summary>
        /// 查找第一个报表组
        /// </summary>

        /// <returns></returns>
        public QueryGroup GetFirstQueryGroup()
        {
            return QueryGroups.GetCount() > 0 ? QueryGroups[0] as QueryGroup : null;
        }

        /// <summary>
        /// 根据因子id查找报表因子配置
        /// 从所有报表中查找
        /// </summary>
        /// <param name="nodeId"></param>
        /// <returns></returns>
        public QueryNode GetQueryNodeByNodeId(string nodeId)
        {
            foreach(QueryGroup queryGroup in QueryGroups)
            {
                foreach(QueryNode node in queryGroup.QueryNodes)
                {
                    if(node != null && node.id == nodeId)
                    {
                        return node;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 根据因子id、所在报表id查找报表因子配置
        /// </summary>
        /// <param name="nodeId"></param>
        /// <returns></returns>
        public QueryNode GetQueryNodeByNodeIdAndGroupId(string nodeId, string groupId)
        {
            foreach(QueryGroup queryGroup in QueryGroups)
            {
                if(queryGroup.id == groupId)
                {
                    foreach(QueryNode node in queryGroup.QueryNodes)
                    {
                        if(node != null && node.id == nodeId)
                        {
                            return node;
                        }
                    }
                }
            }

            return null;
        }

        public static void Reload()
        {
            _instance = null;
        }

        #endregion
    }
}