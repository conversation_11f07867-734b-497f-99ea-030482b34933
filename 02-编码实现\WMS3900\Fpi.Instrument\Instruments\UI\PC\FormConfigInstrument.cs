using System;
using System.Windows.Forms;
using Fpi.Instruments.Properties;
using Fpi.Xml;

namespace Fpi.Instruments.UI.PC
{
    public partial class FormConfigInstrument : Form
    {
        public FormConfigInstrument()
        {
            InitializeComponent();
        }

        #region Control Event

        private void FormConfigInstrument_Load(object sender, EventArgs e)
        {
            this.btnUpdateType.Enabled = this.btnDelType.Enabled = false;
            this.btnUpdateIns.Enabled = this.btnDelIns.Enabled = false;

            LoadType();
            LoadIns();
        }

        private void LoadType()
        {
            if(InstrumentManager.GetInstance().instrumentTypes != null)
            {
                foreach(InstrumentType type in InstrumentManager.GetInstance().instrumentTypes)
                {
                    AddTypeInView(type);
                }
            }
        }

        private void LoadIns()
        {
            if(InstrumentManager.GetInstance().instruments != null)
            {
                foreach(Instrument ins in InstrumentManager.GetInstance().instruments)
                {
                    AddInsInView(ins);
                }
            }
        }

        private void btnAddType_Click(object sender, EventArgs e)
        {
            FormEditType form = new FormEditType();
            if(form.ShowDialog() == DialogResult.OK)
            {
                AddTypeInView(form.InstrumentType);
            }
        }

        private void btnUpdateType_Click(object sender, EventArgs e)
        {
            InstrumentType type = this.lvType.SelectedItems[0].Tag as InstrumentType;
            FormEditType form = new FormEditType(type);
            if(form.ShowDialog() == DialogResult.OK)
            {
                UpdateTypeInView(form.InstrumentType);
            }
        }

        private void btnDelType_Click(object sender, EventArgs e)
        {
            RemoveInView(this.lvType);
        }

        private void btnAddIns_Click(object sender, EventArgs e)
        {
            FormEditIns form = new FormEditIns();
            if(form.ShowDialog() == DialogResult.OK)
            {
                AddInsInView(form.Instrument);
            }
        }

        private void btnUpdateIns_Click(object sender, EventArgs e)
        {
            Instrument ins = this.lvIns.SelectedItems[0].Tag as Instrument;
            FormEditIns form = new FormEditIns(ins);
            if(form.ShowDialog() == DialogResult.OK)
            {
                UpdateInsInView(form.Instrument);
            }
        }

        private void btnDelIns_Click(object sender, EventArgs e)
        {
            RemoveInView(this.lvIns);
        }

        private void lvType_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ListViewItem lvi = (sender as ListView).GetItemAt(e.X, e.Y);
            if(lvi != null)
            {
                lvi.Selected = true;
                btnUpdateType_Click(null, null);
            }
        }

        private void lvIns_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ListViewItem lvi = (sender as ListView).GetItemAt(e.X, e.Y);
            if(lvi != null)
            {
                lvi.Selected = true;
                btnUpdateIns_Click(null, null);
            }
        }

        private void lvType_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool selected = (this.lvType.SelectedItems.Count > 0);
            this.btnUpdateType.Enabled = this.btnDelType.Enabled = selected;
        }

        private void lvIns_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool selected = (this.lvIns.SelectedItems.Count > 0);
            this.btnUpdateIns.Enabled = this.btnDelIns.Enabled = selected;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                SaveType();
                SaveIns();

                InstrumentManager.GetInstance().Save();
                if(MessageBox.Show(Resources.ConfigSave, Resources.SystemPrompt, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    InstrumentManager.ReLoad();
                }
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            }
        }

        #endregion

        private void AddTypeInView(InstrumentType type)
        {
            ListViewItem lvi = new ListViewItem(new string[] {
                        type.id,
                        type.name,
                        type.directLink ? Resources.Yes:Resources.No});

            lvi.Tag = type;
            lvi.Selected = true;
            this.lvType.Items.Add(lvi);
        }
        private void AddInsInView(Instrument ins)
        {
            ListViewItem lvi = new ListViewItem(new string[] {
                        ins.id,
                        ins.name,
                        ins.InstrumentType != null ? ins.InstrumentType.name : "-",
                        ins.Router != null ? ins.Router.name : "-",
                        ins.address.ToString(),
                        ins.desc});

            lvi.Tag = ins;
            lvi.Selected = true;
            this.lvIns.Items.Add(lvi);
        }

        private void UpdateTypeInView(InstrumentType type)
        {
            ListViewItem lvi = this.lvType.SelectedItems[0];
            lvi.Tag = type;

            lvi.SubItems[0].Text = type.id;
            lvi.SubItems[1].Text = type.name;
            lvi.SubItems[2].Text = (type.directLink ? Resources.Yes : Resources.No);

            lvi.Selected = true;

        }

        private void UpdateInsInView(Instrument ins)
        {
            ListViewItem lvi = this.lvIns.SelectedItems[0];
            lvi.Tag = ins;

            lvi.SubItems[0].Text = ins.id;
            lvi.SubItems[1].Text = ins.name;
            lvi.SubItems[2].Text = (ins.InstrumentType != null ? ins.InstrumentType.name : string.Empty);
            lvi.SubItems[3].Text = (ins.Router != null ? ins.Router.name : string.Empty);
            lvi.SubItems[4].Text = ins.address.ToString();
            lvi.SubItems[5].Text = ins.desc;
        }

        private void RemoveInView(ListView view)
        {
            IdNameNode node = view.SelectedItems[0].Tag as IdNameNode;
            if(MessageBox.Show(string.Format(Resources.ConfirmDelete, node.name), Resources.SystemPrompt, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                view.SelectedItems[0].Remove();
            }
        }


        private void SaveType()
        {
            NodeList list = new NodeList();
            foreach(ListViewItem lvi in this.lvType.Items)
            {
                InstrumentType type = lvi.Tag as InstrumentType;
                if(!list.Add(type))
                {
                    throw new Exception(string.Format(Resources.IntrumentTypeExist, type.id));
                }
            }

            InstrumentManager.GetInstance().instrumentTypes = list;
        }
        private void SaveIns()
        {
            NodeList list = new NodeList();
            foreach(ListViewItem lvi in this.lvIns.Items)
            {
                Instrument ins = lvi.Tag as Instrument;
                if(!list.Add(ins))
                {
                    throw new Exception(string.Format(Resources.InstrumentExist, ins.id));
                }
            }

            InstrumentManager.GetInstance().instruments = list;
        }
    }
}