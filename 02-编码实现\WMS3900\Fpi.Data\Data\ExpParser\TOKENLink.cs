﻿namespace Fpi.Data.ExpParser
{
    public class TOKENLink
    {
        private TOKENLink _next;
        private TOKENLink _prev;

        public TOKENLink(IToken token)
        {
            this.Token = token;
        }

        public TOKENLink Next
        {
            get => this._next;
            set
            {
                if(value != this)
                {
                    this._next = value;
                }
            }
        }

        public TOKENLink Prev
        {
            get => this._prev;
            set
            {
                if(value != this)
                {
                    this._prev = value;
                }
            }
        }

        public IToken Token { get; }
    }
}

