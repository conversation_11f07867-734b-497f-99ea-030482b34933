﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.Xml;

namespace Fpi.HB.UI.HisData
{
    /// <summary>
    /// 查询选项配置窗口
    /// </summary>
    public partial class FrmQueryConfig : Form
    {
        private readonly TabControl _tb = new TabControl();

        /// <summary>
        /// UC列表
        /// </summary>
        private readonly List<UC_QueryConfig> _list = new List<UC_QueryConfig>();

        /// <summary>
        /// 构造
        /// </summary>
        public FrmQueryConfig()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 窗口初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void QueryConfig_Load(object sender, EventArgs e)
        {
            this.pnlBox.Controls.Add(_tb);
            _tb.Dock = DockStyle.Fill;
            _tb.SelectedIndexChanged += new EventHandler(tb_SelectedIndexChanged);

            foreach(QueryGroup mp in ReportManager.GetInstance().QueryGroups)
            {
                TabPage tp = new TabPage(mp.name);
                UC_QueryConfig uc = new UC_QueryConfig(mp);
                _list.Add(uc);
                uc.Dock = DockStyle.Fill;
                tp.Tag = mp;
                tp.Controls.Add(uc);
                _tb.TabPages.Add(tp);
            }
        }

        /// <summary>
        /// 切换tab页
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tb_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(this._tb.TabCount > 0)
            {
                this.txtName.Text = this._tb.SelectedTab.Text;
            }
        }

        /// <summary>
        /// 添加组
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            if(this.txtName.Text.Trim().Length < 1)
            {
                FpiMessageBox.ShowInfo("请先输入组名!");
                return;
            }
            QueryGroup mp = new QueryGroup();
            mp.name = this.txtName.Text.Trim();
            TabPage tp = new TabPage(mp.name);
            tp.Tag = mp;
            UC_QueryConfig uc = new UC_QueryConfig(mp);
            _list.Add(uc);
            uc.Dock = DockStyle.Fill;
            tp.Controls.Add(uc);
            _tb.TabPages.Add(tp);
        }

        /// <summary>
        /// 删除组
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDel_Click(object sender, EventArgs e)
        {
            _list.Remove(_tb.SelectedTab.Controls[0] as UC_QueryConfig);
            _tb.TabPages.Remove(_tb.SelectedTab);

        }

        /// <summary>
        /// 重命名组
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRename_Click(object sender, EventArgs e)
        {
            if(this.txtName.Text.Trim().Length < 1)
            {
                FpiMessageBox.ShowInfo("组名不能为空!");
                return;
            }
            (_tb.SelectedTab.Tag as QueryGroup).name = this.txtName.Text.Trim();
            _tb.SelectedTab.Text = this.txtName.Text.Trim();
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if(ReportManager.GetInstance().QueryGroups == null)
                {
                    ReportManager.GetInstance().QueryGroups = new NodeList();
                }

                ReportManager.GetInstance().QueryGroups.Clear();

                // 先保存测量数据表
                foreach(UC_QueryConfig uc in _list)
                {
                    QueryGroup group = (uc.Parent as TabPage).Tag as QueryGroup;
                    if(group.id == ReportManager.MeasureGroupId)
                    {
                        uc.Save();
                        group.name = (uc.Parent as TabPage).Text;
                        ReportManager.GetInstance().QueryGroups.Add(group);
                    }
                }

                // 再保存其他数据表
                int i = 1;
                foreach(UC_QueryConfig uc in _list)
                {
                    QueryGroup group = (uc.Parent as TabPage).Tag as QueryGroup;
                    if(group.id != ReportManager.MeasureGroupId)
                    {
                        uc.Save();
                        group.id = i.ToString();
                        group.name = (uc.Parent as TabPage).Text;
                        ReportManager.GetInstance().QueryGroups.Add(group);
                        i++;
                    }
                }

                ReportManager.GetInstance().Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 关闭
        /// </summary>
        /// <param name="e"></param>
        protected override void OnClosed(EventArgs e)
        {
            ReportManager.Reload();
            base.OnClosed(e);
        }
    }
}