﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace Fpi.Entrance
{
    /// <summary>
    /// 图片转换帮助类
    /// </summary>
    public static class ImageHelper
    {
        #region 公有方法

        /// <summary>
        /// 将Base64字符串转换为Image对象并保存到文件
        /// </summary>
        /// <param name="base64String">Base64编码的图片数据</param>
        /// <param name="outputPath">输出文件路径（可选）</param>
        /// <returns>转换后的Image对象</returns>
        public static Image ConvertBase64ToImage(string base64String, string outputPath = null)
        {
            if(string.IsNullOrWhiteSpace(base64String))
            {
                throw new ArgumentException("Base64字符串不能为空");
            }

            try
            {
                // 清理可能存在的Base64前缀
                var cleanBase64 = CleanBase64String(base64String);

                // 转换Base64字符串为字节数组
                byte[] imageBytes = Convert.FromBase64String(cleanBase64);

                // 验证图片数据有效性
                ValidateImageHeader(imageBytes);

                // 创建内存流并生成Image对象
                using(var ms = new MemoryStream(imageBytes))
                {
                    var image = Image.FromStream(ms);

                    // 可选：保存到文件
                    if(!string.IsNullOrWhiteSpace(outputPath))
                    {
                        SaveImageToFile(image, outputPath);
                    }

                    // 必须创建新的Image对象，因为原始流会被释放
                    return CloneImage(image);
                }
            }
            catch(FormatException ex)
            {
                throw new ArgumentException("无效的Base64格式", ex);
            }
            catch(ArgumentException ex)
            {
                throw new ArgumentException("无效的图片数据", ex);
            }
            catch(ExternalException ex)
            {
                throw new InvalidOperationException("GDI+错误", ex);
            }
        }

        /// <summary>
        /// 自适应填充PictureBox并保持宽高比
        /// </summary>
        public static void FillPictureBox(PictureBox pictureBox, Image image)
        {
            if(pictureBox == null || image == null)
            {
                return;
            }

            // 释放旧图片资源
            pictureBox.Image?.Dispose();

            // 设置图片显示模式
            pictureBox.SizeMode = PictureBoxSizeMode.Zoom;

            // 计算最佳显示尺寸
            var containerSize = pictureBox.ClientSize;
            var imageSize = image.Size;

            // 计算缩放比例
            var ratio = Math.Min((float)containerSize.Width / imageSize.Width,
                                (float)containerSize.Height / imageSize.Height);

            // 创建新尺寸的图片
            var newSize = new Size((int)(imageSize.Width * ratio),
                                 (int)(imageSize.Height * ratio));

            // 高质量缩放
            var scaledImage = new Bitmap(newSize.Width, newSize.Height);
            using(var graphics = Graphics.FromImage(scaledImage))
            {
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.DrawImage(image, 0, 0, newSize.Width, newSize.Height);
            }

            // 设置图片并居中显示
            pictureBox.Image = scaledImage;
            pictureBox.SizeMode = PictureBoxSizeMode.CenterImage;
        }

        #endregion

        #region 私有方法

        private static string CleanBase64String(string input)
        {
            // 处理常见的Base64 URL前缀
            var index = input.IndexOf("base64,", StringComparison.Ordinal);
            return index > 0 ? input.Substring(index + 7) : input;
        }

        private static void ValidateImageHeader(byte[] imageBytes)
        {
            if(imageBytes.Length < 8)
            {
                throw new ArgumentException("图片数据过短");
            }

            // 检查常见图片格式的文件头
            if(!(IsJpeg(imageBytes) || IsPng(imageBytes) || IsGif(imageBytes) || IsBmp(imageBytes)))
            {
                throw new ArgumentException("不支持的图片格式");
            }
        }

        private static bool IsJpeg(byte[] bytes)
        {
            return bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF;
        }

        private static bool IsPng(byte[] bytes)
        {
            return bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47;
        }

        private static bool IsGif(byte[] bytes)
        {
            return bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46;
        }

        private static bool IsBmp(byte[] bytes)
        {
            return bytes[0] == 0x42 && bytes[1] == 0x4D;
        }

        private static Image CloneImage(Image sourceImage)
        {
            var bitmap = new Bitmap(sourceImage.Width, sourceImage.Height);
            using(var g = Graphics.FromImage(bitmap))
            {
                g.DrawImage(sourceImage, 0, 0);
            }
            return bitmap;
        }

        private static void SaveImageToFile(Image image, string path)
        {
            var format = GetImageFormat(image);
            image.Save(path, format);
        }

        private static ImageFormat GetImageFormat(Image image)
        {
            if(ImageFormat.Jpeg.Equals(image.RawFormat))
            {
                return ImageFormat.Jpeg;
            }

            if(ImageFormat.Png.Equals(image.RawFormat))
            {
                return ImageFormat.Png;
            }

            if(ImageFormat.Gif.Equals(image.RawFormat))
            {
                return ImageFormat.Gif;
            }

            if(ImageFormat.Bmp.Equals(image.RawFormat))
            {
                return ImageFormat.Bmp;
            }

            return ImageFormat.Png; // 默认保存为PNG
        }
    }

    #endregion
}
