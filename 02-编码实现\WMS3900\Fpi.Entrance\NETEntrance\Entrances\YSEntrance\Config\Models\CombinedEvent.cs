﻿using System;
using System.ComponentModel;

namespace Fpi.Entrance.YS.Models
{
    /// <summary>
    /// 结合人员信息与开关门状态存储类
    /// </summary>
    public class CombinedEvent
    {
        /// <summary>
        /// 智能识别终端序列号
        /// </summary>
        [Description("智能识别终端编码(智能识别终端序列号)")]
        public string DeviceSN { get; set; }

        /// <summary>
        /// 人员姓名
        /// </summary>
        [Description("人员姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 开关门状态
        /// </summary>
        [Description("开关门状态")]
        public eDoorState DoorStatus { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        [Description("时间")]
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 图片编码
        /// </summary>
        [Description("图片编码")]
        public string FaceImageNameStr { get; set; }
    }
}
