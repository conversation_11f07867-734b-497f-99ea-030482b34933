﻿/*==================================================================================================
** 类 名 称:DBConfig
** 创 建 人:xiaopeng_liu
** 当前版本：V1.0.0
** CLR 版本:4.0.30319.42000
** 创建时间:2018/4/19 16:56:20

** 修改人		修改时间		修改后版本		修改内容


** 功能描述：
 
==================================================================================================
 Copyright @2018. Focused Photonics Inc. All rights reserved.
==================================================================================================*/

namespace Fpi.HB.Business.DB
{
    public static class DBConfig
    {
        /// <summary>
        /// 数据库名称
        /// </summary>
        public static string DB_NAME = "wms3900";

        /// <summary>
        /// 分钟数据表前缀
        /// </summary>
        public const string TABLE_NAME_PRIX = "fpi_";

        /// <summary>
        /// 数据库测量项需要加上前缀，与存储数据当前数据测量项区别，值为"F"
        /// </summary>
        public const string PREFIX_F = "F";

        /// <summary>
        /// 测量项标志后缀，对应数据库字段后缀，值为"_flag"
        /// </summary>
        public const string POSTFIX = "_flag";

        /// <summary>
        /// 时间格式
        /// </summary>
        public const string DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

        /// <summary>
        /// 分钟数据表
        /// </summary>
        public const string REAL_DATA_TABLE = "fpi_sys";

        /// <summary>
        /// 日志数据表
        /// </summary>
        public const string LOG_DATA = "log";
    }
}
