﻿namespace Fpi.HB.Business.Protocols.Interface
{
    /// <summary>
    /// 数据补传任务监听接口
    /// </summary>
    public interface IDateSupplementListener
    {
        /// <summary>
        /// 补遗任务结束通知
        /// </summary>
        void OnDateSupplementDone(string info);

        /// <summary>
        /// 补遗过程信息通知
        /// </summary>
        /// <param name="index"></param>
        /// <param name="count"></param>
        void OnDateSupplement(int index, int count);
    }
}
