1、x86适配
软件USB视频模块有组件要求x86。当前测试x86下“采水点水体颜色”方法会报异常，请测试修复下。

2、接口调整
仪表部分（原2-10至2-16）调整为6个接口，每个接口有一个或多个返回值，多个返回值按bit位传输。

2.1 监控编号-1	拍摄内容：配水预处理沉砂池							
对应功能：配水预处理沉砂池脏污识别
（返回正常0、脏污1）

2.2 监控编号-2	拍摄内容：配水预处理管路							
对应功能：配水预处理管路脏污识别
（返回正常0、脏污1）

2.3 监控编号-3	拍摄内容：五参数流通池								
对应功能：五参数流通池脏污识别
（返回正常0、脏污1）

2.4 监控编号-4	拍摄内容：五参数废液桶液位								
对应功能：五参数废液桶、纯水桶、废水桶液位识别
（返回废液桶状态、废水桶状态、纯水桶状态，bit位表示。废液桶、废水桶满（大于16）置1，纯水桶空（小于4）置1）

2.5 监控编号-5	拍摄内容：高指+氨氮仪表
监控编号-7	拍摄内容：总磷+总氮仪表						
对应功能：左右两个仪表管路脏污识别、反应单元脏污识别
（图片包含左右两个仪表，返回左侧仪表管路脏污、左侧仪表反应单元脏污、右侧仪表管路脏污、右侧仪表反应单元脏污，bit位表示，各bit位正常0、脏污1）

2.6 监控编号-6	拍摄内容：高指+氨氮质控单元+纯水桶、废液桶、废水桶
监控编号-8	拍摄内容：总磷+总氮质控单元+纯水桶、废液桶、废水桶
对应功能：左右两个仪表水样管状态、标样管状态、质控水样杯脏污、质控标样杯脏污识别，下方废液桶状态、废水桶状态、纯水桶液位识别
（图片包含左右两个质控仪，下方有三个桶。返回左侧水样管状态、左侧标样管状态、左侧水样杯状态、左侧标样杯状态、 右侧水样管状态、右侧标样管状态、右侧水样杯状态、右侧标样杯状态、废液桶状态、废水桶状态、纯水桶状态，11个状态。各bit位正常0、脏污1，废液桶、废水桶满（大于16）置1，纯水桶空（小于4）置1）


其中五参数设备还未装好，设备3、4对应照片稍晚提供。
设备5、7识别内容相同，但对应仪表不同，拍照位置不同。后续会提供右侧部分仪表照片，当前先按提供的左侧部分素材做开发。
设备6、8同上。
监控设备1、2、5、6对应图像见附件。识别位置已标注。

3、接口返回值
示例：“监测站内人员异常不规范行为”，返回值中需识别三种异常行为，人员未穿工服、人员吸烟、物料乱堆放及物品遗留。
改为使用bit位形式表示状态，从低位到高位的三个bit位表示三种异常行为的触发状态。0（0000 0000）表示一切正常；1（0000 0001）表示人员未穿工服；2（0000 0010）表示人员吸烟；4（0000 0100）表示物料乱堆放及物品遗留；6（0000 0110）表示人员吸烟且物料乱堆放及物品遗留；按这个逻辑。

其他接口（2.4、2.5、2.6）中，类似多个检测内容的，都按这种形式组织返回值。
