using System;
using Fpi.Communication.Ports.SyncPorts;
using Fpi.Communication.Ports.SyncPorts.ResendKeys;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Reflection;
using Fpi.Xml;

namespace Fpi.Communication.Ports.UI.PC
{
    public partial class UC_SyncPort : BaseConfigureView//UserControl
    {
        public UC_SyncPort()
        {
            InitializeComponent();
            BindUI();
        }

        private void BindUI()
        {
            this.cmbKey.Items.Add(string.Empty);

            Type[] keyList = ReflectionHelper.GetChildTypes(typeof(IResendKey));
            foreach(Type type in keyList)
            {
                this.cmbKey.Items.Add(type.FullName);
            }
        }

        protected override void ShowConfig(object obj)
        {
            BaseNode configNode = configObj as BaseNode;

            //this.lblKey.Visible = false;
            //this.cmbKey.Visible = false;

            string key = configNode.GetPropertyValue(SyncPort.PropertyName_ResendKey, string.Empty);
            string timeOut = configNode.GetPropertyValue(SyncPort.PropertyName_Timeout, SyncPort.DEFAULT_TIMEOUT.ToString());
            string tryTimes = configNode.GetPropertyValue(SyncPort.PropertyName_TryTimes, SyncPort.DEFAULT_TRYTIMES.ToString());

            this.nuTimeout.Value = int.Parse(timeOut);
            this.nuTrytimes.Value = int.Parse(tryTimes);
            this.cmbKey.SelectedItem = key;
        }

        public override object Save()
        {
            BaseNode configNode = configObj as BaseNode;

            string key = (this.cmbKey.SelectedItem == null ? string.Empty : this.cmbKey.SelectedItem.ToString());
            string timeout = this.nuTimeout.Value.ToString();
            string tryTimes = this.nuTrytimes.Value.ToString();

            configNode.SetProperty(SyncPort.PropertyName_ResendKey, key);
            configNode.SetProperty(SyncPort.PropertyName_Timeout, timeout);
            configNode.SetProperty(SyncPort.PropertyName_TryTimes, tryTimes);

            configNode.Save();

            return configObj;
        }
    }
}