﻿using System;
using System.Windows.Forms;
using Fpi.Communication.Manager;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.Util.ThreadRelated;
using Sunny.UI;

namespace Fpi.HB.Business.Protocols
{
    public partial class FrmDataSupplement : UIForm, IDateSupplementListener
    {
        #region 字段属性

        /// <summary>
        /// 数据补传控制模块
        /// </summary>
        private readonly SupplementController _supplementController = new SupplementController();

        /// <summary>
        /// 上传数据类型
        /// </summary>
        private Type uploadDataType;

        #endregion

        #region 构造

        public FrmDataSupplement()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void DataSupplement_Load(object sender, EventArgs e)
        {
            this.dtpStartTime.Value = DateTime.Today;
            this.dtpEndTime.Value = DateTime.Now;
            // 获取有效出通道
            foreach(Pipe pipe in PortManager.GetInstance().pipes)
            {
                // 通道启用，且所用协议实现了IGetSuppleData接口
                if(pipe.valid && pipe.Protocol != null && pipe.Protocol.Sender is IGetSuppleData)
                {
                    this.cmbPipeList.Items.Add(pipe);
                }
            }
            //EnumOperate.BandEnumToCmb(this.cmbUploadDataType, typeof(eUploadDataType));
            //this.cmbUploadDataType.SelectedIndex = -1;
            this.cmbPipeList.SelectedIndex = -1;
        }

        /// <summary>
        /// 开始补传
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSupplement_Click(object sender, EventArgs e)
        {
            try
            {
                if(btnSupplement.Text == eSupplementType.停止补传.ToString())
                {
                    _supplementController.StopSupple();
                    btnSupplement.Enabled = false;
                    FpiMessageBox.ShowInfo("触发停止成功，请等待...");
                }
                else
                {
                    // 检查补传参数是否合法
                    if(this.cmbPipeList.SelectedItem == null)
                    {
                        throw new Exception("请选择有效数据传输通道！");
                    }
                    if(this.dtpStartTime.Value >= this.dtpEndTime.Value)
                    {
                        throw new Exception("起始时间不可晚于结束时间！");
                    }
              
                    ChangeControlState(eSupplementType.开始补传);
                    txtSuppleInfo.Text = @"开始补传...";

                    // 触发补遗任务执行
                    _supplementController.StartSuppleAsync((Pipe)this.cmbPipeList.SelectedItem, this.dtpStartTime.Value, this.dtpEndTime.Value, (int)this.cmbUploadDataType.SelectedValue, this, int.Parse(txtSendInterval.Text));

                    FpiMessageBox.ShowInfo("触发开始补传成功，请等待程序执行...");
                }
            }
            catch(Exception ex)
            {
                string errorInfo = "补传控制失败：" + ex.Message;
                txtSuppleInfo.Text += errorInfo;
                FpiMessageBox.ShowError(errorInfo);

                // 触发开始补传出错时，任务还没开始执行，故需手动恢复控件状态
                ChangeControlState(eSupplementType.停止补传);
            }
        }

        /// <summary>
        /// 关闭界面
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DataSupplement_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 补遗任务执行中，退出需确认
            if(_supplementController.IsRunning)
            {
                if(FpiMessageBox.ShowQuestion("退出本页面会停止当前补传任务，确认退出？") == DialogResult.Yes)
                {
                    _supplementController.StopSupple();
                }
                else
                {
                    e.Cancel = true;
                }
            }
        }

        /// <summary>
        /// 通道选择变更，显示协议跟着变更
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cmbPipeList_SelectedIndexChanged(object sender, EventArgs e)
        {
            txtProtocolName.Text = string.Empty;
            var pipe = cmbPipeList.SelectedItem as Pipe;
            txtProtocolName.Text = pipe.Protocol.FriendlyName;

            uploadDataType = (pipe.Protocol.Sender as IDataUpload).UploadDataType;
            cmbUploadDataType.Items.Clear();

            if(uploadDataType.IsEnum)
            {
                EnumOperate.BandEnumToCmb(cmbUploadDataType, uploadDataType);
            }

            //txtProtocolName.Text = cmbPipeList.SelectedItem is Pipe ? ((Pipe)cmbPipeList.SelectedItem).Protocol.FriendlyName : string.Empty;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 开始/结束任务时修改控件状态
        /// </summary>
        /// <param name="type"></param>
        private void ChangeControlState(eSupplementType type)
        {
            if(type == eSupplementType.开始补传)
            {
                ThreadHelper.UpdateControlText(btnSupplement, eSupplementType.停止补传.ToString());
                ThreadHelper.UpdateControlEnable(pnlParamConfig, false);
                OnDateSupplement(0, 0);
            }
            else
            {
                ThreadHelper.UpdateControlEnable(btnSupplement, true);
                ThreadHelper.UpdateControlText(btnSupplement, eSupplementType.开始补传.ToString());
                ThreadHelper.UpdateControlEnable(pnlParamConfig, true);
            }
        }

        /// <summary>
        /// 数据补传结果通知
        /// </summary>
        public void OnDateSupplementDone(string info)
        {
            ChangeControlState(eSupplementType.停止补传);

            // 添加日志信息
            ThreadHelper.AddControlText(txtSuppleInfo, info);
        }

        /// <summary>
        /// 数据补传过程通知
        /// </summary>
        /// <param name="index"></param>
        /// <param name="count"></param>
        public void OnDateSupplement(int index, int count)
        {
            try
            {
                UpdateProgressBarXValue(prgUpload, index, count);
            }
            catch(Exception ex)
            {
                // 添加日志信息
                ThreadHelper.AddControlText(txtSuppleInfo, ex.Message);
            }
        }

        /// <summary>
        /// 更新进度条委托
        /// </summary>
        /// <param name="con"></param>
        /// <param name="index"></param>
        /// <param name="count"></param>
        private delegate void UpdatePro(UIProcessBar con, int index, int count);

        /// <summary>
        /// 跨线程更新ProgressBarX控件值
        /// </summary>
        /// <param name="ctrl"></param>
        /// <param name="index"></param>
        /// <param name="count"></param>
        private void UpdateProgressBarXValue(UIProcessBar ctrl, int index, int count)
        {
            if(ctrl.InvokeRequired)
            {
                this.Invoke(new UpdatePro(UpdateProgressBarXValue), ctrl, index, count);
            }
            else
            {
                ctrl.Maximum = count;
                ctrl.Value = index;
                ctrl.Text = index + "/" + count;
            }
        }

        #endregion

        #region 私有资源

        /// <summary>
        /// 补传类型
        /// </summary>
        private enum eSupplementType
        {
            开始补传,

            停止补传
        }

        #endregion
    }
}