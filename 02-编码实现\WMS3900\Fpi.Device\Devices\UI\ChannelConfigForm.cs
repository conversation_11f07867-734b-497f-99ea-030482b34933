﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Devices.Channel;
using Fpi.UI.Common.PC;
using Fpi.Xml;

namespace Fpi.Devices.UI
{
    public partial class ChannelConfigForm : Form
    {
        private eDeviceChannelType _curChlType = eDeviceChannelType.InValue;
        private NodeList _channelList = new NodeList();

        //��ͼƬ��С��䷽ʽ����LV�߶�
        private ImageList _tempList = new ImageList();

        private ChannelConfigForm()
        {
            InitializeComponent();
        }

        public ChannelConfigForm(NodeList chlList, eDeviceChannelType chlType)
            : this()
        {
            this._channelList = chlList;
            this._curChlType = chlType;
            this._tempList.ImageSize = new Size(1, 18);
        }

        private void FormConfigIO_Load(object sender, EventArgs e)
        {
            this.InitDevicesChannel();
        }

        private void InitDevicesChannel()
        {
            if(this._channelList != null)
            {
                switch(this._curChlType)
                {
                    case eDeviceChannelType.InValue:

                        this.lvInValue.Visible = true;
                        this.lvInValue.SmallImageList = this._tempList;
                        this.lvOutValue.Visible = false;
                        this.lvInSwitch.Visible = false;
                        this.lvOutSwitch.Visible = false;

                        foreach(DataChannel chl in this._channelList)
                        {
                            ListViewItem lv = new ListViewItem(chl.id);
                            lv.SubItems.Add(chl.name);

                            if(chl is InValueChannel inValueChl)
                            {
                                if(inValueChl.ValueNode != null)
                                {
                                    lv.SubItems.Add(inValueChl.ValueNode.name);
                                    lv.SubItems.Add(inValueChl.UnitId);
                                }
                                else
                                {
                                    lv.SubItems.Add(string.Empty);
                                    lv.SubItems.Add(string.Empty);
                                }
                            }

                            lv.Tag = chl;
                            this.lvInValue.Items.Add(lv);
                        }

                        break;
                    case eDeviceChannelType.OutValue:

                        this.lvInValue.Visible = false;
                        this.lvOutValue.Visible = true;
                        this.lvOutValue.SmallImageList = this._tempList;
                        this.lvInSwitch.Visible = false;
                        this.lvOutSwitch.Visible = false;

                        foreach(DataChannel chl in this._channelList)
                        {
                            ListViewItem lv = new ListViewItem(chl.id);
                            lv.SubItems.Add(chl.name);

                            if(chl is OutValueChannel outValueChl)
                            {
                                if(outValueChl.ValueNode != null)
                                {
                                    lv.SubItems.Add(outValueChl.ValueNode.name);
                                    lv.SubItems.Add(outValueChl.UnitId);
                                }
                                else
                                {
                                    lv.SubItems.Add(string.Empty);
                                    lv.SubItems.Add(string.Empty);
                                }
                            }
                            lv.Tag = chl;
                            this.lvOutValue.Items.Add(lv);
                        }

                        break;
                    case eDeviceChannelType.InSwitch:

                        this.lvInValue.Visible = false;
                        this.lvOutValue.Visible = false;
                        this.lvInSwitch.Visible = true;
                        this.lvInSwitch.SmallImageList = this._tempList;
                        this.lvOutSwitch.Visible = false;

                        foreach(DataChannel chl in this._channelList)
                        {
                            ListViewItem lv = new ListViewItem(chl.id);
                            lv.SubItems.Add(chl.name);

                            if(chl is InSwitchChannel inSwitchChl)
                            {
                                if(inSwitchChl.StateNode != null)
                                {
                                    lv.SubItems.Add(inSwitchChl.StateNode.name);
                                }
                                else
                                {
                                    lv.SubItems.Add(string.Empty);
                                }
                            }
                            lv.Tag = chl;
                            this.lvInSwitch.Items.Add(lv);
                        }

                        break;
                    case eDeviceChannelType.OutSwitch:

                        this.lvInValue.Visible = false;
                        this.lvOutValue.Visible = false;
                        this.lvInSwitch.Visible = false;
                        this.lvOutSwitch.Visible = true;
                        this.lvOutSwitch.SmallImageList = this._tempList;

                        foreach(DataChannel chl in this._channelList)
                        {
                            ListViewItem lv = new ListViewItem(chl.id);
                            lv.SubItems.Add(chl.name);

                            if(chl is OutSwitchChannel outSwitchChl)
                            {
                                if(outSwitchChl.StateNode != null)
                                {
                                    lv.SubItems.Add(outSwitchChl.StateNode.name);
                                }
                                else
                                {
                                    lv.SubItems.Add(string.Empty);
                                }
                            }
                            lv.Tag = chl;
                            this.lvOutSwitch.Items.Add(lv);
                        }

                        break;
                }
            }
        }

        private void ShowChannelItem(Fpi.Xml.NodeList nodeList, ListView listView)
        {
            //if (nodeList == null)
            //{
            //    return;
            //}

            //ListViewItem lvi = null;
            //VarNode var = null;

            //foreach (DeviceChannel dc in nodeList)
            //{
            //    var = dc.GetVarNode();
            //    if (dc is Simulate)
            //    {
            //        Simulate sim = dc as Simulate;
            //        Unit unit = sim.GetUnit(); 

            //        lvi = new ListViewItem(new string[] { 
            //            sim.id,
            //            sim.name,
            //            (var != null ? var.name : null),
            //            (unit != null ? unit.name : null)
            //        });
            //    }
            //    else if (dc is Switch)
            //    {
            //        Switch swi = dc as Switch;
            //        lvi = new ListViewItem(new string[] { 
            //            swi.id,
            //            swi.name,
            //            (var != null ? var.name : null)
            //        });
            //    }

            //    if (lvi != null)
            //    {
            //        lvi.Tag = dc;
            //        listView.Items.Add(lvi);
            //    }
            //}
        }

        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ListView view = sender as ListView;
            ListViewItem lvi = view.GetItemAt(e.X, e.Y);
            VarNode var;

            if(lvi != null)
            {
                DataChannel chl = lvi.Tag as DataChannel;
                if(chl is ValueChannel valChl)
                {
                    EditValueChannelForm form = new EditValueChannelForm(ref valChl);
                    if(form.ShowDialog() == DialogResult.OK)
                    {
                        var = chl.GetVarNode();
                        Unit unit = valChl.GetUnit();
                        lvi.SubItems[2].Text = (var != null ? var.name : null);
                        lvi.SubItems[3].Text = (unit != null ? unit.name : null);

                        //if (var != null)
                        //{
                        //    valChl.VarNodeId = var.id;
                        //    valChl.UnitId = (var as ValueNode).selfUSitId;
                        //}
                        //else
                        //{
                        //    valChl.VarNodeId = string.Empty;
                        //    valChl.UnitId = string.Empty;
                        //}
                    }
                }
                else if(chl is SwitchChannel swiChl)
                {
                    EditSwitchChannelForm form = new EditSwitchChannelForm(swiChl);
                    if(form.ShowDialog() == DialogResult.OK)
                    {
                        var = chl.GetVarNode();
                        lvi.SubItems[2].Text = (var != null ? var.name : null);


                        swiChl.VarNodeId = var != null ? var.id : string.Empty;
                    }
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        // begin modify by wuwei 2022/7/6 增加对通道配置页面中新增和移除通道功能
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            DataChannel dataChannel = GenerateNewChannel();
            int channelIdx = GetCurrentListView().Items.Count + 1;

            dataChannel.id = $"{dataChannel.ChannelType}Channel_{channelIdx:D2}";

            if(_channelList != null && _channelList.GetCount() > 0)
            {
                IdNameNode dataChannel1 = _channelList.FindNode(dataChannel.id);
                if(null != dataChannel1)
                {
                    channelIdx = channelIdx + 1;
                    dataChannel.id = $"{dataChannel.ChannelType}Channel_{channelIdx:D2}";
                }
            }
            dataChannel.name = $"通道{channelIdx}";
            switch(dataChannel.ChannelType)
            {
                case eDeviceChannelType.InValue:
                    dataChannel.name = $"数值输入通道{channelIdx:D2}";
                    break;
                case eDeviceChannelType.OutValue:
                    dataChannel.name = $"数值输出通道{channelIdx:D2}";
                    break;
                case eDeviceChannelType.InSwitch:
                    dataChannel.name = $"开关量输入通道{channelIdx:D2}";
                    break;
                case eDeviceChannelType.OutSwitch:
                    dataChannel.name = $"开关量输出通道{channelIdx:D2}";
                    break;

            }


            //打开页面
            if(GetEditDataChannelForm(dataChannel).ShowDialog() == DialogResult.OK)
            {
                _channelList.Add(dataChannel);
            }
            UpdateCurrentListViewContent();
        }
        /// <summary>
        /// 根据当前通道数据类型创建对应的数据通道
        /// </summary>
        /// <returns></returns>
        private DataChannel GenerateNewChannel()
        {
            return DataChannelFactory.CreateNewChannel(this._curChlType);
        }


        /// <summary>
        /// 获取ListView 中的Item
        /// </summary>
        /// <param name="channel"></param>
        /// <returns></returns>
        private ListViewItem GetListViewItem(DataChannel channel)
        {
            ListViewItem viewItem = new ListViewItem(channel.id);
            viewItem.SubItems.Add(channel.name);
            viewItem.Tag = channel;
            if(this._curChlType == eDeviceChannelType.InValue || this._curChlType == eDeviceChannelType.OutValue)
            {
                if(channel is ValueChannel inValueChl && inValueChl.ValueNode != null)
                {
                    viewItem.SubItems.Add(inValueChl.ValueNode.name);
                    viewItem.SubItems.Add(inValueChl.UnitId);
                }
            }
            else if(this._curChlType == eDeviceChannelType.InSwitch || this._curChlType == eDeviceChannelType.OutSwitch)
            {
                if(channel is SwitchChannel inSwitchChl && inSwitchChl.StateNode != null)
                {
                    viewItem.SubItems.Add(inSwitchChl.StateNode.name);
                }
            }
            return viewItem;
        }
        /// <summary>
        /// 更新当前页面ListView中的内容
        /// </summary>
        private void UpdateCurrentListViewContent()
        {
            //这里最好做成数据绑定，这样在修改时直接反应到界面
            ListView listView = GetCurrentListView();
            listView.Items.Clear();
            foreach(DataChannel channel in this._channelList)
            {
                listView.Items.Add(GetListViewItem(channel));
            }

        }
        /// <summary>
        /// 获取当前页面的Listview
        /// </summary>
        /// <returns></returns>
        private ListView GetCurrentListView()
        {
            var dic = GetListViewDic();
            return dic[this._curChlType];
        }
        /// <summary>
        /// ListView对应的通道数据类型
        /// </summary>
        /// <returns></returns>
        private Dictionary<eDeviceChannelType, ListView> GetListViewDic()
        {
            return new Dictionary<eDeviceChannelType, ListView>()
            {
                { eDeviceChannelType.InSwitch,lvInSwitch },
                { eDeviceChannelType.OutSwitch,lvOutSwitch },
                { eDeviceChannelType.InValue,lvInValue },
                { eDeviceChannelType.OutValue,lvOutValue },
            };
        }

        /// <summary>
        /// 获取通道编辑页面
        /// </summary>
        /// <param name="channel"></param>
        /// <returns></returns>
        private Form GetEditDataChannelForm(DataChannel channel)
        {
            return channel is ValueChannel valChl ? new EditValueChannelForm(ref valChl) : (Form)new EditSwitchChannelForm(channel as SwitchChannel);

        }
        /// <summary>
        /// 移除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRemove_Click(object sender, EventArgs e)
        {
            if(GetCurrentListView().SelectedItems.Count > 0
               && FpiMessageBox.ShowQuestion("移除所选通道？") == DialogResult.Yes)
            {
                var selectChannel = GetCurrentListView().SelectedItems[0].Tag as DataChannel;
                _channelList.Remove(selectChannel);
                UpdateCurrentListViewContent();
            }
        }
    }
}