﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;

namespace Fpi.DB
{
    /*
     * pan_xu modify this file(2012.11.23)
     * Clear all methods, then add following methods:
     * 1. ExecuteNonQuery
     * 2. ExecuteQuery
     * 3. ExecuteQueryReturnDataReader
     * 4. ExecuteQueryReturnFloatValues
     * 5. QueryRecordCount
     */

    /// <summary>
    /// 数据库访问类
    /// </summary>
    public sealed class DbAccess
    {
        /// <summary>
        /// 执行SQL语句，返回影响的行数
        /// </summary>
        /// <param name="commandText">语句</param>
        /// <returns></returns>
        public static int BatExecuteNonQuery(List<string> commandTextList)
        {
            int rows = 0;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            IDbTransaction trans = null;
            try
            {
                conn.Open();
                trans = conn.BeginTransaction();
                foreach(string commandText in commandTextList)
                {
                    IDbCommand command = conn.CreateCommand();
                    command.CommandText = commandText;
                    command.Transaction = trans;
                    rows = command.ExecuteNonQuery();
                }
                trans.Commit();
            }
            catch
            {
                if(trans != null)
                {
                    trans.Rollback();
                }
            }
            finally
            {
                try
                {
                    conn.Close();
                }
                catch
                {
                }
            }

            return rows;
        }

        /// <summary>
        /// 执行SQL语句，返回影响的行数
        /// </summary>
        /// <param name="commandText">语句</param>
        /// <returns></returns>
        public static int ExecuteNonQuery(string commandText)
        {
            int rows = 0;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();
                IDbCommand command = conn.CreateCommand();
                command.CommandText = commandText;
                rows = command.ExecuteNonQuery();
            }
            //catch(Exception ex)
            //{
            //    LogUtil.Debug("exception", commandText.Replace('\'', '\"') + "\r\n" + ex.Message);
            //}
            finally
            {
                try
                {
                    conn.Close();
                }
                catch
                {
                }
            }
            return rows;
        }

        /// <summary>
        /// 执行SQL语句，返回影响的行数(参数化)
        /// </summary>
        /// <param name="commandText"></param>
        /// <param name="paramNames"></param>
        /// <param name="paramValues"></param>
        /// <returns></returns>
        public static int ExecuteNonQuery(string commandText, string[] paramNames, object[] paramValues)
        {
            int rows = 0;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();
                IDbCommand command = conn.CreateCommand();
                command.CommandText = commandText;
                for(int i = 0; i < paramNames.Length; i++)
                {
                    IDataParameter param = DbFactory.GetInstance().CreateDataParameter();
                    param.ParameterName = paramNames[i];
                    param.Value = paramValues[i];
                    command.Parameters.Add(param);
                }
                rows = command.ExecuteNonQuery();
            }
            finally
            {
                try
                {
                    conn.Close();
                }
                catch
                {
                }
            }
            return rows;
        }

        /// <summary>
        /// 执行SQL语句，返回影响的行数(参数化)
        /// </summary>
        /// <param name="commandText"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static int ExecuteNonQuery(string commandText, IDataParameter[] parameters)
        {
            int rows = 0;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();
                IDbCommand command = conn.CreateCommand();
                command.CommandText = commandText;
                for(int i = 0; i < parameters.Length; i++)
                {
                    command.Parameters.Add(parameters[i]);
                }
                rows = command.ExecuteNonQuery();
            }
            finally
            {
                try
                {
                    conn.Close();
                }
                catch
                {
                }
            }
            return rows;
        }

        /// <summary>
        /// 执行SQL语句，返回影响的行数(指定事务)
        /// </summary>
        /// <param name="tran"></param>
        /// <param name="commandText"></param>
        /// <returns></returns>
        public static int ExecuteNonQuery(IDbTransaction tran, string commandText)
        {
            int rows = 0;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();

                //构造DbCommand
                IDbCommand command = conn.CreateCommand();
                if(tran != null)
                {
                    command.Transaction = tran;
                }
                command.CommandText = commandText;

                rows = command.ExecuteNonQuery();
            }
            finally
            {
                if(conn != null)
                {
                    conn.Close();
                }
            }
            return rows;
        }

        /// <summary>
        /// 执行SQL语句，返回影响的行数(指定事务和参数)
        /// </summary>
        /// <param name="tran"></param>
        /// <param name="commandText"></param>
        /// <param name="cmdParms"></param>
        /// <returns></returns>
        public static int ExecuteNonQuery(IDbTransaction tran, string commandText, IDataParameter[] cmdParms)
        {
            int rows = 0;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();

                //构造DbCommand
                IDbCommand command = conn.CreateCommand();
                if(tran != null)
                {
                    command.Transaction = tran;
                }
                command.CommandText = commandText;
                foreach(IDataParameter parm in cmdParms)
                {
                    command.Parameters.Add(parm);
                }
                rows = command.ExecuteNonQuery();
            }
            finally
            {
                if(conn != null)
                {
                    conn.Close();
                }
            }
            return rows;
        }

        /// <summary>
        /// 执行查询操作
        /// </summary>
        public static DataTable ExecuteQuery(string commandText)
        {
            DataSet dataSet = new DataSet();
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();
                IDbCommand command = conn.CreateCommand();
                command.CommandText = commandText;
                IDbDataAdapter adapter = DbFactory.GetInstance().CreateDataAdapter();
                adapter.SelectCommand = command;
                adapter.Fill(dataSet);

                return dataSet.Tables[0];
            }
            catch
            {
                return null;
            }
            finally
            {
                try
                {
                    conn.Close();
                }
                catch
                {

                }
            }
        }

        /// <summary>
        /// 执行SQL查询语句，返回DataReader(调用后务必关闭DataReader)
        /// </summary>
        public static IDataReader ExecuteQueryReturnDataReader(string commandText)
        {
            IDataReader reader = null;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();
                IDbCommand command = conn.CreateCommand();
                command.CommandText = commandText;
                reader = command.ExecuteReader(CommandBehavior.CloseConnection);
                return reader;
            }
            catch(Exception ex)
            {
                try
                {
                    if(reader != null)
                    {
                        reader.Close();
                    }
                    else
                    {
                        conn.Close();
                    }
                }
                catch
                {
                }

                return null;
            }
        }

        /// <summary>
        /// 执行SQL查询语句，返回DataReader
        /// </summary>
        /// <param name="commandText">Sql命令</param>
        /// <param name="paramNames">参数名称</param>
        /// <param name="paramValues">参数值</param>
        /// <returns>结果对象</returns>
        public static IDataReader ExecuteQueryReturnDataReader(string commandText, string[] paramNames, object[] paramValues)
        {
            IDataReader reader = null;
            IDbConnection conn = DbFactory.GetInstance().CreateConnection();
            try
            {
                conn.Open();
                //构造DbCommand
                IDbCommand command = conn.CreateCommand();
                command.CommandText = commandText;
                for(int i = 0; i < paramNames.Length; i++)
                {
                    IDataParameter param = DbFactory.GetInstance().CreateDataParameter();
                    param.ParameterName = paramNames[i];
                    param.Value = paramValues[i];
                    command.Parameters.Add(param);
                }
                reader = command.ExecuteReader(CommandBehavior.CloseConnection);
            }
            catch
            {
                try
                {
                    if(reader != null)
                    {
                        reader.Close();
                    }
                    else
                    {
                        conn.Close();
                    }
                }
                catch
                {
                }

                return null;
            }
            return reader;
        }

        /// <summary>
        /// 执行SQL查询语句，返回一维单精度浮点型数组
        /// </summary>
        public static float[] ExecuteQueryReturnFloatValues(string strSql)
        {
            IDataReader reader = ExecuteQueryReturnDataReader(strSql);
            if(null == reader)
            {
                return null;
            }
            try
            {
                if(reader.Read())
                {
                    float[] values = new float[reader.FieldCount];
                    for(int i = 0; i < reader.FieldCount; i++)
                    {
                        try
                        {
                            values[i] = reader.GetFloat(i);
                        }
                        catch
                        {
                            values[i] = 0.0f;
                        }
                    }
                    return values;
                }
            }
            catch
            {
                return null;
            }
            finally
            {
                reader.Close();
                reader = null;
            }
            return null;
        }

        /// <summary>
        /// 查询记录条数
        /// </summary>
        public static int QueryRecordCount(string tableName, string where)
        {
            string strSql = string.Format("SELECT COUNT(*) FROM {0}{1}", tableName, string.IsNullOrEmpty(where) ? "" : " WHERE " + where);
            return QueryRecordCount(strSql);
        }

        /// <summary>
        /// 查询记录条数
        /// </summary>
        public static int QueryRecordCount(string strSql)
        {
            DataTable dt = ExecuteQuery(strSql);
            return dt == null ? 0 : Convert.ToInt32(dt.Rows[0][0].ToString());
        }

        public static bool HaveData(string tableName, string beginTime, string endTime)
        {
            string strSql = "select count(*) from " + tableName + " where datatime>='" + beginTime + "' and datatime<='" + endTime + "'";
            IDataReader reader = ExecuteQueryReturnDataReader(strSql);
            if(reader == null)
            {
                return false;
            }

            try
            {
                if(reader.Read())
                {
                    int count = reader.GetInt32(0);
                    return !(count == 0);
                }
            }
            catch(Exception)
            {
                return false;
            }
            finally
            {
                reader.Close();
                reader = null;
            }

            return false;
        }

        /// <summary>
        /// This function checks for any sleeping connections beyond a reasonable time and kills them.
        /// Since .NET appears to have a bug with how pooling MySQL connections are handled and leaves
        /// too many sleeping connections without closing them, we will kill them here.
        /// </summary>
        /// iMinSecondsToExpire - all connections sleeping more than this amount in seconds will be killed.
        /// <returns>integer - number of connections killed</returns>
        public static int KillSleepingConnections(int iMinSecondsToExpire)
        {
            string strSQL = "show processlist";
            ArrayList m_ProcessesToKill = new ArrayList();

            IDbConnection myConn = DbFactory.GetInstance().CreateConnection();

            IDataReader MyReader = null;

            try
            {
                myConn.Open();

                IDbCommand myCmd = myConn.CreateCommand();
                myCmd.CommandText = strSQL;

                // Get a list of processes to kill.
                MyReader = myCmd.ExecuteReader();
                while(MyReader.Read())
                {
                    // Find all processes sleeping with a timeout value higher than our threshold.
                    int iPID = Convert.ToInt32(MyReader["Id"].ToString());
                    string strState = MyReader["Command"].ToString();
                    int iTime = Convert.ToInt32(MyReader["Time"].ToString());

                    if(strState == "Sleep" && iTime >= iMinSecondsToExpire && iPID > 0)
                    {
                        // This connection is sitting around doing nothing. Kill it.
                        m_ProcessesToKill.Add(iPID);
                    }
                }

                MyReader.Close();

                foreach(int aPID in m_ProcessesToKill)
                {
                    strSQL = "kill " + aPID;
                    myCmd.CommandText = strSQL;
                    myCmd.ExecuteNonQuery();
                }
            }
            catch(Exception excep)
            {
            }
            finally
            {
                if(MyReader != null && !MyReader.IsClosed)
                {
                    MyReader.Close();
                }

                if(myConn != null && myConn.State == ConnectionState.Open)
                {
                    myConn.Close();
                }
            }

            return m_ProcessesToKill.Count;
        }
    }
}
