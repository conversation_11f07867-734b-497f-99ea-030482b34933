{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm", "ProjectFileName": "Fpi.Alarm.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Alarm\\AlarmConfig\\AlarmCode.cs"}, {"SourceFile": "Alarm\\AlarmConfig\\AlarmGrade.cs"}, {"SourceFile": "Alarm\\AlarmConfig\\AlarmGroup.cs"}, {"SourceFile": "Alarm\\AlarmConfig\\AlarmManager.cs"}, {"SourceFile": "Alarm\\AlarmConfig\\AlarmSource.cs"}, {"SourceFile": "Alarm\\AlarmInfo.cs"}, {"SourceFile": "Alarm\\AlarmSave\\DbSaveAlarm.cs"}, {"SourceFile": "Alarm\\AlarmSave\\ISaveAlarm.cs"}, {"SourceFile": "Alarm\\AlarmState.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FrmQueryHistoryAlarm.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FrmQueryHistoryAlarm.Designer.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FrmQueryCurrentAlarm.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FrmQueryCurrentAlarm.Designer.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FrmConfigAlarm.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FrmConfigAlarm.Designer.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmCode.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmCode.Designer.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmGrade.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmGrade.Designer.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmGroup.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmGroup.Designer.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmSource.cs"}, {"SourceFile": "Alarm\\UI\\PC\\FormEditAlarmSource.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\WinFormsUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll", "OutputItemRelativePath": "Fpi.Alarm.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}