﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Fpi.DB.SqlUtil;

namespace Fpi.DB.Manager
{
    /// <summary>
    /// 数据库表类
    /// </summary>
    public class FpiTable
    {
        #region 字段属性

        //private DBType dbtype = DBType.odbc;

        /// <summary>
        /// 表名称
        /// </summary>
        public string TableName { get; set; }

        public List<FpiColumn> FpiColumns { get; set; } = new List<FpiColumn>();

        public List<FpiRow> FpiRows { get; set; } = new List<FpiRow>();

        #endregion

        #region 构造

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="tablename"></param>
        public FpiTable(string tablename)
        {
            TableName = tablename;
        }

        #endregion

        #region 建表

        /// <summary>
        /// 删除表
        /// </summary>
        public void DropTable()
        {
            var sb = new StringBuilder();
            sb.Append("drop table ").Append(TableName);
            try
            {
                DbAccess.ExecuteNonQuery(sb.ToString());
            }
            catch
            {
            }
        }

        /// <summary>
        /// 创建表
        /// </summary>
        public void CreateTable()
        {
            // 如果还没建库，先新建数据库
            if(!DataBaseManager.GetInstance().HasCreateDB)
            {
                DataBaseManager.GetInstance().HasCreateDB = DataBaseManager.GetInstance().InitDataBase();
            }

            switch(DbFactory.DBType)
            {
                case DBType.odbc:
                    CreateMysqlTable();
                    break;
                case DBType.mysql:
                    CreateMysqlTable();
                    break;
                case DBType.mssql:
                    CreateMssqlTable();
                    break;
                case DBType.sqlite:
                    CreateSqliteTable();
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 创建sqlite表
        /// </summary>
        private void CreateSqliteTable()
        {
            #region 建表

            StringBuilder sb = new StringBuilder("CREATE TABLE IF NOT EXISTS ").Append(TableName);
            sb.Append("(");
            var primaryKeyColumns = new List<string>();
            var indexList = new List<string>();
            var indexColumnList = new Dictionary<string, string>();
            foreach(FpiColumn column in this.FpiColumns)
            {
                switch(column.Type)
                {
                    case ColumnType.Datetime:
                        sb.Append("`" + column.Name + "`" + " DATETIME");
                        break;
                    case ColumnType.Double:
                        sb.Append("`" + column.Name + "`" + " DOUBLE");
                        break;
                    case ColumnType.Float:
                        sb.Append("`" + column.Name + "`" + " FLOAT");
                        break;
                    case ColumnType.Int:
                        sb.Append("`" + column.Name + "`" + " INTEGER");
                        break;
                    case ColumnType.Tinyint:
                        sb.Append("`" + column.Name + "`" + " TINYINT");
                        break;
                    case ColumnType.Varchar:
                        sb.Append("`" + column.Name + "`" + " VARCHAR(" + column.Length + ")");
                        break;
                    case ColumnType.Text:
                        sb.Append("`" + column.Name + "`" + " TEXT");
                        break;
                    case ColumnType.LongText:
                        sb.Append("`" + column.Name + "`" + " TEXT");
                        break;
                }

                if(!column.CanNull)
                {
                    sb.Append(" NOT NULL");
                }

                // AUTOINCREMENT列只能单独作为主键
                if(column.AutoColumn && column.PrimaryKey)
                {
                    sb.Append(" PRIMARY KEY AUTOINCREMENT");
                }
                // 未与AUTOINCREMENT联用的主键可能是复合主键
                else if(column.PrimaryKey)
                {
                    primaryKeyColumns.Add(column.Name);
                }

                sb.Append(",");
                // 索引
                if(!string.IsNullOrEmpty(column.IndexName))
                {
                    if(!indexList.Contains(column.IndexName))
                    {
                        indexList.Add(column.IndexName);
                    }

                    indexColumnList.Add(column.Name, column.IndexName);
                }
            }

            // 主键
            var sbkey = new StringBuilder();
            if(primaryKeyColumns.Count > 0)
            {
                sbkey.Append("primary key(");
                foreach(string keycol in primaryKeyColumns)
                {
                    sbkey.Append(keycol);
                    sbkey.Append(",");
                }

                sbkey = sbkey.Remove(sbkey.Length - 1, 1);
                sbkey.Append(")");
            }

            if(sbkey.Length > 0)
            {
                sb.Append(sbkey);
                sb.Append(",");
            }

            sb = sb.Remove(sb.Length - 1, 1);
            sb.Append(") ");
            try
            {
                DbAccess.ExecuteNonQuery(sb.ToString());
            }
            catch
            {
            }

            // 建立各索引
            var indexStrList = new List<string>();
            foreach(string indexName in indexList)
            {
                var sbindex = new StringBuilder();
                sbindex.Append("CREATE INDEX IF NOT EXISTS ").Append(indexName).Append(" ON ").Append(TableName)
                    .Append(" (");
                foreach(KeyValuePair<string, string> indexcol in indexColumnList)
                {
                    if(indexcol.Value == indexName)
                    {
                        sbindex.Append(indexcol.Key);
                        sbindex.Append(",");
                    }
                }

                sbindex = sbindex.Remove(sbindex.Length - 1, 1);
                sbindex.Append(")");
                indexStrList.Add(sbindex.ToString());
            }

            foreach(string keyindexstr in indexStrList)
            {
                try
                {
                    DbAccess.ExecuteNonQuery(keyindexstr);
                }
                catch
                {
                }
            }

            #endregion

            #region 更新表

            //增加列
            string colSql = "PRAGMA table_info(" + TableName + ")";

            var dt = DbAccess.ExecuteQuery(colSql);
            var columns = new List<string>();
            foreach(DataRow row in dt.Rows)
            {
                string columnName = row["name"]?.ToString().ToLower();
                if(!string.IsNullOrWhiteSpace(columnName) && !columns.Contains(columnName))
                {
                    columns.Add(columnName);
                }
            }

            // 修改列的语句集合
            var alterSql = new List<string>();
            foreach(FpiColumn column in this.FpiColumns)
            {
                if(columns.Contains(column.Name.ToLower()))
                {
                    continue;
                }

                var sbAlter = new StringBuilder();
                sbAlter.Append("ALTER TABLE " + TableName + " ADD COLUMN ");
                switch(column.Type)
                {
                    case ColumnType.Datetime:
                        sbAlter.Append("`" + column.Name + "`" + " DATETIME");
                        break;
                    case ColumnType.Double:
                        sbAlter.Append("`" + column.Name + "`" + " DOUBLE");
                        break;
                    case ColumnType.Float:
                        sbAlter.Append("`" + column.Name + "`" + " FLOAT");
                        break;
                    case ColumnType.Int:
                        sbAlter.Append("`" + column.Name + "`" + " INTEGER");
                        break;
                    case ColumnType.Tinyint:
                        sbAlter.Append("`" + column.Name + "`" + " TINYINT");
                        break;
                    case ColumnType.Varchar:
                        sbAlter.Append("`" + column.Name + "`" + " VARCHAR(" + column.Length + ")");
                        break;
                    case ColumnType.Text:
                        sbAlter.Append("`" + column.Name + "`" + " TEXT");
                        break;
                    case ColumnType.LongText:
                        sbAlter.Append("`" + column.Name + "`" + " TEXT");
                        break;
                }

                if(!column.CanNull)
                {
                    sbAlter.Append(" NOT NULL");
                }

                sbAlter.AppendLine(";");
                alterSql.Add(sbAlter.ToString());
            }

            // 逐行添加
            if(alterSql.Count > 0)
            {
                foreach(string sql in alterSql)
                {
                    try
                    {
                        DbAccess.ExecuteNonQuery(sql);
                    }
                    catch
                    {
                    }
                }
            }

            #endregion
        }

        /// <summary>
        /// 创建sqlserver表
        /// </summary>
        private void CreateMssqlTable()
        {
            string existsql = "select count(1) from sysobjects where name='" + TableName + "'";
            int count = DbAccess.QueryRecordCount(existsql);
            if(count < 1) //不存在表则创建
            {
                #region 建表

                StringBuilder sb = new StringBuilder("CREATE TABLE ").Append(TableName);
                sb.Append("(");
                var primaryKeyColumns = new List<string>();
                var indexList = new List<string>();
                var indexColumnList = new Dictionary<string, string>();
                foreach(FpiColumn column in this.FpiColumns)
                {
                    switch(column.Type)
                    {
                        case ColumnType.Datetime:
                            sb.Append("`" + column.Name + "`" + " datetime");
                            break;
                        case ColumnType.Float:
                            sb.Append("`" + column.Name + "`" + " float");
                            break;
                        case ColumnType.Int:
                            sb.Append("`" + column.Name + "`" + " bigint");
                            break;
                        case ColumnType.Tinyint:
                            sb.Append("`" + column.Name + "`" + " tinyint");
                            break;
                        case ColumnType.Varchar:
                            sb.Append("`" + column.Name + "`" + " varchar(" + column.Length + ")");
                            break;
                        case ColumnType.Double:
                            sb.Append("`" + column.Name + "`" + " double");
                            break;
                        case ColumnType.Text:
                            sb.Append("`" + column.Name + "`" + " text");
                            break;
                        case ColumnType.LongText:
                            sb.Append("`" + column.Name + "`" + " longtext");
                            break;
                    }

                    if(!column.CanNull)
                    {
                        sb.Append("  NOT NULL ");
                    }

                    if(column.AutoColumn)
                    {
                        sb.Append(" identity(1,1) ");
                    }

                    if(column.PrimaryKey)
                    {
                        primaryKeyColumns.Add(column.Name);
                    }

                    if(!string.IsNullOrEmpty(column.IndexName))
                    {
                        if(!indexList.Contains(column.IndexName))
                        {
                            indexList.Add(column.IndexName);
                        }

                        indexColumnList.Add(column.Name, column.IndexName);
                    }

                    sb.Append(",");
                }

                //主键
                var sbkey = new StringBuilder();
                if(primaryKeyColumns.Count > 0)
                {
                    sbkey.Append("primary key(");
                    foreach(string keycol in primaryKeyColumns)
                    {
                        sbkey.Append(keycol);
                        sbkey.Append(",");
                    }

                    sbkey = sbkey.Remove(sbkey.Length - 1, 1);
                    sbkey.Append(")");
                }

                if(sbkey.Length > 0)
                {
                    sb.Append(sbkey);
                    sb.Append(",");
                }

                sb = sb.Remove(sb.Length - 1, 1);
                sb.Append(")");
                try
                {
                    DbAccess.ExecuteNonQuery(sb.ToString());
                }
                catch
                {
                }

                //索引
                var indexStrList = new List<string>();
                foreach(string indexName in indexList)
                {
                    var sbindex = new StringBuilder();
                    sbindex.Append("create index ").Append(indexName).Append(" on ").Append(TableName).Append("(");
                    foreach(KeyValuePair<string, string> indexcol in indexColumnList)
                    {
                        if(indexcol.Value == indexName)
                        {
                            sbindex.Append(indexcol.Key);
                            sbindex.Append(",");
                        }
                    }

                    sbindex = sbindex.Remove(sbindex.Length - 1, 1);
                    sbindex.Append(")");
                    indexStrList.Add(sbindex.ToString());
                }

                foreach(string keyindexstr in indexStrList)
                {
                    try
                    {
                        DbAccess.ExecuteNonQuery(keyindexstr);
                    }
                    catch
                    {
                    }
                }

                #endregion
            }

            #region 更新表

            //增加列
            string colSql = $"SELECT a.name FROM syscolumns a , sysobjects b WHERE a.id=b.id and b.name='{TableName}'";

            IDataReader dr = DbAccess.ExecuteQueryReturnDataReader(colSql);
            var columns = new List<string>();
            while(dr.Read())
            {
                columns.Add(dr.GetString(0).ToLower());
            }

            var sbAlter = new StringBuilder();

            foreach(FpiColumn column in this.FpiColumns)
            {
                if(columns.Contains(column.Name.ToLower()))
                {
                    continue;
                }

                if(sbAlter.Length < 1)
                {
                    sbAlter.Append($"alter table {TableName} add ");
                }

                switch(column.Type)
                {
                    case ColumnType.Datetime:
                        sbAlter.Append("`" + column.Name + "`" + " datetime");
                        break;
                    case ColumnType.Float:
                        sbAlter.Append("`" + column.Name + "`" + " float");
                        break;
                    case ColumnType.Int:
                        sbAlter.Append("`" + column.Name + "`" + " bigint");
                        break;
                    case ColumnType.Tinyint:
                        sbAlter.Append("`" + column.Name + "`" + " tinyint");
                        break;
                    case ColumnType.Varchar:
                        sbAlter.Append("`" + column.Name + "`" + " varchar(" + column.Length + ")");
                        break;
                    case ColumnType.Double:
                        sbAlter.Append("`" + column.Name + "`" + " double");
                        break;
                    case ColumnType.Text:
                        sbAlter.Append("`" + column.Name + "`" + " text");
                        break;
                    case ColumnType.LongText:
                        sbAlter.Append("`" + column.Name + "`" + " longtext");
                        break;
                }

                sbAlter.Append(",");
            }

            if(sbAlter.Length > 0)
            {
                sbAlter.Remove(sbAlter.Length - 1, 1);
                try
                {
                    DbAccess.ExecuteNonQuery(sbAlter.ToString());
                }
                catch
                {
                }
            }

            #endregion
        }

        /// <summary>
        /// 创建mysql表
        /// </summary>
        private void CreateMysqlTable()
        {
            #region 建表

            StringBuilder sb = new StringBuilder("CREATE TABLE IF NOT EXISTS ").Append(TableName);
            sb.Append("(");
            var primaryKeyColumns = new List<string>();
            var indexList = new List<string>();
            var indexColumnList = new Dictionary<string, string>();
            foreach(FpiColumn column in this.FpiColumns)
            {
                switch(column.Type)
                {
                    case ColumnType.Datetime:
                        sb.Append("`" + column.Name + "`" + " datetime");
                        break;
                    case ColumnType.Float:
                        sb.Append("`" + column.Name + "`" + " float");
                        break;
                    case ColumnType.Int:
                        sb.Append("`" + column.Name + "`" + " int(11)");
                        break;
                    case ColumnType.Tinyint:
                        sb.Append("`" + column.Name + "`" + " tinyint");
                        break;
                    case ColumnType.Varchar:
                        sb.Append("`" + column.Name + "`" + " varchar(" + column.Length + ")");
                        break;
                    case ColumnType.Double:
                        sb.Append("`" + column.Name + "`" + " double");
                        break;
                    case ColumnType.Text:
                        sb.Append("`" + column.Name + "`" + " text");
                        break;
                    case ColumnType.LongText:
                        sb.Append("`" + column.Name + "`" + " longtext");
                        break;
                }

                if(!column.CanNull)
                {
                    sb.Append(" NOT NULL ");
                }

                if(column.AutoColumn)
                {
                    sb.Append(" auto_increment ");
                }

                if(column.PrimaryKey)
                {
                    primaryKeyColumns.Add(column.Name);
                }

                if(!string.IsNullOrEmpty(column.IndexName))
                {
                    if(!indexList.Contains(column.IndexName))
                    {
                        indexList.Add(column.IndexName);
                    }

                    indexColumnList.Add(column.Name, column.IndexName);
                }

                sb.Append(",");
            }

            //主键
            var sbkey = new StringBuilder();
            if(primaryKeyColumns.Count > 0)
            {
                sbkey.Append("primary key(");
                foreach(string keycol in primaryKeyColumns)
                {
                    sbkey.Append(keycol);
                    sbkey.Append(",");
                }

                sbkey = sbkey.Remove(sbkey.Length - 1, 1);
                sbkey.Append(")");
            }

            if(sbkey.Length > 0)
            {
                sb.Append(sbkey);
                sb.Append(",");
            }

            //索引
            var indexStrList = new List<string>();
            foreach(string indexName in indexList)
            {
                var sbindex = new StringBuilder();
                sbindex.Append("key ").Append(indexName).Append("(");
                foreach(KeyValuePair<string, string> indexcol in indexColumnList)
                {
                    if(indexcol.Value == indexName)
                    {
                        sbindex.Append(indexcol.Key);
                        sbindex.Append(",");
                    }
                }

                sbindex = sbindex.Remove(sbindex.Length - 1, 1);
                sbindex.Append(")");
                indexStrList.Add(sbindex.ToString());
            }

            foreach(string keyindexstr in indexStrList)
            {
                sb.Append(keyindexstr);
                sb.Append(",");
            }

            sb = sb.Remove(sb.Length - 1, 1);
            sb.Append(")  DEFAULT CHARSET=utf8");
            try
            {
                DbAccess.ExecuteNonQuery(sb.ToString());
            }
            catch
            {
            }

            #endregion

            #region 更新表

            string colSql = "show columns from " + TableName;

            IDataReader dr = DbAccess.ExecuteQueryReturnDataReader(colSql);
            var columns = new List<string>();
            while(dr.Read())
            {
                columns.Add(dr.GetString(0).ToLower());
            }

            var sbAlter = new StringBuilder();

            foreach(FpiColumn column in this.FpiColumns)
            {
                if(columns.Contains(column.Name.ToLower()))
                {
                    continue;
                }

                if(sbAlter.Length < 1)
                {
                    sbAlter.Append($"alter table {TableName} add column (");
                }

                switch(column.Type)
                {
                    case ColumnType.Datetime:
                        sbAlter.Append("`" + column.Name + "`" + " datetime");
                        break;
                    case ColumnType.Float:
                        sbAlter.Append("`" + column.Name + "`" + " float");
                        break;
                    case ColumnType.Int:
                        sbAlter.Append("`" + column.Name + "`" + " int(11)");
                        break;
                    case ColumnType.Tinyint:
                        sbAlter.Append("`" + column.Name + "`" + " tinyint");
                        break;
                    case ColumnType.Varchar:
                        sbAlter.Append("`" + column.Name + "`" + " varchar(" + column.Length + ")");
                        break;
                    case ColumnType.Double:
                        sbAlter.Append("`" + column.Name + "`" + " double");
                        break;
                    case ColumnType.Text:
                        sbAlter.Append("`" + column.Name + "`" + " text");
                        break;
                    case ColumnType.LongText:
                        sbAlter.Append("`" + column.Name + "`" + " longtext");
                        break;
                }

                if(!column.CanNull)
                {
                    sb.Append(" NOT NULL ");
                }

                sbAlter.Append(",");
            }

            if(sbAlter.Length > 0)
            {
                sbAlter.Remove(sbAlter.Length - 1, 1);
                sbAlter.Append(")");
                try
                {
                    DbAccess.ExecuteNonQuery(sbAlter.ToString());
                }
                catch
                {
                }
            }

            #endregion
        }

        #endregion

        #region 增、删、改

        /// <summary>
        /// 判断记录是否存在
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public bool ExistRecord(SearchConditionCollection filter)
        {
            string where = filter == null ? string.Empty : filter.ToString(); // where子句
            if(where != string.Empty)
            {
                where = " where " + where;
            }
            string sqlCount = string.Format(
                   "select count(*) from {0}{1}",
                   TableName, where);

            int itemCount = DbAccess.QueryRecordCount(sqlCount);
            return itemCount > 0;
        }

        /// <summary>
        /// 增加记录
        /// </summary>
        /// <param name="row"></param>
        public void AddRecord(FpiRow row)
        {
            string sbInsert = GetInsertSql(row);
            DbAccess.ExecuteNonQuery(sbInsert);
        }

        /// <summary>
        /// 增加列
        /// </summary>
        public void AddColumn(FpiColumn column)
        {
            FpiColumns.Add(column);
        }

        /// <summary>
        /// 生成sql语句
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public string GetInsertSql(FpiRow row)
        {
            StringBuilder sbInsert = new StringBuilder();
            StringBuilder sbValues = new StringBuilder();
            sbInsert.Append("insert into ").Append(TableName).Append("(");
            foreach(FpiColumn col in this.FpiColumns)
            {
                if(col.AutoColumn)
                {
                    continue;
                }

                sbInsert.Append(col.Name).Append(",");
                object value = row.GetFieldValue(col.Name);
                string valstr = string.Empty;
                if(value == null || value is DBNull || value.Equals("DBNull") || value?.ToString() == "DBNull")
                {
                    valstr = "null";
                }
                else
                {
                    try
                    {
                        switch(col.Type)
                        {
                            case ColumnType.Datetime:
                                valstr = "'" + Convert.ToDateTime(value).ToString("yyyy-MM-dd HH:mm:ss") + "'";
                                break;
                            case ColumnType.Double:
                                var tmpValue1 = Convert.ToDouble(value);
                                if(double.IsNaN(tmpValue1) || double.IsInfinity(tmpValue1))
                                {
                                    valstr = "null";
                                }
                                else
                                {
                                    valstr = tmpValue1.ToString("G10");
                                }
                                break;
                            case ColumnType.Float:
                                var tmpValue2 = Convert.ToSingle(value);
                                if(float.IsNaN(tmpValue2) || float.IsInfinity(tmpValue2))
                                {
                                    valstr = "null";
                                }
                                else
                                {
                                    valstr = tmpValue2.ToString("G10");
                                }
                                break;
                            case ColumnType.Int:
                            case ColumnType.Tinyint:
                                valstr = Convert.ToInt32(value).ToString();
                                break;
                            case ColumnType.Varchar:
                                valstr = "'" + value.ToString() + "'";
                                break;
                            case ColumnType.Text:
                                valstr = "'" + value.ToString() + "'";
                                break;
                            default:
                                break;
                        }
                    }
                    catch
                    {
                        valstr = "null";
                    }
                }
                sbValues.Append(valstr).Append(",");
            }

            sbInsert.Remove(sbInsert.Length - 1, 1);
            sbInsert.Append(") values(");
            sbValues.Remove(sbValues.Length - 1, 1);

            sbInsert.Append(sbValues.ToString()).Append(")");


            return sbInsert.ToString();
        }

        #endregion

        #region 查询

        /// <summary>
        /// 获取第一条记录
        /// </summary>
        /// <param name="ordercolumn"></param>
        /// <param name="ordertype"></param>
        /// <returns></returns>
        public FpiRow GetFirstRow(string ordercolumn, OrderType? ordertype)
        {
            string select = BuildSelectString(true);
            // 排序
            if(!string.IsNullOrEmpty(ordercolumn))
            {
                select += " order by " + ordercolumn + " " + ordertype.ToString();
            }
            switch(DbFactory.DBType)
            {
                case DBType.odbc:
                case DBType.mysql:
                case DBType.sqlite:
                    select += " limit 0,1";
                    break;
                case DBType.mssql:
                    select = select.Replace("select", "select top 1 ");
                    break;
                default:
                    break;
            }
            List<FpiRow> rows = ReadData(select);
            return rows.Count > 0 ? rows[0] : null;
        }

        /// <summary>
        /// 获取记录数
        /// </summary>
        /// <returns></returns>
        public int GetRecordCount()
        {
            return DbAccess.QueryRecordCount(this.TableName, string.Empty);
        }

        public List<FpiRow> Search(SearchConditionCollection filter)
        {
            int itemCount = 0;
            return SearchTable(filter, null, null, null, null, out itemCount);
        }

        public List<FpiRow> Search(SearchConditionCollection filter, string ordercolumn, OrderType? ordertype)
        {
            int itemCount = 0;
            return SearchTable(filter, ordercolumn, ordertype, null, null, out itemCount);
        }

        public List<FpiRow> Search(SearchConditionCollection filter, int pageSize, int pageIndex, out int itemCount)
        {
            return SearchTable(filter, null, null, pageSize, pageIndex, out itemCount);
        }

        public List<FpiRow> Search(SearchConditionCollection filter, string ordercolumn, OrderType? ordertype, int pageSize, int pageIndex, out int itemCount)
        {
            return SearchTable(filter, ordercolumn, ordertype, pageSize, pageIndex, out itemCount);
        }

        /// <summary>
        /// 分页查询数据
        /// </summary>
        /// <param name="filter">条件</param>
        /// <param name="order">排序</param>
        /// <param name="pageSize">每页记录数</param>
        /// <param name="pageNumber">第几页</param>
        /// <param name="itemCount">总记录数</param>
        /// <returns></returns>
        private List<FpiRow> SearchTable(SearchConditionCollection filter, string ordercolumn, OrderType? ordertype, int? pageSize, int? pageNumber, out int itemCount)
        {
            itemCount = -1;
            bool paging = pageSize != null; // 是否分页
            string select = BuildSelectString(true);
            string where = filter == null ? string.Empty : filter.ToString(); // where子句
            string cmd = select; // 完整的查询命令，不包含分页和排序
            if(where != string.Empty)
            {
                where = " where " + where;
            }
            cmd = select + where;
            // 排序
            if(!string.IsNullOrEmpty(ordercolumn))
            {
                cmd += " order by " + ordercolumn + " " + ordertype.ToString();
            }

            // 如果分页
            if(paging)
            {
                // 计算当前查询在不分页的情况下的记录总数
                string sqlCount = string.Format(
                    "select count(*) from {0}{1}",
                    TableName, where);

                itemCount = DbAccess.QueryRecordCount(sqlCount);
                if(itemCount < 1)
                {
                    return null;
                }
                // 在完整的查询命令中附加分页命令
                switch(DbFactory.DBType)
                {
                    case DBType.odbc:
                    case DBType.mysql:
                    case DBType.sqlite:
                        cmd += BuildMysqlPagingString(itemCount, (int)pageSize, (int)pageNumber);
                        break;
                    case DBType.mssql:
                        cmd = BuildMssqlPagingString(cmd, itemCount, (int)pageSize, (int)pageNumber, ordercolumn, ordertype, where);
                        break;
                    default:
                        break;
                }

            }

            // 读取数据
            return ReadData(cmd);
        }

        /// <summary>
        /// 构建Mssql分页串
        /// </summary>
        /// <param name="select"></param>
        /// <param name="itemCount"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        private string BuildMssqlPagingString(string select, int itemCount, int pageSize, int pageNumber, string ordercolumn, OrderType? ordertype, string where)
        {
            StringBuilder sbSql = new StringBuilder();
            if(string.IsNullOrEmpty(ordercolumn) || ordertype == null)
            {
                throw new Exception("未指定排序列，不支持分页！");

            }
            else if(ordertype == OrderType.Asc)
            {
                sbSql.Append("select top ");
                sbSql.Append(pageSize);
                sbSql.Append(" ");
                sbSql.Append(select.Substring(select.IndexOf("select") + 6, select.IndexOf("from") - select.IndexOf("select") - 6));
                sbSql.Append(" from ");
                sbSql.Append(TableName);
                sbSql.Append(" where (");
                if(!string.IsNullOrEmpty(where))
                {
                    sbSql.Append("(");
                    sbSql.Append(where.Replace("where", ""));
                    sbSql.Append(") and ");
                }
                sbSql.Append(ordercolumn);
                sbSql.Append(" >=(select max(");
                sbSql.Append(ordercolumn);
                sbSql.Append(") from (select top ");
                sbSql.Append(1 + (pageNumber - 1) * pageSize);
                sbSql.Append(" ");
                sbSql.Append(ordercolumn);
                sbSql.Append(" from ");
                sbSql.Append(select.Substring(select.IndexOf("from") + 4));
                sbSql.Append(") as T)) order by ");
                sbSql.Append(ordercolumn);
                sbSql.Append(" ");
                sbSql.Append(ordertype.ToString());
            }
            else if(ordertype == OrderType.Desc)
            {
                sbSql.Append("select top ");
                sbSql.Append(pageSize);
                sbSql.Append(" ");
                sbSql.Append(select.Substring(select.IndexOf("select") + 6, select.IndexOf("from") - select.IndexOf("select") - 6));
                sbSql.Append(" from ");
                sbSql.Append(TableName);
                sbSql.Append(" where (");
                if(!string.IsNullOrEmpty(where))
                {
                    sbSql.Append("(");
                    sbSql.Append(where.Replace("where", ""));
                    sbSql.Append(") and ");
                }
                sbSql.Append(ordercolumn);
                sbSql.Append(" <=(select min(");
                sbSql.Append(ordercolumn);
                sbSql.Append(") from (select top ");
                sbSql.Append(1 + (pageNumber - 1) * pageSize);
                sbSql.Append(" ");
                sbSql.Append(ordercolumn);
                sbSql.Append(" from ");
                sbSql.Append(select.Substring(select.IndexOf("from") + 4));
                sbSql.Append(") as T)) order by ");
                sbSql.Append(ordercolumn);
                sbSql.Append(" ");
                sbSql.Append(ordertype.ToString());
            }
            return sbSql.ToString();
        }

        /// <summary>
        /// 构建Mysql分页串
        /// </summary>
        /// <param name="itemCount"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        private string BuildMysqlPagingString(int itemCount, int pageSize, int pageNumber)
        {
            // 计算总页数
            double divide = itemCount / pageSize;
            double floor = System.Math.Floor(divide);
            if(itemCount % pageSize != 0)
            {
                floor++;
            }

            int pc = Convert.ToInt32(floor);
            int num = 0;

            if(pageNumber > pc)
            {
                pageNumber = pc;
            }

            if(pageNumber < 1)
            {
                pageNumber = 1;
            }

            num = (pageNumber - 1) * pageSize;

            return string.Format(" limit {0},{1} ", num, pageSize);
        }

        /// <summary>
        /// 使用指定的SQL读取数据
        /// </summary>
        /// <param name="cmd"></param>
        /// <returns></returns>
        private List<FpiRow> ReadData(string cmd)
        {
            List<FpiRow> result = new List<FpiRow>();
            try
            {
                using(IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(cmd))
                {
                    while(reader.Read())
                    {
                        object[] values = new object[reader.FieldCount];
                        reader.GetValues(values);
                        FpiRow row = new FpiRow();
                        for(int i = 0; i < reader.FieldCount; i++)
                        {
                            row.SetFieldValue(FpiColumns[i].Name, values[i]);
                        }
                        result.Add(row);
                    }
                }
            }
            catch(Exception)
            {
            }
            return result;
        }

        /// <summary>
        /// 得到表select语句
        /// </summary>
        /// <returns></returns>
        internal string BuildSelectString(bool withAutoColumn)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select ");
            foreach(FpiColumn column in FpiColumns)
            {
                if(column.AutoColumn && !withAutoColumn)
                {
                    continue;
                }
                sb.Append(column.Name);
                sb.Append(",");
            }
            sb.Remove(sb.Length - 1, 1);
            sb.Append(" from ");
            sb.Append(this.TableName);
            return sb.ToString();
        }

        /// <summary>
        /// 得到列数(不包含自增列)
        /// </summary>
        /// <returns></returns>
        internal int ColumnCount
        {
            get
            {
                int count = 0;
                foreach(FpiColumn column in FpiColumns)
                {
                    if(column.AutoColumn)
                    {
                        continue;
                    }
                    count++;
                }
                return count;
            }
        }

        #endregion
    }
}