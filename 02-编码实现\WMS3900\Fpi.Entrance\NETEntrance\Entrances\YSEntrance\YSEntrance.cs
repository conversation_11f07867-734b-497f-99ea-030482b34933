﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Fpi.Entrance.YS.Controllers;
using Fpi.Entrance.YS.Models;
using Fpi.Xml;
using Newtonsoft.Json;

namespace Fpi.Entrance
{
    public class YSEntrance : BaseNETEntrance
    {
        #region 字段

        /// <summary>
        /// 报警监听服务
        /// </summary>
        public PushNotificationService _pushNotificationService { get; set; }

        #endregion

        #region 构造

        public YSEntrance()
        {
            if(string.IsNullOrEmpty(Description))
            {
                Description = "宇视门禁";
            }

            if(id.Contains("DefaultID"))
            {
                id = "YushiEntrance";
            }

            if(name.Contains("DefaultName"))
            {
                name = "宇视门禁";
            }
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(name) || name.Contains("DefaultName") ? "宇视门禁" : name;
        }

        public override void InitEntranceAsync()
        {
            try
            {
                CleanEntranceAsync();

                // 自动创建订阅
                YSSubscriptionHelper.CreateSubscriptionAsync(this.ReceivePort, this.Ip, this.User, this.Pwd, this.id).Wait();

                _pushNotificationService = new(this.ReceivePort, this.id);
                // 启动服务
                _pushNotificationService.Start();
            }
            catch(Exception ex)
            {
                EntranceLogHelper.Info($"[{this.name}]初始化门禁失败: {ex.Message}");
            }
        }

        public override async void CleanEntranceAsync()
        {
            try
            {
                // 删除订阅
                List<string> strs = VarConfig.GetValue($"{this.id}SubscriptionIDlist").Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries).ToList();

                foreach(var item in strs)
                {
                    await YSSubscriptionHelper.DeleteSubscriptionAsync(item, this.Ip, this.User, this.Pwd);
                }

                _pushNotificationService?.Stop();
            }
            catch(Exception ex)
            {
                EntranceLogHelper.Info($"[{this.name}]清除门禁订阅失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 远程开门
        /// </summary>
        public override async Task<string> OpenDoorAsync()
        {
            try
            {
                HttpClient httpClient = new HttpClient();
                // 设置基础认证
                var byteArray = Encoding.ASCII.GetBytes($"{User}:{Pwd}");
                httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
                httpClient.BaseAddress = new Uri($"http://{Ip}:{Port}/");
                httpClient.Timeout = TimeSpan.FromSeconds(4);

                // 构造请求头
                var request = new HttpRequestMessage(HttpMethod.Put, "/LAPI/V1.0/PACS/Controller/RemoteOpened")
                {
                    Content = new StringContent("", Encoding.UTF8, "text/plain") // 请求体为空
                };

                // 发送 PUT 请求
                var response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                // 打印原始响应内容。等待操作返归
                string rawContent = await response.Content.ReadAsStringAsync();
                EntranceLogHelper.Info($"原始响应内容: {rawContent}");

                // 检查响应状态码
                // 解析响应内容
                var jsonResponse = JsonConvert.DeserializeObject<RemoteControlResponse>(rawContent);
                if(jsonResponse.Response.StatusCode == 0 && jsonResponse.Response.StatusString == "Succeed")
                {
                    EntranceLogHelper.Info($"[{this.name}]门禁远程开启成功！");
                }
                else
                {
                    throw new Exception($"状态码[{jsonResponse.Response.StatusString}]");
                }

                return rawContent;
            }
            catch(Exception ex)
            {
                var info = $"[{this.name}]门禁远程开启错误:{ex.Message}";
                EntranceLogHelper.Info(info);
                return await Task.FromException<string>(new Exception(info));
                //throw new Exception(info);
            }
        }

        /// <summary>
        /// 远程关门
        /// </summary>
        public override async Task<string> CloseDoorAsync()
        {
            try
            {
                throw new Exception($"此设备暂不支持此操作！");
            }
            catch(Exception ex)
            {
                var info = $"[{this.name}]门禁远程关闭错误:{ex.Message}";
                EntranceLogHelper.Info(info);
                return await Task.FromException<string>(new Exception(info));
            }
        }

        #endregion
    }
}