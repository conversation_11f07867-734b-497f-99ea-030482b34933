<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtAlarmVolume.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="txtAlarmVolume.Location" type="System.Drawing.Point, System.Drawing">
    <value>277, 6</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtAlarmVolume.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="txtAlarmVolume.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 16</value>
  </data>
  <data name="txtAlarmVolume.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 5, 5, 5</value>
  </data>
  <data name="txtAlarmVolume.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 29</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtAlarmVolume.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;txtAlarmVolume.Name" xml:space="preserve">
    <value>txtAlarmVolume</value>
  </data>
  <data name="&gt;&gt;txtAlarmVolume.Type" xml:space="preserve">
    <value>Sunny.UI.UITextBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;txtAlarmVolume.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;txtAlarmVolume.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="uiLabel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="uiLabel1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uiLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="uiLabel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>156, 11</value>
  </data>
  <data name="uiLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 21</value>
  </data>
  <data name="uiLabel1.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="uiLabel1.Text" xml:space="preserve">
    <value>预警容量(mb)：</value>
  </data>
  <data name="&gt;&gt;uiLabel1.Name" xml:space="preserve">
    <value>uiLabel1</value>
  </data>
  <data name="&gt;&gt;uiLabel1.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel1.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;uiLabel1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="chkAutoClear.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="chkAutoClear.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 7</value>
  </data>
  <data name="chkAutoClear.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="chkAutoClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 29</value>
  </data>
  <data name="chkAutoClear.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="chkAutoClear.Text" xml:space="preserve">
    <value>自动清理日志</value>
  </data>
  <data name="&gt;&gt;chkAutoClear.Name" xml:space="preserve">
    <value>chkAutoClear</value>
  </data>
  <data name="&gt;&gt;chkAutoClear.Type" xml:space="preserve">
    <value>Sunny.UI.UICheckBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;chkAutoClear.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;chkAutoClear.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnOK.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>571, 50</value>
  </data>
  <data name="btnOK.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 35</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnCancel.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>688, 50</value>
  </data>
  <data name="btnCancel.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 35</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="uiLabel2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="uiLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="uiLabel2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="uiLabel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 44</value>
  </data>
  <data name="uiLabel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>469, 44</value>
  </data>
  <data name="uiLabel2.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="uiLabel2.Text" xml:space="preserve">
    <value>自动清理启用后，软件所在磁盘空间低于预警容量时会自动循环清理时间最久的日志文件,直到剩余磁盘空间大于预警容量。</value>
  </data>
  <data name="&gt;&gt;uiLabel2.Name" xml:space="preserve">
    <value>uiLabel2</value>
  </data>
  <data name="&gt;&gt;uiLabel2.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel2.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;uiLabel2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="pnlFunc.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="pnlFunc.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="pnlFunc.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 511</value>
  </data>
  <data name="pnlFunc.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="pnlFunc.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="pnlFunc.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 88</value>
  </data>
  <data name="pnlFunc.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="pnlFunc.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlFunc.Name" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlFunc.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.Name" xml:space="preserve">
    <value>lsbMsgTypes</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.Type" xml:space="preserve">
    <value>Sunny.UI.UIListBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.Parent" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;uiLabel3.Name" xml:space="preserve">
    <value>uiLabel3</value>
  </data>
  <data name="&gt;&gt;uiLabel3.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel3.Parent" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;uiLabel3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="uiPanel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="uiPanel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="uiPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 35</value>
  </data>
  <data name="uiPanel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="uiPanel1.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="uiPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>332, 476</value>
  </data>
  <data name="uiPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="uiPanel1.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;uiPanel1.Name" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;uiPanel1.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiPanel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;uiPanel1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lsbMsgTypes.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="lsbMsgTypes.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="lsbMsgTypes.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 16</value>
  </data>
  <data name="lsbMsgTypes.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="lsbMsgTypes.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="lsbMsgTypes.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="lsbMsgTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>332, 460</value>
  </data>
  <data name="lsbMsgTypes.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lsbMsgTypes.Text" xml:space="preserve">
    <value>lsbMsgTypes</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.Name" xml:space="preserve">
    <value>lsbMsgTypes</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.Type" xml:space="preserve">
    <value>Sunny.UI.UIListBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.Parent" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;lsbMsgTypes.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="uiLabel3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="uiLabel3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uiLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="uiLabel3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="uiLabel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, -4</value>
  </data>
  <data name="uiLabel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 21</value>
  </data>
  <data name="uiLabel3.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="uiLabel3.Text" xml:space="preserve">
    <value>日志类型</value>
  </data>
  <data name="&gt;&gt;uiLabel3.Name" xml:space="preserve">
    <value>uiLabel3</value>
  </data>
  <data name="&gt;&gt;uiLabel3.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel3.Parent" xml:space="preserve">
    <value>uiPanel1</value>
  </data>
  <data name="&gt;&gt;uiLabel3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;chkList.Name" xml:space="preserve">
    <value>chkList</value>
  </data>
  <data name="&gt;&gt;chkList.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckedListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkList.Parent" xml:space="preserve">
    <value>uiPanel2</value>
  </data>
  <data name="&gt;&gt;chkList.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;uiLabel4.Name" xml:space="preserve">
    <value>uiLabel4</value>
  </data>
  <data name="&gt;&gt;uiLabel4.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel4.Parent" xml:space="preserve">
    <value>uiPanel2</value>
  </data>
  <data name="&gt;&gt;uiLabel4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="uiPanel2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="uiPanel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 12pt</value>
  </data>
  <data name="uiPanel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>337, 35</value>
  </data>
  <data name="uiPanel2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="uiPanel2.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="uiPanel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>458, 476</value>
  </data>
  <data name="uiPanel2.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="uiPanel2.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;uiPanel2.Name" xml:space="preserve">
    <value>uiPanel2</value>
  </data>
  <data name="&gt;&gt;uiPanel2.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiPanel2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;uiPanel2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chkList.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="chkList.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="chkList.HorizontalScrollbar" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chkList.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 16</value>
  </data>
  <data name="chkList.Size" type="System.Drawing.Size, System.Drawing">
    <value>458, 460</value>
  </data>
  <data name="chkList.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;chkList.Name" xml:space="preserve">
    <value>chkList</value>
  </data>
  <data name="&gt;&gt;chkList.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckedListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkList.Parent" xml:space="preserve">
    <value>uiPanel2</value>
  </data>
  <data name="&gt;&gt;chkList.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="uiLabel4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="uiLabel4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uiLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="uiLabel4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="uiLabel4.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, -4</value>
  </data>
  <data name="uiLabel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 21</value>
  </data>
  <data name="uiLabel4.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="uiLabel4.Text" xml:space="preserve">
    <value>输出方式选择</value>
  </data>
  <data name="&gt;&gt;uiLabel4.Name" xml:space="preserve">
    <value>uiLabel4</value>
  </data>
  <data name="&gt;&gt;uiLabel4.Type" xml:space="preserve">
    <value>Sunny.UI.UILabel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;uiLabel4.Parent" xml:space="preserve">
    <value>uiPanel2</value>
  </data>
  <data name="&gt;&gt;uiLabel4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>800, 600</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="$this.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 35, 5, 1</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>WindowsDefaultLocation</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>日志配置</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FrmConfigLog</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>Sunny.UI.UIForm, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
</root>