﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Fpi.Entrance.YS.Models;
using Fpi.Xml;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Fpi.Entrance.YS.Controllers
{
    /// <summary>
    /// 门禁订阅帮助类
    /// </summary>
    public static class YSSubscriptionHelper
    {
        #region 公共方法

        #region 创建订阅

        public static async Task CreateSubscriptionAsync(int port, string ip, string name, string password, string id)
        {
            var handler = new DigestAuthHandler(name, password);
            using var client = new HttpClient(handler)
            {
                BaseAddress = new Uri($"http://{ip}")
            };

            try
            {
                // 订阅id集合
                List<string> subscriptionList = new();

                // 创建刷脸订阅（Type=1024）
                var request = new
                {
                    AddressType = 0,
                    IPAddress = GetLocalIp(),
                    Port = port,
                    Duration = 4294967295, // Duration为 4294967295表示 永久订阅，永久订阅时，不需 要再调“刷新订阅接口”
                    Type = 1024,
                    SubscribePersonCondition = new
                    {
                        LibIDNum = 65535,
                        LibIDList = new byte[] { }
                    }
                };
                subscriptionList.AddRange(await SubscriptionServiceAsync(client, request));

                //// 创建告警订阅（Type=255）
                //var alarmRequest = new
                //{
                //    AddressType = 0,
                //    IPAddress = GetLocalIp(),
                //    Port = port,
                //    Duration = 4294967295,
                //    Type = 255
                //};
                // subscriptionList.AddRange(await SubscriptionServiceAsync(client, alarmRequest));

                VarConfig.SetValue($"{id}SubscriptionIDlist", string.Join("|", subscriptionList.Where(a => !string.IsNullOrEmpty(a))));
                VarConfig.Save();
            }
            catch(Exception ex)
            {
                EntranceLogHelper.Info($"创建订阅异常: {ex.Message}");
            }
        }

        #endregion

        #region 删除订阅

        /// <summary>
        /// 删除指定订阅
        /// </summary>
        /// <param name="subscriptionId">订阅ID（从创建订阅接口获取）</param>
        /// <returns>是否删除成功</returns>
        public static async Task<bool> DeleteSubscriptionAsync(string subscriptionId, string ip, string name, string password)
        {
            // 初始化带HTTP摘要认证的HttpClient
            var handler = new DigestAuthHandler(name, password);
            using HttpClient httpClient = new HttpClient(handler)
            {
                BaseAddress = new Uri($"http://{ip}/") // 根据设备协议调整HTTP/HTTPS
            };
            httpClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json"));

            try
            {
                // 构造API地址
                var apiUrl = $"LAPI/V1.0/System/Event/Subscription/{subscriptionId}";

                // 发送DELETE请求
                var response = await httpClient.DeleteAsync(apiUrl);

                // 处理响应
                if(response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();

                    // 然后解析实际的JSON内容
                    var jsonObject = JObject.Parse(content);

                    UnSubscriptionResponse result = jsonObject.ToObject<UnSubscriptionResponse>();

                    return result?.Response.ResponseCode == "0" && result.Response.StatusCode == 0;
                }

                // 处理HTTP错误状态码
                throw new HttpRequestException($"HTTP错误: {response.StatusCode}");
            }
            catch(Exception ex)
            {
                // 记录日志或抛出自定义异常
                EntranceLogHelper.Info($"删除指定订阅: {ex.Message}");
                return false;
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建订阅
        /// </summary>
        /// <param name="client"></param>
        /// <param name="obj"></param>
        /// <returns></returns>
        private static async Task<List<string>> SubscriptionServiceAsync(HttpClient client, object obj)
        {
            // 订阅id集合
            List<string> subscriptionList = new();
            try
            {
                var content = new StringContent(
                         JsonConvert.SerializeObject(obj),
                         Encoding.UTF8,
                         "application/json");

                var sendinfo = await content.ReadAsStringAsync();
                EntranceLogHelper.Info($"创建订阅发送始报文: {sendinfo}");

                HttpResponseMessage response = await client.PostAsync(
                    "/LAPI/V1.0/System/Event/Subscription",
                    content);

                // 解析响应
                var responcontent = await response.Content.ReadAsStringAsync();

                EntranceLogHelper.Info($"创建订阅回复原始报文: {responcontent}");

                // 然后解析实际的JSON内容
                var jsonObject = JObject.Parse(responcontent);

                SubscriptionResponse result = jsonObject.ToObject<SubscriptionResponse>();

                // 优先使用Response级别的CreatedID
                if(result?.Response.SubResponseCode == 0 && result.Response.StatusCode == 0)
                {
                    // 直接返回Response中的CreatedID              
                    subscriptionList.Add(result.Response.Data.ID);
                }
            }
            catch(Exception ex)
            {
                EntranceLogHelper.Info($"创建订阅错误: {ex.Message}");
            }
            return subscriptionList;
        }

        /// <summary>
        /// 获取本地IP
        /// </summary>
        /// <returns></returns>
        private static string GetLocalIp()
        {
            return Dns.GetHostEntry(Dns.GetHostName())
                .AddressList.First(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                .ToString();
        }

        #endregion
    }
}