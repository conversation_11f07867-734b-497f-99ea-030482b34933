﻿using System;
using System.IO;
using System.Text;
using Fpi.Log.Config;
using log4net.Appender;
using log4net.Core;
using log4net.Layout;
using log4net.Util;

namespace Fpi.Log
{
    /// <summary>
    /// 文本输出附着器
    /// </summary>
    public class PatternFileAppender : AppenderSkeleton
    {
        public PatternFileAppender()
        {
        }
        /// <summary>
        /// 文本格式
        /// </summary>
        public PatternLayout File { get; set; } = null;
        /// <summary>
        /// 文本编码
        /// </summary>
        public Encoding Encoding { get; set; } = Encoding.UTF8;
        /// <summary>
        /// 输出
        /// </summary>
        /// <param name="loggingEvent"></param>
        protected override void Append(LoggingEvent loggingEvent)
        {
            if(!LogManager.GetInstance().TxtOutput(loggingEvent.Level.Name))
            {
                return;
            }

            try
            {
                StringWriter stringWriter = new StringWriter();
                File.Format(stringWriter, loggingEvent);
                string fileName = stringWriter.ToString();

                fileName = SystemInfo.ConvertToFullPath(fileName);

                FileStream fileStream = null;

                string directoryFullName = Path.GetDirectoryName(fileName);

                if(!Directory.Exists(directoryFullName))
                {
                    Directory.CreateDirectory(directoryFullName);
                }

                fileStream = new FileStream(fileName, FileMode.Append, FileAccess.Write, FileShare.Read);

                using(StreamWriter streamWriter = new StreamWriter(fileStream, Encoding))
                {
                    RenderLoggingEvent(streamWriter, loggingEvent);
                }

                fileStream.Close();
            }
            catch(Exception ex)
            {
                ErrorHandler.Error("Failed to append to file", ex);
            }
        }

    }
}
