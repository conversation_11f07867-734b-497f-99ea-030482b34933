﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace Fpi.Camera.UI
{
    /// <summary>
    /// 视频显示子面板
    /// </summary>
    public partial class PlayerItem : Panel
    {
        #region 字段属性

        /// <summary>
        /// 控件ID，与对应摄像机ID一致
        /// </summary>
        public string Id;

        /// <summary>
        /// 显示界面
        /// </summary>
        public PictureBox Preview = new PictureBox { Dock = DockStyle.Fill, BackColor = Color.Black, SizeMode = PictureBoxSizeMode.StretchImage };

        /// <summary>
        /// 容器组(用来左上角显示摄像机名字)
        /// </summary>
        private readonly Panel _gbMain = new Panel { Dock = DockStyle.Fill, Padding = new Padding(1, 1, 1, 1) };

        /// <summary>
        /// 当前边框颜色
        /// </summary>
        private Color _borderColor = Color.Black;

        /// <summary>
        /// 显示控件（picturebox）句柄
        /// </summary>
        private readonly IntPtr _previewHandle;

        /// <summary>
        /// 标题栏
        /// </summary>
        private readonly Label _titleLabel;

        /// <summary>
        /// 是否被选中(选中则更改颜色)
        /// </summary>
        public bool OnSelected
        {
            set => _borderColor = value ? Color.Red : Color.Black;
        }

        #endregion

        #region 构造

        /// <summary>
        /// 视频显示子面板
        /// </summary>
        /// <param name="id"></param>
        public PlayerItem(string id)
        {
            InitializeComponent();
            Id = id;
            _previewHandle = Preview.Handle;

            // 标题
            _titleLabel = new Label();
            _titleLabel.AutoSize = false;
            _titleLabel.Dock = DockStyle.Top;
            _titleLabel.Height = 50;
            _titleLabel.TextAlign = ContentAlignment.MiddleLeft;
            _titleLabel.ForeColor = Color.White;
            _titleLabel.BackColor = Color.FromArgb(15, 68, 134);
            _titleLabel.Font = new Font("微软雅黑", 12, FontStyle.Regular, GraphicsUnit.Point, 134);

            // 点击事件传递
            _titleLabel.Click += (sender, e) => OnClick(e);
            Preview.Click += (sender, e) => OnClick(e);
            _gbMain.Paint += RePaint;

            _gbMain.Controls.Add(Preview);
            _gbMain.Controls.Add(_titleLabel);

            this.Controls.Add(_gbMain);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更改控件选中状态
        /// 为true时突出显示，为false时正常显示
        /// </summary>
        public void SelectedState(bool flag)
        {
            OnSelected = flag;
            Refresh();
        }

        /// <summary>
        /// 更新相机名称显示
        /// </summary>
        /// <param name="name"></param>
        public void UpdateCameraName(string name)
        {
            // 对应摄像机名称（显示在界面左上角）
            _titleLabel.Text = @"     " + name;

            _gbMain.Controls.Clear();
            _gbMain.Controls.Add(_titleLabel);
            _gbMain.Controls.Add(Preview);
            // 调整控件叠放顺序，防止视频画面被遮挡（显示出来以后才可调节，构造函数中调节没用）
            Preview.BringToFront();
        }

        /// <summary>
        /// 获取显示控件（picturebox）句柄
        /// </summary>
        /// <returns></returns>
        public IntPtr GetPicHandle()
        {
            return _previewHandle;
        }

        /// <summary>
        /// 点击自身，触发选中状态
        /// </summary>
        public void SelectSelf()
        {
            OnClick(null);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 重绘，改变边框以展示是否被选中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void RePaint(object sender, PaintEventArgs e)
        {
            Panel pnl = (Panel)sender;
            Pen pen = new Pen(_borderColor, 2) { DashStyle = DashStyle.Solid };
            e.Graphics.DrawLine(pen, 0, 0, 0, pnl.Height);
            e.Graphics.DrawLine(pen, 0, 0, pnl.Width, 0);
            e.Graphics.DrawLine(pen, pnl.Width - 1, pnl.Height - 1, 0, pnl.Height - 1);
            e.Graphics.DrawLine(pen, pnl.Width - 1, pnl.Height - 1, pnl.Width - 1, 0);
        }

        #endregion
    }
}
