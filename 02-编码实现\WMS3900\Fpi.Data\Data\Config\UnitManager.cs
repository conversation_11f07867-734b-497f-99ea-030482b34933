﻿//==================================================================================================
//类名：     UnitManager   
//创建人:    hongbing_mao
//创建时间:  2013-1-5 16:26:12
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Fpi.Data.ExpParser;
using Fpi.Log;
using Fpi.Xml;


namespace Fpi.Data.Config
{
    /// <summary>
    /// 单位管理类
    /// </summary>
    public class UnitManager : BaseNode
    {
        public NodeList UnitGroups;

        private readonly Hashtable _symbolTable = new Hashtable();
        private readonly ExpressionParse _expParse = new ExpressionParse();

        #region 构造

        private static readonly object syncObj = new object();
        private static UnitManager _instance;
        public static UnitManager GetInstance()
        {
            lock(syncObj)
            {
                if(_instance == null)
                {
                    _instance = new UnitManager();
                }
            }
            return _instance;
        }

        private UnitManager()
        {
            loadXml();
        }

        #endregion

        /// <summary>
        /// 得到所有单位
        /// </summary>
        /// <returns></returns>
        public Unit[] GetAllUnit()
        {
            List<Unit> units = new List<Unit>();
            if(UnitGroups != null)
            {
                foreach(UnitGroup ug in UnitGroups)
                {
                    if(ug.Units != null)
                    {
                        foreach(Unit unit in ug.Units)
                        {
                            units.Add(unit);
                        }
                    }
                }
            }
            return units.ToArray();
        }

        /// <summary>
        /// 和Node无关的单位转换
        /// </summary>
        /// <param name="val">原值</param>
        /// <param name="fromUnitId">原单位</param>
        /// <param name="toUnitId">目标单位</param>
        /// <returns></returns>
        public double TransUnitValue(double val, string fromUnitId, string toUnitId)
        {
            return double.IsNaN(val) ? double.NaN : TransUnitValue(null, val, fromUnitId, toUnitId);
        }
        /// <summary>
        /// 将数值从Node的内部单位转换为目标单位
        /// </summary>
        /// <param name="node">对象</param>
        /// <param name="val">原值</param>
        /// <param name="toUnitId">目标单位</param>
        /// <returns></returns>
        public double TransFromSelfUnitValue(ValueNode node, double val, string toUnitId)
        {
            return double.IsNaN(val) ? double.NaN : TransUnitValue(node, val, node.SelfUnitId, toUnitId);
        }
        /// <summary>
        /// 将数值从目标单位转换为Node的内部单位
        /// </summary>
        /// <param name="node">对象</param>
        /// <param name="val">原值</param>
        /// <param name="fromUnitId">目标单位</param>
        /// <returns></returns>
        public double TransToSelfUnitValue(ValueNode node, double val, string fromUnitId)
        {
            return double.IsNaN(val) ? double.NaN : TransUnitValue(node, val, fromUnitId, node.SelfUnitId);
        }
        /// <summary>
        /// 转化单位值
        /// </summary>
        /// <param name="node">对象</param>
        /// <param name="val">原值</param>
        /// <param name="fromUnitId">原单位</param>
        /// <param name="toUnitId">目标单位</param>
        /// <returns></returns>
        public double TransUnitValue(ValueNode node, double val, string fromUnitId, string toUnitId)
        {
            try
            {
                if(fromUnitId == toUnitId || string.IsNullOrEmpty(fromUnitId) || string.IsNullOrEmpty(toUnitId))
                {
                    return val;
                }
                //不同组单位不能转化
                UnitGroup ugFrom = FindUnitGroup(fromUnitId);
                UnitGroup ugTo = FindUnitGroup(toUnitId);
                if(ugFrom != ugTo || ugFrom == null || ugTo == null)
                {
                    throw new Exception("不同类型单位之间不能转化！");
                }
                Unit unitFrom = ugFrom.Units[fromUnitId] as Unit;
                Unit unitTo = ugTo.Units[toUnitId] as Unit;
                if(unitFrom.IsCurrentUnit())//将数值从标准单位转换为指定单位
                {
                    return TransValueFromSelfUnit(node, val, unitTo);
                }
                else if(unitTo.IsCurrentUnit())//将数值从指定单位转换为标准单位
                {
                    return TransValueToSelfUnit(node, val, unitFrom);
                }
                else //先转换为标准单位，再转换为指定单位
                {
                    double standVal = TransValueToSelfUnit(node, val, unitFrom);
                    return TransValueFromSelfUnit(node, standVal, unitTo);
                }
            }
            catch(Exception ex)
            {
                string error = string.Format("单位转换异常：因子名称：{0} 目标单位{1}，原单位：{2}, exception:{3}", node.name, toUnitId, node.UnitIds, ex.Message);
                LogUtil.Error(error);
                return double.NaN;
            }

        }

        /// <summary>
        /// 通过id查找unit
        /// </summary>
        /// <param name="unitid"></param>
        /// <returns></returns>
        public Unit FindUnitById(string unitid)
        {
            if(UnitGroups != null)
            {
                foreach(UnitGroup ug in UnitGroups)
                {
                    if(ug.Units != null)
                    {
                        foreach(Unit unit in ug.Units)
                        {
                            if(unit.id == unitid)
                            {
                                return unit;
                            }
                        }
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 删除单位
        /// </summary>
        /// <param name="unitId"></param>
        public void DeleteUnit(string unitId)
        {
            if(UnitGroups != null)
            {
                foreach(UnitGroup ug in UnitGroups)
                {
                    if(ug.Units != null)
                    {
                        for(int i = 0; i < ug.Units.GetCount(); i++)
                        {
                            Unit unit = ug.Units[0] as Unit;
                            if(unit.id == unitId)
                            {
                                ug.Units.Remove(unit);
                                return;
                            }
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 通过单位id查找单位所在的单位组
        /// </summary>
        /// <param name="unitid"></param>
        /// <returns></returns>
        private UnitGroup FindUnitGroup(string unitid)
        {
            if(UnitGroups != null)
            {
                foreach(UnitGroup ug in UnitGroups)
                {
                    if(ug.Units.FindNode(unitid) != null)
                    {
                        return ug;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 将数值从标准单位转换为指定单位
        /// </summary>
        /// <param name="node">对象</param>
        /// <param name="val">标准单位的值</param>
        /// <param name="unit">指定单位</param>
        private double TransValueFromSelfUnit(ValueNode node, double val, Unit unit)
        {
            val = TransUnit(node, val, unit.Trans);
            return val;
        }

        /// <summary>
        /// 将数值从指定单位转换为标准单位
        /// </summary>
        /// <param name="node">对象</param>
        /// <param name="val">数值</param>
        /// <param name="unit">指定单位</param>
        private double TransValueToSelfUnit(ValueNode node, double val, Unit unit)
        {
            val = TransUnit(node, val, unit.ReverseTrans);
            return val;
        }

        /// <summary>
        /// 转换计算
        /// </summary>
        /// <param name="node"></param>
        /// <param name="val"></param>
        /// <param name="unitFormula"></param>
        /// <returns></returns>
        private double TransUnit(ValueNode node, double val, string unitFormula)
        {
            if(string.IsNullOrEmpty(unitFormula))
            {
                throw new Exception("未配置单位转换公式！");
            }

            //获取所有变量
            string[] symbols = GetSymbols(unitFormula);
            // string exp = unitFormula.Replace("self", val.ToString());  //转换时出现问题
            string exp = unitFormula.Replace("self", Convert.ToDecimal(val).ToString());
            if(symbols != null)
            {
                foreach(string symbol in symbols)
                {
                    exp = exp.Replace(symbol, GetSymbolValStr(symbol, node));
                }
            }
            //计算
            _expParse.Expression = exp;
            double res = Convert.ToDouble(_expParse.Execute().Value);
            return res;
        }

        /// <summary>
        /// 得到特殊标记值
        /// </summary>
        /// <param name="symbol"></param>
        /// <param name="node"></param>
        /// <returns></returns>
        private string GetSymbolValStr(string symbol, ValueNode node)
        {
            string result = string.Empty;
            switch(symbol)
            {
                case "$Molwt":
                    if(node == null || string.IsNullOrEmpty(node.Molwt))
                    {
                        throw new Exception("未配置对象分子量不能转化!");
                    }
                    result = node.Molwt;
                    break;
                default:
                    break;
            }
            return string.IsNullOrEmpty(result) ? throw new Exception("未识别的标识:" + symbol) : result;
        }

        /// <summary>
        /// 得到所有特殊标记
        /// </summary>
        /// <param name="formu"></param>
        /// <returns></returns>
        private string[] GetSymbols(string formu)
        {
            lock(_symbolTable)
            {
                string[] results = (string[])_symbolTable[formu];
                if(results == null)
                {
                    Regex r = new Regex(@"[\s]*[$][\w]+[\s]*");
                    MatchCollection mc = r.Matches(formu);
                    results = new string[mc.Count];
                    for(int i = 0; i < mc.Count; i++)
                    {
                        results[i] = mc[i].Value.Trim();
                    }
                    _symbolTable[formu] = results;
                }
                return results;
            }
        }






    }
}
