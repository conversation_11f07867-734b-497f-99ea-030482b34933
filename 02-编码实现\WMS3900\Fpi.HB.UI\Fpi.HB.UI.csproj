﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{25031008-3C88-4D40-9172-F4FE61B1C993}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.HB.UI</RootNamespace>
    <AssemblyName>Fpi.HB.UI</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.8.3.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
    <Reference Include="ZedGraph, Version=5.1.5.19703, Culture=neutral, PublicKeyToken=02a83cbd123fcd60, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\ZedGraph.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DeciveParam\FrmDeviceParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DeciveParam\FrmDeviceParam.Designer.cs">
      <DependentUpon>FrmDeviceParam.cs</DependentUpon>
    </Compile>
    <Compile Include="DeciveParam\FrmDeviceParamNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DeciveParam\FrmDeviceParamNew.Designer.cs">
      <DependentUpon>FrmDeviceParamNew.cs</DependentUpon>
    </Compile>
    <Compile Include="DeciveParam\UC_NoParamPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeciveParam\UC_NoParamPanel.Designer.cs">
      <DependentUpon>UC_NoParamPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="HisData\FrmQueryConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HisData\FrmQueryConfig.Designer.cs">
      <DependentUpon>FrmQueryConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="HisData\UC_QueryConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="HisData\UC_QueryConfig.Designer.cs">
      <DependentUpon>UC_QueryConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Remote\FrmRemoteConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Remote\FrmRemoteConfig.Designer.cs">
      <DependentUpon>FrmRemoteConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Remote\UC_RemoteConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Remote\UC_RemoteConfig.Designer.cs">
      <DependentUpon>UC_RemoteConfig.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Device\Fpi.Device.csproj">
      <Project>{88fef5d2-e039-4ac0-942b-442f23755978}</Project>
      <Name>Fpi.Device</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.Business\Fpi.HB.Business.csproj">
      <Project>{13650425-1448-4DF5-884F-B7CD466ECB24}</Project>
      <Name>Fpi.HB.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.FlowChart\Fpi.UI.FlowChart.csproj">
      <Project>{14FCA54C-BEA4-489F-8E5F-6BDFD9F13619}</Project>
      <Name>Fpi.UI.FlowChart</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2d502016-b3b3-43ff-9bae-ad1d2a18d42e}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="DeciveParam\FrmDeviceParam.resx">
      <DependentUpon>FrmDeviceParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeciveParam\FrmDeviceParamNew.resx">
      <DependentUpon>FrmDeviceParamNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeciveParam\UC_NoParamPanel.resx">
      <DependentUpon>UC_NoParamPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HisData\FrmQueryConfig.resx">
      <DependentUpon>FrmQueryConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HisData\UC_QueryConfig.resx">
      <DependentUpon>UC_QueryConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Remote\FrmRemoteConfig.resx">
      <DependentUpon>FrmRemoteConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Remote\UC_RemoteConfig.resx">
      <DependentUpon>UC_RemoteConfig.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>