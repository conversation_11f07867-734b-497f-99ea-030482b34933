﻿using System;
using System.Text.RegularExpressions;

namespace Fpi.Devices.Channel
{
    public class RoundProcessor : Processor
    {
        #region — 属性 —

        /// <summary>是否四舍五入:0,四舍五入；1，截断</summary>
        private int _RoundBle;

        public int RoundAble
        {
            get => _RoundBle;
            set => _RoundBle = value;
        }

        /// <summary>保留小数位数</summary>
        private int _KeepDigits;

        public int Digits
        {
            get => _KeepDigits;
            set => _KeepDigits = value;
        }

        #endregion

        public RoundProcessor()
        {
            this.ucParamConfig = new ConfigRoundPanel();
        }

        public RoundProcessor(string param)
            : this()
        {
            this.SetParam(param);
        }

        public override void SetParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length == 2)
            {
                int.TryParse(paramStrs[0], out _KeepDigits);
                int.TryParse(paramStrs[1], out _RoundBle);
            }
        }

        /// <summary>
        /// 输入数据处理
        /// </summary>
        /// <param name="inputValue"></param>
        /// <returns></returns>
        public override double InputProcessData(double inputValue)
        {
            double dataProcessed = double.NaN;
            if(_RoundBle == 0)  //四舍五入
            {
                dataProcessed = _KeepDigits > 0 ? Math.Round(inputValue, _KeepDigits) : Math.Round(inputValue);
            }
            else //截断
            {
                if(_KeepDigits > 0)
                {
                    int TenPow = 1;
                    for(int i = 0; i < _KeepDigits; i++)
                    {
                        TenPow = TenPow * 10;
                    }
                    dataProcessed = Math.Truncate(inputValue * TenPow) / TenPow;
                }
                else
                {
                    return Math.Truncate(inputValue);
                }
            }
            return dataProcessed;
        }

        #region 截断小数处理方法
        private double ProcessMethod1(double dealdata, int keepdigit)
        {
            double datartn = double.NaN;
            datartn = Math.Floor(Math.Pow(10, keepdigit) * dealdata) / Math.Pow(10, keepdigit);
            return datartn;
        }
        private double ProcessMethod2(double dealdata, int keepdigit)
        {
            double datartn = double.NaN;
            string str = dealdata.ToString();
            int decLen = 0;
            if(str.IndexOf('.') > -1)
            {
                decLen = str.Length - str.IndexOf('.') - 1;
                if(decLen <= keepdigit)
                {
                    return dealdata;
                }
                else
                {
                    datartn = double.Parse(str.Substring(0, str.IndexOf('.') + 1 + keepdigit));
                }
            }
            else
            {
                return dealdata;
            }

            return datartn;
        }

        private double ProcessMethod3(double dealdata, int keepdigit)
        {
            double datartn = double.NaN;
            string formatStr = "#0.";
            if(keepdigit > 0)
            {
                for(int i = 0; i < keepdigit; i++)
                {
                    formatStr += "0";
                }
                datartn = double.Parse(dealdata.ToString(formatStr));
            }
            else
            {
                return dealdata;
            }
            return datartn;
        }

        private double ProcessMethod4(double dealdata, int keepdigit)
        {
            double datartn = double.NaN;
            datartn = double.Parse(Regex.Match(dealdata.ToString(), @"[\d]+.[\d]{0," + keepdigit + "}").Value);
            return datartn;
        }

        #endregion End

        /// <summary>
        /// 输出数据处理
        /// </summary>
        /// <param name="outputValue"></param>
        /// <returns></returns>
        public override double OutputProcessData(double outputValue)
        {
            return outputValue;
        }

        public override string ToString()
        {
            return "小数保留位数(是否四舍五入)处理器";
        }
    }
}
