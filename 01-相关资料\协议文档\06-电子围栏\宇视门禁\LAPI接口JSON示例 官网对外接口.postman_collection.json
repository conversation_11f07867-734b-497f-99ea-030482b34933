{"info": {"_postman_id": "ab92dec9-0d14-4416-bb87-455eaefd6370", "name": "LAPI接口JSON示例 官网对外接口", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1、心跳间隔设置", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n\"KeepAliveInterval\":1000,\r\n\"RecordResponseEnable\":0\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/PACS/Controller/LongConnectionInfo", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PACS", "Controller", "LongConnectionInfo"]}}, "response": []}, {"name": "2、获取设备信息", "request": {"method": "GET", "header": [], "url": {"raw": "http://*************/LAPI/V1.0/System/DeviceBasicInfo", "protocol": "http", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "System", "DeviceBasicInfo"]}}, "response": []}, {"name": "3、设备重启", "request": {"method": "PUT", "header": [], "url": {"raw": "*************/LAPI/V1.0/System/Reboot", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "System", "Reboot"]}}, "response": []}, {"name": "4、远程开门", "request": {"method": "PUT", "header": [], "url": {"raw": "*************/LAPI/V1.0/PACS/Controller/RemoteOpened", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PACS", "Controller", "RemoteOpened"]}}, "response": []}, {"name": "5、远程开门（自定义显示界面）接口", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"ResultCode\": 1,\r\n    \"PassTime\": \"20240619111655\",\r\n    \"ResultCmd\": 2,\r\n    \"CodeStatus\": 1,\r\n    \"ResultColor\": 1,\r\n    \"ResultMsg\": \"签到成功111\",\r\n    \"AudioEnable\": 0,\r\n    \"AudioIndex\": 1,\r\n    \"ShowInfoNum\": 3,\r\n    \"ShowInfoList\": [\r\n        {\r\n            \"Key\": \"姓名：\",\r\n            \"Value\": \"测试\"\r\n        },\r\n        {\r\n            \"Key\": \"欢迎：\",\r\n            \"Value\": \"欢迎光临\"\r\n        },\r\n        {\r\n            \"Key\": \"公司：\",\r\n            \"Value\": \"宇视科技\"\r\n        }\r\n    ],\r\n    \"ImageEnable\": 1,\r\n    \"ImageLen\": 21108,\r\n    \"ImageData\":\"/9j/4AAQSkZJRgABAQEAYABgAAD/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAEAAAAAAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCADgALADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD5d1L4cyYO1N27n2H+efyrnNQ8HTW4w0OfcV9Y6p8I3UsPLAwPTOa5XXvhMzKw8oH1A+or0vZ2N9D5e1Dw40ZIaM56nP1rIu9J2tkZ3djnrX0Jr3wv2E5j9T0rjtf+Gh2n93t/wpB7NHil/pjLL8qsR1GRyay7y0UZHPQ16drngeS3GV7dAK5DWPD8sRb5c/0pcplKnY4u7h2qevSs65GDx0FbWq25ib5lZfrWHeS4fr8vp61LiyFpoUboE53djVCZtuatXVxsJ/kKzLi5z/npUjHXb7JPYgfyqq84H4frRqFwvnYz2H8hVCe49+PbvRcCSabgj0NUppOetOeYbvWq81wBQA2WTHTJqN3JB9P501pwvp0z9KiaXC9xQA9rnHeoZLkKp6+lRTTqrfhVSa7ycflQNFia7z7mq813t/liqs13hv8A69KsZf5mP50DsK0pmYZ4706Jcn8acI8f14qSJcEcfWgPQ/p+8T/spXULN/op/wC+K8+8S/s1zwFx9nb6bK/VG++G+l32fl25rnNc/Z80/UQ21Vbd2IxVrERJ5pH5G+Kf2eZE3fuW+hHv9K848WfAmaBWxDnB/u9K/XzxX+yNa3sbFbbn/ZGa8r8cfsT7lbbC3sMf/Xq+dMr2lj8ifFvwXmiLFo8jr90j+leW+LPhpJZs2Y2LdwBX6ufEP9iy6gEjC0Zl9QtfO/xT/ZPurJJv9FlU9cEcVRMp3Pzf8V+Cni3fLyOAMV5/rnhrazfLt/DrX2R8UvgbcaU8m63f5RnhfSvG9X+G01prHktYtc+YSqqI2bjqWG084Azxk46c4oaJi+5836jpTRSnaW6+lW9b+Fup3ei2uo2MbTxzW/mTxgAeSQSp5z1JGcHBAYE5zXunxd+FOlfDm5vtP1LyTeabcG1ju7CdWW4iVmBkOVPX5cZYnBxk4Iryb4mfEGzu9Mt7OyiZfsa7Q0jMXkPPJIwOOAOB646AYyNLHO+MfhjdaNpOn3DuN18A+ChXy0KIy7hg7Wzv+8QeBwAQW4W4jltZmWSORedvIq14h8YzG9yk00u/DSBht57jHIOOmcc+lY7eNLqyYMFjbByN2SAfpnH6VAco26maCTa3yt78GoXuquP8Ur1p2VltfJb7wMK5/kM//Xq03iPSdfjRZolWXP3oAIcex4IIHsAfelzBymHLc4HX86ryXuf89KueI9B+xR/aLWZbqyP8YI3IfQ9/89qx2jPlkssgKnHTgcZqk7hykjXG76VC0zSjC8D+dIkRdu4FTpb52r+GaCthkFsC2ferKxfJUiQ+WKk2cd/pQTuRhcH8enrQTt9vxqQIx68U3ydx96lsR/dUzKfUUgPH3m/OuIsPjXY3Cjdt/BhW1ZfETTr3H7zGcdxWLoyRHOje/wCBN+dNkgjuhtkjVvTIzVWHxHY3ICrPH7c1aS4ilT5ZEP8AwLNTytFXM3UvAel6ojCS2Rdw52ivOPiN+ynpPi21l8uGPzGHBxivWwuVxlcVDe6jDpsLSTzRxovXJqo1JLYmSXU/Nz9pb9haPw8s11qGy0sWDbXwCWPpg+oz05zivz4/aMm0HwZo8NjpvmSTxEtNJJbDaJAvBU/ePzAYJwAOq55r9Y/+ChH7RujHwxcacjRtKnJO7qB+n+R71+L37VPjyDUNWuuUVWJIGRz14rvppvWRnGSvofLvxS1ySWdlWRyrkNuI27umOM4+leT+II2VWZ5Pmck8gZ/Ou88e6n5txJLv/eMfkA5zXmOvXbPMzbmJZjnBpSsdEYsxtStYYGbLKzZ68kfnWHqn71vl5Wr1/LufPzMB+tUbqZni3Kirg+tYuxSTMyWNkDKfu9xUM1xMh++x29M9qkmkkPP3v6VWuHkLE4HPWodh2Y+z1e40+QNHIyMeeO9bVn4z+0qsN2jTQ5BbodtcvLcN0xioRcyIfekPlO6XQ1uEL20izR/ex3FN+w+RNtb5WXrXJ2Gv3VmQUmkTBzhTW9Ya79vCg8yEc54o5g5S4VK+noeaAmD+NCIzLntnFSLHuquYzsBHyg/nT0hyeO1LFDz/AL1WILNnPbC0XE1Y/qfk8Qalp/eRQKWD4ualph/1knHua+mNY/Znhu92yNSPZq4XxV+yfMVbyo5F7+tdSqRZzcrR5ZB+0vqGmtlpOnqTWjZftj3ltII/MG5uAM1z/wAQ/wBnHWNG3tHbTSAZxtUt+leH/Fr7V8N9Ku490ltfyLtbZwyDj+LPB59+9J26E2PpzXP+CgkPg+xcTTRyagoz5YkO2MH19T7CvBfjR/wUqublbpYbry4z0Csfxr4v8d/E29jiuIGndpMZVievXqfyH515FqOsanq1+0O6Roy2DjuaXKoe8zeNNydkeo/tE/tVXHjBZG+0sxbJPzZxXxz8SvFt1rl3JuEzfMcelfRWofBi4vbMNGrSbvvcc/Ss2L9mOaWQNcQsFb7oB/T/AOvXHWzCC2PRpZbJnxr4j0+6up+I5cdgcnNc9eeAbjUZG2xuje/Q195y/suxy2u1rXHXnng1g3P7OJsboq1uxwevSvOlmiPQp5U+58P3PwivWt2xGzMvIGKy7j4XX0f3reQ8cLs5avvKz+AkVq+42pX3Oe1On+AlnPJ+8t89ulYPNDp/sm58K6D+z/q3im7jjWEwq3LFhgIPr6+1dhF+xtOLTCszSHrlRX2zoXwdttMiXy7Xb278V02nfDZJ33tbLuYbeK55ZlN7G9PKoJe8fnPr37Gmro37lPMJ6AKeawr/APZI1yziZvsspK9QEJIr9VtI+Ccd8EDQsqMPm/D+ddJd/s92clsqx26snY4wfel/ak0EsppH4z6x+zh4msLOW4XSbySCJSzP5Z6Vy+nx/Yp9sjYCnBXGC3t/kV+3Fr+yrba2xt1gWNZz+8fGc+o/GvhD/gol+wbdfDHWLrxZoNhJ/ZrNm5iQZER/v/Q966sLmXPLkmcOJy/kV4anylpV1/a8rx+W3mRjhQudw/yKvRWjb2RlKspIIIwR6/yrHlhmfUUkWceZsHLAjafT/wCvX1l+wH8HdB8eQXV9rmlwapqli+EjuwWhQYyrKn3HPDE7wSMAjGMn1eex47geY/Bv9lfxT8X5I5bKx+w6Y3Jv7wGOAjvs4LSH2QEZ6letfWXwW/Yq8LfDGKG4uIf7e1iNg/2u7iXZGR/zzi5VeRnJ3OD0YV7ZpHh2SUBQjenWuu0P4dTTlcxN9KnnbJ5T+gAt6DNOK5FfKelft9QLEPMbJ/3q1Iv2/NMIAY4PbpzWv1eRy+0R9EX2gWephhNbxsf7xHSvgn/goD8O9J0Ga4khkiRmByB2Oa9O+KP/AAUT0/TvDEwtZFjmZfmJI+QfhX5z/tf/ALZ7eMrubddFmYZY+Z056VvRi1uwerVjwX4sFZdYmt4X3qzYxj/PvXWfDb4TfaLK1keIfMdxbuK8o8M+J28deN4VXBV3yTn0/wA96+rfCC29hpUMca7yFGegNeTmmKa9xHv5XhVL3mWtL8I2lpFGvlqdo5yvJovtMs4WAWFfUntj6Vel1hcN5NvJMzDG0Dn9cUyz0ebWGbfaqgB6kZ3V83KpJs+kjRSKE+lWk0QXaiNjG7GeK5nxL4a0+3LMq7m3BTn8K9Hi8C+ZB8y4yODz27Vha58MpZpcbWz0Uc4rJyb3LjFXPNRoULH5e3XIqG68MQyN/d47DpXX6l8OrnT/AJVkVWx1LHt+FUD4NvnlbdcfKf4eB09D/jS5jeO5z9no626ADlV7nt+lb2n2cLtH90qOnQbRV608F3UzIzQNIB3Lk/Xitmw8HKqruhXpkBiVPrUcw7It6Ncx2kG3y42ZRuxjgfjW1YajHN5arGRIfUcH6VpeF/hbb6lFtkAZW5LqWOCO/wCFd3pHwbtY0Tl92AC/mbdw+mDVaswqWOY8MPZz3i+ZEyTL8pwMh+ev4e9O+M/wD0f4r/D/AFDTbi3WT7Rbuj5X5WJH5/jXVX/wwktrkSf6xVOQR29Oa0NMhmKeW/LYKk9/8/rWkbo5Zq5/PX+018C5vg58UdU0dlH+hzt5ZAwHjzwR/Liu/wD2EPEaaJ8U9P8AtDyJayERzgFApQ45bdxjIXHcEjHOAfp7/gsL+zz/AMI34ktNeW3VFuJXjaTPDgncMgjAwcjORnPr1+M/hPq1x4E8dWN5Z/ubmyuUnjypyjqQcY64zwR6V9ThantKSbPmsVT5JM/YXwd8L0kihmjj/dTKJEyCMg8jrz+dejeH/hyqbf3foDkVpfs6G3+KXwo0HxBa2S2A1S1SWS1GCIXHyuox/ddWX/gOOuTXrOk+CfLK/L+laJ2PPcj4KX4z3UeF+3TewbB/pmobz476gEby7qFivZw38wa+f5PiigT/AFmf0pqfEiFnVtytyMr03D0z2z616tzl23PcPHHxrvJPh6zysrTFjtfO1eAvTjOADnJPVvbFfKnxJ+I02qag8e4FXkBIVjlm6f0FdX4+8W3Gp+E5mgf5VjJOxSQnbHXpjHJJye54ryL4WafL8UPjDo+kW8TSNdXi5VfQZY/kB+lc3OlFtndTjdpI+vf2HfhE14JtUuoW8w9Ay8j2HvX0pq8Gn+EdN825mht1xwXcLuHtnrzxxWr8K/h/Y/D/AMHWlqqrFsA3MTjcfU++ayPjdrui6vpclvJGsixr8yRvsUnryRgn05OCOMdK+VxEva1HJn1WHiqcFGJ4740/bI8O+Grry9MU6kykhpY03Rgj0PRu3Q496f4f/wCCiGh2Vuv263+zzNyq7AyyDPbn2+vtXC+L9J0bV1kSKNY9owpaZmK47g5/z6V5x4t+A+j+JImEdxBu9PPL+/OSfTPJ/CiEaS3RpL2r2PqO0/4KAeF9a8mEXltGzgEBsLn2wfrWtbftOaTcx4jnjkL5O7K7T9RX5weNv2eV0K5byLvzOpCpIGA+tYen6Tr2ivth1K5WNeQnmNx+tVKhSktGEKs4u0kfpwPjrpt+SrtCPT/Ioj+IlnenKyKN3cDn26nI71+evhvxt4gs0iEl5JJ5fYsTn/OK9o8F/EuYaYrTSfv8Annv/niuWpTUTvpVFLSx9Oav8So9NRRGEaPuWPt7fyrm7v462ulAzNcL5n3tu7jPPGTyf8+1eJ698R2ureXMrYXovXvXiPjnX9W1y6ZbOaQLyOSfXqDmpp0k2XKooK9j7IX9uO18Oyt9ovrWKDON28fMMdMf1rB1f/grbougXaLa3Ml03QiLkeufT/PevhDUfh1d6lzeXjsW4JVjzWv4Y+CukQlftTRozHH7+ULu/wCAnrXdGnSW551WrVm7JWPsqD/grtrmq3Pk6PGk/IBUxkOfdeSD+OPp0r2v4T/tua74jhtbi/8ADt9dMxXzJ7ZizDJ+YLEyIW245YtgZGM818cfCv4e+GNDWPybq2t2U7gRG2Cc+pGPXnOK+ivhh4n0/SI1ltNWtb4qcSIsomUMOmTnPc+1Z1HDZIy9lPdnuf7WXwytf2v/ANl/W7OO1hi1iOye6sVWRZJI5kG7b2PO3HbrX44fBXwjJ8QviDpunzGS3uLy6S3ZliMjKWITO0EE4PYc9vQV+6nwO+IllrOiQ2bR/ZHZcsgbarMOASAcEYA55PTp0r84fiJ+yVZfB/8A4Kl2XhOSO6tvD3izxFbX2nm1A82K3vZwQI93B2SmSNQepiHeuzL52bgefmFNuCkfrl8EfhnceEfA+k6bdRL9ssbOKGdguFeRV2sy+qtgMrD7wbnBBFei6f4Z5+7W7o3h+S00+3juPJeaFAkjRgiNmAwSgJJCnsCSVGBk4ydaDTfm6cV6SXU8Fn8xV5c3tmTtmfv+H+c1WHiO+QHdIW+tfS3jv9jzU9MnfbF+TA5/PFeW+IfgDqumTSD7PL8pxj5D/Jj/ACr1JPQyjqz0BfhXZ+Ff2atLur5ry68X+JdOXWEBO230yxljEtsiqOZJZYysjM3A81VUKULu7/gkN8IF8a/tVXF5eQDboukXF2pcZCSM0cYP/fLtXeeCdKn8a/C3wndTLKs+gW0Hh+9RhjYtrGkULDvtaBYuehYOATtNfQX/AATi+DVj4B+O/wAXmtVOyzhtLWLPVUlluH692xCufX8K+bliZvnhPc+yqYClGFKtS2a/E7742s9lBJbWXmLtAyycY69MetfOfxG0XUFt28+eVVVvnZRuKg+w619neLvCv9p3mW2fe+Ukfd9a4vxZ4Js1t/nhE27lkK9ue3rXkyep1w2PhjX/AI1+G/hhp0klr4Z1vxVqW4DL222ND9Tn06kcV578Wf2+/G/gnwj/AGlY+C/CVrp8rLF87PetFu7vt2hPfg8nFfdnjXwNavpZWCxt2GRmIIqsV68Dj/Ir58+L/wADNP1cTNP4ekd5UKP5UkRVgRjkMD+dbUp018SCVOU17rsfE/iH/goD4t1698vVPDHh2aOQ7A1msiBj7Plgc8cD17VrWPxCXxRqos2t/seoPyLcsGDHGSFI6n26/WvQNb/Zb0PQ777RaaDDZyKxIaUxDBHsq5zW18P/ANmmLWtSgmbSbN2jkWRZTHyrg5Ug9cgjOfauipKlb3VYzp0K0filc5HwxbJePvuF8uOP7zHjbivbPBHwUutWsVe3TzI2GVbjaR/Wun0b9nqG3udskLTbTvbcg/fHr37V9CfDPw1FZaX9naLy2mAxtGCvHTj8sV50ql3od8YOKuz5R8YfCGTR7eRp4VXjghfz7V5F4yiTQLZ2SEhs7VIBzmv0R174Tw+JdLmWSOKRo8gHA3Kffj+tfPnxJ/ZbuFu5Gjt9ykFgCODz9P8AOacZ66g1zLQ+HfEnjYabrYsN6tfbQ7RGQLs3ZwGbqCeuAM4IPGRXm8n7V3jLSb5hp8ekaX5LlCi23mSgg4+Znzk++BX2B43/AGf/AOzJZZltUaZjumLRgnPYnj6dfSuZ0z4F2+s3376z0u6cEf6wGJj06Fc16FOpTSu1c82vh6knpKwfBD42/FDUvBtlq3/CTJDczFjaaZcWcVy94BwCBGmUBOQN2BjBzg5r6o8L6R8QPGehaffeMvAuh2F9cKrQ31g6vMRwcSBD09sZ/ryPwX+G/wDwjlzCbfTdBtxGQRIXeZ1P/Aif8K+qvA0kVxYQrc3XnYAysfCoO+eCBWFSpF7IpU3BJN3IfhB4I1LS5dPuyZkVJARjdt+nPQ4zwfWuH/4Ky/De4Op/Cfx5Ys8F/Y3MmkSzJ/rAQUubcjvlGW4I9z619UfDu3sL+FY12owUEHjkHofw/rXN/ts/DKT4ifA2y0uG3mubpPEFgbSONdzPK7PCAB6nzSo92FKlUaldEcsW7T2Pob9hr4r6p8cf2eNO1rWcSalFcyWc0o6TbUjcN7f6zH4CvY47b2rmv2fvg3D8C/g9ofhdTG0+nw7ruRPuyXD/ADSYPdQcIp7qgrsvI3dq96imoK58vipRlWk4bX0PjP8Abi/Zr8P/AAvvJFhvLYb1L7S43Jz0/kfxr86vi1qdppN9OsN0uFYj5Wz3r3H9pz9rCbxvc3DXDG4dyTuZif618b/EXxRbareyu6uMk/dB68erV63KedTutTt/gn8bNP8ABvxCjt9Wul/sHWyljqDOcLbbnxFcH08t2yT2jaX1r7z/AGSdHXRvGvj7dJIb66tNMe6jYYb9wbxM49zMvT275r8h9WNlKzbVutxPRnG3HfjBr9X/APgmH4ik8cfsv2eu3YWfUljuNDkneYSSXkds8JjaQ4z5mwkMSPmwGPLGvEzKhZqqvRn1GWYq9J0Z9NV+p6t4n1KC1s4wzqWbJbHGB9f8iuA1nxNH5kmG+VuMDoDj07Vc+Kuv+RHHM0j75FwT2I69K85u9TuLuHapWPcpI4PX65/wrxKkdT2qV2ir4r8Q77lnjZt4P3iMY/nXmPiuLVvEO+O1m4OSfmGK76Dwqr2rrcTSM2Szsx5+mPQdufzrMGgWOnwSbJH3Md8jJ/H2Ge5/EcVnsdtOCPOtN+CjajP9o1S/Zi33Y9uec+vTn2rsvDugW/h5V3BY1UbSxPp6c0a74yh8OaVcOkQeREJiXdt3dwMgZznuK8hn+L15478a2Ol2u6Ga4k2ld+7bxlgDgcfhzip5mzTl1Po7RJ7PUGxCTJIuASQQBzXX6AY4NQj2sMZwSegNcP4Q8P3Gkwwxtll2hmJPy1q6p4/0bQZGjluFzgblzRytmfK3oz1Wy8PLJPdeVvxcjzMnnn6j8PpWH4u8KSDSNy4Z1+8pGFYYNebJ+1db6frHnQ30kSofLAdQQQDjuP8AOM16X4P/AGjPCfjTS5Iby6txNIuCwYAc+uK05RTptI8L8WadY31zeMq2yi1USTFmCNGGdUGASCfmZRgDvk8AmvPNe+EtlfyNJbyfMx3KB1H6/wCeK7r9s/4E6hpPhebx54VvFvLXR0Muo26n52tiQDKuOP3fUgY+TJzxXlPw3+Jk+p28TSSbVwACwzgfln9aUtBwjfU09J0u/wBBDebFI67tuMDDAe4PpXp/w68U3NmVWQSRrwACvzfrz0rP0i/s9SjTdNHLIp3AHAx15A64H512mkWsN+sG1V8xBwwG1WA4weo6+/51C1M6kbHp3w48fSQLatzJFvJDdM8fnxz7j6V9NfBEW3jLxhpaTxrL9lkF1GGGQHjBZHGfQgEH2B6ivkvwvpcVgrRr8u/l1+8D05I4GRzX1J+xPbte+L7ubB2WlmzkH+EuyqOfzruwsPfR4eOnam2j6U8rdQB7VIoyvFFe4fMn4T6tomn6+Sz6TqAHOCpzn8zXJ678F9Fu3LC11aFiP+eYb+lftpon/BLTwbptkqSzNO2OSIgAaW//AOCXXg68X5JNv1jFel7Sn/Mc2ttj8Hb/APZ50i4J2X19CRz89qTj8hXsH7BVzdfAf9oXw7ax65JL4e17U4rLULKWJ0hl85TAsnX5ZEZ0YMP7gDZXIP6t6x/wSR8O3LN5NxDuP96OuG8V/wDBGqO5RjY3VmrZyCG2FT26f0rOrGnOLjc1pVpQkmjwb4v6TJY63cWskbRtG7A5U5yDj9BxxXDlGiiO3asfQnBO7v0719gftS/s7X2g22nanqCJ9qurKP7UVYczooWQ5HXJG7/gVfJ/i6xj02Jo2AVY/ukcfyr5fEUXFn1+DxSnFNHL6prR0/PzNHtbd09f07dea4/xB4kYS71w7ElQ/G4D69cfT0q94mujDHKuPO5zhzla5S+vpJyyrngenyj/AD/X0rjkl1PXp1Dk/HWr3XiBPKUsytwU5JbtSeCvhde+DdXtNehVZGsyZWhP3pOCCuTxnnj3xXZaB4XgJ+1XciqFUsXI2hR71sXviez0pXjjmZtgA4GScD+EDk9CMDrWa8jb2iR4b8fv2+tWtY5LPR7W3eQExuHuBb7PrwST7Y/H18Nt/wBqHxPqs0j3en3Eyk/6yC63BfbBXn8xX0b45+EelfEjWbi5ksYY7jcRnYPmJ9QcEnnsMdeelY2pfs4w2uhSLCiRtGpYBvlUHHAHTk/TrXRTdNLUylKcn7p4afjZLePtZ7rr9x1+Yf5+tQ+IPjL4wvdGjt9Alj0SHfk3U0fnSvjtg/Io9yG+orW8N/BLXbzxRcRtpsisWwfMGFHoc16xoX7MdzZQW6sPN3EFgHVlOPQ89PwIx2rbmgtiX7W2pR/Z7+OPjhPC2o2N9q0mqi7tJbSVym2MCVCp3KOOhPQc1uWHhOTw6I2h/eQ8ZB/w6V01j4Sj8FwwxNCI/PYKAseNxwec4I4APXjn167keo6fPbm1LbfOGAuzlTjPQd+vUf8A1+eVuge2MHQdae3uV2s2c5bBLcfTuMfhXpXgjxK8Em5t0ihRvH3VK4ycc8H8fxrzWXRGjutsaxMoztOcH/H8P0rsvhzpkl3LHuX5ozt39MjuCeD/ADNEfMyq1Lo+ivCU0WpW0cituY4yB83PUg/pX2Z+xR4U/srwBfaq0e3+0pxFECMfJGO3tuZvyr47+Cfhm58Y65p+l2MBe7u5xDEn947ioJPoQoY+g64INfo54R8L2/g3w1p+k2v+o06BIVP98gcsfcnJ/GvTwdPXmPm8yq6cqNBxhaAeKcUppXafrXonjmTJ+1DdadDuu9NkjUDksmAKxbz9vzw/pjstxtVgcYVf/r1H8Zfjr5Xw81HWLDT9JhsYAsX7+BLh3LMF4GMDrnqelfLP7M1vpPxc/aMS11TTbbVLNbK+unjuEDRFkgcqdn3eGwRxxj6V2e5a9jl95vc+pIf+ChHh+73fZ7Oa4x1KLkD6nPFTD9oK6+N3h7Wk0ddP0tdKhSaea4QzSBXfaAoBx69TXx/8Zo76y1LSovKWyh+yl0jiXauC5GcD6V6l+xIPtuj+NLJlupLq/gto1/dOybUnQnLAEL98/eIz2zWfMrXSNFHuz1DUfgNN8Sv2YNSk0+e41LX472a8haVv+PnbhWiUDgZUHaP7wGTX5+/EVciT+6MjBGCCP89K/WT4UXlj4B+G0jX9xBY29nPI0klxIIkXd8wyWIHP1r8/f+CjVt4Fn8d3OueDvEGnXGoalOW1HSIUcMZGP+vjJXaS7ZDJnJfkZJIHm4yPMuY9bL6zjPl6HyXr115O75uVbgYz9M1yov4rAs01vL5a5IO3pV7VPFEcbNu+72Jz+Fcj401lhYlVZ/XIA6V4cj6qnJDfG/xLW0URrcCNV5YgjeMc9MfzPb8+B1H4wWME8k11cW9rHGd0YZmwGHIY5zyMDHOBtB65rB8XRz67dYWQhm4yTyPrWXZfAXRNXjaTWkW7YnOJG3fkM/0o91LU0jFt6Gf4z/b1stMWeHSljurhflEvmYjU9yRmvN7v9rDxV4kk3y6xMI1OVVY2Mcec+2K9avfg34P8OJm3s7e2XA6RBuPx6fWpI/EOk+G4VS30/SWjUYjP2faVAHqv59cexrog6XQ9CjGbXxJHnV1+2t4tfQGsW1T6yfY33Aex247VzmlftR+LNCvxc2epau0u7dxA5UH1wRivTl+IGjw3Ceda2MjRnIMkfzZ55B696vaB8T9PtiyJFHHDIxyqrhRkeg/Dr6cYOK0Xs10NJQbX8RGJ4d/4KFeILq1+y65YXE8LY/fLaN065Iwe/tVrSv2io/EmpZjmma3kwfnhZTn2yOP5da7nSdf8N6wyrNZQ3XmZZmeJdrE4zlQNuc8njk8nJq8/hDw3Km5bO3h5ZSUX5WbPGB/WspSp9EebiKenxJm54S+K0er2dvuZi6kA7sDcCex/z1619AfB/T47rThdQ4/eAFg2ck84I/Qc+tfM/hXwFDeatCLP5IFcAjd9457d/SvuD9jH4V6d458VWtrq1wbPQrNftN+8QJmmjU58mML8xdsgZAJRSWwTgFQhzSsjza1RU4XZ9Xf8E+/gZJpGmv4y1CLaJEe30pWHzSZyss306xr65k9FJ+nkG38a5fwv8TPCt/aw2ml6lp6W9tGsMMER8tIUUAKgBwAAMDHbiumtrmO6hWSKSOSNuVZGDKfoRxXuU4qMbI+Vq1HOXMySoSdp/nT2b5v8O9RMwIrQyufOvxys73RP2Ybq3ktZIJry4iMCyr5bzqh3yMqnBYKo3HGcDJPHNeU/8E/9GudS+Olw0V81rJb6NfSvtRXZ18vaV+bpksOfbHet74r/ABrsPGfi/wDtDx14ks1v7e7MdnpvhnddtDbFQ4gjmuo4N8yrmSRoopdwLH5VCgYnhv42+G/AXg3VIfB2lf2bHeyxXN1eXTpNeSBW3xqvmhOI2ZCrIBGGZGDO21qp1NLE8utzlP8AgqP8EvHHj743+HYfB+k6hNZ6b4RR3ujMLSxSZr2dXRppGSMyLHtcoCXC4OCK8Z+G/wCwN42vLnzNd8faVouk4ElxBp+qvqOoXcajcwiWHfAXDDaA8uN2DzwD1HxX+K2q+IZ2uNU1bV7y6tbeOea3BklkaUnLW+5yy/JFkFQzsAMktlsx/C34m/ZU0+L7KmmGWCJZrz70Sx5OdhRVjBZQF27lABj+Rjmp9tK1i4xOg+LPxQbwroOn+H/D81/baXpq/Yvs9nHdX7WkrMrGVhCU82abdtZy0hxkKcbQPi34t+Ldc139pnwvbyahcQ6fYTL4gdhvjZvJuQUjQScKvHVRjG7gNuNfTPxXtG1e7kW8urXXNEukaKeM3P8AZGl2zxJJm224kN0qo8pdZN4AVsldx2fLX7SWmr4U8caXq1np+m262sh027uNPS4aECSJdqNNKf3rrIuzd8pIUkAjAXgxUnyOx6mA5faLmPVvjj4KFjrd1Nb/ALvc5O0DCNk9R6V5PeXkhcxybh82CCelfQXilB4j0OEt87eUpLA5zlRXh/j7QJtNnZkXcvQZHWvnI1tbM+mjDscQ/hy4a6lI6bsIP71P/wCEduGt2GTxwcDNNvPEbWMrRyqyr91lI4Off3q7pHjmxhtlRpFjwMAMfT0PNa8xpz9zOn8E3WrqFKsV245PXFc9q3woZ5iPm9iO36V3J+INpHPj9zs+8H38g/Tt/wDr71R8SfFKzMXyGPoQCBu+venHmuTKpE8l1z4ffY5Mfe5OTiptF8OGe4EQMY+vb3rU1bxd9t3SK33jnn1z9etZtr4ljtpd+zcytjngk/5HWur3jP2kTvNG+GEsMW87BGcMH9/wrZ0Lwxcx32JXiaOMlQoByW6j255rI8N/FRotPS3eNGwCAOu3v+n9TXTaXr8mpzr5a4GQSxHJ/D+prnd1uU5J7Hf/AAn8JSalrcNvb7VVvvSsBti6ZPqT6D2r638A+MF8EwyaTptneMuh28d3sQEtI0gYmVishYj5CG/0dxuUAEcKfn34NeRaGEKwVVPzE9Sa7bTdXXWviN4l1pw/9k6RBHoDyfYYLyzlZoA8nnRiQXDbfPljBjCHJfkqkgk6sDNupZHlZnG1M9C+MWvr4Z01b63vfJk8lMSWsUxadN7kxui4bflsKRtLs4YgbsDtPh9+0yljLO0uvMunW8MIneEXkclqzKoUO6hlVt7LkFt25mxuUfL478WPinaaZ4BWS9Nw9pHPDMbmS4U+fbNG+yeGbZiRWmKRuZkzG7ODuypfj/hf4Ta28QCG1u4rC8hUs1g9i1vJJ9oSNnPnxHKjbOZFTZuPmucEbq9o+c3PtzW/2k/FPgXw0usW+qTXmlyyhMXcPmXFuxYJtdWAI2tnO7Byu0AZzTfD/wC37r17r7aZdaPpKzRv/r5JZLWGeHCt5i+YAT8rg7VyCSoDYcNXj/hP4kN4Bgi8P+IG8mzkVYri8luAGSdyHj+baNzFn2gOd20BnYMSByvxT1v/AIV3q9rAs1jpcNxOfkito4o2YzKxRkWFismAwiK7ndvLV0KlS1RbM9jzPTfH2seMtUKyllnvfLmijumWMXKmI7mmVFmQquABsEbkKBuyNten+GdRtLrRNFjs7y1vLto9lvd2zx20MEO3y4wfmLbVjnLpI7YlUFjzIpf5K8CeMrmFLq3tNN012vFEwmO+dLbaVgML75QpnZBhSoYYRz5Y+XHvHhLxq3jLQoo5Y7r7brMCWV3bagkrPEk0h4eGBmgO13EpgBiYRuNzxOdz1IehW+KWo30erXVvfeVerstpmSOWaCaa4lVCruzoS0o/ekQhVLEvmNCFjPGaR4xuLW626heeXDiO8DRWq+Z5J6iRoxtDiQKQ7oVDAk7iAw7T4rfDjRrbTJNSuvOsrTfIy3P2pbWO2MqJGZpriYEzK21WPkqwUoEjLbSzcV4wg1Yaz/Y2maTJdQs7mO+MIZ4XRI98rvhGkX92drMGaIyD5yYzCkFxuelx+KLf4haBBq1pMi3Vv5ySy3f7vT9IRXQodpOGCM7Sbw6EqwDNjyjF498afCkXxB06+Sb+0G05cxzajdXMGk6bprKDM8cayKXkaN0kVC+BkSL8gUyrWtPiBefDTUIdM1qweHT5rtZgt5cqDdAAeRM5VUdNpAO5B5Y3HIDrgaXiubTbzV4/EEl3Z3WnWF8LXStEklSE28l15bCdzwQwbc4woCNO5DcSTmJRT32NIycXdGL8Pviq1posOm6gt4YVythqMlvJDb6lAMFHQyKvJRkbGOjr1GDVrxHcw6jH1WQNxwRWl4x8PyeOItK0rUdJbVte1OO3lexaOVI9NtmQ5ZTEhLSgRLsK+Wd0rqFVI9sPmfiPwf4n8BSXTaHNP4y0fTzGt5LaQ+WlhJIwVYcvKWdvmUAcvk4YAjFeFisuafNT2PosHmkJLlqFHxZ4XjlO5VC4ztwMjH06153rnhrymfbvRuchTkH8P/rV0kHxltddV4m3R3ELtG8MgIeNwcMrKcFWBBBBwRjFZeq6rHeBirc+nBrijCcXZnqc0ZK6PP8AWrO8sz8shZefusePw/8ArVg3V9M7FZHmY+712WqaxHHPtmUKuf4+/wCfFYt7NY3Hzb1UDpg4/wDrV1w5jjkkY9vIXb/lszEnBZ61NM0r7W2JGk2dxuzWdJqlnaMf33U9Ac/41LY+MrOB+MsVOcE9/p1q/fJXKjvPDtpb2kg8tO3BIFeheD3WGRGkYbVPJPCj6npXh8XxStLd2Zj5Yj5+ds49sV1vgybxF8UdKutQs2uvDvhuxi82fW57KW4iRTwpRE67mIUEkA54JwRWaw85vYqWKhTWrPa5viddTaivh7w2L2fXbiBphNbWUl3HpsQBPnMifebjCISu9uCVXcw9I+HWpaX4X+H9vMuq26+HFu5Hi8T6Vpv2XUtEmY74UuRK+26hkmy8jyMwVZgW2LNFMPJ/DfhTwv8AB7wPLJdNI1h4lvvs662qDbZ3MWM72ErF/MJjKky3EErSogSJd2674o+ON14Bh+3ajJYN4u0loLfUnS2SKHxVbtvSGdrdld0YLM5VsAo7SQsdu1V9rC4VUlpufO43GOq7dDtfiz8RrrVfF/8AY1vbQ/29JF5upx2cb/ZL25hhBaaFS6yBJ1ESsjttRgoJG5nf1b4f6Ppuh6lNYW6WskMZAtIJ7h4SscMSzJKyuAxjHm7GYMQ0TsVcBmhX5H/Zu0GTWNU1VVaWaO1e2ilMEM07Sxo221VkiKiUNcCOPyyrF9/lkDzXdfp/xJqNxbzw2uivjSb2z22a5hkt7GSUXMCyoz+YrxrJDbBZYiYkaRWZJhLEjdUkcKloe5ajrC+KvC2uXi28Nzp94kNxLGzzM0zB9zl3KxssUJZ5Vdd7fu1eMKpEbc546Zb7wq1tdapctdPcHT7XWT5NtGrv5cL2zSeWRGJitvJlEaGQS+YNiMFj4X4OfG/ULPxfar/ZurfZmgVbWE+Y/wBoK+QL103L5oVRC48xmjjdGg5DLI79t8YfDUc+vtqltfwizvorqS2aYedZTRRwi3aCW3w7SRgRGMuq8eciuOuKjpuS0fHfghdU0xxpNrdXzzWOcwm5nEnlbV2mTytsWVUTH5QRINhDg7mr3H4VeJ9J1DTrCRb2H+z7NiIpblxcrcSREkLGu7MjtvKmJRuThcqwCDwbRPClrq2taWslrHJLZwi6tbmW3mFpbRBY3RzcSgIAzg73j3yb1hIYhiIvWND+Jcmn+HdRuryzvNQM9nJOrxGaFbwOixrFAYlL7JNzx7ThpA7ggDzAXIOp6B4t+I02kapbaRpc1rcXV1GLqSKK6jSWwKwDdFI5MjKgZkLuu87HCgXElwkVx542p2/haNvst5/aUd1avJBeTo1u96FSKUW8U7Ru0NmNyCTDmRXhRnLSTQ+bkS+JbdPCN1eSXF5Isjvb3MC2f26a+kSVzD9oQEJlQGlihJAWKOPekgZyurqkjaJYXFnrFpHO2sfZjNEWiunu7pEE4a5m3Fpkje8REV1WMTXOSFjWSUwoq4cxhzaJHqPhjTL7VLjUNajuoxqZt447i18xni8xbJIgVCsBEXSWXezDcR8pQDE19L7wdotldu2n3mk6bH/oqnSVukuFIh8yRCjgFWMSSnIAYoTnDqT3NtqFxa6Vp8lr/Z9ylleR+bqMYdLeaFDHbXU/mxtGuQgt2RisSM2BsdUUDG1/RpJ9B1xbC1VbyznMZS+lQXCRuv2iGLzFVv3KRB8RsGYq+CDtUoNW2K5rnOav8X59N8O6gLy6uD4l1nNnJdTWsskVu77N22VwQUjQJtzC7EQYKHdht/xJqWgPdaX4WsfsXlTR/amaXS/O328iSotsTsWQiVHdAQ25Us3LkEyo3C634e/4RS/b7P5K3U3zzQSB8QzhVUSmPBffH8mY3DbNqoo3MprmZdRi0rVdSuLJksbiWxP2o3EUN19lhjBeR51EgMTeZMgAdZMlXYYGdjSuHNY7K98H+E/jf8TNQW6W3i0jTxDaq1q8f2pVihiCRzTkF9/70IHGYpSgZXOx0PmkHwZt9d0GfWdL8TRW+myQPPaWk00d1cyMpx5C4ZN0jAM6gqBtKDcXYLW74d+I9jpXgy+gdbiK+aG5nDS+cRKrsJXVJQvzRqjFQrDK4j3oGAWs1FbQfhJqVjbX0urDU7QS3E0bs32yJopGgARHZ/JEqxhgUX+JgAW3CJYeEtZI3p4qpD4WcjqPwP17U7ZTa3ujXPmFo4lEzwtLMAjeViREKsd64EmwsA5AIRiOI8U/BLxdogsy1vYyz32cQQ30UkkOGkVvMwSq7WidTk4zgdTivXfiJAvhr4d2P2qG8jgtMKJ3gK/2ez3FvIjmSZ2KbGUEiTOUB5yig8p41tdPuPC1vHJdW+otNJ5zCHfdx3Gx3VYgWKtEqozFWUttIBBclic/qsOhr/aFQ8+1T4HeIbTUILea/wBDWO5BKXUUrTW8gDMo2yRoQcsuAWKrl0BYbq17P9ltrK+ax1zxZ9gvCnmQQWOnNdJeckmNHDg+aqgs6bCVCORvEbEdXrup6brvgfTYdWuFmaSKS6thb6msP2X7OqqJJG8wMGVUt0X95nG3cikE1JqF7HD4Vs9Wtfsaahvml8hZCZo3ErSSA53FvOZGQruHK7m+8a0WHgjP65N9TV8E/CrwvoSiO20Vh4o04rBdW2q/8TFZJ9kkm0JIi5Ro42cuqR7BBPltm2avQdb+M9i95pHi7w7L/Y7Kkx1bRVgbyWgIjMjrGoMKzI3HEREkcSswMbbK4G08c29z8V7XUtN1JZTDvh1BFlSeYpHumj3qin/Vm3iyP3ikRkOGwxat4NOrLaa5a6Tp81jpc0byy2bHzmIT7KGIjyC5LeWCzKJFEExf5k21Xs10MZVJS3Z2d7fab4DuJvDrwWeqeG9YspGSGJIHW0llhCARR7FWR2do+QikBWbIDYPL+MDqAh1LX9WuL6+vIbJbSHyIxdLOYkjIkSRMhZCYZefkOc7cnCjpPD/gWW1vmmXUtO1q/tYTLH50MjLDb2jxyyNhl86NwZCo2Y3oqspiCkVd8QeFI/EHh+1hvFureGzRLm7VdOSG7lBMscRkiVmaCOGMvCUDIQEZGDHJrSJiX/2Xvh9qeoW0cem282oTRtdSyppj75roLCEa2kztVZCZrSRQS8bGRo/lWS5Rvpj4l6bKPBFtI2qzXt3cWbJp91cFXtLhpIJsusk0SRKB5YdkCDayE8LcSCvOfh5b/wDCJQ2CxwLp9vHqIiLzRpN9tngiaFI7VmdFZDbwzCSEeYoSViu2WZ2HqHi6LOltYXEMt3atPD5uozAQSoiQW+TsGSLgNEAWXCDMZDkcBNXK9Dz/AML+I30DxXMskk2g+GYdQt7DTZprR7WSSNbko0rRyYcHyTKjySMdy3IG8M8yj2nwD4uj1nRobPUFuLZIYodRmiku9jaXNLbrdrCu/a6tDbzxIdm5UV8MCrvJXyxe+JzH4ptW0+S3hje4ZIza2w3DdlbYiJirzsJYROywqiI8gYb/AJGb2b4JfFOG+umuFaO7m1yGws7Sx855FmuLY3COIzL+9kbZDZReY3zAGFd2RtpcoM8d0HSWsv3azz2M2mwyTxxRWdt+4ga2SRJFHkHy1cBk2bS+5ozhhJsW1e31ndWULS3Vz/YWk2k8s4Mk8yRQIDNGpuQ7s0824oCZs+WHnjzLHbSV+xWmf8EW/gTo1hplpa6R4igs9Hcy2luuu3HlRuXVy7KT87llB3vudWLMpViWMD/8ETPgO+w/2X4i3R3S3qt/bMvEyyeb5g7Kxf5iVAOVj/55RbKJPx4069vPFPiJNYhvmt47WFluLeFpLpNNmS5eFVhY7EeWKPJ3+W27DfJGWRjN5+bCHRrr+0L9Zp47eVJQALvCSJIPMSRtscct2ijJMjliofLRuP11u/8Aghl8A7y3uo207xSn2x1Mhj1yZCUUjbEcdYlCoFjbKrsXABGavj/gir8DTZ/Z5LHxRNbssivHJrczCQSBQ+e/O0dMcZX7vFAH5Q2982seIrz+0A00drJdRPtwslolxOk29WLKC43xSAyIj7WJKkKySN0yK6163s11RV1JInSO+NwDu+2x21iwlk3bjEiwWk0TkYZhIuAokbH63r/wR5+CsNw00Gna9bTM8LmSLVHDFopDImTjnBZxg8bXZfunFS6f/wAEhfg3pc8kkFn4jjeYHzGTV5FdiVVNwYAMrBEVQykFQPlIpdbgfijc+MNMvdAVb5Xmu7iWO2tgEMzQWt6nmoEjjAG8XEqxBByvzuAIpFY83qvgr+2LqS88q4WG+c6dHLcoJozHDO8JEsaYDBGM21WwFYHcu4l1/bjTv+CH/wCz/pEdutrofiK3W2RkUR69crkttDNw3DFUVCy4Owbfu8VYH/BFn4GCCKP+y/EG2C2WzjA1eRRHEG3BQAAB8wBJAycAHIGKYH4MeNoNQstWa4u2upP7Tvljlia8WWBIUFwzCWUt50sjzXLSgqm1NyKCrBgtbUvDF1a30ljcRvatbqbidNvlrdRB22SMDv4mzJtjCx7FjwATuFfvFZ/8EOfgLYSzPFpviRTcW62sn/E3YgxqzkKBtwB87ggYBDEGq93/AMEJ/wBn++1GS5l0rxIzOH2p/a7+Whfbk7cYZhsUKX3FQAq4AAoA/CGX4cxyM2lnVdSk8tIfJN3EG3TSOsMZ8qMsSXeQyuu5VxsyFOZTjw6de+K5rx4b9lkvJEit4JlDLBJlhAhnUtIy/LH91VTCkDoC37yL/wAG/wD+z2dMks5LXxpJbzQtA4bX5N5U262/3wA4IjUAYIxk9iQZ/wDhwX+zuJLcDSfEy29vN9o+zf21IYXkwQrEEZBX5cFSPuA8ksSAfg9pfwWk0iK4aO9kulkknvUSS8it5LNUJO1mYlQ4QLlMsACTlAo3P8L/AA90+a9i0LWv7WuxBHe2yoXMEkskbqBNGpdWJYpwdrhGkdQo2hG/evVP+CFP7P8Aq2jNp8ml+JfskkbROh1mRmkDSNI252BZiS7ckkgEgYyc2bL/AIIg/AfS9QhubPTvE1m0JUqsWsyBODu6YPVvmOO49CQQD8LLD4P3l3DLJJpKwv8A6PE2nurLNdSiRBLF5YOUE00MVsmcKUIwPLO5er13wAItIg094P7UtY4o9Mz525bkxm7WRYYzsjVWZJWeUGJleP5iVV6/a3T/APgiX8CdMs2t49O8SNbuzSGJ9YkZA52/vAuMK4ZdysACrHI6LjTvv+CO/wAE7uwS2j03X7WGGB7aAW+ryRPbxtFDDtVx82AlvDgEkAoD1ANBV9D8eNNsbOy1m5uoo7NZjas5WC1ezlnN05aEW7SYDBnByWKxvtXb5bNxR1Xw9Ddyu1rpdrbQ6bPLcTCC1EbSB2a5WaMyhd3zCWNVbABLjam4Bf2cvP8AgkJ8Fb2yeBtH1aNZLc2rPHqDRyFCAD84G4HgdCMEAjGBVB/+CNXwXmCebB4rk8tSika3JHsGWIK7AoUqHZQVwcYHOBgJPyq+FdrfWV3oGrLp0Md3p6GazZY47eS3Vri5a/SKWSNFjiMZDK+9ZNpuZAjx26ozv2mzJq1nptrDMLi3vLM2N280flwxRyRmMTOfMUrJ5QdkbAO4Krukbux/VS8/4I1/BW9tkt2s/EwtYnZo7f8AtmQxxKwCvEmQSkTIFQxqQpUdMsxaHxD/AMEXfgj4ikDNa+KrNY4RBFHZ63LbR24ByhRUwFZecEc/Mc5p+otT8WZ/Fcel6jqGoas1zb6e0UtpqkkyMxCCOK6CO2wosjyNPCrSb3ZzGAdjKydt8ItS1CwNwLdr6KG1tXuriDy4MXN1LeytcxLyC1t/aAtYovIDcWbKgaOTy3/Vrw7/AMEJvgD4XvPOtdN8VH94kpSXXZZE3oV2sFPCt8o+dcNnnO7BGhpX/BFD4J6REqxr40ZvLWNpX8Qz+c+37UyZkGH+R7ppEGdqtDAQAIlFGgz/2Q==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "*************/LAPI/V1.0/PACS/Controller/GUIShowInfoEx", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PACS", "Controller", "GUIShowInfoEx"]}}, "response": []}, {"name": "6、人员库查询", "request": {"method": "GET", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/BasicInfo", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "BasicInfo"]}}, "response": []}, {"name": "7、人员库人员清空", "request": {"method": "DELETE", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People"]}}, "response": []}, {"name": "8、新建人员库", "request": {"auth": {"type": "digest", "digest": [{"key": "password", "value": "admin_123", "type": "string"}, {"key": "qop", "value": "", "type": "string"}, {"key": "nonce", "value": "", "type": "string"}, {"key": "realm", "value": "", "type": "string"}, {"key": "username", "value": "admin", "type": "string"}, {"key": "algorithm", "value": "MD5", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Num\": 1,\r\n    \"LibList\": [\r\n        {\r\n            \"ID\": 10,\r\n            \"Type\": 3,\r\n            \"LastChange\": 1719541364,\r\n            \"Name\": \"宇视科技\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/BasicInfo", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "BasicInfo"]}}, "response": []}, {"name": "9、人员信息的新增", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Num\": 1,\r\n    \"PersonInfoList\": [\r\n        {\r\n            \"PersonID\": 123456,\r\n            \"LastChange\": 1718182729,\r\n            \"PersonCode\": \"\",\r\n            \"PersonName\": \"wwweee\",\r\n            \"Remarks\": \"宇视科技\",\r\n            \"TimeTemplateNum\": 0,\r\n            \"TimeTemplateList\": [\r\n                   {\r\n                    \"BeginTime\": 0,\r\n                    \"EndTime\": **********,\r\n                    \"Index\": 0\r\n                }\r\n            ],\r\n            \"IdentificationNum\": 3,\r\n            \"IdentificationList\": [\r\n                {\r\n                    \"Type\": 1,\r\n                    \"Number\": \"11111111\"\r\n                },\r\n                {\r\n                    \"Type\": 5,\r\n                    \"Number\": \"10101\"\r\n                },\r\n                {\r\n                    \"Type\": 0,\r\n                    \"Number\": \"429006200001011111\"\r\n                }\r\n            ],\r\n            \"PersonPIN\": \"12345678\",\r\n            \"ImageNum\": 1,\r\n            \"ImageList\": [\r\n                {\r\n                    \"FaceID\": 123456,\r\n                    \"Name\": \"123456_123456.jpg\",\r\n                    \"Size\": 21108,\r\n                    \"Data\": \"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\"\r\n                }\r\n            ],\r\n            \"FingerprintNum\": 1,\r\n            \"FingerprintList\": [\r\n                {\r\n                    \"FingerprintID\": 123456,\r\n                    \"Manufacturer\": \"\",\r\n                    \"FeatureVersion\": \"\",\r\n                    \"FeatureSize\": 1080,\r\n                    \"Feature\": \"xTFZAMSHgpre8ffGsIpSphgL+QVCiPL2AGSYSreG+w7QbidHu4rDPeiLt4aqhkta4PI2RLKIO4nRXlblwoizjcjbiBvFiROC0JN4i6iIC4rZnlbVGYeznlhPJs5kiSO67a6pSoKIk/rj6HgW04jsBZiTp0mMiCQm46hW0ZCHpEnlakVL0YrEXgAJyUNjilSC7zYpQ2qKNSXnNAfDfYeVZQAyOAluiUVt6DYmg2OK3XXoOBhC7IetkRhLuInhiRXWF0vHgmyJjj74NjpDVYh6hr8/iBG5iIt+0NoGm1aLzDYHeCgBWIw0Xe84GQEWhyRaH391iZKHJIXnLDXJz4t0oQAH14LriCZF78m4RnmIVm7+sikG3YnebQf/2IHHhvJO+HH2xeOIMm39KMgIN4rCZv5+SMgvhUKSN/4JhamFks7peDdFnIXsJuAyFsSVhJS1+Hn4wRuEtWYH/8cBiYYdbfg2GIMLhe19H8fZA8eMy8rvw/sC04w1vg/D2kJgjAYG+D4aQQqGzmHv/6iDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACL2cRJCZXFzQSQ/FYlTU0MsMjO0RWU6JRc58iOUFpUyIyPE2BF7PXasMSTGSjalKSRzVJc1T/VCF3NSiCLIhGNHNWMnqTEzYVODU7aREaUxef9Xj/T/qCEfRyFVVUX/92U/ePQv8i8/+RKWAAAAAAAeWWwWAmDVZJIIEBHdcgEWEnDmhYNKBBDlUtFoAAUxkmlMNnDqUqEmAzVkUgkVEMCahogXBJPrkFlNEbCpaRgZEKCaZlAeIhCZggQvAxIkZW1cMjDsY8wCEmBDkGEiMiGWcO9NJEHZYEI3UyQygvoGNGRSgjscNHWoYTAgQSG8cEhSMaHpcKEsYxWIcfI+YxMUgCtmMHSakQFBEzYSoHUsNmEygsARkxXNgEBjMhmucO4KR1ACoA4NDA8LHR4HEQYaCAkKEBIUExYoHxcEKwEYJDAqL0wz\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "http://*************/LAPI/V1.0/PeopleLibraries/3/People", "protocol": "http", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People"]}}, "response": []}, {"name": "10、人员信息的修改", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Num\": 1,\r\n    \"PersonInfoList\": [\r\n        {\r\n            \"PersonID\": 123456,\r\n            \"LastChange\": 1694745638,\r\n            \"PersonCode\": \"\",\r\n            \"PersonName\": \"wwweee\",\r\n            \"Remarks\": \"\",\r\n            \"TimeTemplateNum\": 0,\r\n            \"TimeTemplateList\": [\r\n                {\r\n                    \"BeginTime\": 0,\r\n                    \"EndTime\": **********,\r\n                    \"Index\": 0\r\n                }\r\n            ],\r\n            \"IdentificationNum\": 2,\r\n            \"IdentificationList\": [\r\n                {\r\n                    \"Type\": 1,\r\n                    \"Number\": \"222222\"\r\n                },\r\n                {\r\n                    \"Type\": 99,\r\n                    \"Number\": \"421221\"\r\n                }\r\n            ],\r\n\"PersonPIN\": \"123456\",\r\n             \"ImageNum\": 1,\r\n             \"ImageList\": [\r\n                {\r\n                    \"FaceID\": 123456,\r\n                    \"Name\": \"123456.jpg\",\r\n                    \"Size\": 21108,\r\n                    \"Data\": \"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\"\r\n                }\r\n            ],\r\n            \"FingerprintNum\": 1,\r\n            \"FingerprintList\": [\r\n                {\r\n                    \"FingerprintID\": 123456,\r\n                    \"FeatureSize\": 1080,\r\n                    \"Feature\": \"xRB3ABqFktoH/94CCYkLHQCJyIV4ipNZB35JgxiGq3UHQ9mCEYhL4ehDyMMXhaQZB//cAo6GpEkAOhmBiokEXe70GwQHif1G6EX9wxWGZVYAA96BD4gmEgfDzkOIiAZB+D4eA4qIvkH/fgmBf4nWQfg+KwLsigJh+E36BhyHYk3/f8vBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGImkXdihxZlGGKHFUVWnHYkYTAYQQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALd+wZUmS7gAxlgmZhsVBhQiaRkN06RTREkM0SREXEgXxhh0ae4A4ZZFjikM4RcEfc4D0bhEbA4I1El1AV4K48cEIUkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQHBgMBCQIKAAsMAAAAAAAAAAAAAAAAAAAAAAAAAAcA\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}\r\n"}, "url": {"raw": "http://*************/LAPI/V1.0/PeopleLibraries/3/People", "protocol": "http", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People"]}}, "response": []}, {"name": "11、人员信息的删除", "request": {"method": "DELETE", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456"]}}, "response": []}, {"name": "12、人员信息的查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Num\": 0, \r\n    \"QueryInfos\": [\r\n        {\r\n            \"QryType\": 27, \r\n            \"QryCondition\": 0, \r\n            \"QryData\": \"1001\"\r\n        }, \r\n        {\r\n            \"QryType\": 55, \r\n            \"QryCondition\": 0, \r\n            \"QryData\": \"Uniview\"\r\n        }\r\n    ], \r\n    \"Limit\": 6, \r\n    \"Offset\": 0\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/Info", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "Info"]}}, "response": []}, {"name": "13、图片质量判断", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Num\": 1,\r\n    \"FaceInfoList\": [\r\n        {\r\n            \"PersonID\": 1023,\r\n            \"FaceID\": 12344,\r\n            \"FaceImage\": {\r\n                \"Name\": \"1023.jpg\",\r\n                \"Size\": 51292,\r\n                \"Data\": \"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\"\r\n            }\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/FaceFeatureExtraction", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "FaceFeatureExtraction"]}}, "response": []}, {"name": "14、时间模板信息单个查询", "request": {"method": "GET", "header": [], "url": {"raw": "http://*************/LAPI/V1.0/PeopleLibraries/TimeTemplates/0", "protocol": "http", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "TimeTemplates", "0"]}}, "response": []}, {"name": "15、时间模板信息全部查询", "request": {"method": "GET", "header": [], "url": {"raw": "http://*************/LAPI/V1.0/PeopleLibraries/TimeTemplates/UpdateTime", "protocol": "http", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "TimeTemplates", "UpdateTime"]}}, "response": []}, {"name": "16、时间模板的新增", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Name\": \"访客计划模板123\",\r\n    \"LastChange\": 1515401684,\r\n    \"WeekPlan\": {\r\n        \"Enabled\": 1,\r\n        \"Num\": 7,\r\n        \"Days\": [\r\n            {\r\n                \"ID\": 0,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"12:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"22:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 1,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"10:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"12:00:00\",\r\n                        \"End\": \"18:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 2,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 3,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 4,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 5,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 6,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    },\r\n    \"Exception\": {\r\n        \"Enabled\": 1,\r\n        \"Num\": 1,\r\n        \"ExceptionDays\": [\r\n            {\r\n                \"ID\": 0,\r\n                \"Enabled\": 1,\r\n                \"Date\": \"2024-06-21\",\r\n                \"Num\": 1,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"DoorCtrlStatus\": 0\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/LAPI/V1.0/PeopleLibraries/TimeTemplates/1", "protocol": "http", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "TimeTemplates", "1"]}}, "response": []}, {"name": "17、时间模板的修改", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Name\": \"访客计划模板123\",\r\n    \"LastChange\": 1515401684,\r\n    \"WeekPlan\": {\r\n        \"Enabled\": 1,\r\n        \"Num\": 7,\r\n        \"Days\": [\r\n            {\r\n                \"ID\": 0,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"12:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"22:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 1,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"10:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"12:00:00\",\r\n                        \"End\": \"18:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 2,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 3,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 4,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 5,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            },\r\n            {\r\n                \"ID\": 6,\r\n                \"Num\": 8,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    },\r\n                    {\r\n                        \"Begin\": \"24:00:00\",\r\n                        \"End\": \"24:00:00\",\r\n                        \"ArmingType\": 0\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    },\r\n    \"Exception\": {\r\n        \"Enabled\": 1,\r\n        \"Num\": 1,\r\n        \"ExceptionDays\": [\r\n            {\r\n                \"ID\": 0,\r\n                \"Enabled\": 1,\r\n                \"Date\": \"2024-06-21\",\r\n                \"Num\": 1,\r\n                \"TimeSectionInfos\": [\r\n                    {\r\n                        \"Begin\": \"00:00:00\",\r\n                        \"End\": \"23:59:59\",\r\n                        \"DoorCtrlStatus\": 0\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n}"}, "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/TimeTemplates/0", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "TimeTemplates", "0"]}}, "response": []}, {"name": "18、时间模板的删除", "request": {"method": "DELETE", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/TimeTemplates/1", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "TimeTemplates", "1"]}}, "response": []}, {"name": "19、创建订阅", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n\"AddressType\":\t0,\t\t\t\t\t\t   \r\n\"IPAddress\":\t\"*************\",\t\t\t\t\r\n\"Port\":\t7777,\t\t\t\t\t\t\t\t\r\n\"Duration\": **********,\t\t\t\t\t\t\r\n\"Type\":255\r\n}"}, "url": {"raw": "*************/LAPI/V1.0/System/Event/Subscription", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "System", "Event", "Subscription"]}}, "response": []}, {"name": "20、刷新订阅（若为永久订阅，则不需要刷新）", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"Duration\": 3600\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/System/Event/Subscription/1000", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "System", "Event", "Subscription", "1000"]}}, "response": []}, {"name": "21、删除订阅", "request": {"method": "DELETE", "header": [], "url": {"raw": "*************/LAPI/V1.0/System/Event/Subscription/1000", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "System", "Event", "Subscription", "1000"]}}, "response": []}, {"name": "22、消防布防", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"ID\":\t1,\r\n\t\"Name\":\t\"消防报警\",\r\n\t\"GBID\":\t\"\",\r\n\t\"RunMode\": 1,\r\n\t\"Enabled\": 1\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/IO/InputSwitches/1/BasicInfos", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "IO", "InputSwitches", "1", "BasicInfos"]}}, "response": []}, {"name": "23、防拆布防", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"ID\":\t2,\r\n\t\"Name\":\t\"防拆报警\",\r\n\t\"GBID\":\t\"\",\r\n\t\"RunMode\": 1,\r\n\t\"Enabled\": 1\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/IO/InputSwitches/2/BasicInfos", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "IO", "InputSwitches", "2", "BasicInfos"]}}, "response": []}, {"name": "24、门磁报警布防", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"ID\":\t3,\r\n\t\"Name\":\t\"门磁报警\",\r\n\t\"GBID\":\t\"\",\r\n\t\"RunMode\": 1,\r\n\t\"Enabled\": 1\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/IO/InputSwitches/3/BasicInfos", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "IO", "InputSwitches", "3", "BasicInfos"]}}, "response": []}, {"name": "25、安检门报警布防", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"ID\":\t4,\r\n\t\"Name\":\t\"安检门报警\",\r\n\t\"GBID\":\t\"\",\r\n\t\"RunMode\": 1,\r\n\t\"Enabled\": 1\r\n}\r\n"}, "url": {"raw": "*************/LAPI/V1.0/IO/InputSwitches/4/BasicInfos", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "IO", "InputSwitches", "4", "BasicInfos"]}}, "response": []}, {"name": "26、指纹采集接口", "request": {"method": "GET", "header": [], "url": {"raw": "*************:8888/ysfinger/generate/?uid=4", "host": ["192", "168", "53", "45"], "port": "8888", "path": ["ys<PERSON>", "generate", ""], "query": [{"key": "uid", "value": "4"}]}}, "response": []}, {"name": "27、指纹提取接口", "request": {"method": "GET", "header": [], "url": {"raw": "*************:8888/ysfinger/merge", "host": ["192", "168", "53", "49"], "port": "8888", "path": ["ys<PERSON>", "merge"]}}, "response": []}, {"name": "28、获取指定人员所有的人脸信息", "request": {"method": "GET", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456/Faces", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456", "Faces"]}}, "response": []}, {"name": "29、获取指定人员人脸信息", "request": {"method": "GET", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456/Faces/1", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456", "Faces", "1"]}}, "response": []}, {"name": "30、添加指定人脸信息", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Num\": 1,\r\n    \"FaceInfoList\": [\r\n        {\r\n            \"FaceID\": 1,\r\n            \"ImageName\": \"123.jpg\",\r\n            \"ImageSize\": 89608,\r\n            \"ImageData\": \"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\",\r\n            \"ImageURL\": \"\",\r\n            \"FeatureNum\": 1,\r\n            \"FeatureList\": [\r\n                {\r\n                    \"Manufacturer\": \"100\",\r\n                    \"FeatureVersion\": \"10041721\",\r\n                    \"Feature\": \"H5RjvQkf+ztVpay9VFc8vG5XvLtEV7w9VFc8vDpXvDsiu1s85gaZPV/zHD0iu1s8dUENvXhoBT0JH/u8CR/7O8hquL0+H3u7bPOcvDtyAz55QY28Ole8PFnMJD1yQY09bBoVvV3MpL3nBpm9HJTjPVRXPLw+H3u7U6UsPTQJTL0u4lO9CR/7vPwe+zxyQQ092LiovV3MpL0lu1u9Ph97u7leB75Tpaw8dUENvaHpHr6+Q8A9OwnMvCjiUz0DH3s9X/McPRyUYz0uCcw8AAAAANzfoD2+Q8A9Olc8PHlBjbxsQY08CR97vFelLL1lGhU9/nuBPV/znDwvu9u8sTcPvnJBDT1HfjQ9VaWsvS4JzDwPbWs95gYZPvwe+zwORvO95waZvT4fe7tS8xw8OwnMvLE3D774VIm9Fm3rPAkf+zsJH3u8CR/7uyK72zzg36C9/nuBPV3MpL1yQY09NAnMvecGGb55QY28WcykPTpXPDyqzte9X/McPTowxD34VIm99VSJPUdXvLxBVzw9CR/7OzowxD1ZzKQ9mYDnPbkcyL0vu9u8R1e8vGzznLwiu1s8V6UsvTsJzLxs8xy8VFc8vC+727x4aAU9wUPAvf57gT1HVzy9H5RjvXtohb3vHns8Ph97ux+UY70lu9u9ZRoVPTpXvDtdzCS9LgnMPB+UY71TpSw9WcwkPRyUYz1dzCS9MQnMPWsaFb4clOM9Ax97PSW7270JRnM9NUsLvjpXPDxTpay8EEZzvTpXPDxTpaw8Olc8PFOlrDyoztc9bBoVvfVUiT1Tpay8HJRjPR+UY717aIW9aRqVPQkf+7ttHvs6ZPOcvSK7Wzxj85w9yGq4vda4KD7VHns7OwnMvD4f+7p5QY28dEGNvT4fe7tKfrQ9CR/7O1OlrLwGH3u9U6WsPEFXPD1HfjQ9PDDEvR7WIr5lGhU9CR/7vBZt6zx7aIW9QVc8PUFXPD00CUw9CR/7uwAAAAB1QQ29uRzIvTRLCz5lGhU9JbtbvQB8gb0clOM9mYDnPf57gT06Vzw8eGgFPchquL1HVzy9Ph97uzsJzLzIari9ckENPVWlrL1XpSy9KOJTPQB8gb0+H3u7AAAAAF/znDx4aAU9aRqVPTQJTD1HfjQ9NAlMPTQJTL3g36C9PjBEvVLzHDzvLZG9rvXPPV/znDw+H3u7R1c8vV3MpL0JH/u7n6ffPW0e+zpHfjQ9qM7XPT4fe7vvLZG9U6UsPb5DwD14aAU9XcwkvTpXvDyoztc94N+gvVLzHDze3yA+Ole8OzpXvDwJH3u8eUGNvFOlrLwu4lO9X/OcPP57gT10QY292LiovQkfe7wuCcw8AHyBvWbzHL1HVzy9IrtbvA==\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456/Faces", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456", "Faces"]}}, "response": []}, {"name": "31、修改指定人脸信息", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"FaceID\": 1,\r\n    \"ImageName\": \"123\",\r\n    \"ImageSize\": 21108,\r\n    \"ImageData\": \"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\",\r\n    \"FeatureNum\": 0,\r\n    \"FeatureList\": [\r\n        {\r\n            \"Manufacturer\": \"\",\r\n            \"FeatureVersion\": \"\",\r\n            \"Feature\": \"\"\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456/Faces/1", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456", "Faces", "1"]}}, "response": []}, {"name": "32、删除指定人脸信息", "request": {"method": "DELETE", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456/Faces/1", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456", "Faces", "1"]}}, "response": []}, {"name": "33、删除指定人员的所有人脸信息", "request": {"method": "DELETE", "header": [], "url": {"raw": "*************/LAPI/V1.0/PeopleLibraries/3/People/123456/Faces", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PeopleLibraries", "3", "People", "123456", "Faces"]}}, "response": []}, {"name": "34、查询常开常闭状态", "request": {"method": "GET", "header": [], "url": {"raw": "*************/LAPI/V1.0/PACS/Controller/OpenDoorTemplates", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PACS", "Controller", "OpenDoorTemplates"]}}, "response": []}, {"name": "35、修改常开常闭状态", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Type\": 2,\r\n    \"SafetyTipsEnabled\": 1,\r\n    \"DayNum\": 7,\r\n    \"Day\": [\r\n        {\r\n            \"ID\": 0,\r\n            \"SectionNum\": 1,\r\n            \"TimeSection\": [\r\n                {\r\n                    \"Begin\": \"00:00:00\",\r\n                    \"End\": \"23:59:59\",\r\n                    \"OpenDoorType\": 2\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"ID\": 1,\r\n            \"SectionNum\": 1,\r\n            \"TimeSection\": [\r\n                {\r\n                    \"Begin\": \"00:00:00\",\r\n                    \"End\": \"23:59:59\",\r\n                    \"OpenDoorType\": 1\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"ID\": 2,\r\n            \"SectionNum\": 1,\r\n            \"TimeSection\": [\r\n                {\r\n                    \"Begin\": \"00:00:00\",\r\n                    \"End\": \"23:59:59\",\r\n                    \"OpenDoorType\": 1\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"ID\": 3,\r\n            \"SectionNum\": 1,\r\n            \"TimeSection\": [\r\n                {\r\n                    \"Begin\": \"00:00:00\",\r\n                    \"End\": \"23:59:59\",\r\n                    \"OpenDoorType\": 1\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"ID\": 4,\r\n            \"SectionNum\": 0,\r\n            \"TimeSection\": []\r\n        },\r\n        {\r\n            \"ID\": 5,\r\n            \"SectionNum\": 0,\r\n            \"TimeSection\": []\r\n        },\r\n        {\r\n            \"ID\": 6,\r\n            \"SectionNum\": 1,\r\n            \"TimeSection\": [\r\n                {\r\n                    \"Begin\": \"00:00:00\",\r\n                    \"End\": \"23:59:59\",\r\n                    \"OpenDoorType\": 2\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "*************/LAPI/V1.0/PACS/Controller/OpenDoorTemplates", "host": ["192", "168", "53", "49"], "path": ["LAPI", "V1.0", "PACS", "Controller", "OpenDoorTemplates"]}}, "response": []}]}