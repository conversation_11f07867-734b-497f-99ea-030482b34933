﻿using System;
using System.Buffers;
using System.Collections.Concurrent;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Fpi.DB.Manager;
using Fpi.Entrance.YS.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Fpi.Entrance.YS.Controllers
{
    /// <summary>
    /// 门禁记录推送服务
    /// 处理TCP短连接和协议解析
    /// 所有短连接均不需要回复
    /// </summary>
    public class PushNotificationService
    {
        #region 属性字段

        /// <summary>
        /// 门禁记录表
        /// </summary>
        private const string ENTRANCE_DATA_TABLE = "entrance_data";

        /// <summary>
        /// 监听服务
        /// </summary>
        private TcpListener _listener;

        /// <summary>
        /// 最新一条人员核验缓存
        /// </summary>
        private PersonVerification _personEvents;

        /// <summary>
        /// 最新一条门磁告警缓存
        /// </summary>
        private DoorContactAlarm _alarmEvents;

        /// <summary>
        /// 使用线程安全的缓存容器(原始上传门禁记录，后续在此基础上去重)
        /// </summary>
        private static readonly ConcurrentDictionary<string, DateTime> _recordCache = new();

        /// <summary>
        /// 缓存过期时间（1分钟）
        /// </summary>
        private static readonly TimeSpan _expiration = TimeSpan.FromMinutes(1);

        /// <summary>
        /// 数据库写入锁
        /// </summary>
        private static readonly object _dbLockObj = new();

        /// <summary>
        /// 是否在监听
        /// </summary>
        private volatile bool _isRunning = false;

        /// <summary>
        /// 监听锁
        /// </summary>
        private static readonly object _lockObj = new();

        /// <summary>
        /// 监听端口
        /// </summary>
        private readonly int _port;

        /// <summary>
        /// 设备id
        /// </summary>
        private static string _id;

        /// <summary>
        /// 接收超时时间
        /// </summary>
        private readonly TimeSpan ReceiveTimeout = TimeSpan.FromSeconds(5);

        #endregion

        #region 构造

        // 构造函数
        public PushNotificationService(int port, string id)
        {
            _port = port;
            _id = id;
        }

        #endregion

        #region 公有方法

        public void Start()
        {
            lock(_lockObj)
            {
                if(_isRunning)
                {
                    return;
                }
                _isRunning = true;
            }

            _listener = new TcpListener(IPAddress.Any, _port);
            _listener.Start();
            EntranceLogHelper.Info($"订阅推送服务开始监听：{_listener.LocalEndpoint}");

            Task.Run(async () =>
            {
                try
                {
                    while(_isRunning)
                    {
                        var client = await _listener.AcceptTcpClientAsync();

                        // 为每个客户端创建独立任务
                        Task.Run(() => { HandleClientAsync(client); });
                    }
                }
                catch(Exception ex)
                {
                    EntranceLogHelper.Info($"监听出错：{ex.Message}");
                }
            });
        }

        /// <summary>
        /// 停止监听
        /// </summary>
        /// <returns></returns>
        public void Stop()
        {
            lock(_lockObj)
            {
                if(!_isRunning)
                {
                    return;
                }
                _isRunning = false;
            }

            try
            {
                _listener?.Stop();
                _recordCache.Clear();
                EntranceLogHelper.Info($"停止监听");
            }
            catch(Exception ex)
            {
                EntranceLogHelper.Info($"停止监听失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        private void HandleClientAsync(TcpClient client)
        {
            using(client)
            {
                using(var stream = client.GetStream())
                {
                    var tempBuffer = ArrayPool<byte>.Shared.Rent(1024 * 1024);
                    var receivedData = new StringBuilder();
                    //using var cts = new CancellationTokenSource(ReceiveTimeout);

                    try
                    {
                        while(true)
                        {
                            //var task = stream.ReadAsync(tempBuffer, 0, tempBuffer.Length, cts.Token);
                            //int bytesRead = task.Result;
                            //int bytesRead = await stream.ReadAsync(tempBuffer, 0, tempBuffer.Length, cts.Token);
                            int bytesRead = stream.Read(tempBuffer, 0, tempBuffer.Length);
                            if(bytesRead == 0)
                            {
                                break; // 连接关闭
                            }

                            // 处理分包和粘包
                            receivedData.Append(Encoding.UTF8.GetString(tempBuffer, 0, bytesRead));

                            EntranceLogHelper.Info($"推送原始报文：{receivedData}");

                            // 去除包头
                            var unescapedJson = ExtractJsonFromHttpResponse(receivedData.ToString());

                            ProcessNotification(unescapedJson);
                            receivedData.Remove(0, unescapedJson.Length);
                        }
                    }
                    catch(Exception ex)
                    {
                        EntranceLogHelper.Info($"错误处理：{ex.Message}");
                    }
                    finally
                    {
                        // 归还内存池
                        ArrayPool<byte>.Shared.Return(tempBuffer);
                    }
                }
            }
        }

        #region 接收处理

        /// <summary>
        /// 处理人员核验事件
        /// </summary>
        private void ProcessPersonEvent(PersonVerification personEvent)
        {
            // 只保留过去一分钟的上传记录
            if(Math.Abs(personEvent.Timestamp - new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds()) <= _expiration.TotalSeconds)
            {
                // 添加到缓存
                _personEvents = personEvent;

                TryCorrelateEvents(personEvent.DeviceCode);
            }
        }

        /// <summary>
        /// 处理门磁告警事件
        /// </summary>
        private void ProcessAlarmEvent(DoorContactAlarm alarmEvent)
        {
            // 只保留过去一分钟的上传记录
            if(Math.Abs(alarmEvent.StatusInfo.DoorStatus.TimeStamp - new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds()) <= _expiration.TotalSeconds)
            {
                // 添加到缓存
                _alarmEvents = alarmEvent;

                TryCorrelateEvents(alarmEvent.StatusInfo.DoorStatus.DeviceID);
            }
        }

        private void TryCorrelateEvents(string deviceSN)
        {
            // 验证时间差
            //if(Math.Abs(person.Timestamp - alarm.StatusInfo.DoorStatus.TimeStamp) <= EventTimeWindow)
            //{
            //    if(Enum.IsDefined(typeof(eDoorState), alarm.StatusInfo.DoorStatus.Status))
            //    {
            // 生成完整事件
            eDoorState state = _personEvents.LibMatInfoList.FirstOrDefault().MatchPersonInfo.PersonName.Equals("未识别") ?
                eDoorState.UnRecognize : eDoorState.Opened;
            var combinedEvent = new CombinedEvent
            {
                DeviceSN = deviceSN,
                UserName = _personEvents.LibMatInfoList.FirstOrDefault().MatchPersonInfo.PersonName,
                DoorStatus = /*alarm.StatusInfo.DoorStatus.Status*/state,
                EventTime = DateTimeOffset.FromUnixTimeSeconds(_personEvents.Timestamp).ToOffset(TimeSpan.FromHours(8)).DateTime,
                FaceImageNameStr = _personEvents.FaceInfoList.FirstOrDefault().FaceImage.Data
            };

            // 去重
            if(!IsDuplicate(combinedEvent))
            {
                // 保存到数据库
                WriteEntranceDataToDb(combinedEvent);
            }
            //    }
            //}
        }

        #endregion

        #region 去重

        /// <summary>
        /// 检查是否为重复记录
        /// </summary>
        /// <param name="deviceCode">设备唯一编码</param>
        /// <param name="recordId">记录ID</param>
        /// <returns>true表示需要过滤，false表示新记录</returns>
        private static bool IsDuplicate(CombinedEvent combinedEvent)
        {
            // 同一个设备的同一个人脸
            var key = $"{combinedEvent.DeviceSN}_{combinedEvent.UserName}";

            // 如果存在且未过期且同一个人
            if(_recordCache.TryGetValue(key, out var param) && ((DateTime.Now - param) <= _expiration))
            {
                return true;
            }

            // 更新或添加新记录
            _recordCache.AddOrUpdate(key,
                k => DateTime.Now,
                (k, v) => DateTime.Now);

            return false;
        }

        /// <summary>
        /// 清理过期记录
        /// </summary>
        private static void CleanExpiredRecords(object state)
        {
            var now = DateTime.Now;
            foreach(var kvp in _recordCache)
            {
                if((now - kvp.Value) > _expiration)
                {
                    _recordCache.TryRemove(kvp.Key, out _);
                }
            }
        }

        #endregion

        #region 保存到数据库

        /// <summary>
        /// 写门禁记录到数据库
        /// </summary>
        /// <param name="time"></param>
        /// <param name="sourceId"></param>
        /// <param name="info"></param>
        private static void WriteEntranceDataToDb(CombinedEvent combinedEvent)
        {
            FpiTable table = FpiDataBase.GetInstance().FindTableByName(ENTRANCE_DATA_TABLE);
            if(table == null)
            {
                throw new Exception("门禁记录表不存在！");
            }

            lock(_dbLockObj)
            {
                FpiRow row = new FpiRow();
                row.SetFieldValue("datatime", combinedEvent.EventTime);
                row.SetFieldValue("sourceid", _id);
                row.SetFieldValue("sourcesn", combinedEvent.DeviceSN);
                row.SetFieldValue("name", combinedEvent.UserName);
                row.SetFieldValue("status", combinedEvent.DoorStatus);
                row.SetFieldValue("faceinfo", combinedEvent.FaceImageNameStr);
                table.AddRecord(row);
            }
        }

        #endregion

        /// <summary>
        /// 移除报头无效信息，获取Json
        /// </summary>
        /// <param name="rawHttpResponse">回复原始报文</param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        private static string ExtractJsonFromHttpResponse(string rawHttpResponse)
        {
            // 查找JSON起始和结束位置
            int jsonStart = rawHttpResponse.IndexOf('{');
            int jsonEnd = rawHttpResponse.LastIndexOf('}');

            if(jsonStart == -1 || jsonEnd == -1 || jsonEnd <= jsonStart)
            {
                throw new ArgumentException("回应报文无效！");
            }

            // 提取JSON部分
            string jsonString = rawHttpResponse.Substring(
                jsonStart,
                jsonEnd - jsonStart + 1
            );

            // 验证JSON格式
            try
            {
                JToken.Parse(jsonString);
            }
            catch(JsonException ex)
            {
                throw new ArgumentException("Json格式错误", ex);
            }

            return jsonString;
        }

        /// <summary>
        /// 根据上传的数据进行反序列化
        /// </summary>
        /// <param name="json"></param>
        private void ProcessNotification(string json)
        {
            try
            {
                // 然后解析实际的JSON内容
                var jsonObject = JObject.Parse(json);

                // 根据数据类型处理
                if(json.Contains("LibMatInfoList")) // 人员核验记录
                {
                    PersonVerification person = jsonObject.ToObject<PersonVerification>();
                    ProcessPersonEvent(person);
                }
                //else if(json.Contains("DoorStatus")) // 门磁告警
                //{
                //    DoorContactAlarm alarmEvent = jsonObject.ToObject<DoorContactAlarm>();
                //    ProcessAlarmEvent(alarmEvent);
                //}
            }
            catch(Exception ex)
            {
                EntranceLogHelper.Info($"解析异常：{ex.Message}\n原始数据：{json}");
            }
        }

        #endregion
    }
}
