﻿using System.ComponentModel;

namespace Fpi.Camera
{
    #region 摄像机类型

    /// <summary>
    /// 摄像机类型
    /// </summary>
    public enum eCameraType
    {
        /// <summary>
        /// 海康摄像机
        /// </summary>
        [Description("海康摄像机")]
        HikCamera,

        /// <summary>
        /// 大华摄像机
        /// </summary>
        [Description("大华摄像机")]
        DHCamera,

        /// <summary>
        /// 宇视摄像机
        /// </summary>
        [Description("宇视摄像机")]
        YSCamera
    }

    #endregion

    #region 控制云台方向类型（共用）

    /// <summary>
    /// 控制云台方向类型（共用）
    /// </summary>
    public enum ControlType : byte
    {
        /// <summary>
        /// 右边
        /// </summary>
        RIGHT = 1,

        /// <summary>
        /// 左边
        /// </summary>
        LEFT,

        /// <summary>
        /// 上面
        /// </summary>
        UP,

        /// <summary>
        /// 下面
        /// </summary>
        DOWN,
        /// <summary>
        /// Upper left
        /// 左上
        /// </summary>
        LEFTTOP = 0x20,
        /// <summary>
        /// Upper right
        /// 右上
        /// </summary>
        RIGHTTOP,
        /// <summary>
        /// Down left
        /// 左下
        /// </summary>
        LEFTDOWN,
        /// <summary>
        /// Down right 
        /// 右下
        /// </summary>
        RIGHTDOWN,
        /// <summary>
        /// 雨刷
        /// </summary>
        WIPER
    }

    #endregion

    #region 大华平台控制类型

    /// <summary>
    /// 大华平台控制类型
    /// </summary>
    public enum DHPTZControlType : byte
    {
        /// <summary>
        /// +Zoom in 
        /// 变倍+
        /// </summary>
        ZOOM_ADD_CONTROL,
        /// <summary>
        /// -Zoom out
        /// 变倍-
        /// </summary>
        ZOOM_DEC_CONTROL,
        /// <summary>
        /// +Focus 
        /// 调焦+
        /// </summary>
        FOCUS_ADD_CONTROL,
        /// <summary>
        /// -Focus
        /// 调焦-
        /// </summary>
        FOCUS_DEC_CONTROL,
        /// <summary>
        /// + Aperture 
        /// 光圈+
        /// </summary>
        APERTURE_ADD_CONTROL,
        /// <summary>
        /// -Aperture
        /// 光圈-
        /// </summary>
        APERTURE_DEC_CONTROL,
    }

    #endregion
}