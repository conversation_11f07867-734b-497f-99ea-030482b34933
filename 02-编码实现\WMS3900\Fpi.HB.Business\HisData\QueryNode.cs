﻿using Fpi.Data.Config;
using Fpi.Xml;

namespace Fpi.HB.Business.HisData
{
    /// <summary>
    /// 查询节点类
    /// </summary>
    public class QueryNode : IdNameNode
    {
        public string UnitId;//单位
        public string Formula;//公式（暂未用）
        public int Dec = 2;//显示小数位数

        /// <summary>
        /// 选项名带单位
        /// </summary>
        /// <returns></returns>
        public string GetDisplayName()
        {
            ValueNode node = DataManager.GetInstance().GetValueNodeById(id);
            string result = node.name;
            if(node.Units != null && node.Units.GetCount() > 0)
            {
                result += "(" + node.Units[UnitId].name + ")";
            }
            return result;
        }

        /// <summary>
        /// 单位名
        /// </summary>
        /// <returns></returns>
        public string GetUnitName()
        {
            ValueNode node = DataManager.GetInstance().GetValueNodeById(id);
            return node.Units != null && node.Units.GetCount() > 0
                ? string.IsNullOrEmpty(UnitId) ? node.Units[0].name : node.Units[UnitId].name
                : string.Empty;
        }

        /// <summary>
        /// 关联数据因子
        /// </summary>
        /// <returns></returns>
        public ValueNode GetValueNode()
        {
            return DataManager.GetInstance().GetValueNodeById(id);
        }

        public override string ToString()
        {
            return GetDisplayName();
        }
    }
}