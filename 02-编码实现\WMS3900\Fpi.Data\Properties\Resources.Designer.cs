﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Fpi.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Fpi.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Add {
            get {
                object obj = ResourceManager.GetObject("Add", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Compute {
            get {
                object obj = ResourceManager.GetObject("Compute", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 配置已保存,是否立即生效? 的本地化字符串。
        /// </summary>
        internal static string ConfigSave {
            get {
                return ResourceManager.GetString("ConfigSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确定删除:{0}? 的本地化字符串。
        /// </summary>
        internal static string ConfirmDelete {
            get {
                return ResourceManager.GetString("ConfirmDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Edit {
            get {
                object obj = ResourceManager.GetObject("Edit", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 已启用输入处理器，但未选择具体实现 的本地化字符串。
        /// </summary>
        internal static string EnableInputProcessor {
            get {
                return ResourceManager.GetString("EnableInputProcessor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已启用输出处理器，但未选择具体实现 的本地化字符串。
        /// </summary>
        internal static string EnableOutputProcessor {
            get {
                return ResourceManager.GetString("EnableOutputProcessor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 存在重复编号的分组或变量,请修改后保存 的本地化字符串。
        /// </summary>
        internal static string ExistGroupVarNode {
            get {
                return ResourceManager.GetString("ExistGroupVarNode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已经存在此编号:{0}! 的本地化字符串。
        /// </summary>
        internal static string ExistId {
            get {
                return ResourceManager.GetString("ExistId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Group {
            get {
                object obj = ResourceManager.GetObject("Group", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Group_Del {
            get {
                object obj = ResourceManager.GetObject("Group_Del", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 编号或名称不可为空 的本地化字符串。
        /// </summary>
        internal static string IdNameEmpty {
            get {
                return ResourceManager.GetString("IdNameEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Node {
            get {
                object obj = ResourceManager.GetObject("Node", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Node_Del {
            get {
                object obj = ResourceManager.GetObject("Node_Del", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 未配置单位转换公式 的本地化字符串。
        /// </summary>
        internal static string NotConfigUnit {
            get {
                return ResourceManager.GetString("NotConfigUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 找不到编号为{0}的DataGroup 的本地化字符串。
        /// </summary>
        internal static string NotFindIdGroup {
            get {
                return ResourceManager.GetString("NotFindIdGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0}不是数值型变量 的本地化字符串。
        /// </summary>
        internal static string NotValueNode {
            get {
                return ResourceManager.GetString("NotValueNode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Remove {
            get {
                object obj = ResourceManager.GetObject("Remove", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Search {
            get {
                object obj = ResourceManager.GetObject("Search", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 设置了公式不能再设置值:{0} 的本地化字符串。
        /// </summary>
        internal static string SetFormula {
            get {
                return ResourceManager.GetString("SetFormula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统提示 的本地化字符串。
        /// </summary>
        internal static string SystemPrompt {
            get {
                return ResourceManager.GetString("SystemPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 至少须两组数据才能进行拟合计算 的本地化字符串。
        /// </summary>
        internal static string TwoDataCompute {
            get {
                return ResourceManager.GetString("TwoDataCompute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未知的报警限编号:{0} 的本地化字符串。
        /// </summary>
        internal static string UnknowAlarmId {
            get {
                return ResourceManager.GetString("UnknowAlarmId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未知的量程编号:{0} 的本地化字符串。
        /// </summary>
        internal static string UnknowScopeId {
            get {
                return ResourceManager.GetString("UnknowScopeId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未知的单位编号:{0} 的本地化字符串。
        /// </summary>
        internal static string UnknowUnitId {
            get {
                return ResourceManager.GetString("UnknowUnitId", resourceCulture);
            }
        }
    }
}
