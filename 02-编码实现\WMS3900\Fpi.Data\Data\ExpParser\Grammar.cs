﻿using System;
using System.Collections.Generic;
using Fpi.Alarm;
using Fpi.Data.Config;
using Fpi.Data.FuncServer;

namespace Fpi.Data.ExpParser
{
    public class Grammar
    {
        private readonly Evaluator _eval;

        public Grammar(Evaluator eval)
        {
            this._eval = eval;
        }

        public void Key_Analyze(TOKENLink startLink, out TOKENLink endLink, out List<TOKENLink> commaList)
        {
            int num = 1;
            TOKENLink item = startLink;
            commaList = new List<TOKENLink>();
            do
            {
                item = item.Next;
                if(item.Token.Type == ETokenType.token_operator)
                {
                    if(((TOKEN<Operator>)item.Token).Tag.Type == EOperatorType.LeftParen)
                    {
                        num++;
                    }
                    else if(((TOKEN<Operator>)item.Token).Tag.Type == EOperatorType.RightParen)
                    {
                        num--;
                    }
                }
                else if((num == 1) && (item.Token.Type == ETokenType.token_separator))
                {
                    commaList.Add(item);
                }
            }
            while(num != 0);
            endLink = item;
        }

        public IOperand Key_AND(TOKENLink startLink, out TOKENLink endLink)
        {
            IOperand operand = null;
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            TOKENLink next = startLink.Next.Next;
            foreach(TOKENLink link2 in list)
            {
                operand = this._eval.ExpressionEvaluate(next, link2.Prev);
                if(!((operand == null) || (operand.Type != EDataType.Dbool) || ((Operand<bool>)operand).TValue))
                {
                    break;
                }
                next = link2.Next;
            }
            if(!((operand != null) && ((operand.Type != EDataType.Dbool) || !((Operand<bool>)operand).TValue)))
            {
                operand = this._eval.ExpressionEvaluate(next, endLink.Prev);
            }
            return operand;
        }

        public IOperand Key_False(TOKENLink startLink, out TOKENLink endLink)
        {
            endLink = startLink.Next.Next;
            return new Operand<bool>(EDataType.Dbool, false);
        }

        public IOperand Key_IF(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand2 = this._eval.ExpressionEvaluate(startLink.Next.Next, list[0].Prev);
            return !((operand2 == null) || (operand2.Type != EDataType.Dbool) || !((Operand<bool>)operand2).TValue)
                ? this._eval.ExpressionEvaluate(list[0].Next, list[1].Prev)
                : this._eval.ExpressionEvaluate(list[1].Next, endLink.Prev);
        }

        public IOperand Key_Len(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
            return new Operand<int>(EDataType.Dint, operand.ToString().Length);
        }

        public IOperand Key_Not(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
            return !((operand == null) || (operand.Type != EDataType.Dbool) || !((Operand<bool>)operand).TValue)
                ? new Operand<bool>(EDataType.Dbool, false)
                : (IOperand)new Operand<bool>(EDataType.Dbool, true);
        }

        public IOperand Key_NowDate(TOKENLink startLink, out TOKENLink endLink)
        {
            endLink = startLink.Next.Next;
            return new Operand<DateTime>(EDataType.Ddatetime, DateTime.Now);
        }

        public IOperand Key_OR(TOKENLink startLink, out TOKENLink endLink)
        {
            IOperand operand = null;
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            TOKENLink next = startLink.Next.Next;
            foreach(TOKENLink link2 in list)
            {
                operand = this._eval.ExpressionEvaluate(next, link2.Prev);
                if(!(operand == null || (operand.Type != EDataType.Dbool) || !((Operand<bool>)operand).TValue))
                {
                    break;
                }
                next = link2.Next;
            }
            if(!((operand == null) || (operand.Type != EDataType.Dbool) || ((Operand<bool>)operand).TValue))
            {
                operand = this._eval.ExpressionEvaluate(next, endLink.Prev);
            }
            return operand;
        }

        public IOperand Key_ToDateTime(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
            return new Operand<DateTime>(EDataType.Ddatetime, Convert.ToDateTime(operand.Value));
        }

        public IOperand Key_ToDouble(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
            return new Operand<double>(EDataType.Ddouble, Convert.ToDouble(operand.Value));
        }

        public IOperand Key_ToInt(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
            return new Operand<int>(EDataType.Dint, Convert.ToInt32(operand.Value));
        }

        public IOperand Key_ToString(TOKENLink startLink, out TOKENLink endLink)
        {
            IOperand operand;
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            if(list.Count == 0)
            {
                operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
                return new Operand<string>(EDataType.Dstring, operand.ToString());
            }
            operand = this._eval.ExpressionEvaluate(startLink.Next.Next, list[0].Prev);
            IOperand operand2 = this._eval.ExpressionEvaluate(list[0].Next, endLink.Prev);
            if(operand.Type == EDataType.Ddatetime)
            {
                Operand<DateTime> operand4 = (Operand<DateTime>)operand;
                return new Operand<string>(EDataType.Dstring, operand4.TValue.ToString(operand2.ToString()));
            }
            else
            {
                return operand.Type == EDataType.Ddouble
                    ? new Operand<string>(EDataType.Dstring, ((double)operand.Value).ToString(operand2.ToString()))
                    : operand.Type == EDataType.Dint
                                    ? new Operand<string>(EDataType.Dstring, ((int)operand.Value).ToString(operand2.ToString()))
                                    : (IOperand)new Operand<string>(EDataType.Dstring, operand.Value.ToString());
            }
        }

        public IOperand Key_True(TOKENLink startLink, out TOKENLink endLink)
        {
            endLink = startLink.Next.Next;
            return new Operand<bool>(EDataType.Dbool, true);
        }

        public IOperand Key_GetNodeValue(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            object unit = string.Empty;
            object id;
            if(list.Count > 0)
            {
                unit = ((TOKEN<IOperand>)list[0].Next.Token).Tag.Value;
                id = ((TOKEN<IOperand>)list[0].Prev.Token).Tag.Value;
            }
            else
            {
                id = ((TOKEN<IOperand>)startLink.Next.Next.Token).Tag.Value;
            }
            VarNode node = DataManager.GetInstance().GetVarNodeById(id.ToString());
            double value = double.NaN;
            if(node != null && node is ValueNode)
            {
                value = unit != null && unit.ToString().Length > 0 ? (node as ValueNode).GetValue(unit.ToString()) : (node as ValueNode).GetValue();
            }

            return new Operand<double>(EDataType.Ddouble, value);
        }

        public IOperand Key_GetNodeIntValue(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            object unit = string.Empty;
            object id;
            if(list.Count > 0)
            {
                unit = ((TOKEN<IOperand>)(list[0].Next.Token)).Tag.Value;
                id = ((TOKEN<IOperand>)(list[0].Prev.Token)).Tag.Value;
            }
            else
            {
                id = ((TOKEN<IOperand>)(startLink.Next.Next.Token)).Tag.Value;
            }
            VarNode node = DataManager.GetInstance().GetVarNodeById(id.ToString());
            int value = int.MinValue;
            try
            {
                value = unit != null && unit.ToString().Length > 0 && node is ValueNode
                    ? Convert.ToInt32((node as ValueNode).GetValue(unit.ToString()))
                    : Convert.ToInt32(node.GetValue());
            }
            catch
            {
                // ignored
            }
            return new Operand<int>(EDataType.Dint, value);
        }

        public IOperand Key_GetNodeBoolValue(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            object id = ((TOKEN<IOperand>)(startLink.Next.Next.Token)).Tag.Value;
            VarNode node = DataManager.GetInstance().GetVarNodeById(id.ToString());
            bool value = false;

            if(node != null && node is StateNode)
            {
                value = (node as StateNode).GetValue();
            }
            return new Operand<bool>(EDataType.Dbool, value);
        }

        /// <summary>
        /// 获取指定因子列表因子布尔值，任一因子值为ture，结果为ture
        /// </summary>
        /// <param name="startLink"></param>
        /// <param name="endLink"></param>
        /// <returns></returns>
        public IOperand Key_GetAllNodeBoolValueOR(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            string nodeids = ((TOKEN<IOperand>)startLink.Next.Next.Token).Tag.Value.ToString();
            string[] nodeList = nodeids.Split(',');
            bool value = false;

            foreach(string id in nodeList)
            {
                StateNode node = DataManager.GetInstance().GetStateNodeById(id);
                if(node != null)
                {
                    value = value || node.GetValue();
                }
            }
            return new Operand<bool>(EDataType.Dbool, value);
        }

        /// <summary>
        /// 获取指定报警码是否为报警状态
        /// </summary>
        /// <param name="startLink"></param>
        /// <param name="endLink"></param>
        /// <returns></returns>
        public IOperand Key_GetAlarmNodeBoolValue(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            bool value = false;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            object alarmCode = string.Empty;
            object alarmSouceID = string.Empty;
            if(list.Count > 0)
            {
                alarmCode = ((TOKEN<IOperand>)list[0].Next.Token).Tag.Value;
                alarmSouceID = ((TOKEN<IOperand>)list[0].Prev.Token).Tag.Value;
            }
            else
            {
                alarmSouceID = ((TOKEN<IOperand>)startLink.Next.Next.Token).Tag.Value;
            }

            AlarmSource source = AlarmManager.GetInstance().alarmSources[alarmSouceID.ToString()] as AlarmSource;
            List<string> codeList = new List<string>();
            codeList.AddRange(((string)alarmCode).Split('|'));

            foreach(AlarmState state in source.AlarmStateList)
            {
                if(codeList.Contains(state.AlarmCode.id) && state.IsAlarm)
                {
                    value = true;
                    break;
                }
            }
            return new Operand<bool>(EDataType.Dbool, value);
        }

        public IOperand Key_IsAlarmNode(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            string nodeids = ((TOKEN<IOperand>)(startLink.Next.Next.Token)).Tag.Value.ToString();
            string[] nodeList = nodeids.Split(',');
            bool alarm = false;
            foreach(string id in nodeList)
            {
                ValueNode node = DataManager.GetInstance().GetValueNodeById(id);
                if(node != null)
                {
                    alarm = node.IsOverAlarmLimit(node.GetValue());
                    if(alarm)
                    {
                        break;
                    }
                }
            }

            return new Operand<bool>(EDataType.Dbool, alarm);
        }

        public IOperand Key_IsNodeCycleValueAlarm(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            string nodeids = ((TOKEN<IOperand>)startLink.Next.Next.Token).Tag.Value.ToString();
            string[] nodeList = nodeids.Split(',');
            bool alarm = false;
            foreach(string id in nodeList)
            {
                ValueNode node = DataManager.GetInstance().GetValueNodeById(id);
                if(node != null && node.CycleFlag != (int)eValueNodeState.Z)
                {
                    alarm = node.IsOverAlarmLimit(node.CycleValue);
                    if(alarm)
                    {
                        break;
                    }
                }
            }

            return new Operand<bool>(EDataType.Dbool, alarm);
        }

        public IOperand Key_DoubleNaN(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            IOperand operand = this._eval.ExpressionEvaluate(startLink.Next.Next, endLink.Prev);
            bool isnan = double.IsNaN(Convert.ToDouble(operand.Value));
            return new Operand<bool>(EDataType.Dbool, isnan);
        }

        public IOperand Key_CASE(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            TOKENLink start = startLink.Next.Next;
            for(int i = 0; i < list.Count; i++)
            {
                if(i % 2 == 0)
                {

                    IOperand operand2 = this._eval.ExpressionEvaluate(start, list[i].Prev);
                    if(!((operand2 == null) || (operand2.Type != EDataType.Dbool) || !((Operand<bool>)operand2).TValue))
                    {
                        return this._eval.ExpressionEvaluate(list[i].Next, list[i].Next);
                    }
                    if(i < list.Count - 1)
                    {
                        start = list[i + 1].Next;
                    }
                }
            }
            return this._eval.ExpressionEvaluate(endLink.Prev, endLink.Prev);
        }

        public IOperand Key_Invoke(TOKENLink startLink, out TOKENLink endLink)
        {
            List<TOKENLink> list;
            this.Key_Analyze(startLink.Next, out endLink, out list);
            var parameters = new List<object>();
            if(list.Count > 0)
            {
                for(int i = 0; i < list.Count; i++)
                {
                    IOperand operand2 = this._eval.ExpressionEvaluate(list[i].Next, i < list.Count - 1 ? list[i + 1].Prev : endLink.Prev);
                    parameters.Add(operand2.Value);
                }
            }
            string service = ((TOKEN<IOperand>)(startLink.Next.Next.Token)).Tag.Value.ToString();
            object[] paramsObj = null;
            if(parameters.Count > 0)
            {
                paramsObj = parameters.ToArray();
            }
            object result = ServiceManager.GetInstance().Invoke(service, paramsObj);
            if(result == null)
            {
                return new Operand<string>(EDataType.Dstring, "");
            }

            if(result is bool)
            {
                return new Operand<bool>(EDataType.Dbool, Convert.ToBoolean(result));
            }
            else if(result is double)
            {
                return new Operand<double>(EDataType.Ddouble, Convert.ToDouble(result));
            }
            else if(result is int)
            {
                return new Operand<int>(EDataType.Dint, Convert.ToInt32(result));
            }
            else if(result is DateTime)
            {
                return new Operand<DateTime>(EDataType.Ddatetime, Convert.ToDateTime(result));
            }
            return new Operand<string>(EDataType.Dstring, result.ToString());
        }
    }
}

