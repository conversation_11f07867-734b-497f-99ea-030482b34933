﻿//==================================================================================================
//类名：     OutSimulateDataChannel   
//创建人:    曹旭
//创建时间:  2012-9-26 15:12:44
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using System;
using Fpi.Data.Config;
using Fpi.Xml;

namespace Fpi.Devices.Channel
{
    public class OutValueChannel : ValueChannel
    {
        public OutValueChannel()
        {
            this.ChannelType = eDeviceChannelType.OutValue;
        }

        public override BaseNode Init(System.Xml.XmlNode node)
        {
            base.Init(node);

            if(this.ValueNode != null)
            {
                this.ValueNode.ValueChangedEvent += new ValueChangeHandler(SetOutValue);
            }

            return this;
        }

        /// <summary>
        /// 设置模拟量数据输出（将变量值输出到量通道值上）
        /// </summary>
        public override void SetOutValue(VarNode varNode, object newValue)
        {
            this.ChannelValue = this.DataProcessor != null ? this.DataProcessor.OutputProcessData(Convert.ToDouble(newValue)) : (object)Convert.ToDouble(newValue);
        }
    }
}
