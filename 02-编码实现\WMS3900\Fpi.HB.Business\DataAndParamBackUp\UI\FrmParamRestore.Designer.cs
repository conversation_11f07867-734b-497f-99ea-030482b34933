﻿namespace Fpi.HB.Business
{
    partial class FrmParamRestore
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmParamRestore));
            this.btnRestore = new System.Windows.Forms.Button();
            this.ofDialog = new System.Windows.Forms.OpenFileDialog();
            this.btnXMLresetFile = new System.Windows.Forms.Button();
            this.txtConfigResetFile = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // btnRestore
            // 
            this.btnRestore.Location = new System.Drawing.Point(368, 77);
            this.btnRestore.Name = "btnRestore";
            this.btnRestore.Size = new System.Drawing.Size(75, 23);
            this.btnRestore.TabIndex = 1;
            this.btnRestore.Text = "执行恢复";
            this.btnRestore.UseVisualStyleBackColor = true;
            this.btnRestore.Click += new System.EventHandler(this.btnRestore_Click);
            // 
            // ofDialog
            // 
            this.ofDialog.Filter = "bak 文件|*.bak";
            // 
            // btnXMLresetFile
            // 
            this.btnXMLresetFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnXMLresetFile.Location = new System.Drawing.Point(401, 27);
            this.btnXMLresetFile.Name = "btnXMLresetFile";
            this.btnXMLresetFile.Size = new System.Drawing.Size(51, 23);
            this.btnXMLresetFile.TabIndex = 11;
            this.btnXMLresetFile.Text = "...";
            this.btnXMLresetFile.UseVisualStyleBackColor = true;
            this.btnXMLresetFile.Click += new System.EventHandler(this.btnXMLresetFile_Click);
            // 
            // txtConfigResetFile
            // 
            this.txtConfigResetFile.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtConfigResetFile.CanEmpty = false;
            this.txtConfigResetFile.DigitLength = 2;
            this.txtConfigResetFile.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtConfigResetFile.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtConfigResetFile.IsValidCheck = false;
            this.txtConfigResetFile.Label = "备份目录";
            this.txtConfigResetFile.Location = new System.Drawing.Point(83, 28);
            this.txtConfigResetFile.MaxValue = null;
            this.txtConfigResetFile.MinValue = null;
            this.txtConfigResetFile.Name = "txtConfigResetFile";
            this.txtConfigResetFile.ReadOnly = true;
            this.txtConfigResetFile.Size = new System.Drawing.Size(312, 21);
            this.txtConfigResetFile.TabIndex = 10;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(12, 32);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "恢复目录：";
            // 
            // FormConfigRestore
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(455, 112);
            this.Controls.Add(this.btnXMLresetFile);
            this.Controls.Add(this.txtConfigResetFile);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.btnRestore);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormConfigRestore";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "配置文件恢复";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnRestore;
        private System.Windows.Forms.OpenFileDialog ofDialog;
        private System.Windows.Forms.Button btnXMLresetFile;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtConfigResetFile;
        private System.Windows.Forms.Label label4;
    }
}