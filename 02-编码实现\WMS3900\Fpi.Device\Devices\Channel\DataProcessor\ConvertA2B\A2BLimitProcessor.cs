﻿namespace Fpi.Devices.Channel
{
    public class A2BLimitProcessor : Processor
    {
        #region — 属性 —

        /// <summary>电流/电压 最小值</summary>
        private double _minSignalValue;

        /// <summary>电流/电压 最大值</summary>
        private double _maxSignalValue;

        /// <summary>对应数值 最小值</summary>
        private double _minDigitalValue;

        /// <summary>对应数值 最大值</summary>
        private double _maxDigitalValue;

        #endregion

        public A2BLimitProcessor()
        {
            this.ucParamConfig = new ConfigA2BPanel();
        }

        public A2BLimitProcessor(string param)
            : this()
        {
            this.SetParam(param);
        }

        public override void SetParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length == 4)
            {
                double.TryParse(paramStrs[0], out _minSignalValue);
                double.TryParse(paramStrs[1], out _maxSignalValue);

                double.TryParse(paramStrs[2], out _minDigitalValue);
                double.TryParse(paramStrs[3], out _maxDigitalValue);
            }

            if(paramStrs.Length == 5)
            {
                double.TryParse(paramStrs[0], out _minSignalValue);
                double.TryParse(paramStrs[1], out _maxSignalValue);

                double.TryParse(paramStrs[2], out _minDigitalValue);
                double.TryParse(paramStrs[3], out _maxDigitalValue);
            }

        }

        /// <summary>
        /// 输入数据处理
        /// </summary>
        /// <param name="inputValue"></param>
        /// <returns></returns>
        public override double InputProcessData(double inputValue)
        {
            double dataProcessed = double.NaN;
            // 输入信号不足，返回下限值
            if(inputValue <= this._minSignalValue)
            {
                return _minDigitalValue;
            }
            // 输入信号过大，返回上限值
            if(inputValue >= this._maxSignalValue)
            {
                return _maxDigitalValue;
            }
            // 合适信号，计算值
            dataProcessed = (this._maxDigitalValue - this._minDigitalValue)
                            / (this._maxSignalValue - this._minSignalValue)
                            * (inputValue - this._minSignalValue)
                            + this._minDigitalValue;

            return dataProcessed;
        }

        /// <summary>
        /// 输出数据处理
        /// </summary>
        /// <param name="outputValue"></param>
        /// <returns></returns>
        public override double OutputProcessData(double outputValue)
        {
            double dataProcessed = double.NaN;

            dataProcessed = outputValue <= this._minDigitalValue
                ? this._minSignalValue
                : outputValue >= this._maxDigitalValue
                    ? this._maxSignalValue
                    : (this._maxSignalValue - this._minSignalValue)
                                                / (this._maxDigitalValue - this._minDigitalValue)
                                                * (outputValue - this._minDigitalValue)
                                                + this._minSignalValue;

            return dataProcessed;
        }

        public override string ToString()
        {
            return "范围转换处理器[信号锁限值](如：4-20MA、0-5V等)";
        }
    }
}
