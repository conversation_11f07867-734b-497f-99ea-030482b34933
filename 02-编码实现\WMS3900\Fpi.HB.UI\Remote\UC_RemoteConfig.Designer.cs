﻿namespace Fpi.HB.UI.Remote
{
    partial class UC_RemoteConfig
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlValid = new Sunny.UI.UIPanel();
            this.ckbValid = new Sunny.UI.UICheckBox();
            this.pnlMain = new Sunny.UI.UIPanel();
            this.pnlDetail = new Sunny.UI.UIPanel();
            this.gbHttp = new Sunny.UI.UIGroupBox();
            this.cmbContentTypes = new Sunny.UI.UIComboBox();
            this.txtHttpHeaders = new Sunny.UI.UIRichTextBox();
            this.uiLabel15 = new Sunny.UI.UILabel();
            this.txtUrl = new Sunny.UI.UITextBox();
            this.uiLabel13 = new Sunny.UI.UILabel();
            this.uiLabel14 = new Sunny.UI.UILabel();
            this.gbUdp = new Sunny.UI.UIGroupBox();
            this.txtUdpIp = new Sunny.UI.UIIPTextBox();
            this.txtLocalPort = new Sunny.UI.UITextBox();
            this.txtDesPort = new Sunny.UI.UITextBox();
            this.uiLabel10 = new Sunny.UI.UILabel();
            this.uiLabel11 = new Sunny.UI.UILabel();
            this.uiLabel12 = new Sunny.UI.UILabel();
            this.gbCOM = new Sunny.UI.UIGroupBox();
            this.cbmIndex = new Sunny.UI.UIComboBox();
            this.cbxBaud = new Sunny.UI.UIComboBox();
            this.cbxPort = new Sunny.UI.UIComboBox();
            this.lblIndex = new Sunny.UI.UILabel();
            this.uiLabel6 = new Sunny.UI.UILabel();
            this.uiLabel7 = new Sunny.UI.UILabel();
            this.gbTcpClient = new Sunny.UI.UIGroupBox();
            this.txtPortNum = new Sunny.UI.UITextBox();
            this.tbxAddress = new Sunny.UI.UIIPTextBox();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.gbTcpSvr = new Sunny.UI.UIGroupBox();
            this.txtListenerPort = new Sunny.UI.UITextBox();
            this.uiLabel9 = new Sunny.UI.UILabel();
            this.gbProtocol = new Sunny.UI.UIGroupBox();
            this.btnAdvConfig = new Sunny.UI.UIButton();
            this.lblDescription = new Sunny.UI.UILabel();
            this.cmbProtocol = new Sunny.UI.UIComboBox();
            this.txtInterval = new Sunny.UI.UITextBox();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.gbBus = new Sunny.UI.UIGroupBox();
            this.rdbHttp = new Sunny.UI.UIRadioButton();
            this.rdbUdp = new Sunny.UI.UIRadioButton();
            this.rdbTcpSvr = new Sunny.UI.UIRadioButton();
            this.rdbTcpClient = new Sunny.UI.UIRadioButton();
            this.rdbComm = new Sunny.UI.UIRadioButton();
            this.tbxPort = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.pnlValid.SuspendLayout();
            this.pnlMain.SuspendLayout();
            this.pnlDetail.SuspendLayout();
            this.gbHttp.SuspendLayout();
            this.gbUdp.SuspendLayout();
            this.gbCOM.SuspendLayout();
            this.gbTcpClient.SuspendLayout();
            this.gbTcpSvr.SuspendLayout();
            this.gbProtocol.SuspendLayout();
            this.gbBus.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlValid
            // 
            this.pnlValid.Controls.Add(this.ckbValid);
            this.pnlValid.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlValid.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlValid.Location = new System.Drawing.Point(0, 0);
            this.pnlValid.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlValid.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlValid.Name = "pnlValid";
            this.pnlValid.RectColor = System.Drawing.Color.Transparent;
            this.pnlValid.Size = new System.Drawing.Size(795, 36);
            this.pnlValid.TabIndex = 1;
            this.pnlValid.Text = null;
            this.pnlValid.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // ckbValid
            // 
            this.ckbValid.BackColor = System.Drawing.Color.Transparent;
            this.ckbValid.Cursor = System.Windows.Forms.Cursors.Hand;
            this.ckbValid.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ckbValid.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(126)))), ((int)(((byte)(255)))));
            this.ckbValid.Location = new System.Drawing.Point(36, 7);
            this.ckbValid.MinimumSize = new System.Drawing.Size(1, 1);
            this.ckbValid.Name = "ckbValid";
            this.ckbValid.Size = new System.Drawing.Size(150, 29);
            this.ckbValid.TabIndex = 0;
            this.ckbValid.Text = "启用此路输出";
            this.ckbValid.CheckedChanged += new System.EventHandler(this.ckbValid_CheckedChanged);
            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.pnlDetail);
            this.pnlMain.Controls.Add(this.gbProtocol);
            this.pnlMain.Controls.Add(this.gbBus);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlMain.Location = new System.Drawing.Point(0, 36);
            this.pnlMain.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlMain.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.RectColor = System.Drawing.Color.Transparent;
            this.pnlMain.Size = new System.Drawing.Size(795, 439);
            this.pnlMain.TabIndex = 2;
            this.pnlMain.Text = null;
            this.pnlMain.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlDetail
            // 
            this.pnlDetail.Controls.Add(this.gbHttp);
            this.pnlDetail.Controls.Add(this.gbUdp);
            this.pnlDetail.Controls.Add(this.gbCOM);
            this.pnlDetail.Controls.Add(this.gbTcpClient);
            this.pnlDetail.Controls.Add(this.gbTcpSvr);
            this.pnlDetail.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlDetail.Location = new System.Drawing.Point(12, 73);
            this.pnlDetail.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDetail.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDetail.Name = "pnlDetail";
            this.pnlDetail.RectColor = System.Drawing.Color.Transparent;
            this.pnlDetail.Size = new System.Drawing.Size(771, 180);
            this.pnlDetail.TabIndex = 3;
            this.pnlDetail.Text = null;
            this.pnlDetail.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // gbHttp
            // 
            this.gbHttp.Controls.Add(this.cmbContentTypes);
            this.gbHttp.Controls.Add(this.txtHttpHeaders);
            this.gbHttp.Controls.Add(this.uiLabel15);
            this.gbHttp.Controls.Add(this.txtUrl);
            this.gbHttp.Controls.Add(this.uiLabel13);
            this.gbHttp.Controls.Add(this.uiLabel14);
            this.gbHttp.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbHttp.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbHttp.Location = new System.Drawing.Point(0, 0);
            this.gbHttp.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbHttp.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbHttp.Name = "gbHttp";
            this.gbHttp.Padding = new System.Windows.Forms.Padding(20, 32, 20, 0);
            this.gbHttp.Size = new System.Drawing.Size(771, 180);
            this.gbHttp.TabIndex = 12;
            this.gbHttp.Text = "通讯链路配置";
            this.gbHttp.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmbContentTypes
            // 
            this.cmbContentTypes.DataSource = null;
            this.cmbContentTypes.DropDownAutoWidth = true;
            this.cmbContentTypes.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbContentTypes.FillColor = System.Drawing.Color.White;
            this.cmbContentTypes.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbContentTypes.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbContentTypes.Items.AddRange(new object[] {
            "application/json",
            "application/x-www-form-urlencoded"});
            this.cmbContentTypes.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbContentTypes.Location = new System.Drawing.Point(145, 65);
            this.cmbContentTypes.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbContentTypes.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbContentTypes.Name = "cmbContentTypes";
            this.cmbContentTypes.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbContentTypes.Size = new System.Drawing.Size(612, 29);
            this.cmbContentTypes.SymbolSize = 24;
            this.cmbContentTypes.TabIndex = 9;
            this.cmbContentTypes.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbContentTypes.Watermark = "";
            // 
            // txtHttpHeaders
            // 
            this.txtHttpHeaders.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtHttpHeaders.FillColor = System.Drawing.Color.White;
            this.txtHttpHeaders.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtHttpHeaders.Location = new System.Drawing.Point(145, 100);
            this.txtHttpHeaders.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtHttpHeaders.MinimumSize = new System.Drawing.Size(1, 1);
            this.txtHttpHeaders.Name = "txtHttpHeaders";
            this.txtHttpHeaders.Padding = new System.Windows.Forms.Padding(2);
            this.txtHttpHeaders.ShowText = false;
            this.txtHttpHeaders.Size = new System.Drawing.Size(612, 75);
            this.txtHttpHeaders.TabIndex = 8;
            this.txtHttpHeaders.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiLabel15
            // 
            this.uiLabel15.AutoSize = true;
            this.uiLabel15.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel15.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel15.Location = new System.Drawing.Point(62, 104);
            this.uiLabel15.Name = "uiLabel15";
            this.uiLabel15.Size = new System.Drawing.Size(74, 21);
            this.uiLabel15.TabIndex = 7;
            this.uiLabel15.Text = "标头信息";
            // 
            // txtUrl
            // 
            this.txtUrl.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtUrl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtUrl.Location = new System.Drawing.Point(145, 30);
            this.txtUrl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtUrl.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtUrl.Name = "txtUrl";
            this.txtUrl.Padding = new System.Windows.Forms.Padding(5);
            this.txtUrl.ShowText = false;
            this.txtUrl.Size = new System.Drawing.Size(612, 29);
            this.txtUrl.TabIndex = 6;
            this.txtUrl.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtUrl.Watermark = "";
            // 
            // uiLabel13
            // 
            this.uiLabel13.AutoSize = true;
            this.uiLabel13.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel13.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel13.Location = new System.Drawing.Point(62, 69);
            this.uiLabel13.Name = "uiLabel13";
            this.uiLabel13.Size = new System.Drawing.Size(74, 21);
            this.uiLabel13.TabIndex = 2;
            this.uiLabel13.Text = "连接类型";
            // 
            // uiLabel14
            // 
            this.uiLabel14.AutoSize = true;
            this.uiLabel14.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel14.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel14.Location = new System.Drawing.Point(78, 34);
            this.uiLabel14.Name = "uiLabel14";
            this.uiLabel14.Size = new System.Drawing.Size(58, 21);
            this.uiLabel14.TabIndex = 1;
            this.uiLabel14.Text = "服务器";
            // 
            // gbUdp
            // 
            this.gbUdp.Controls.Add(this.txtUdpIp);
            this.gbUdp.Controls.Add(this.txtLocalPort);
            this.gbUdp.Controls.Add(this.txtDesPort);
            this.gbUdp.Controls.Add(this.uiLabel10);
            this.gbUdp.Controls.Add(this.uiLabel11);
            this.gbUdp.Controls.Add(this.uiLabel12);
            this.gbUdp.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbUdp.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbUdp.Location = new System.Drawing.Point(0, 0);
            this.gbUdp.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbUdp.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbUdp.Name = "gbUdp";
            this.gbUdp.Padding = new System.Windows.Forms.Padding(20, 32, 20, 0);
            this.gbUdp.Size = new System.Drawing.Size(771, 180);
            this.gbUdp.TabIndex = 11;
            this.gbUdp.Text = "通讯链路配置";
            this.gbUdp.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtUdpIp
            // 
            this.txtUdpIp.FillColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.txtUdpIp.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtUdpIp.Location = new System.Drawing.Point(145, 29);
            this.txtUdpIp.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtUdpIp.MinimumSize = new System.Drawing.Size(1, 1);
            this.txtUdpIp.Name = "txtUdpIp";
            this.txtUdpIp.Padding = new System.Windows.Forms.Padding(1);
            this.txtUdpIp.ShowText = false;
            this.txtUdpIp.Size = new System.Drawing.Size(230, 29);
            this.txtUdpIp.TabIndex = 8;
            this.txtUdpIp.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtLocalPort
            // 
            this.txtLocalPort.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtLocalPort.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtLocalPort.Location = new System.Drawing.Point(145, 139);
            this.txtLocalPort.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtLocalPort.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtLocalPort.Name = "txtLocalPort";
            this.txtLocalPort.Padding = new System.Windows.Forms.Padding(5);
            this.txtLocalPort.ShowText = false;
            this.txtLocalPort.Size = new System.Drawing.Size(230, 29);
            this.txtLocalPort.TabIndex = 7;
            this.txtLocalPort.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtLocalPort.Watermark = "";
            // 
            // txtDesPort
            // 
            this.txtDesPort.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtDesPort.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtDesPort.Location = new System.Drawing.Point(145, 84);
            this.txtDesPort.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtDesPort.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtDesPort.Name = "txtDesPort";
            this.txtDesPort.Padding = new System.Windows.Forms.Padding(5);
            this.txtDesPort.ShowText = false;
            this.txtDesPort.Size = new System.Drawing.Size(230, 29);
            this.txtDesPort.TabIndex = 6;
            this.txtDesPort.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtDesPort.Watermark = "";
            // 
            // uiLabel10
            // 
            this.uiLabel10.AutoSize = true;
            this.uiLabel10.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel10.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel10.Location = new System.Drawing.Point(62, 142);
            this.uiLabel10.Name = "uiLabel10";
            this.uiLabel10.Size = new System.Drawing.Size(74, 21);
            this.uiLabel10.TabIndex = 3;
            this.uiLabel10.Text = "本机端口";
            // 
            // uiLabel11
            // 
            this.uiLabel11.AutoSize = true;
            this.uiLabel11.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel11.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel11.Location = new System.Drawing.Point(62, 88);
            this.uiLabel11.Name = "uiLabel11";
            this.uiLabel11.Size = new System.Drawing.Size(74, 21);
            this.uiLabel11.TabIndex = 2;
            this.uiLabel11.Text = "目标端口";
            // 
            // uiLabel12
            // 
            this.uiLabel12.AutoSize = true;
            this.uiLabel12.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel12.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel12.Location = new System.Drawing.Point(79, 33);
            this.uiLabel12.Name = "uiLabel12";
            this.uiLabel12.Size = new System.Drawing.Size(57, 21);
            this.uiLabel12.TabIndex = 1;
            this.uiLabel12.Text = "IP地址";
            // 
            // gbCOM
            // 
            this.gbCOM.Controls.Add(this.cbmIndex);
            this.gbCOM.Controls.Add(this.cbxBaud);
            this.gbCOM.Controls.Add(this.cbxPort);
            this.gbCOM.Controls.Add(this.lblIndex);
            this.gbCOM.Controls.Add(this.uiLabel6);
            this.gbCOM.Controls.Add(this.uiLabel7);
            this.gbCOM.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbCOM.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbCOM.Location = new System.Drawing.Point(0, 0);
            this.gbCOM.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbCOM.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbCOM.Name = "gbCOM";
            this.gbCOM.Padding = new System.Windows.Forms.Padding(20, 32, 20, 0);
            this.gbCOM.Size = new System.Drawing.Size(771, 180);
            this.gbCOM.TabIndex = 5;
            this.gbCOM.Text = "通讯链路配置";
            this.gbCOM.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cbmIndex
            // 
            this.cbmIndex.DataSource = null;
            this.cbmIndex.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cbmIndex.FillColor = System.Drawing.Color.White;
            this.cbmIndex.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbmIndex.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cbmIndex.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cbmIndex.Location = new System.Drawing.Point(145, 137);
            this.cbmIndex.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cbmIndex.MinimumSize = new System.Drawing.Size(63, 0);
            this.cbmIndex.Name = "cbmIndex";
            this.cbmIndex.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cbmIndex.Size = new System.Drawing.Size(230, 29);
            this.cbmIndex.SymbolSize = 24;
            this.cbmIndex.TabIndex = 10;
            this.cbmIndex.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbmIndex.Watermark = "";
            // 
            // cbxBaud
            // 
            this.cbxBaud.DataSource = null;
            this.cbxBaud.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cbxBaud.FillColor = System.Drawing.Color.White;
            this.cbxBaud.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbxBaud.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cbxBaud.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cbxBaud.Location = new System.Drawing.Point(145, 82);
            this.cbxBaud.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cbxBaud.MinimumSize = new System.Drawing.Size(63, 0);
            this.cbxBaud.Name = "cbxBaud";
            this.cbxBaud.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cbxBaud.Size = new System.Drawing.Size(230, 29);
            this.cbxBaud.SymbolSize = 24;
            this.cbxBaud.TabIndex = 8;
            this.cbxBaud.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxBaud.Watermark = "";
            // 
            // cbxPort
            // 
            this.cbxPort.DataSource = null;
            this.cbxPort.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cbxPort.FillColor = System.Drawing.Color.White;
            this.cbxPort.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbxPort.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cbxPort.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cbxPort.Location = new System.Drawing.Point(145, 27);
            this.cbxPort.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cbxPort.MinimumSize = new System.Drawing.Size(63, 0);
            this.cbxPort.Name = "cbxPort";
            this.cbxPort.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cbxPort.Size = new System.Drawing.Size(230, 29);
            this.cbxPort.SymbolSize = 24;
            this.cbxPort.TabIndex = 7;
            this.cbxPort.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxPort.Watermark = "";
            // 
            // lblIndex
            // 
            this.lblIndex.AutoSize = true;
            this.lblIndex.BackColor = System.Drawing.Color.Transparent;
            this.lblIndex.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblIndex.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblIndex.Location = new System.Drawing.Point(94, 140);
            this.lblIndex.Name = "lblIndex";
            this.lblIndex.Size = new System.Drawing.Size(42, 21);
            this.lblIndex.TabIndex = 3;
            this.lblIndex.Text = "通道";
            // 
            // uiLabel6
            // 
            this.uiLabel6.AutoSize = true;
            this.uiLabel6.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel6.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel6.Location = new System.Drawing.Point(78, 86);
            this.uiLabel6.Name = "uiLabel6";
            this.uiLabel6.Size = new System.Drawing.Size(58, 21);
            this.uiLabel6.TabIndex = 2;
            this.uiLabel6.Text = "波特率";
            // 
            // uiLabel7
            // 
            this.uiLabel7.AutoSize = true;
            this.uiLabel7.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel7.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel7.Location = new System.Drawing.Point(94, 31);
            this.uiLabel7.Name = "uiLabel7";
            this.uiLabel7.Size = new System.Drawing.Size(42, 21);
            this.uiLabel7.TabIndex = 1;
            this.uiLabel7.Text = "串口";
            // 
            // gbTcpClient
            // 
            this.gbTcpClient.Controls.Add(this.txtPortNum);
            this.gbTcpClient.Controls.Add(this.tbxAddress);
            this.gbTcpClient.Controls.Add(this.uiLabel2);
            this.gbTcpClient.Controls.Add(this.uiLabel1);
            this.gbTcpClient.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbTcpClient.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbTcpClient.Location = new System.Drawing.Point(0, 0);
            this.gbTcpClient.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbTcpClient.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbTcpClient.Name = "gbTcpClient";
            this.gbTcpClient.Padding = new System.Windows.Forms.Padding(20, 32, 20, 0);
            this.gbTcpClient.Size = new System.Drawing.Size(771, 180);
            this.gbTcpClient.TabIndex = 1;
            this.gbTcpClient.Text = "通讯链路配置";
            this.gbTcpClient.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtPortNum
            // 
            this.txtPortNum.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtPortNum.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPortNum.Location = new System.Drawing.Point(145, 111);
            this.txtPortNum.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtPortNum.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtPortNum.Name = "txtPortNum";
            this.txtPortNum.Padding = new System.Windows.Forms.Padding(5);
            this.txtPortNum.ShowText = false;
            this.txtPortNum.Size = new System.Drawing.Size(230, 29);
            this.txtPortNum.TabIndex = 4;
            this.txtPortNum.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtPortNum.Watermark = "";
            // 
            // tbxAddress
            // 
            this.tbxAddress.FillColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.tbxAddress.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tbxAddress.Location = new System.Drawing.Point(145, 50);
            this.tbxAddress.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tbxAddress.MinimumSize = new System.Drawing.Size(1, 1);
            this.tbxAddress.Name = "tbxAddress";
            this.tbxAddress.Padding = new System.Windows.Forms.Padding(1);
            this.tbxAddress.ShowText = false;
            this.tbxAddress.Size = new System.Drawing.Size(230, 29);
            this.tbxAddress.TabIndex = 3;
            this.tbxAddress.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(62, 115);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(74, 21);
            this.uiLabel2.TabIndex = 2;
            this.uiLabel2.Text = "远程端口";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(78, 54);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(57, 21);
            this.uiLabel1.TabIndex = 1;
            this.uiLabel1.Text = "远程IP";
            // 
            // gbTcpSvr
            // 
            this.gbTcpSvr.Controls.Add(this.txtListenerPort);
            this.gbTcpSvr.Controls.Add(this.uiLabel9);
            this.gbTcpSvr.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbTcpSvr.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbTcpSvr.Location = new System.Drawing.Point(0, 0);
            this.gbTcpSvr.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbTcpSvr.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbTcpSvr.Name = "gbTcpSvr";
            this.gbTcpSvr.Padding = new System.Windows.Forms.Padding(20, 32, 20, 0);
            this.gbTcpSvr.Size = new System.Drawing.Size(771, 180);
            this.gbTcpSvr.TabIndex = 11;
            this.gbTcpSvr.Text = "通讯链路配置";
            this.gbTcpSvr.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtListenerPort
            // 
            this.txtListenerPort.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtListenerPort.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtListenerPort.Location = new System.Drawing.Point(145, 85);
            this.txtListenerPort.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtListenerPort.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtListenerPort.Name = "txtListenerPort";
            this.txtListenerPort.Padding = new System.Windows.Forms.Padding(5);
            this.txtListenerPort.ShowText = false;
            this.txtListenerPort.Size = new System.Drawing.Size(230, 29);
            this.txtListenerPort.TabIndex = 6;
            this.txtListenerPort.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtListenerPort.Watermark = "";
            // 
            // uiLabel9
            // 
            this.uiLabel9.AutoSize = true;
            this.uiLabel9.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel9.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel9.Location = new System.Drawing.Point(62, 89);
            this.uiLabel9.Name = "uiLabel9";
            this.uiLabel9.Size = new System.Drawing.Size(74, 21);
            this.uiLabel9.TabIndex = 4;
            this.uiLabel9.Text = "监听端口";
            // 
            // gbProtocol
            // 
            this.gbProtocol.Controls.Add(this.btnAdvConfig);
            this.gbProtocol.Controls.Add(this.lblDescription);
            this.gbProtocol.Controls.Add(this.cmbProtocol);
            this.gbProtocol.Controls.Add(this.txtInterval);
            this.gbProtocol.Controls.Add(this.uiLabel3);
            this.gbProtocol.Controls.Add(this.uiLabel4);
            this.gbProtocol.Controls.Add(this.uiLabel5);
            this.gbProtocol.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbProtocol.Location = new System.Drawing.Point(12, 253);
            this.gbProtocol.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbProtocol.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbProtocol.Name = "gbProtocol";
            this.gbProtocol.Padding = new System.Windows.Forms.Padding(20, 32, 20, 32);
            this.gbProtocol.Size = new System.Drawing.Size(771, 181);
            this.gbProtocol.TabIndex = 2;
            this.gbProtocol.Text = "通讯协议配置";
            this.gbProtocol.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnAdvConfig
            // 
            this.btnAdvConfig.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAdvConfig.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAdvConfig.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnAdvConfig.Location = new System.Drawing.Point(670, 138);
            this.btnAdvConfig.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnAdvConfig.Name = "btnAdvConfig";
            this.btnAdvConfig.Size = new System.Drawing.Size(87, 32);
            this.btnAdvConfig.Style = Sunny.UI.UIStyle.Custom;
            this.btnAdvConfig.StyleCustomMode = true;
            this.btnAdvConfig.TabIndex = 8;
            this.btnAdvConfig.Text = "协议配置";
            this.btnAdvConfig.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAdvConfig.Click += new System.EventHandler(this.btnAdvConfig_Click);
            // 
            // lblDescription
            // 
            this.lblDescription.AutoSize = true;
            this.lblDescription.BackColor = System.Drawing.Color.Transparent;
            this.lblDescription.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblDescription.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(126)))), ((int)(((byte)(255)))));
            this.lblDescription.Location = new System.Drawing.Point(8, 144);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(489, 21);
            this.lblDescription.Style = Sunny.UI.UIStyle.Custom;
            this.lblDescription.TabIndex = 7;
            this.lblDescription.Text = "实时数据间隔（注:实时数据间隔设置为\"0\",则表示不发送实时数据）";
            // 
            // cmbProtocol
            // 
            this.cmbProtocol.DataSource = null;
            this.cmbProtocol.DropDownAutoWidth = true;
            this.cmbProtocol.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbProtocol.FillColor = System.Drawing.Color.White;
            this.cmbProtocol.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbProtocol.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbProtocol.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbProtocol.Location = new System.Drawing.Point(145, 46);
            this.cmbProtocol.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbProtocol.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbProtocol.Name = "cmbProtocol";
            this.cmbProtocol.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbProtocol.Size = new System.Drawing.Size(612, 29);
            this.cmbProtocol.SymbolSize = 24;
            this.cmbProtocol.TabIndex = 6;
            this.cmbProtocol.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbProtocol.Watermark = "";
            this.cmbProtocol.SelectedIndexChanged += new System.EventHandler(this.cmbProtocol_SelectedIndexChanged);
            // 
            // txtInterval
            // 
            this.txtInterval.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtInterval.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtInterval.Location = new System.Drawing.Point(145, 100);
            this.txtInterval.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtInterval.MinimumSize = new System.Drawing.Size(1, 16);
            this.txtInterval.Name = "txtInterval";
            this.txtInterval.Padding = new System.Windows.Forms.Padding(5);
            this.txtInterval.ShowText = false;
            this.txtInterval.Size = new System.Drawing.Size(101, 29);
            this.txtInterval.TabIndex = 5;
            this.txtInterval.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtInterval.Watermark = "";
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(30, 104);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(106, 21);
            this.uiLabel3.TabIndex = 4;
            this.uiLabel3.Text = "实时数据间隔";
            // 
            // uiLabel4
            // 
            this.uiLabel4.AutoSize = true;
            this.uiLabel4.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel4.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel4.Location = new System.Drawing.Point(30, 50);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(106, 21);
            this.uiLabel4.TabIndex = 3;
            this.uiLabel4.Text = "通讯协议配置";
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(261, 104);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(58, 21);
            this.uiLabel5.TabIndex = 9;
            this.uiLabel5.Text = "（秒）";
            // 
            // gbBus
            // 
            this.gbBus.Controls.Add(this.rdbHttp);
            this.gbBus.Controls.Add(this.rdbUdp);
            this.gbBus.Controls.Add(this.rdbTcpSvr);
            this.gbBus.Controls.Add(this.rdbTcpClient);
            this.gbBus.Controls.Add(this.rdbComm);
            this.gbBus.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbBus.Location = new System.Drawing.Point(12, 0);
            this.gbBus.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbBus.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbBus.Name = "gbBus";
            this.gbBus.Padding = new System.Windows.Forms.Padding(20, 32, 20, 0);
            this.gbBus.Size = new System.Drawing.Size(771, 73);
            this.gbBus.TabIndex = 0;
            this.gbBus.Text = "通讯链路类型";
            this.gbBus.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // rdbHttp
            // 
            this.rdbHttp.AutoSize = true;
            this.rdbHttp.BackColor = System.Drawing.Color.Transparent;
            this.rdbHttp.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbHttp.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbHttp.Location = new System.Drawing.Point(529, 35);
            this.rdbHttp.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbHttp.Name = "rdbHttp";
            this.rdbHttp.Size = new System.Drawing.Size(67, 26);
            this.rdbHttp.TabIndex = 4;
            this.rdbHttp.Text = "Http";
            this.rdbHttp.CheckedChanged += new System.EventHandler(this.rdbHttp_CheckedChanged);
            // 
            // rdbUdp
            // 
            this.rdbUdp.AutoSize = true;
            this.rdbUdp.BackColor = System.Drawing.Color.Transparent;
            this.rdbUdp.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbUdp.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbUdp.Location = new System.Drawing.Point(663, 35);
            this.rdbUdp.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbUdp.Name = "rdbUdp";
            this.rdbUdp.Size = new System.Drawing.Size(67, 26);
            this.rdbUdp.TabIndex = 3;
            this.rdbUdp.Text = "UDP";
            this.rdbUdp.CheckedChanged += new System.EventHandler(this.rdbUdp_CheckedChanged);
            // 
            // rdbTcpSvr
            // 
            this.rdbTcpSvr.AutoSize = true;
            this.rdbTcpSvr.BackColor = System.Drawing.Color.Transparent;
            this.rdbTcpSvr.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbTcpSvr.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbTcpSvr.Location = new System.Drawing.Point(351, 35);
            this.rdbTcpSvr.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbTcpSvr.Name = "rdbTcpSvr";
            this.rdbTcpSvr.Size = new System.Drawing.Size(111, 26);
            this.rdbTcpSvr.TabIndex = 2;
            this.rdbTcpSvr.Text = "TCP服务端";
            this.rdbTcpSvr.CheckedChanged += new System.EventHandler(this.rdbTcpSvr_CheckedChanged);
            // 
            // rdbTcpClient
            // 
            this.rdbTcpClient.AutoSize = true;
            this.rdbTcpClient.BackColor = System.Drawing.Color.Transparent;
            this.rdbTcpClient.Checked = true;
            this.rdbTcpClient.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbTcpClient.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbTcpClient.Location = new System.Drawing.Point(173, 35);
            this.rdbTcpClient.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbTcpClient.Name = "rdbTcpClient";
            this.rdbTcpClient.Size = new System.Drawing.Size(111, 26);
            this.rdbTcpClient.TabIndex = 1;
            this.rdbTcpClient.Text = "TCP客户端";
            this.rdbTcpClient.CheckedChanged += new System.EventHandler(this.rdbTcpClient_CheckedChanged);
            // 
            // rdbComm
            // 
            this.rdbComm.AutoSize = true;
            this.rdbComm.BackColor = System.Drawing.Color.Transparent;
            this.rdbComm.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbComm.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbComm.Location = new System.Drawing.Point(41, 35);
            this.rdbComm.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbComm.Name = "rdbComm";
            this.rdbComm.Size = new System.Drawing.Size(65, 26);
            this.rdbComm.TabIndex = 0;
            this.rdbComm.Text = "串口";
            this.rdbComm.CheckedChanged += new System.EventHandler(this.rdbComm_CheckedChanged);
            // 
            // tbxPort
            // 
            this.tbxPort.CanEmpty = true;
            this.tbxPort.DigitLength = 2;
            this.tbxPort.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Int;
            this.tbxPort.InvalidBackColor = System.Drawing.Color.Gold;
            this.tbxPort.IsValidCheck = false;
            this.tbxPort.Label = "远程端口";
            this.tbxPort.Location = new System.Drawing.Point(81, 48);
            this.tbxPort.MaxLength = 6;
            this.tbxPort.MaxValue = "65535";
            this.tbxPort.MinValue = "1";
            this.tbxPort.Name = "tbxPort";
            this.tbxPort.Size = new System.Drawing.Size(241, 21);
            this.tbxPort.TabIndex = 13;
            // 
            // UC_RemoteConfig
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlValid);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_RemoteConfig";
            this.RectColor = System.Drawing.Color.Transparent;
            this.Size = new System.Drawing.Size(795, 475);
            this.Load += new System.EventHandler(this.RemoteConfigUserControl_Load);
            this.pnlValid.ResumeLayout(false);
            this.pnlMain.ResumeLayout(false);
            this.pnlDetail.ResumeLayout(false);
            this.gbHttp.ResumeLayout(false);
            this.gbHttp.PerformLayout();
            this.gbUdp.ResumeLayout(false);
            this.gbUdp.PerformLayout();
            this.gbCOM.ResumeLayout(false);
            this.gbCOM.PerformLayout();
            this.gbTcpClient.ResumeLayout(false);
            this.gbTcpClient.PerformLayout();
            this.gbTcpSvr.ResumeLayout(false);
            this.gbTcpSvr.PerformLayout();
            this.gbProtocol.ResumeLayout(false);
            this.gbProtocol.PerformLayout();
            this.gbBus.ResumeLayout(false);
            this.gbBus.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private Fpi.UI.Common.PC.Controls.FpiTextBox tbxPort;
        private System.Windows.Forms.ToolTip toolTip1;
        private Sunny.UI.UIPanel pnlValid;
        private Sunny.UI.UICheckBox ckbValid;
        private Sunny.UI.UIPanel pnlMain;
        private Sunny.UI.UIGroupBox gbProtocol;
        private Sunny.UI.UIButton btnAdvConfig;
        private Sunny.UI.UILabel lblDescription;
        private Sunny.UI.UIComboBox cmbProtocol;
        private Sunny.UI.UITextBox txtInterval;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UIGroupBox gbTcpClient;
        private Sunny.UI.UITextBox txtPortNum;
        private Sunny.UI.UIIPTextBox tbxAddress;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIGroupBox gbBus;
        private Sunny.UI.UIRadioButton rdbUdp;
        private Sunny.UI.UIRadioButton rdbTcpSvr;
        private Sunny.UI.UIRadioButton rdbTcpClient;
        private Sunny.UI.UIRadioButton rdbComm;
        private Sunny.UI.UIPanel pnlDetail;
        private Sunny.UI.UIGroupBox gbCOM;
        private Sunny.UI.UILabel lblIndex;
        private Sunny.UI.UILabel uiLabel6;
        private Sunny.UI.UILabel uiLabel7;
        private Sunny.UI.UIGroupBox gbUdp;
        private Sunny.UI.UITextBox txtLocalPort;
        private Sunny.UI.UITextBox txtDesPort;
        private Sunny.UI.UILabel uiLabel10;
        private Sunny.UI.UILabel uiLabel11;
        private Sunny.UI.UILabel uiLabel12;
        private Sunny.UI.UIComboBox cbmIndex;
        private Sunny.UI.UIComboBox cbxBaud;
        private Sunny.UI.UIComboBox cbxPort;
        private Sunny.UI.UIGroupBox gbTcpSvr;
        private Sunny.UI.UITextBox txtListenerPort;
        private Sunny.UI.UILabel uiLabel9;
        private Sunny.UI.UIIPTextBox txtUdpIp;
        private Sunny.UI.UIRadioButton rdbHttp;
        private Sunny.UI.UIGroupBox gbHttp;
        private Sunny.UI.UITextBox txtUrl;
        private Sunny.UI.UILabel uiLabel13;
        private Sunny.UI.UILabel uiLabel14;
        private Sunny.UI.UILabel uiLabel15;
        private Sunny.UI.UIRichTextBox txtHttpHeaders;
        private Sunny.UI.UIComboBox cmbContentTypes;
    }
}
