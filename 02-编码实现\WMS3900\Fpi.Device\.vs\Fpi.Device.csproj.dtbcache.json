{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device", "ProjectFileName": "Fpi.Device.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Channel\\DataChannelFactory.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\Coefficient\\CoeffProcessor.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\Coefficient\\ConfigCoeffPanel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\Coefficient\\ConfigCoeffPanel.Designer.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\A2BLimitProcessor.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\A2BProcessor.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\A2BRoundProcessor.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\ConfigA2BPanel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\ConfigA2BPanel.Designer.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\ConfigA2BRoundPanel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ConvertA2B\\ConfigA2BRoundPanel.Designer.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\RoundData\\ConfigRoundPanel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\RoundData\\ConfigRoundPanel.Designer.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\RoundData\\RoundProcessor.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ScopeRevise\\ConfigScopeRevisePanel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ScopeRevise\\ConfigScopeRevisePanel.Designer.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\ScopeRevise\\ScopeReviseProcessor.cs"}, {"SourceFile": "Devices\\Channel\\ValueChannel.cs"}, {"SourceFile": "Devices\\DeciveEnum.cs"}, {"SourceFile": "Devices\\DeviceManager.cs"}, {"SourceFile": "Devices\\Device.cs"}, {"SourceFile": "Devices\\Channel\\DataChannel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\IParamConfig.cs"}, {"SourceFile": "Devices\\Channel\\InValueChannel.cs"}, {"SourceFile": "Devices\\Channel\\InSwitchChannel.cs"}, {"SourceFile": "Devices\\Channel\\OutValueChannel.cs"}, {"SourceFile": "Devices\\Channel\\OutSwitchChannel.cs"}, {"SourceFile": "Devices\\Channel\\DataProcessor\\Processor.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\CommonProtocol\\CommonPort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\CommonProtocol\\CommonProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\CommonProtocol\\CommonProtocolDesc.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\CommonProtocol\\CommonSyncParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Fpi_General\\FpiBoardParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Fpi_General\\FpiBoardProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Fpi_General\\FpiBoardProtocolDesc.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\HJ212\\HJ212Parser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\HJ212\\H212Port.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\HJ212\\HJ212Protocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\HJ212\\HJ212ProtocolDesc.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Http\\HttpParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Hub_Fpi\\HubParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Hub_Fpi\\HubProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Hub_Fpi_New\\NewHubParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Hub_Fpi_New\\NewHubProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Hub_Fpi_New\\NewHubReceiver.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\LineProtocol\\LineProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\LineProtocol\\LineSyncParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\LineProtocol\\LinePort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModbusProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModBusTCPPYProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModbusTCPProtocolDesc.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModbusTCPPort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModbusTCPParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModbusTCPProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\ModBusTCP\\ModbusTCPPYPort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusRevertParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusRevertPort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusPort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusRevertProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\Modbus\\ModbusProtocolDesc.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\SimpleSync\\SimpleSyncPort.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\SimpleSync\\SimpleSyncParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\SimpleSync\\SimpleSyncProtocol.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\SimpleReceive\\SampleReceiver.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\SimpleReceive\\SimpleParser.cs"}, {"SourceFile": "Devices\\DeviceProtocols\\SimpleReceive\\SimpleReceiveProtocol.cs"}, {"SourceFile": "Devices\\Interface\\IIODeviceOperation.cs"}, {"SourceFile": "Devices\\UI\\DeviceConfigPanel.cs"}, {"SourceFile": "Devices\\UI\\DeviceConfigPanel.Designer.cs"}, {"SourceFile": "Devices\\UI\\EditValueChannelForm.cs"}, {"SourceFile": "Devices\\UI\\EditValueChannelForm.Designer.cs"}, {"SourceFile": "Devices\\UI\\EditSwitchChannelForm.cs"}, {"SourceFile": "Devices\\UI\\EditSwitchChannelForm.Designer.cs"}, {"SourceFile": "Devices\\UI\\ChannelConfigForm.cs"}, {"SourceFile": "Devices\\UI\\ChannelConfigForm.Designer.cs"}, {"SourceFile": "Devices\\UI\\SingleDeviceConfigForm.cs"}, {"SourceFile": "Devices\\UI\\SingleDeviceConfigForm.Designer.cs"}, {"SourceFile": "Devices\\UI\\SerialPortParamPanel.cs"}, {"SourceFile": "Devices\\UI\\SerialPortParamPanel.Designer.cs"}, {"SourceFile": "Devices\\UI\\NetWorkParamPanel.cs"}, {"SourceFile": "Devices\\UI\\NetWorkParamPanel.Designer.cs"}, {"SourceFile": "Devices\\UI\\OtherParamPanel.cs"}, {"SourceFile": "Devices\\UI\\OtherParamPanel.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Devices\\Channel\\SwitchChannel.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Instrument\\bin\\Debug\\Fpi.Instrument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Instrument\\bin\\Debug\\Fpi.Instrument.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll", "OutputItemRelativePath": "Fpi.Device.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}