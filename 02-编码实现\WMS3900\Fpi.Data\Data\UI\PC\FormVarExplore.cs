using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Properties;

namespace Fpi.Data.UI.PC
{
    public partial class FormVarExplore : Form
    {
        private eVarType varType = eVarType.Object;

        public FormVarExplore()
        {
            InitializeComponent();
        }

        public FormVarExplore(eVarType varType) : this()
        {
            this.varType = varType;
        }

        private bool _MultiSelect;
        public bool MultiSelect
        {
            get
            {
                _MultiSelect = this.listView.MultiSelect;
                return _MultiSelect;
            }
            set
            {
                _MultiSelect = value;
                this.listView.MultiSelect = value;
            }
        }

        public VarNode VarNode { get; set; }
        public List<VarNode> VarNodes { get; set; } = new List<VarNode>();

        private void FormVarExplore_Load(object sender, EventArgs e)
        {
            LoadNodes();
        }

        private void LoadNodes()
        {
            foreach(VarNode vn in DataManager.GetInstance().GetAllVarNodes())
            {
                if(vn.VarType == varType)
                {
                    ListViewItem lvi = new ListViewItem(new string[]{
                    vn.id,
                    vn.name,
                    vn.FullName});

                    lvi.Tag = vn;
                    this.listView.Items.Add(lvi);

                    if(!_MultiSelect)
                    {
                        if(VarNode == vn)
                        {
                            lvi.Checked = true;
                        }
                    }
                    else
                    {
                        if(VarNodes != null && VarNodes.Contains(vn))
                        {
                            lvi.Checked = true;
                        }
                    }
                }
            }
        }

        private void listView_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            if(this.listView.MultiSelect)
            {
                return;
            }

            if(e.Item.Checked)
            {
                e.Item.Selected = true;
                foreach(ListViewItem lvi in this.listView.Items)
                {
                    if(lvi != e.Item)
                    {
                        lvi.Checked = false;
                    }
                }
            }
        }

        private void btnSetNull_Click(object sender, EventArgs e)
        {
            VarNode = null;
            VarNodes = new List<VarNode>();

            foreach(ListViewItem lvi in this.listView.CheckedItems)
            {
                lvi.Checked = false;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if(!_MultiSelect)
                {
                    if(this.listView.CheckedItems.Count > 0)
                    {
                        VarNode = this.listView.CheckedItems[0].Tag as VarNode;
                    }
                }
                else
                {
                    VarNodes = new List<VarNode>();
                    for(int i = 0; i < this.listView.CheckedItems.Count; i++)
                    {
                        VarNodes.Add(this.listView.CheckedItems[i].Tag as VarNode);
                    }
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}