﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Fpi.Communication.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Fpi.Communication.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 接受 TCP Client 连接:{0} 的本地化字符串。
        /// </summary>
        internal static string AcceptTcpConnect {
            get {
                return ResourceManager.GetString("AcceptTcpConnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Add {
            get {
                object obj = ResourceManager.GetObject("Add", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 CAN通讯必须绑定具体通道 的本地化字符串。
        /// </summary>
        internal static string BindChannel {
            get {
                return ResourceManager.GetString("BindChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 在生成port类型列表时发生异常:{0}\r\n port类型:{1} 的本地化字符串。
        /// </summary>
        internal static string BuildPortTypeException {
            get {
                return ResourceManager.GetString("BuildPortTypeException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 在生成协议列表时发生异常:{0} \r\n协议类型:{1} 的本地化字符串。
        /// </summary>
        internal static string BuildProtocolTypeException {
            get {
                return ResourceManager.GetString("BuildProtocolTypeException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 生成协议配置界面异常:{0} 的本地化字符串。
        /// </summary>
        internal static string BuildProtocolViewException {
            get {
                return ResourceManager.GetString("BuildProtocolViewException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 在生成物理链路类型列表时发生异常:{0}\r\n物理链路类型:{1} 的本地化字符串。
        /// </summary>
        internal static string BuildRouterExeption {
            get {
                return ResourceManager.GetString("BuildRouterExeption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 物理链路:{0} 发生读取异常:{1} 的本地化字符串。
        /// </summary>
        internal static string BusReadException {
            get {
                return ResourceManager.GetString("BusReadException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 CAN配置选项 的本地化字符串。
        /// </summary>
        internal static string CANConfigOption {
            get {
                return ResourceManager.GetString("CANConfigOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 CAN参数未配置 的本地化字符串。
        /// </summary>
        internal static string CANNotConfig {
            get {
                return ResourceManager.GetString("CANNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通道{0} 的本地化字符串。
        /// </summary>
        internal static string Channel {
            get {
                return ResourceManager.GetString("Channel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 通道关闭时发生异常:{1} 的本地化字符串。
        /// </summary>
        internal static string ChannelCloseException {
            get {
                return ResourceManager.GetString("ChannelCloseException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 通道未启用 的本地化字符串。
        /// </summary>
        internal static string ChannelDisable {
            get {
                return ResourceManager.GetString("ChannelDisable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [0] 通道未启用或未正确开启，导致发送失败 的本地化字符串。
        /// </summary>
        internal static string ChannelDisableAndSendFailed {
            get {
                return ResourceManager.GetString("ChannelDisableAndSendFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通道编号 的本地化字符串。
        /// </summary>
        internal static string ChannelId {
            get {
                return ResourceManager.GetString("ChannelId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通道编号冲突:{0} 的本地化字符串。
        /// </summary>
        internal static string ChannelIdConflict {
            get {
                return ResourceManager.GetString("ChannelIdConflict", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通道命名 的本地化字符串。
        /// </summary>
        internal static string ChannelName {
            get {
                return ResourceManager.GetString("ChannelName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 通道打开时发生异常:{1} 的本地化字符串。
        /// </summary>
        internal static string ChannelOpenException {
            get {
                return ResourceManager.GetString("ChannelOpenException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 通道打开失败 的本地化字符串。
        /// </summary>
        internal static string ChannelOpenFailed {
            get {
                return ResourceManager.GetString("ChannelOpenFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 通道未配置有效的通讯协议 的本地化字符串。
        /// </summary>
        internal static string ChannelProtocolNotConfig {
            get {
                return ResourceManager.GetString("ChannelProtocolNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 通道未配置有效的物理链路 的本地化字符串。
        /// </summary>
        internal static string ChannelRooterNotConfig {
            get {
                return ResourceManager.GetString("ChannelRooterNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭Can总线失败:{0} 的本地化字符串。
        /// </summary>
        internal static string CloseCanFailed {
            get {
                return ResourceManager.GetString("CloseCanFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭Can总线成功:{0} 的本地化字符串。
        /// </summary>
        internal static string CloseCanSucceed {
            get {
                return ResourceManager.GetString("CloseCanSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭TCP连接失败:{0} 的本地化字符串。
        /// </summary>
        internal static string CloseTcpFailed {
            get {
                return ResourceManager.GetString("CloseTcpFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭TCP端口失败:{0} 的本地化字符串。
        /// </summary>
        internal static string CloseTcpPortFailed {
            get {
                return ResourceManager.GetString("CloseTcpPortFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭TCP端口成功:{0} 的本地化字符串。
        /// </summary>
        internal static string CloseTcpPortSucceed {
            get {
                return ResourceManager.GetString("CloseTcpPortSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭TCP连接成功:{0} 的本地化字符串。
        /// </summary>
        internal static string CloseTcpSucceed {
            get {
                return ResourceManager.GetString("CloseTcpSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭USB总线失败 的本地化字符串。
        /// </summary>
        internal static string CloseUsbFailed {
            get {
                return ResourceManager.GetString("CloseUsbFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭USB总线成功 的本地化字符串。
        /// </summary>
        internal static string CloseUsbSucceed {
            get {
                return ResourceManager.GetString("CloseUsbSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 串口(RS232)配置选项 的本地化字符串。
        /// </summary>
        internal static string CommConfigOption {
            get {
                return ResourceManager.GetString("CommConfigOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 串口参数未配置 的本地化字符串。
        /// </summary>
        internal static string CommParamNotConfig {
            get {
                return ResourceManager.GetString("CommParamNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通讯超时! 的本地化字符串。
        /// </summary>
        internal static string CommunicationTimeOut {
            get {
                return ResourceManager.GetString("CommunicationTimeOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 必须配置串口 的本地化字符串。
        /// </summary>
        internal static string ConfigComm {
            get {
                return ResourceManager.GetString("ConfigComm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 配置文件错误 的本地化字符串。
        /// </summary>
        internal static string ConfigFileError {
            get {
                return ResourceManager.GetString("ConfigFileError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 多通道GPRS必须配置通道索引 的本地化字符串。
        /// </summary>
        internal static string ConfigGPRSChannel {
            get {
                return ResourceManager.GetString("ConfigGPRSChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP服务器的本地监听端口不能为零且不能冲突!推荐1024～65535 的本地化字符串。
        /// </summary>
        internal static string ConfigListenPort {
            get {
                return ResourceManager.GetString("ConfigListenPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 配置已保存，是否立即生效? 的本地化字符串。
        /// </summary>
        internal static string ConfigSave {
            get {
                return ResourceManager.GetString("ConfigSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 必须配置TCP服务器域名或IP地址 的本地化字符串。
        /// </summary>
        internal static string ConfigTcpClient {
            get {
                return ResourceManager.GetString("ConfigTcpClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 构造通讯链路时发生异常:{1} 的本地化字符串。
        /// </summary>
        internal static string ConstructRooterException {
            get {
                return ResourceManager.GetString("ConstructRooterException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 创建Bus失败:{0} 的本地化字符串。
        /// </summary>
        internal static string CreateBusFailed {
            get {
                return ResourceManager.GetString("CreateBusFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 创建Port失败:{0} 的本地化字符串。
        /// </summary>
        internal static string CreatePortFailed {
            get {
                return ResourceManager.GetString("CreatePortFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 创建Protocol失败:{0} 的本地化字符串。
        /// </summary>
        internal static string CreateProtocolFailed {
            get {
                return ResourceManager.GetString("CreateProtocolFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 自定义接收器 的本地化字符串。
        /// </summary>
        internal static string CustomReceiver {
            get {
                return ResourceManager.GetString("CustomReceiver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 自定义发送器 的本地化字符串。
        /// </summary>
        internal static string CustomSender {
            get {
                return ResourceManager.GetString("CustomSender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 数据帧太多,传输数据太大。 的本地化字符串。
        /// </summary>
        internal static string DataTooLong {
            get {
                return ResourceManager.GetString("DataTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 删除该通道:{0}? 的本地化字符串。
        /// </summary>
        internal static string DeleteChannel {
            get {
                return ResourceManager.GetString("DeleteChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 目标地址与当前地址不匹配, 目标地址:{0} 的本地化字符串。
        /// </summary>
        internal static string DestAddressError {
            get {
                return ResourceManager.GetString("DestAddressError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 目标地址长度小于0,当前目标地址长度为: {0} 的本地化字符串。
        /// </summary>
        internal static string DestAddressLength {
            get {
                return ResourceManager.GetString("DestAddressLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Edit {
            get {
                object obj = ResourceManager.GetObject("Edit", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 接收到的数据帧没有在窗口中。帧号:{0} 的本地化字符串。
        /// </summary>
        internal static string FrameNotInWindow {
            get {
                return ResourceManager.GetString("FrameNotInWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 GPRS 通道:{0} 的本地化字符串。
        /// </summary>
        internal static string GPRSChannel {
            get {
                return ResourceManager.GetString("GPRSChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 宏电多通道GPRS配置选项 的本地化字符串。
        /// </summary>
        internal static string GPRSConfigOption {
            get {
                return ResourceManager.GetString("GPRSConfigOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 HongDianBus未配置串口 的本地化字符串。
        /// </summary>
        internal static string HongDianCommNotConfig {
            get {
                return ResourceManager.GetString("HongDianCommNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 多通道宏电模块的通道索引参数未配置 的本地化字符串。
        /// </summary>
        internal static string HongDianGPRSNotConfig {
            get {
                return ResourceManager.GetString("HongDianGPRSNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 根据FPI-485数据帧格式，整型数据范围应在0~9999之间 的本地化字符串。
        /// </summary>
        internal static string IntScope {
            get {
                return ResourceManager.GetString("IntScope", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 FPI-485数据帧格式，长整型数据范围应在0~99999999之间 的本地化字符串。
        /// </summary>
        internal static string LongScope {
            get {
                return ResourceManager.GetString("LongScope", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Modify {
            get {
                object obj = ResourceManager.GetObject("Modify", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 否 的本地化字符串。
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 配置界面非 BaseConfigureView 类型 的本地化字符串。
        /// </summary>
        internal static string NotConfigView {
            get {
                return ResourceManager.GetString("NotConfigView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} 未包含正确的解析器 的本地化字符串。
        /// </summary>
        internal static string NotContainParser {
            get {
                return ResourceManager.GetString("NotContainParser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0}类型不受支持 的本地化字符串。
        /// </summary>
        internal static string NotSupportType {
            get {
                return ResourceManager.GetString("NotSupportType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开Can总线失败:{0} 的本地化字符串。
        /// </summary>
        internal static string OpenCanFailed {
            get {
                return ResourceManager.GetString("OpenCanFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开Can总线成功:{0} 的本地化字符串。
        /// </summary>
        internal static string OpenCanSucceed {
            get {
                return ResourceManager.GetString("OpenCanSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 建立TCP连接失败:{0} 的本地化字符串。
        /// </summary>
        internal static string OpenTcpFailed {
            get {
                return ResourceManager.GetString("OpenTcpFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开TCP端口失败:{0} 的本地化字符串。
        /// </summary>
        internal static string OpenTcpPortFailed {
            get {
                return ResourceManager.GetString("OpenTcpPortFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开TCP端口成功:{0} 的本地化字符串。
        /// </summary>
        internal static string OpenTcpPortSucceed {
            get {
                return ResourceManager.GetString("OpenTcpPortSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 建立TCP连接成功:{0} 的本地化字符串。
        /// </summary>
        internal static string OpenTcpSucceed {
            get {
                return ResourceManager.GetString("OpenTcpSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开USB总线失败 的本地化字符串。
        /// </summary>
        internal static string OpenUsbFailed {
            get {
                return ResourceManager.GetString("OpenUsbFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开USB总线成功 的本地化字符串。
        /// </summary>
        internal static string OpenUsbSucceed {
            get {
                return ResourceManager.GetString("OpenUsbSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 参数配置 的本地化字符串。
        /// </summary>
        internal static string ParamConfig {
            get {
                return ResourceManager.GetString("ParamConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 读回应与写的参数不一致,请将读写分开处理. 的本地化字符串。
        /// </summary>
        internal static string ParamNotIdentical {
            get {
                return ResourceManager.GetString("ParamNotIdentical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 port类型冲突:{0} 的本地化字符串。
        /// </summary>
        internal static string PortTypeConflict {
            get {
                return ResourceManager.GetString("PortTypeConflict", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 协议配置 的本地化字符串。
        /// </summary>
        internal static string ProtocolConfig {
            get {
                return ResourceManager.GetString("ProtocolConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 协议冲突:{0} 的本地化字符串。
        /// </summary>
        internal static string ProtocolConflict {
            get {
                return ResourceManager.GetString("ProtocolConflict", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通讯协议内部参数配置 [{0}] 的本地化字符串。
        /// </summary>
        internal static string ProtocolInternalParamConfig {
            get {
                return ResourceManager.GetString("ProtocolInternalParamConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 此通讯协议无须配置! 的本地化字符串。
        /// </summary>
        internal static string ProtocolNotNeedConfig {
            get {
                return ResourceManager.GetString("ProtocolNotNeedConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通讯协议用户参数配置 [{0}] 的本地化字符串。
        /// </summary>
        internal static string ProtocolUserParamConfig {
            get {
                return ResourceManager.GetString("ProtocolUserParamConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 读取命令无回应 的本地化字符串。
        /// </summary>
        internal static string ReadNotResponse {
            get {
                return ResourceManager.GetString("ReadNotResponse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 接收数据： 的本地化字符串。
        /// </summary>
        internal static string ReceiveData {
            get {
                return ResourceManager.GetString("ReceiveData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 接收 的本地化字符串。
        /// </summary>
        internal static string Recv {
            get {
                return ResourceManager.GetString("Recv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP客户端的远程服务端参数未配置。 的本地化字符串。
        /// </summary>
        internal static string RemoteServerNotConfig {
            get {
                return ResourceManager.GetString("RemoteServerNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Remove {
            get {
                object obj = ResourceManager.GetObject("Remove", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 链路配置 的本地化字符串。
        /// </summary>
        internal static string RooterConfig {
            get {
                return ResourceManager.GetString("RooterConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 此通讯链路无须配置! 的本地化字符串。
        /// </summary>
        internal static string RooterNotNeedConfig {
            get {
                return ResourceManager.GetString("RooterNotNeedConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 物理链路类型冲突:{0} 的本地化字符串。
        /// </summary>
        internal static string RouterConflict {
            get {
                return ResourceManager.GetString("RouterConflict", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Search {
            get {
                object obj = ResourceManager.GetObject("Search", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 请先选中要删除的通道 的本地化字符串。
        /// </summary>
        internal static string SelectDeleteChannel {
            get {
                return ResourceManager.GetString("SelectDeleteChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请先选中要修改的通道 的本地化字符串。
        /// </summary>
        internal static string SelectModifyChannel {
            get {
                return ResourceManager.GetString("SelectModifyChannel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 发送 的本地化字符串。
        /// </summary>
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 发送数据： 的本地化字符串。
        /// </summary>
        internal static string SendData {
            get {
                return ResourceManager.GetString("SendData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置失败 的本地化字符串。
        /// </summary>
        internal static string SettingFailed {
            get {
                return ResourceManager.GetString("SettingFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置成功 的本地化字符串。
        /// </summary>
        internal static string SettingSucceed {
            get {
                return ResourceManager.GetString("SettingSucceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 源地址长度小于0,当前源地址长度为:{0} 的本地化字符串。
        /// </summary>
        internal static string SourceAddressLength {
            get {
                return ResourceManager.GetString("SourceAddressLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统提示 的本地化字符串。
        /// </summary>
        internal static string SystemPrompt {
            get {
                return ResourceManager.GetString("SystemPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP客户端配置选项 的本地化字符串。
        /// </summary>
        internal static string TcpClientConfigOption {
            get {
                return ResourceManager.GetString("TcpClientConfigOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP 侦听器错误:{0} 的本地化字符串。
        /// </summary>
        internal static string TcpListenError {
            get {
                return ResourceManager.GetString("TcpListenError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP服务端的本地监听端口参数未配置 的本地化字符串。
        /// </summary>
        internal static string TcpLocalPortNotConfig {
            get {
                return ResourceManager.GetString("TcpLocalPortNotConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP服务端配置选项 的本地化字符串。
        /// </summary>
        internal static string TcpServerConfigOption {
            get {
                return ResourceManager.GetString("TcpServerConfigOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TCP服务器端口不能为零 的本地化字符串。
        /// </summary>
        internal static string TcpServerPortEmpty {
            get {
                return ResourceManager.GetString("TcpServerPortEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TcpServer 读取数据发生错误:{0} 的本地化字符串。
        /// </summary>
        internal static string TcpServerReadError {
            get {
                return ResourceManager.GetString("TcpServerReadError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 该项无须配置... 的本地化字符串。
        /// </summary>
        internal static string TheItemNotNeedConfig {
            get {
                return ResourceManager.GetString("TheItemNotNeedConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置命令无回应 的本地化字符串。
        /// </summary>
        internal static string WriteNotResponse {
            get {
                return ResourceManager.GetString("WriteNotResponse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 是 的本地化字符串。
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
