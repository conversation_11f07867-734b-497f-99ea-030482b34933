﻿namespace Fpi.Data.ExpParser
{
    using System;

    public class ToolBox
    {
        public TOKENLink InfixToPostfix(TOKENLink startLink, TOKENLink endLink)
        {
            TOKENLink link = null;
            TOKENLink link2 = null;
            TOKENLink link3 = null;
            TOKENLink next = startLink;
            KeyValueList<IToken, int> list = new KeyValueList<IToken, int>();
            int num = 0;
            while(true)
            {
                if(next.Token.Type == ETokenType.token_operand)
                {
                    TOKENLink link5 = new TOKENLink(next.Token);
                    if(link == null)
                    {
                        link = link5;
                        link2 = link5;
                    }
                    else
                    {
                        link2.Next = link5;
                        link5.Prev = link2;
                        link2 = link5;
                    }
                }
                else
                {
                    if(next.Token.Type != ETokenType.token_operator)
                    {
                        throw new Exception(string.Format("Error! 后缀表达式出现未解析类型“{0}”（索引：{1}）", next.Token.Type.ToString(), next.Token.Index.ToString()));
                    }
                    if(((TOKEN<Operator>)next.Token).Tag.Type == EOperatorType.LeftParen)
                    {
                        num++;
                    }
                    else if(((TOKEN<Operator>)next.Token).Tag.Type == EOperatorType.RightParen)
                    {
                        num--;
                    }
                    else
                    {
                        TOKENLink link6 = new TOKENLink(next.Token);
                        list.Add(link6.Token, num);
                        if(link3 == null)
                        {
                            link3 = link6;
                        }
                        else
                        {
                            link3.Next = link6;
                            link6.Prev = link3;
                            link3 = link6;
                        }
                        while(link3.Prev != null)
                        {
                            if((list[link3.Prev.Token] <= list[link3.Token]) && ((list[link3.Prev.Token] != list[link3.Token]) || (((TOKEN<Operator>)link3.Prev.Token).Tag.PRI < ((TOKEN<Operator>)link3.Token).Tag.PRI)))
                            {
                                break;
                            }
                            TOKENLink prev = link3.Prev;
                            if(link3.Prev.Prev != null)
                            {
                                link3.Prev.Prev.Next = link3;
                                link3.Prev = link3.Prev.Prev;
                            }
                            else
                            {
                                link3.Prev = null;
                            }
                            link2.Next = prev;
                            prev.Prev = link2;
                            link2 = prev;
                        }
                    }
                }
                if(next == endLink)
                {
                    for(TOKENLink link8 = link3; link8 != null; link8 = link3)
                    {
                        link3 = link3.Prev;
                        link2.Next = link8;
                        link8.Prev = link2;
                        link2 = link8;
                    }
                    link.Prev = null;
                    link2.Next = null;
                    return link;
                }
                next = next.Next;
            }
        }
    }
}

