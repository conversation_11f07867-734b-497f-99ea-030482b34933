using System;
using System.Windows.Forms;
using Fpi.Alarm.Properties;
using Fpi.UI.Common.PC;

namespace Fpi.Alarm.UI.PC
{
    public partial class FormEditAlarmGrade : Form
    {
        public FormEditAlarmGrade()
        {
            InitializeComponent();
        }
        public FormEditAlarmGrade(AlarmGrade grade) : this()
        {
            this.AlarmGrade = grade;
        }

        public AlarmGrade AlarmGrade { get; private set; }


        private void FormEditAlarmGrade_Load(object sender, EventArgs e)
        {
            if(AlarmGrade != null)
            {
                this.txtGradeId.Text = AlarmGrade.id;
                this.txtGradeName.Text = AlarmGrade.name;
                this.nuGrade.Value = AlarmGrade.level;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtGradeId.Text) || string.IsNullOrEmpty(this.txtGradeName.Text))
            {
                throw new Exception(Resources.AlarmGradeCodeEmpty);
            }

            if(AlarmGrade == null)
            {
                AlarmGrade = new AlarmGrade();
            }

            AlarmGrade.id = this.txtGradeId.Text;
            AlarmGrade.name = this.txtGradeName.Text;
            AlarmGrade.level = (int)this.nuGrade.Value;
        }

    }
}