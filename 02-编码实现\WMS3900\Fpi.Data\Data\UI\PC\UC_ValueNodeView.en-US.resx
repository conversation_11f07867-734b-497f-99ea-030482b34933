﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pageBase.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pageBase.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageBase.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageBase.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 322</value>
  </data>
  <data name="pageBase.Text" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="pageLocal.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="pageLocal.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageLocal.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageLocal.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 322</value>
  </data>
  <data name="pageLocal.Text" xml:space="preserve">
    <value>Extended</value>
  </data>
  <data name="pageUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="pageUnit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageUnit.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 322</value>
  </data>
  <data name="pageUnit.Text" xml:space="preserve">
    <value>Units</value>
  </data>
  <data name="pageScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="pageScope.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageScope.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 322</value>
  </data>
  <data name="pageScope.Text" xml:space="preserve">
    <value>Scopes</value>
  </data>
  <data name="pageLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="pageLimit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageLimit.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 322</value>
  </data>
  <data name="pageLimit.Text" xml:space="preserve">
    <value>Alarm limits</value>
  </data>
  <data name="pageFixed.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="pageFixed.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageFixed.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="pageFixed.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 322</value>
  </data>
  <data name="pageFixed.Text" xml:space="preserve">
    <value>Fixed value</value>
  </data>
  <data name="tabControl1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 106</value>
  </data>
  <data name="cmbLimit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 23</value>
  </data>
  <data name="cmbScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 71</value>
  </data>
  <data name="cmbScope.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 23</value>
  </data>
  <data name="cmbUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 246</value>
  </data>
  <data name="cmbUnit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 23</value>
  </data>
  <data name="nuSmooth.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 38</value>
  </data>
  <data name="nuSmooth.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="nuSmooth.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 110</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>107, 15</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Default alarm limit</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 74</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 15</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Default scope</value>
  </data>
  <data name="txtFormula.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 139</value>
  </data>
  <data name="txtFormula.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="txtFormula.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 49</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 248</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 15</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Default unit</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 39</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 15</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Smooth times</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 152</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 15</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>compute Formula</value>
  </data>
  <data name="panel8.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 272</value>
  </data>
  <data name="panel8.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="panel8.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 46</value>
  </data>
  <data name="btnDelUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 9</value>
  </data>
  <data name="btnDelUnit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnDelUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnDelUnit.Text" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="btnAddUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 9</value>
  </data>
  <data name="btnAddUnit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnAddUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnAddUnit.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="btnUpdateUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 9</value>
  </data>
  <data name="btnUpdateUnit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnUpdateUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnUpdateUnit.Text" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="lvUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 4</value>
  </data>
  <data name="lvUnit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lvUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 314</value>
  </data>
  <data name="columnHeader5.Text" xml:space="preserve">
    <value>unit id</value>
  </data>
  <data name="columnHeader6.Text" xml:space="preserve">
    <value>unit name</value>
  </data>
  <data name="columnHeader7.Text" xml:space="preserve">
    <value>convert Formula</value>
  </data>
  <data name="columnHeader8.Text" xml:space="preserve">
    <value>converse Formula</value>
  </data>
  <data name="panel9.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 272</value>
  </data>
  <data name="panel9.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="panel9.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 46</value>
  </data>
  <data name="btnDelScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 9</value>
  </data>
  <data name="btnDelScope.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnDelScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnDelScope.Text" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="btnDelScope.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="btnAddScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 9</value>
  </data>
  <data name="btnAddScope.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnAddScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnAddScope.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="btnAddScope.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="btnUpdateScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 9</value>
  </data>
  <data name="btnUpdateScope.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnUpdateScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnUpdateScope.Text" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="btnUpdateScope.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="lvScope.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 4</value>
  </data>
  <data name="lvScope.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lvScope.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 314</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>id</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>name</value>
  </data>
  <data name="columnHeader3.Text" xml:space="preserve">
    <value>minimum</value>
  </data>
  <data name="columnHeader4.Text" xml:space="preserve">
    <value>maximum</value>
  </data>
  <data name="lvLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 4</value>
  </data>
  <data name="lvLimit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lvLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 268</value>
  </data>
  <data name="columnHeader9.Text" xml:space="preserve">
    <value>id</value>
  </data>
  <data name="columnHeader10.Text" xml:space="preserve">
    <value>name</value>
  </data>
  <data name="columnHeader11.Text" xml:space="preserve">
    <value>lower limit</value>
  </data>
  <data name="columnHeader12.Text" xml:space="preserve">
    <value>upper limit</value>
  </data>
  <data name="panel10.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 272</value>
  </data>
  <data name="panel10.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="panel10.Size" type="System.Drawing.Size, System.Drawing">
    <value>418, 46</value>
  </data>
  <data name="btnDelLimit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="btnDelLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 9</value>
  </data>
  <data name="btnDelLimit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnDelLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnDelLimit.Text" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="btnDelLimit.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageBeforeText</value>
  </data>
  <data name="btnAddLimit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="btnAddLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 9</value>
  </data>
  <data name="btnAddLimit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnAddLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnAddLimit.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="btnAddLimit.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageBeforeText</value>
  </data>
  <data name="btnUpdateLimit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="btnUpdateLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 9</value>
  </data>
  <data name="btnUpdateLimit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnUpdateLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnUpdateLimit.Text" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="btnUpdateLimit.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>ImageBeforeText</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>227, 48</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label12.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gbFixed.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 82</value>
  </data>
  <data name="gbFixed.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="gbFixed.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="gbFixed.Size" type="System.Drawing.Size, System.Drawing">
    <value>404, 136</value>
  </data>
  <data name="gbFixed.Text" xml:space="preserve">
    <value>Fixed parameters</value>
  </data>
  <data name="nuFixedValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 36</value>
  </data>
  <data name="nuFixedValue.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="nuFixedValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="nuFixedRange.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 74</value>
  </data>
  <data name="nuFixedRange.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="nuFixedRange.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 76</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 12</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 39</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 12</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Center value</value>
  </data>
  <data name="chkFixed.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 42</value>
  </data>
  <data name="chkFixed.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="chkFixed.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 16</value>
  </data>
  <data name="chkFixed.Text" xml:space="preserve">
    <value>Enable fixed value</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 15</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9pt</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
</root>