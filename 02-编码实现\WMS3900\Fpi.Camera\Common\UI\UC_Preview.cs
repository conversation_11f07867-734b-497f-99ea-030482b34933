﻿/*==================================================================================================
** 类 名 称:UC_Preview
** 创 建 人:xiaopeng_liu
** 当前版本：V1.0.0
** CLR 版本:4.0.30319.42000
** 创建时间:2017/4/20 18:47:53

** 修改人		修改时间		修改后版本		修改内容


** 功能描述：监控主面板，以分屏形式显示各子摄像机界面
 
==================================================================================================
 Copyright @2017. Focused Photonics Inc. All rights reserved.
==================================================================================================*/
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace Fpi.Camera.UI
{
    /// <summary>
    /// 监控主面板
    /// </summary>
    public partial class UC_Preview : UserControl
    {
        #region 字段属性

        /// <summary>
        /// 子视频控件列表
        /// </summary>
        public List<PlayerItem> PreviewList = new List<PlayerItem>();

        /// <summary>
        /// 当前界面上激活（选中）的摄像机
        /// </summary>
        public BaseNETCamera CurrentCamera;

        /// <summary>
        /// 当前选中的子面板
        /// </summary>
        private PlayerItem _currentPlayerItem;

        /// <summary>
        /// 当前选中的子面板
        /// 变化时触发值改变事件
        /// </summary>
        public PlayerItem CurrentPlayerItem
        {
            get => _currentPlayerItem;
            set
            {
                _currentPlayerItem = value;
                OnCurrentPlayerItemChanged();
            }
        }

        public delegate void CurrentPlayerItemChangedEventHander();

        public event CurrentPlayerItemChangedEventHander CurrentPlayerItemChanged;

        private void OnCurrentPlayerItemChanged()
        {
            if(CurrentPlayerItemChanged != null)
            {
                CurrentPlayerItemChanged();
            }
        }

        /// <summary>
        /// 获取当前选中子面板的图片控件句柄
        /// </summary>
        public IntPtr GetCurrentPlayerItemPicHandle()
        {
            return _currentPlayerItem == null ? IntPtr.Zero : _currentPlayerItem.Preview.Handle;
        }

        #endregion

        #region 构造

        public UC_Preview()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void UC_Preview_Load(object sender, EventArgs e)
        {
            Click += item_Click;
            Init();
        }

        /// <summary>
        /// 子界面点击事件 当前选中界面边框颜色改变
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void item_Click(object sender, EventArgs e)
        {
            // 只有一个摄像机，不突出显示
            if(NETCameraManager.GetInstance().Cameras.GetCount() <= 1)
            {
                return;
            }
            // 选中模块未变
            if(_currentPlayerItem == sender as PlayerItem)
            {
                return;
            }
            if(_currentPlayerItem != null)
            {
                _currentPlayerItem.SelectedState(false);
            }
            _currentPlayerItem = sender as PlayerItem;
            // 改变选中状态
            if(_currentPlayerItem != null)
            {
                _currentPlayerItem.SelectedState(true);
            }

            CurrentCamera = _currentPlayerItem != null ? NETCameraManager.GetInstance().GetCameraById(_currentPlayerItem.Id) : null;
            // 触发事件
            CurrentPlayerItem = _currentPlayerItem;
        }

        /// <summary>
        /// 界面大小变化时重新布局
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UC_Preview_SizeChanged(object sender, EventArgs e)
        {
            RePreviewLayout();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化显示面板
        /// </summary>
        public void Init()
        {
            PreviewList.Clear();
            this.Controls.Clear();
            int i = 0;
            foreach(BaseNETCamera camera in NETCameraManager.GetInstance().Cameras)
            {
                PlayerItem item = camera.GetPreviewInterface();
                //// 刷新界面时，先解除上次绑定
                item.Click -= item_Click;
                item.Click += item_Click;
                item.TabIndex = i++;
                PreviewList.Add(item);
                this.Controls.Add(item);
            }
            RefreshAllCameraName();
            RePreviewLayout();
            if(CurrentPlayerItem != null)
            {
                CurrentPlayerItem.SelectedState(false);
            }
            // 默认选中第一台摄像机
            if(NETCameraManager.GetInstance().Cameras.GetCount() > 0)
            {
                CurrentCamera = (BaseNETCamera)NETCameraManager.GetInstance().Cameras[0];
                CurrentPlayerItem = CurrentCamera.GetPreviewInterface();
                // 只有一台摄像机时，不改变选中状态
                if(NETCameraManager.GetInstance().Cameras.GetCount() > 1)
                {
                    CurrentPlayerItem.SelectedState(true);
                }
            }
            else
            {
                CurrentCamera = null;
                CurrentPlayerItem = null;
            }
        }

        /// <summary>
        /// 刷新当前选中的子面板PictureBox
        /// </summary>
        private delegate void RefreshPictureBoxHander();

        /// <summary>
        /// 刷新当前选中的子面板
        /// </summary>
        public void RefreshCurrentPlayerItem()
        {
            if(_currentPlayerItem != null)
            {
                if(_currentPlayerItem.Preview.InvokeRequired)
                {
                    RefreshPictureBoxHander a = () => { _currentPlayerItem.Preview.Refresh(); };
                    Invoke(a);
                }
                else
                {
                    _currentPlayerItem.Preview.Refresh();
                }
            }
        }

        /// <summary>
        /// 刷新所有子面板对应摄像机的名称显示
        /// </summary>
        public void RefreshAllCameraName()
        {
            foreach(PlayerItem itme in PreviewList)
            {
                BaseNETCamera camera = NETCameraManager.GetInstance().GetCameraById(itme.Id);
                if(camera != null)
                {
                    itme.UpdateCameraName(camera.name);
                }
            }
        }

        #region 最大化/正常显示

        /// <summary>
        /// 最大化显示当前选中的子面板
        /// </summary>
        public void MaxShowCurrentPlayerItem()
        {
            if(_currentPlayerItem != null)
            {
                _currentPlayerItem.Dock = DockStyle.Fill;
                _currentPlayerItem.SelectedState(false);
                _currentPlayerItem.BringToFront();
            }
        }

        /// <summary>
        /// 正常显示当前选中的子面板
        /// </summary>
        public void NormalShowCurrentPlayerItem()
        {
            if(_currentPlayerItem != null)
            {
                _currentPlayerItem.Dock = DockStyle.None;
                // 只有一台摄像机时，不改变选中状态
                if(NETCameraManager.GetInstance().Cameras.GetCount() > 1)
                {
                    CurrentPlayerItem.SelectedState(true);
                }
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 重新布局界面
        /// </summary>
        private void RePreviewLayout()
        {
            int channelCount = PreviewList.Count;
            // 计算视频面板位置和面积
            IList<Rectangle> playerList = CalcPanelRectangle(channelCount);

            //创建面板
            for(var i = 0; i < channelCount; i++)
            {
                PlayerItem item = PreviewList[i];
                item.Size = playerList[i].Size;
                item.Location = playerList[i].Location;
            }
        }

        /// <summary>
        /// 计算视频面板位置和面积
        /// </summary>
        /// <param name="channelCount"></param>
        /// <returns></returns>
        private IList<Rectangle> CalcPanelRectangle(int channelCount)
        {
            IList<Rectangle> result = new List<Rectangle>();

            Size totalArea = this.Size;
            //模数
            int modulo = channelCount > 64 ? 8 : (int)Math.Ceiling(Math.Sqrt(channelCount));
            if(modulo == 0)
            {
                return result;
            }
            //单个画面大小
            int width = (totalArea.Width - (modulo + 1)) / modulo;
            int height = (totalArea.Height - (modulo + 1)) / modulo;

            for(var i = 0; i < channelCount; i++)
            {
                var rect = new Rectangle
                {
                    Width = width,
                    Height = height,
                    X = i % modulo == 0 ? 1 : result[i - 1].X + width + 1,
                    Y = i % modulo == 0 ? (i == 0 ? 1 : result[i - modulo].Y + height + 1) : result[i - 1].Y
                };
                result.Add(rect);
            }
            return result;
        }

        #endregion
    }
}
