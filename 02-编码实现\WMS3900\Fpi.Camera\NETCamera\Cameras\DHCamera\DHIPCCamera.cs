﻿using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using Fpi.Camera.DH.UI;
using Fpi.ExternalSDK.DH;

namespace Fpi.Camera.DH
{
    /// <summary>
    /// 大华摄像机
    /// </summary>
    public class DHIPCCamera : BaseNETCamera
    {
        #region 字段属性

        private const int m_WaitTime = 5000;

        /// <summary>
        /// SDK初始化完成参数标志位
        /// </summary>
        private static bool SdkInitOver { get; set; }

        /// <summary>
        /// 不预览时的图像承载控件
        /// 后台录像使用
        /// </summary>
        private PictureBox _noPreviewCtrl;

        #endregion

        #region 构造

        public DHIPCCamera()
        {
            if(string.IsNullOrEmpty(Description))
            {
                Description = "大华摄像机";
            }

            if(id.Contains("DefaultID"))
            {
                id = "DHIPCCamera";
            }

            if(name.Contains("DefaultName"))
            {
                name = "大华摄像机";
            }

            ControlTypePath = typeof(FrmDHCameraControl).FullName;
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(name) || name.Contains("DefaultName") ? "大华摄像机" : name;
        }

        #region 初始化

        /// <summary>
        /// 初始化参数
        /// </summary>
        public override void InitSDK()
        {
            // 软件只注册一次即可
            // 避免重复注册
            if(!SdkInitOver)
            {
                try
                {
                    //fDisConnectCallBack m_DisConnectCallBack = new fDisConnectCallBack(DisConnectCallBack);
                    //fHaveReConnectCallBack m_ReConnectCallBack = new fHaveReConnectCallBack(ReConnectCallBack);
                    //SdkInitOver = DHNETClient.Init(m_DisConnectCallBack, IntPtr.Zero, null);
                    //SdkInitOver = DHNETClient.InitWithDefaultSetting(m_DisConnectCallBack, m_ReConnectCallBack, IntPtr.Zero, null);
                    SdkInitOver = DHNETClient.Init(null, IntPtr.Zero, null);
                }
                catch(Exception)
                {
                    throw new Exception(name + ":--初始化SDK,error code= " + GetLastError());
                }
            }

            if(!SdkInitOver)
            {
                throw new Exception(name + ":--初始化参数失败,NET_DVR_Init error!");
            }
        }

        /// <summary>
        /// 释放摄像机SDK
        /// </summary>
        public override void CleanSDK()
        {
            if(SdkInitOver)
            {
                try
                {
                    DHNETClient.Cleanup();
                    SdkInitOver = false;
                }
                catch(Exception)
                {
                    throw new Exception(name + ":--释放SDK失败,error code= " + GetLastError());
                }
            }
        }

        #endregion

        #region 登录注销

        /// <summary>
        /// 登录
        /// </summary>
        public override void Login()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                var m_DeviceInfo = new NET_DEVICEINFO_Ex();
                m_LoginID = DHNETClient.LoginWithHighLevelSecurity(Ip, ushort.Parse(Port), User, Pwd, EM_LOGIN_SPAC_CAP_TYPE.TCP, IntPtr.Zero, ref m_DeviceInfo);
                if(IntPtr.Zero == m_LoginID)
                {
                    throw new Exception(name + "登录失败：error code=" + GetLastError());
                }
                PrevierChannel = 0;

                // 刻录机，计算预览通道号
                if(IsDS)
                {
                    PrevierChannel = DsPort - 1;
                }
            }
        }

        /// <summary>
        /// 注销
        /// </summary>
        public override void Logout()
        {
            if(IntPtr.Zero != m_LoginID)
            {
                if(!DHNETClient.Logout(m_LoginID))
                {
                    throw new Exception(name + ":--注销失败: error code=" + GetLastError());
                }
                m_LoginID = IntPtr.Zero;
            }
        }

        #endregion

        #region 视频流相关

        /// <summary>
        /// 开始获取视频流
        /// </summary>
        /// <returns></returns>
        public override void StartRealPlay()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                throw new Exception(name + "开始获取视频流失败:设备还没注册。");
            }

            if(m_NoPreviewRealPlayHandle != IntPtr.Zero)
            {
                throw new Exception(name + "开始获取视频流失败:后台正在获取视频流，不可重复获取！");
            }

            if(m_PreviewRealPlayHandle == IntPtr.Zero)
            {
                m_PreviewRealPlayHandle = DHNETClient.RealPlay(m_LoginID, PrevierChannel, GetPreviewInterface().GetPicHandle(), EM_RealPlayType.Realplay);
                if(IntPtr.Zero == m_PreviewRealPlayHandle)
                {
                    throw new Exception(name + "开始获取视频流失败：error code=" + GetLastError());
                }
            }
        }

        /// <summary>
        /// 停止获取视频流
        /// </summary>
        public override void StopRealPlay()
        {
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                if(!DHNETClient.StopRealPlay(m_PreviewRealPlayHandle))
                {
                    throw new Exception(name + ":--停止获取视频流失败: error code=" + GetLastError());
                }
                m_PreviewRealPlayHandle = IntPtr.Zero;
            }
        }

        /// <summary>
        /// 开始获取视频流(不绑定预览界面)
        /// </summary>
        /// <returns></returns>
        public override void StartNoPreviewRealPlay()
        {
            if(IntPtr.Zero == m_LoginID)
            {
                throw new Exception(name + "开始获取视频流失败:设备还没注册。");
            }

            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                throw new Exception(name + "开始获取视频流失败:前台正在获取视频流，不可重复获取！");
            }

            if(m_NoPreviewRealPlayHandle == IntPtr.Zero)
            {
                _noPreviewCtrl = new PictureBox();
                m_NoPreviewRealPlayHandle = DHNETClient.RealPlay(m_LoginID, PrevierChannel, _noPreviewCtrl.Handle, EM_RealPlayType.Realplay);

                if(IntPtr.Zero == m_NoPreviewRealPlayHandle)
                {
                    throw new Exception(name + "开始获取视频流失败：error code=" + GetLastError());
                }
            }
        }

        /// <summary>
        /// 停止获取视频流(不绑定预览界面)
        /// </summary>
        public override void StopNoPreviewRealPlay()
        {
            if(m_NoPreviewRealPlayHandle != IntPtr.Zero)
            {
                if(!DHNETClient.StopRealPlay(m_NoPreviewRealPlayHandle))
                {
                    m_NoPreviewRealPlayHandle = IntPtr.Zero;
                    throw new Exception(name + ":--停止获取视频流失败: error code=" + GetLastError());
                }
                m_NoPreviewRealPlayHandle = IntPtr.Zero;
                _noPreviewCtrl?.Dispose();
                _noPreviewCtrl = null;
            }
        }

        #endregion

        #region 抓拍

        /// <summary>
        /// 抓拍图像到指定文件
        /// </summary>
        /// <param name="picFileName"></param>
        /// <returns></returns>
        public override bool ScreenShot(string picFileName)
        {
            // 初始化
            InitSDK();

            // 未登录，先登录
            if(IntPtr.Zero == m_LoginID)
            {
                Login();
            }

            bool flag;

            // 预览抓图
            if(m_PreviewRealPlayHandle != IntPtr.Zero)
            {
                flag = DHNETClient.CapturePicture(m_PreviewRealPlayHandle, picFileName, EM_NET_CAPTURE_FORMATS.JPEG);
            }
            // 非预览抓图
            else
            {
                NoPreviewRealPlay();

                Thread.Sleep(1000);

                flag = DHNETClient.CapturePicture(m_NoPreviewRealPlayHandle, picFileName, EM_NET_CAPTURE_FORMATS.JPEG);

                StopNoPreviewRealPlay();
            }

            string str = flag
              ? name + ":--截图成功，文件：" + picFileName
              : name + "截图失败, error code= " + GetLastError();
            //CameraLogHelper.Info(str);
            return flag;
        }

        #endregion

        #region 录像

        /// <summary>
        /// 开始录像
        /// </summary>
        /// <param name="videoFileName"></param>
        /// <returns></returns>
        public override bool StartRecording(string videoFileName)
        {
            // 初始化
            InitSDK();

            // 未登录，先登录
            if(IntPtr.Zero == m_LoginID)
            {
                Login();
            }

            IntPtr handle = m_PreviewRealPlayHandle;

            // 如果当前没开始获取视频流，先开始获取视频流(不绑定预览界面)
            if(m_PreviewRealPlayHandle == IntPtr.Zero && m_NoPreviewRealPlayHandle == IntPtr.Zero)
            {
                NoPreviewRealPlay();

                handle = m_NoPreviewRealPlayHandle;
            }

            if(IsRecording)
            {
                return false;
            }

            //录像保存路径和文件名
            SVideoFileName = videoFileName;

            if(DHNETClient.SaveRealData(handle, SVideoFileName))
            {
                IsRecording = true;
                string str = $"{name}:--开始录像成功!";
                //CameraLogHelper.Info(str);

                return true;
            }
            else
            {
                string str = $"{name}:--开始录像失败, error code= {GetLastError()}";
                //CameraLogHelper.Info(str);
                throw new Exception(str);
            }
        }

        /// <summary>
        /// 停止录像
        /// </summary>
        /// <returns></returns>
        public override bool StopRecording()
        {
            if(!IsRecording)
            {
                return false;
            }

            bool ret = false;

            if(IntPtr.Zero != m_NoPreviewRealPlayHandle)
            {
                ret = DHNETClient.StopSaveRealData(m_NoPreviewRealPlayHandle);
                StopNoPreviewRealPlay();
            }
            else
            {
                ret = DHNETClient.StopSaveRealData(m_PreviewRealPlayHandle);
            }

            if(ret)
            {
                IsRecording = false;
                string str = name + ":--保存录像成功，录像文件名： " + SVideoFileName;
                CameraLogHelper.Info(str);

                return true;
            }
            else
            {
                string str = $"{name}:--停止录像失败, error code= {GetLastError()}";
                //CameraLogHelper.Info(str);
                throw new Exception(str);
            }
        }

        #endregion

        #region 云台控制

        /// <summary>
        /// 云台控制
        /// </summary>
        /// <param name="dwPTZCommand">控制方向类型</param>
        /// <param name="dwStop">是否停止(mouseup 表示停止传入1，down传入0)</param>
        /// <param name="dwSpeed">速度</param>
        public override void PTZControlWithSpeed(ControlType dwPTZCommand, int dwStop, int dwSpeed)
        {
            bool isStop = false;
            if(dwStop == 1)
            {
                isStop = true;
            }
            bool ret = false;
            switch(dwPTZCommand)
            {
                case ControlType.RIGHT:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.RIGHT_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.LEFT:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.LEFT_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.UP:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.UP_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.DOWN:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.DOWN_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.LEFTTOP:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.LEFTTOP, dwSpeed, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.RIGHTTOP:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.RIGHTTOP, dwSpeed, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.LEFTDOWN:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.LEFTDOWN, dwSpeed, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.RIGHTDOWN:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.RIGHTDOWN, dwSpeed, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case ControlType.WIPER:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.LAMP_CONTROL, 1, 0, 0, false, IntPtr.Zero);
                    break;
            }

            if(!ret)
            {
                throw new Exception($"{name}云台控制出错，error code={GetLastError()}");
            }
        }

        #endregion

        #region 校准设备时间

        /// <summary>
        /// 时间校准
        /// </summary>
        /// <returns></returns>
        public override void SetTime()
        {
            IntPtr inPtr = IntPtr.Zero;
            try
            {
                NET_TIME time = NET_TIME.FromDateTime(DateTime.Now);
                inPtr = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(NET_TIME)));
                Marshal.StructureToPtr(time, inPtr, true);

                if(!DHNETClient.SetDevConfig(m_LoginID, EM_DEV_CFG_TYPE.TIMECFG, PrevierChannel, inPtr, (uint)Marshal.SizeOf(typeof(NET_TIME)), 5000))
                {
                    throw new Exception($"error code={GetLastError()}");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"{name}:--校准设备时间失败: {ex.Message}");
            }
            finally
            {
                Marshal.FreeHGlobal(inPtr);
            }
        }

        #endregion

        #region 巡航

        /// <summary>
        /// 开始巡航
        /// </summary>
        public override void PTZCruiseStart()
        {
            if(!DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.CLOSELOOP, 0, 0, 0, false, IntPtr.Zero))
            {
                throw new Exception(name + ":--开始巡航操作失败,error code=" + GetLastError());
            }
        }

        /// <summary>
        /// 结束巡航
        /// </summary>
        public override void PTZCruiseStop()
        {
            if(!DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.CLOSELOOP, 0, 0, 0, true, IntPtr.Zero))
            {
                throw new Exception($"{name}:--结束巡航操作失败，error code={GetLastError()}");
            }
        }

        #endregion

        #region 获取错误日志

        /// <summary>
        /// 获取错误日志
        /// </summary>
        /// <returns></returns>
        public override string GetLastError()
        {
            return DHNETClient.GetLastError();
        }

        #endregion

        #endregion

        #region 公共方法

        #region 预置位

        /// <summary>
        /// 设置预置点
        /// </summary>
        /// <param name="prest"></param>
        /// <returns></returns>
        public void SetControl(int prest)
        {
            if(!DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.POINT_SET_CONTROL, 0, prest, 0, false, IntPtr.Zero))
            {
                throw new Exception($"{name}设置预置点，error code={GetLastError()}");
            }
        }

        /// <summary>
        /// 删除预置点
        /// </summary>
        /// <param name="prest"></param>
        /// <returns></returns>
        public void DelControl(int prest)
        {
            if(!DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.POINT_DEL_CONTROL, 0, prest, 0, false, IntPtr.Zero))
            {
                throw new Exception($"{name}删除预置点，error code={GetLastError()}");
            }
        }

        /// <summary>
        /// 调用预置位（转至预置点）
        /// </summary>
        /// <param name="prest"></param>
        /// <returns></returns>
        public void MoveControl(int prest)
        {
            if(!DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.POINT_MOVE_CONTROL, 0, prest, 0, false, IntPtr.Zero))
            {
                throw new Exception($"{name}调用预置位，error code={GetLastError()}");
            }
        }

        #endregion

        #region 控制

        public void DHPTZControl(DHPTZControlType dwPTZCommand, int dwStop, int dwSpeed)
        {
            bool isStop = false;
            if(dwStop == 1)
            {
                isStop = true;
            }
            bool ret = false;
            switch(dwPTZCommand)
            {
                case DHPTZControlType.ZOOM_ADD_CONTROL:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.ZOOM_ADD_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case DHPTZControlType.ZOOM_DEC_CONTROL:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.ZOOM_DEC_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case DHPTZControlType.FOCUS_ADD_CONTROL:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.FOCUS_ADD_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case DHPTZControlType.FOCUS_DEC_CONTROL:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.FOCUS_DEC_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case DHPTZControlType.APERTURE_ADD_CONTROL:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.APERTURE_ADD_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                case DHPTZControlType.APERTURE_DEC_CONTROL:
                    ret = DHNETClient.PTZControl(m_LoginID, PrevierChannel, EM_EXTPTZ_ControlType.APERTURE_DEC_CONTROL, 0, dwSpeed, 0, isStop, IntPtr.Zero);
                    break;
                default:
                    break;
            }
            if(!ret)
            {
                throw new Exception($"{name}控制出错，error code={GetLastError()}");
            }
        }

        #endregion

        #region 水印

        /// <summary>
        /// 获取水印
        /// </summary>
        /// <param name="text"></param>
        /// <param name="selectindex"></param>
        public void GetOSD(ref string text, ref int selectindex)
        {
            NET_OSD_CUSTOM_TITLE customInfo = new NET_OSD_CUSTOM_TITLE();
            customInfo.dwSize = (uint)Marshal.SizeOf(typeof(NET_OSD_CUSTOM_TITLE));
            customInfo.emOsdBlendType = EM_OSD_BLEND_TYPE.MAIN;
            object obj = customInfo;
            bool ret = DHNETClient.GetOSDConfig(m_LoginID, EM_CFG_OSD_TYPE.CUSTOMTITLE, PrevierChannel, ref obj, m_WaitTime);
            if(!ret)
            {
                throw new Exception(name + "设置水印失败：error code=" + GetLastError());
            }
            customInfo = (NET_OSD_CUSTOM_TITLE)obj;
            text = customInfo.stuCustomTitle[1].szText.Replace("|", "\r\n");
            NET_OSD_CUSTOM_TITLE_TEXT_ALIGN customAlign = new NET_OSD_CUSTOM_TITLE_TEXT_ALIGN();
            customAlign.dwSize = (uint)Marshal.SizeOf(typeof(NET_OSD_CUSTOM_TITLE_TEXT_ALIGN));
            object objAlign = customAlign;
            ret = DHNETClient.GetOSDConfig(m_LoginID, EM_CFG_OSD_TYPE.CUSTOMTITLETEXTALIGN, PrevierChannel, ref objAlign, m_WaitTime);
            if(!ret)
            {
                return;
            }
            customAlign = (NET_OSD_CUSTOM_TITLE_TEXT_ALIGN)objAlign;
            selectindex = (int)customAlign.emTextAlign[1];
        }

        /// <summary>
        /// 设置水印
        /// </summary>
        /// <param name="eM_TITLE_TEXT_ALIGNTYPE"></param>
        /// <param name="customtitle"></param>
        /// <param name="Align"></param>
        /// <returns></returns>
        public void SetOSD(EM_TITLE_TEXT_ALIGNTYPE eM_TITLE_TEXT_ALIGNTYPE, string customtitle, bool Align)
        {
            NET_OSD_CUSTOM_TITLE customInfo = new NET_OSD_CUSTOM_TITLE();
            customInfo.dwSize = (uint)Marshal.SizeOf(typeof(NET_OSD_CUSTOM_TITLE));
            customInfo.emOsdBlendType = EM_OSD_BLEND_TYPE.MAIN;
            customInfo.nCustomTitleNum = 4;
            customInfo.stuCustomTitle = new NET_CUSTOM_TITLE_INFO[8];
            customInfo.stuCustomTitle[1].bEncodeBlend = true;

            customInfo.stuCustomTitle[1].szText = customtitle.Replace("\r\n", "|");
            object obj = customInfo;
            bool ret = DHNETClient.SetOSDConfig(m_LoginID, EM_CFG_OSD_TYPE.CUSTOMTITLE, PrevierChannel, obj, m_WaitTime);
            if(!ret)
            {
                throw new Exception(name + "设置水印失败：error code=" + GetLastError());
            }
            if(Align)
            {
                NET_OSD_CUSTOM_TITLE_TEXT_ALIGN customAlign = new NET_OSD_CUSTOM_TITLE_TEXT_ALIGN();
                customAlign.dwSize = (uint)Marshal.SizeOf(typeof(NET_OSD_CUSTOM_TITLE_TEXT_ALIGN));
                customAlign.nCustomTitleNum = 4;
                customAlign.emTextAlign = new EM_TITLE_TEXT_ALIGNTYPE[8];
                customAlign.emTextAlign[1] = eM_TITLE_TEXT_ALIGNTYPE;
                obj = customAlign;
                ret = DHNETClient.SetOSDConfig(m_LoginID, EM_CFG_OSD_TYPE.CUSTOMTITLETEXTALIGN, PrevierChannel, obj, m_WaitTime);
                if(!ret)
                {
                    throw new Exception(name + "设置水印失败：error code=" + GetLastError());
                }
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 添加了重试机制的后台预览启动
        /// </summary>
        private void NoPreviewRealPlay()
        {
            try
            {
                StartNoPreviewRealPlay();
            }
            catch(Exception ex)
            {
                // 报“打开解码库出错”或"句柄无效"时，尝试重新初始化SDK
                if(ex.Message.Contains("打开解码库出错") || ex.Message.Contains("句柄无效"))
                {
                    CameraLogHelper.Info($"{ex.Message},尝试释放SDK，触发重新初始化SDK");

                    try
                    {
                        DHNETClient.Cleanup();
                        m_LoginID = IntPtr.Zero;
                        SdkInitOver = false;
                        CameraLogHelper.Info($"{name}:--释放SDK成功");
                    }
                    catch(Exception)
                    {
                        CameraLogHelper.Info($"{name}:--释放SDK失败,error code= {GetLastError()}");
                    }
                }

                Thread.Sleep(1000);

                // 初始化
                InitSDK();

                // 未登录，先登录
                if(IntPtr.Zero == m_LoginID)
                {
                    Login();
                }

                // 尝试重试
                StartNoPreviewRealPlay();
            }
        }

        /// <summary>
        /// 断线回调函数
        /// </summary>
        /// <param name="lLoginID"></param>
        /// <param name="pchDVRIP"></param>
        /// <param name="nDVRPort"></param>
        /// <param name="dwUser"></param>
        private void DisConnectCallBack(IntPtr lLoginID, IntPtr pchDVRIP, int nDVRPort, IntPtr dwUser)
        {
            string str = $"{this.name}设备离线";
            CameraLogHelper.Info(str);
        }

        /// <summary>
        /// 重连回调函数
        /// </summary>
        /// <param name="lLoginID"></param>
        /// <param name="pchDVRIP"></param>
        /// <param name="nDVRPort"></param>
        /// <param name="dwUser"></param>
        private void ReConnectCallBack(IntPtr lLoginID, IntPtr pchDVRIP, int nDVRPort, IntPtr dwUser)
        {
            string str = $"{this.name}设备重连在线";
            CameraLogHelper.Info(str);
        }

        #endregion
    }
}