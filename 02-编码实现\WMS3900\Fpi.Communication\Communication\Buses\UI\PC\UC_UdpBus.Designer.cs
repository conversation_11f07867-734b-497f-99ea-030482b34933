﻿namespace Fpi.Communication.Buses.UI.PC
{
    partial class UC_UdpBus
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.txtUdpIp = new System.Windows.Forms.TextBox();
            this.txtLocalPort = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.txtDesPort = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.chkNeedSaveLog = new System.Windows.Forms.CheckBox();
            this.SuspendLayout();
            // 
            // txtUdpIp
            // 
            this.txtUdpIp.Location = new System.Drawing.Point(94, 20);
            this.txtUdpIp.Name = "txtUdpIp";
            this.txtUdpIp.Size = new System.Drawing.Size(241, 21);
            this.txtUdpIp.TabIndex = 15;
            // 
            // txtLocalPort
            // 
            this.txtLocalPort.CanEmpty = true;
            this.txtLocalPort.DigitLength = 2;
            this.txtLocalPort.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtLocalPort.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtLocalPort.IsValidCheck = false;
            this.txtLocalPort.Label = "";
            this.txtLocalPort.Location = new System.Drawing.Point(94, 74);
            this.txtLocalPort.MaxValue = null;
            this.txtLocalPort.MinValue = null;
            this.txtLocalPort.Name = "txtLocalPort";
            this.txtLocalPort.Size = new System.Drawing.Size(241, 21);
            this.txtLocalPort.TabIndex = 14;
            // 
            // txtDesPort
            // 
            this.txtDesPort.CanEmpty = true;
            this.txtDesPort.DigitLength = 2;
            this.txtDesPort.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtDesPort.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtDesPort.IsValidCheck = false;
            this.txtDesPort.Label = "";
            this.txtDesPort.Location = new System.Drawing.Point(94, 47);
            this.txtDesPort.MaxValue = null;
            this.txtDesPort.MinValue = null;
            this.txtDesPort.Name = "txtDesPort";
            this.txtDesPort.Size = new System.Drawing.Size(241, 21);
            this.txtDesPort.TabIndex = 13;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(32, 25);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(41, 12);
            this.label8.TabIndex = 10;
            this.label8.Text = "IP地址";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(32, 52);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(53, 12);
            this.label10.TabIndex = 12;
            this.label10.Text = "目标端口";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(32, 79);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(53, 12);
            this.label11.TabIndex = 11;
            this.label11.Text = "本机端口";
            // 
            // chkNeedSaveLog
            // 
            this.chkNeedSaveLog.AutoSize = true;
            this.chkNeedSaveLog.Location = new System.Drawing.Point(34, 103);
            this.chkNeedSaveLog.Name = "chkNeedSaveLog";
            this.chkNeedSaveLog.Size = new System.Drawing.Size(72, 16);
            this.chkNeedSaveLog.TabIndex = 17;
            this.chkNeedSaveLog.Text = "保存日志";
            this.chkNeedSaveLog.UseVisualStyleBackColor = true;
            // 
            // UC_UdpBus
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.chkNeedSaveLog);
            this.Controls.Add(this.txtUdpIp);
            this.Controls.Add(this.txtLocalPort);
            this.Controls.Add(this.txtDesPort);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.label11);
            this.Name = "UC_UdpBus";
            this.Size = new System.Drawing.Size(366, 122);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TextBox txtUdpIp;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtLocalPort;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtDesPort;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.CheckBox chkNeedSaveLog;
    }
}
