using System;
using Fpi.Communication.Manager;
using Fpi.HB.Business.Protocols.GB.Config;
using Fpi.UI.Common.PC.Configure;

namespace Fpi.HB.Business.Protocols.GB.ConfigUI
{
    public partial class GBConfigUC : BaseConfigureView // Form
    {
        protected SingleCfg singleConfig = null;
        protected GBConfig gbConfigClone = null;
        public GBConfigUC()
        {
            InitializeComponent();
        }
        protected override void ShowConfig(object obj)
        {
            this.Init();
        }

        private void Init()
        {
            gbConfigClone = GBConfig.GetInstance().Clone() as GBConfig;
            if(this.configObj != null)
            {
                this.singleConfig = gbConfigClone.GetConfig(((Pipe)this.configObj).id);
            }

            this.cmbST.Text = singleConfig.ST;
            this.txtPwd.Text = singleConfig.Password;
            this.txtServerAddress.Text = singleConfig.ServerAddress;
            this.nuReCount.Value = singleConfig.ReCount;
            this.nuOverTime.Value = singleConfig.OverTime;
            this.nuWarnTime.Value = singleConfig.WarnTime;

            this.chkMinute.Checked = singleConfig.ReportMinuteCount;
            this.chkHour.Checked = singleConfig.ReportHourCount;
            this.chkDay.Checked = singleConfig.ReportDayCount;
            this.chkReportAlarm.Checked = singleConfig.ReportAlarm;
            this.chkState.Checked = singleConfig.EquipState;
            this.chkFromDb.Checked = singleConfig.FromDB;
            this.txtMN.Text = singleConfig.MN;

            if(this.chkMinute.Checked)
            {
                this.cmbSpan.Text = singleConfig.MinuteTimespan.ToString();
            }
            if(this.chkState.Checked)
            {
                this.cmbSteteSpan.Text = singleConfig.StateTimespan.ToString();
            }
            if(this.chkDay.Checked)
            {
                this.dtpDay.Value = singleConfig.ReportTime.AddYears(2000);
            }

            this.rdInitiative.Checked = singleConfig.AutoReportLost;
            this.rdPassive.Checked = !this.rdInitiative.Checked;
            this.chkNeedFlag.Checked = singleConfig.NeedFlag;
            this.chkNeedQN.Checked = singleConfig.NeedQN;
            this.nuCmdInterval.Value = singleConfig.CmdInterval;
        }

        public override object Save()
        {
            singleConfig.ST = this.cmbST.Text;
            singleConfig.Password = this.txtPwd.Text;
            singleConfig.ServerAddress = this.txtServerAddress.Text;
            singleConfig.ReCount = (int)this.nuReCount.Value;
            singleConfig.WarnTime = (int)this.nuWarnTime.Value;
            singleConfig.OverTime = (int)this.nuOverTime.Value;
            singleConfig.ReportMinuteCount = this.chkMinute.Checked;
            singleConfig.ReportHourCount = this.chkHour.Checked;
            singleConfig.ReportDayCount = this.chkDay.Checked;
            singleConfig.ReportAlarm = this.chkReportAlarm.Checked;
            singleConfig.EquipState = this.chkState.Checked;
            singleConfig.DayReportTime = (this.chkDay.Checked
                                          ? this.dtpDay.Value.ToString(SingleCfg.DayReportTimeFormat)
                                          : "0000");
            singleConfig.MinuteTimespan = (this.chkMinute.Checked ? int.Parse(this.cmbSpan.Text) : 0);
            singleConfig.StateTimespan = (this.chkState.Checked ? int.Parse(this.cmbSteteSpan.Text) : 0);

            singleConfig.AutoReportLost = this.rdInitiative.Checked;
            singleConfig.NeedFlag = this.chkNeedFlag.Checked;
            singleConfig.NeedQN = this.chkNeedQN.Checked;
            singleConfig.FromDB = this.chkFromDb.Checked;
            singleConfig.CmdInterval = (int)this.nuCmdInterval.Value;

            singleConfig.MN = this.txtMN.Text;

            if(!gbConfigClone.singleCfgs.Contains(singleConfig.id))
            {
                gbConfigClone.singleCfgs.Add(singleConfig);
            }

            gbConfigClone.Save();
            gbConfigClone.Save(GBConfig.GetInstance());
            return null;
        }


        private void btnSet_Click(object sender, EventArgs e)
        {
            FrmOutPutConfig frmOutPutConfig = new FrmOutPutConfig((Pipe)this.configObj, singleConfig);

            frmOutPutConfig.ShowDialog();
        }
    }
}
