﻿/*==================================================================================================
** 类 名 称:IDataUpload
** 创 建 人:xiaopeng_liu
** 当前版本：V1.0.0
** CLR 版本:4.0.30319.42000
** 创建时间:2018/5/4 16:51:32

** 修改人		修改时间		修改后版本		修改内容


** 功能描述：数据上传类别方法封装
 
==================================================================================================
 Copyright @2018. Focused Photonics Inc. All rights reserved.
==================================================================================================*/


using System;

namespace Fpi.HB.Business.Protocols.Interface
{
    /// <summary>
    /// 数据上传类别方法封装
    /// </summary>
    public interface IDataUpload
    {
        /// <summary>
        /// 上传指定类型数据
        /// <param name="type">上传数据类型</param>
        /// </summary>
        void UploadData(int type);

        /// <summary>
        /// 上传类型
        /// </summary>
        Type UploadDataType { get; }
    }
}
