﻿namespace Fpi.Devices.Channel
{
    partial class ConfigA2BPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.nuMaxDigital = new System.Windows.Forms.NumericUpDown();
            this.nuMinDigital = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.nuMaxSignal = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.nuMinSignal = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxDigital)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinDigital)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxSignal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinSignal)).BeginInit();
            this.SuspendLayout();
            // 
            // nuMaxDigital
            // 
            this.nuMaxDigital.DecimalPlaces = 2;
            this.nuMaxDigital.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMaxDigital.Location = new System.Drawing.Point(256, 38);
            this.nuMaxDigital.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMaxDigital.Minimum = new decimal(new int[] {
            9999999,
            0,
            0,
            -2147483648});
            this.nuMaxDigital.Name = "nuMaxDigital";
            this.nuMaxDigital.Size = new System.Drawing.Size(80, 21);
            this.nuMaxDigital.TabIndex = 20;
            // 
            // nuMinDigital
            // 
            this.nuMinDigital.DecimalPlaces = 2;
            this.nuMinDigital.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMinDigital.Location = new System.Drawing.Point(107, 38);
            this.nuMinDigital.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMinDigital.Minimum = new decimal(new int[] {
            9999999,
            0,
            0,
            -2147483648});
            this.nuMinDigital.Name = "nuMinDigital";
            this.nuMinDigital.Size = new System.Drawing.Size(80, 21);
            this.nuMinDigital.TabIndex = 21;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label5.Location = new System.Drawing.Point(48, 10);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 15;
            this.label5.Text = "最小信号";
            // 
            // nuMaxSignal
            // 
            this.nuMaxSignal.DecimalPlaces = 2;
            this.nuMaxSignal.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMaxSignal.Location = new System.Drawing.Point(256, 6);
            this.nuMaxSignal.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMaxSignal.Name = "nuMaxSignal";
            this.nuMaxSignal.Size = new System.Drawing.Size(80, 21);
            this.nuMaxSignal.TabIndex = 22;
            this.nuMaxSignal.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label6.Location = new System.Drawing.Point(197, 10);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 14;
            this.label6.Text = "最大信号";
            // 
            // nuMinSignal
            // 
            this.nuMinSignal.DecimalPlaces = 2;
            this.nuMinSignal.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMinSignal.Location = new System.Drawing.Point(107, 6);
            this.nuMinSignal.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMinSignal.Name = "nuMinSignal";
            this.nuMinSignal.Size = new System.Drawing.Size(80, 21);
            this.nuMinSignal.TabIndex = 23;
            this.nuMinSignal.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label7.Location = new System.Drawing.Point(48, 42);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(53, 12);
            this.label7.TabIndex = 17;
            this.label7.Text = "最小数值";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label8.Location = new System.Drawing.Point(197, 42);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(53, 12);
            this.label8.TabIndex = 16;
            this.label8.Text = "最大数值";
            // 
            // ConfigA2BPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.nuMaxDigital);
            this.Controls.Add(this.nuMinDigital);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.nuMaxSignal);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.nuMinSignal);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label8);
            this.Name = "ConfigA2BPanel";
            this.Size = new System.Drawing.Size(398, 64);
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxDigital)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinDigital)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxSignal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinSignal)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.NumericUpDown nuMaxDigital;
        private System.Windows.Forms.NumericUpDown nuMinDigital;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown nuMaxSignal;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown nuMinSignal;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;


    }
}
