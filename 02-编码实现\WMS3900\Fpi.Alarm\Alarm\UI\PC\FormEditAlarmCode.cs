using System;
using System.Windows.Forms;
using Fpi.Alarm.Properties;
using Fpi.UI.Common.PC;

namespace Fpi.Alarm.UI.PC
{
    public partial class FormEditAlarmCode : Form
    {
        public FormEditAlarmCode()
        {
            InitializeComponent();

            if(AlarmManager.GetInstance().alarmGrades != null)
            {
                this.cmbGrade.Items.AddRange(AlarmManager.GetInstance().alarmGrades.GetItems());
            }
        }
        public FormEditAlarmCode(AlarmCode alarmCode)
            : this()
        {
            this.AlarmCode = alarmCode;
        }

        public AlarmCode AlarmCode { get; private set; }

        private void FormEditAlarmItem_Load(object sender, EventArgs e)
        {
            if(AlarmCode != null)
            {
                this.txtCode.Text = AlarmCode.id;
                this.txtName.Text = AlarmCode.name;
                this.txtDesc.Text = AlarmCode.description;
                this.nuRepeat.Value = AlarmCode.repeatInterval;
                this.cmbGrade.SelectedItem = new AlarmGrade(AlarmCode.alarmGradeId);
            }

        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtCode.Text))
            {
                throw new Exception(Resources.AlarmCodeEmpty);
            }

            if(string.IsNullOrEmpty(this.txtName.Text))
            {
                throw new Exception(Resources.AlarmNameEmpty);
            }

            if(this.cmbGrade.SelectedItem == null)
            {
                throw new Exception(Resources.AlarmGradeEmpty);
            }

            if(AlarmCode == null)
            {
                AlarmCode = new AlarmCode();
            }

            AlarmCode.id = this.txtCode.Text;
            AlarmCode.name = this.txtName.Text;
            AlarmCode.description = this.txtDesc.Text;
            AlarmCode.alarmGradeId = (this.cmbGrade.SelectedItem as AlarmGrade).id;
            AlarmCode.repeatInterval = (int)this.nuRepeat.Value;
        }
    }
}