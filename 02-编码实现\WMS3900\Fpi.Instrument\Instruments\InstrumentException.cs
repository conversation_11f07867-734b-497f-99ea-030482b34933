using System;
using Fpi.Util.Exeptions;

namespace Fpi.Instruments
{
    public class InstrumentException : PlatformException
    {
        public InstrumentException()
            : base()
        {
        }

        public InstrumentException(string message)
            : base(message)
        {
        }

        public InstrumentException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}