﻿namespace Fpi.Entrance.YS.Models
{
    /// <summary>
    /// 门禁订阅回应报文类
    /// </summary>
    public class SubscriptionResponse
    {
        public SubSubscriptionResponse Response { get; set; }
    }

    /// <summary>
    /// 响应类定义
    /// </summary>
    public class SubSubscriptionResponse
    {
        /// <summary>
        /// /// <summary>
        /// 响应的 URL 地址。
        /// 通常用于指示请求的实际处理路径。      
        /// </summary>
        public string ResponseUrl { get; set; }

        /// <summary>
        /// 创建的 ID。
        /// 如果操作涉及创建资源（如记录、任务等），此字段可能返回新创建资源的唯一标识符。
        /// 如果未创建任何资源，则可能返回 -1 或其他默认值。
        /// </summary>
        public int CreatedId { get; set; }

        /// <summary>
        /// 响应代码。
        /// 表示操作的总体结果状态。
        /// 通常为 0 表示成功，非零值表示失败或异常。
        /// </summary>
        public int ResponseCode { get; set; }

        /// <summary>
        /// 子响应代码。
        /// 提供更细粒度的操作结果信息。
        /// 通常为 0 表示无额外错误，非零值表示特定的子错误类型。
        /// </summary>
        public int SubResponseCode { get; set; }

        /// <summary>
        /// 响应字符串。
        /// 对操作结果的简短描述，例如 "Succeed" 或 "Failed"。
        /// </summary>
        public string ResponseString { get; set; }

        /// <summary>
        /// 状态码。
        /// 表示操作的具体状态。
        /// 通常与 <see cref="ResponseCode"/> 类似，但可能用于区分不同层次的状态。
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 状态字符串。
        /// 对操作状态的详细描述，例如 "Succeed" 或 "Invalid Arguments"。
        /// </summary>
        public string StatusString { get; set; }

        /// <summary>
        /// 数据内容。
        /// 包含操作返回的具体数据（如果有）。
        /// 可能是一个对象、数组或其他数据结构，具体取决于接口定义。
        /// 如果没有返回数据，则可能为 null。
        /// </summary>
        public SubscriptionData Data { get; set; }
    }

    /// <summary>
    /// 订阅数据
    /// </summary>
    public class SubscriptionData
    {
        /// <summary>
        /// 创建的 ID
        /// </summary>
        public string ID { get; set; }

        public string Reference { get; set; }

        public long CurrentTime { get; set; }

        public long TerminationTime { get; set; }
    }
}
