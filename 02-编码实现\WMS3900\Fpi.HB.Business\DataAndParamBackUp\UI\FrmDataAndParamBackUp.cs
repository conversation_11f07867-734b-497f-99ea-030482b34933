﻿using System;
using System.ComponentModel;
using System.Windows.Forms;
using Fpi.DB.Manager;
using Fpi.HB.Business.Tasks;
using Fpi.Timers.UI.PC;
using Fpi.UI.Common.PC;
using WeifenLuo.WinFormsUI.Docking;

namespace Fpi.HB.Business
{
    /// <summary>
    /// 数据库备份窗口
    /// </summary>
    public partial class FrmDataAndParamBackUp : DockContent
    {
        private bool _manual = false;

        /// <summary>
        /// 备份计划定时器
        /// </summary>
        private Fpi.Timers.Timer _tempTimer = null;

        /// <summary>
        /// 构造
        /// </summary>
        public FrmDataAndParamBackUp()
        {
            InitializeComponent();
        }

        /// <summary>
        /// load
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FormBackup_Load(object sender, EventArgs e)
        {
            FpiDataBase.GetInstance().OnBackuping += new FpiDataBase.BackupReportHander(FormBackup_BackupReportEvent);
            FpiDataBase.GetInstance().OnBackupCompleted += new FpiDataBase.BackupCompletedHander(FormBackup_BackupCompletedEvent);
            FpiDataBase.GetInstance().ErrorEvent += new FpiDataBase.ErrorHander(FormBackup_ErrorEvent);

            CustomTask task = CustomTaskManager.GetInstance().GetCustomTaskByType(typeof(DbBackupTask));

            if(task != null && task.timer != null)
            {
                _tempTimer = task.timer.Clone() as Fpi.Timers.Timer;
            }

            InitUI();
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitUI()
        {
            chkIsBackUpDB.Checked = BackUpDatalManager.GetInstance().IsDBBackUpValid;
            txtBackPath.Text = BackUpDatalManager.GetInstance().DbSavePath;
            txtFileName.Text = BackUpDatalManager.GetInstance().DbSaveFileName;

            chkIsBackUpConfig.Checked = BackUpDatalManager.GetInstance().IsConfigBackUpValid;
            txtXmlBackPath.Text = BackUpDatalManager.GetInstance().XmlSavePath;

            chkIsOpenUpload.Checked = BackUpDatalManager.GetInstance().ISOpenUpload;
            txtStationID.Text = BackUpDatalManager.GetInstance().StationID;
            textStationName.Text = BackUpDatalManager.GetInstance().StationName;
            txtURL.Text = BackUpDatalManager.GetInstance().StationURL;
            nuReCount.Text = BackUpDatalManager.GetInstance().RepitTimes.ToString();
            nuOverTime.Text = BackUpDatalManager.GetInstance().OverTime.ToString();
        }

        private delegate void ErrorHander(Exception ex);
        /// <summary>
        /// 出错
        /// </summary>
        /// <param name="ex"></param>
        private void FormBackup_ErrorEvent(Exception ex)
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new ErrorHander(FormBackup_ErrorEvent), new object[] { ex });
            }
            else
            {
                this.btnBackup.Enabled = true;
                this.pbStep.Value = 0;
                this.lbInfo.Text = "";
                FpiMessageBox.ShowError("备份出错:" + ex.StackTrace.ToString());
            }
        }

        private delegate void BackupCompleteHander();
        /// <summary>
        /// 备份完成事件
        /// </summary>
        private void FormBackup_BackupCompletedEvent()
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new BackupCompleteHander(FormBackup_BackupCompletedEvent));
            }
            else
            {
                this.btnBackup.Enabled = true;
                this.pbStep.Value = 0;
                this.lbInfo.Text = "";
                if(_manual)
                {
                    FpiMessageBox.ShowInfo("备份完成!");
                    _manual = false;
                }
            }
        }

        private delegate void BackupHander(int index, int count, string info);
        /// <summary>
        /// 备份报告事件
        /// </summary>
        /// <param name="index"></param>
        /// <param name="count"></param>
        /// <param name="info"></param>
        private void FormBackup_BackupReportEvent(int index, int count, string info)
        {
            if(this.InvokeRequired)
            {
                this.Invoke(new BackupHander(FormBackup_BackupReportEvent), new object[] { index, count, info });
            }
            else
            {
                this.btnBackup.Enabled = false;
                this.lbInfo.Text = info;
                this.pbStep.Maximum = count;
                this.pbStep.Value = index;
            }
        }

        /// <summary>
        /// 执行备份
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                DbBackupTask.BackUpDBFile();
                FpiMessageBox.ShowInfo("数据库备份完成!");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 选择备份目录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSel_Click(object sender, EventArgs e)
        {
            if(DialogResult.OK == folderDialog.ShowDialog())
            {
                this.txtBackPath.Text = folderDialog.SelectedPath;
            }
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="e"></param>
        protected override void OnClosing(CancelEventArgs e)
        {
            if(!this.btnBackup.Enabled)
            {
                FpiMessageBox.ShowInfo("正在备份数据不能关闭窗口!");
                e.Cancel = true;
                return;
            }

            FpiDataBase.GetInstance().OnBackuping -= FormBackup_BackupReportEvent;
            FpiDataBase.GetInstance().OnBackupCompleted -= FormBackup_BackupCompletedEvent;
            FpiDataBase.GetInstance().ErrorEvent -= FormBackup_ErrorEvent;
            base.OnClosing(e);
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                Save();

                FpiMessageBox.ShowInfo("修改成功！");
                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void btnReconver_Click(object sender, EventArgs e)
        {
            using(FrmDBRestore frm = new FrmDBRestore())
            {
                frm.ShowDialog();
            }
        }

        /// <summary>
        /// 配置文件及参数备份
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnXmlBackup_Click(object sender, EventArgs e)
        {
            try
            {
                DbBackupTask.BackUpConfigFile();
                FpiMessageBox.ShowInfo("系统配置文件备份完成!");
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 配置文件及参数恢复
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnXmlReconver_Click(object sender, EventArgs e)
        {
            new FrmParamRestore().ShowDialog();
        }

        /// <summary>
        /// 选择配置文件保存目录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnXmlSel_Click(object sender, EventArgs e)
        {
            if(DialogResult.OK == folderDialogXml.ShowDialog())
            {
                this.txtXmlBackPath.Text = folderDialogXml.SelectedPath;
            }
        }

        private void btnPlan_Click(object sender, EventArgs e)
        {
            FormConfigTimerUser timerForm = new FormConfigTimerUser(_tempTimer);
            if(timerForm.ShowDialog() == DialogResult.OK)
            {
                _tempTimer = timerForm.Timer;
                if(_tempTimer != null)
                {
                    _tempTimer.description = "数据库备份定时器";
                }

                CustomTask task = CustomTaskManager.GetInstance().GetCustomTaskByType(typeof(DbBackupTask));

                if(task != null && task.timer != null)
                {
                    task.timer = timerForm.Timer;
                }
            }
        }

        #region 私有方法

        private void Check()
        {
            txtBackPath.Check();
            txtFileName.Check();
            txtXmlBackPath.Check();
            if(chkIsOpenUpload.Checked)
            {
                if(string.IsNullOrEmpty(txtStationID.Text) || string.IsNullOrEmpty(textStationName.Text) || string.IsNullOrEmpty(txtURL.Text))
                {
                    throw new Exception("上传参数配置不可为空！");
                }
            }
        }

        private void Save()
        {
            BackUpDatalManager.GetInstance().IsDBBackUpValid = chkIsBackUpDB.Checked;
            BackUpDatalManager.GetInstance().DbSavePath = txtBackPath.Text;
            BackUpDatalManager.GetInstance().DbSaveFileName = txtFileName.Text;

            BackUpDatalManager.GetInstance().IsConfigBackUpValid = chkIsBackUpConfig.Checked;
            BackUpDatalManager.GetInstance().XmlSavePath = txtXmlBackPath.Text;

            BackUpDatalManager.GetInstance().StationID = txtStationID.Text;
            BackUpDatalManager.GetInstance().StationName = textStationName.Text;
            BackUpDatalManager.GetInstance().StationURL = txtURL.Text;
            BackUpDatalManager.GetInstance().ISOpenUpload = chkIsOpenUpload.Checked;
            BackUpDatalManager.GetInstance().RepitTimes = Convert.ToInt32(nuReCount.Text);
            BackUpDatalManager.GetInstance().OverTime = Convert.ToInt32(nuOverTime.Text);
            BackUpDatalManager.GetInstance().Save();

            CustomTaskManager.GetInstance().Save();
        }

        #endregion
    }
}