﻿//==================================================================================================
//类名：     IIOEquipmentOperation   
//创建人:    Administrator
//创建时间:  2012-4-24 16:32:32
//
//修改人    修改时间    修改后版本              修改内容
//
//
//针对IO设备的操作接口
//==================================================================================================

namespace Fpi.Devices.Interface
{
    public interface IIODeviceOperation
    {
        /// <summary>
        /// 初始化IO设备（可用于开关量、模拟量设备初始化参数或复位等操作）
        /// </summary>
        /// <param name="obj"></param>
        void Init(object obj);

        /// <summary>
        /// 读所有输入通道的模拟量和开关量
        /// </summary>
        /// <param name="simulates">返回的模拟量值</param>
        /// <param name="switchs">返回的开关量值</param>
        /// <returns></returns>
        void Read(out double[] simulates, out bool[] switchs);

        /// <summary>
        /// 读所有输入通道的模拟量
        /// </summary>
        /// <param name="simulates">返回的模拟量值</param>
        /// <returns></returns>
        void ReadSimulates(out double[] simulates);

        /// <summary>
        /// 读所有输入通道的开关量
        /// </summary>
        /// <param name="switchs">返回的开关量值</param>
        /// <returns></returns>
        void ReadSwitchs(out bool[] switchs);

        /// <summary>
        /// 写所有输出通道的模拟量和开关量
        /// </summary>
        /// <param name="simulates">模拟量值</param>
        /// <param name="switchs">开关量值</param>
        /// <returns></returns>
        void Write(double[] simulates, bool[] switchs);

        /// <summary>
        /// 写所有输出通道的模拟量
        /// </summary>
        /// <param name="simulates">模拟量值</param>
        /// <returns></returns>
        void WriteSimulates(double[] simulates);

        /// <summary>
        /// 写所有输出通道的开关量
        /// </summary>
        /// <param name="switchs">开关量值</param>
        /// <returns></returns>
        void WriteSwitchs(bool[] switchs);

        /// <summary>
        /// 写某一输出通道的模拟量
        /// </summary>
        /// <param name="chIndex">通道索引</param>
        /// <param name="simulate">模拟量值</param>
        /// <returns>是否成功</returns>
        void WriteSimulate(int chIndex, double simulate);

        /// <summary>
        /// 写某一输出通道的开关量
        /// </summary>
        /// <param name="chIndex">通道索引</param>
        /// <param name="simulate">开关量值</param>
        /// <returns></returns>
        void WriteSwitch(int chIndex, bool swit);
    }
}
