using System;
using System.Windows.Forms;
using Fpi.Alarm.Properties;
using Fpi.UI.Common.PC;

namespace Fpi.Alarm.UI.PC
{
    public partial class FormEditAlarmGroup : Form
    {
        public FormEditAlarmGroup()
        {
            InitializeComponent();
        }
        public FormEditAlarmGroup(AlarmGroup group)
            : this()
        {
            this.AlarmGroup = group;
        }

        public AlarmGroup AlarmGroup { get; private set; }


        private void FormEditAlarmGrade_Load(object sender, EventArgs e)
        {
            if(AlarmGroup != null)
            {
                this.txtGroupId.Text = AlarmGroup.id;
                this.txtGroupName.Text = AlarmGroup.name;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtGroupId.Text) || string.IsNullOrEmpty(this.txtGroupName.Text))
            {
                throw new Exception(Resources.AlarmGradeCodeEmpty);
            }

            if(AlarmGroup == null)
            {
                AlarmGroup = new AlarmGroup();
            }
            AlarmGroup.id = this.txtGroupId.Text;
            AlarmGroup.name = this.txtGroupName.Text;
        }
    }
}