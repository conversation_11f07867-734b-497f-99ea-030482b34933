﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Fpi.Entrance.YS.Controllers
{
    /// <summary>
    /// 实现 HTTP 摘要认证的 HttpClientHandler
    /// 符合 RFC 2617 规范
    /// </summary>
    public class DigestAuthHandler : HttpClientHandler
    {
        #region 属性字段

        /// <summary>
        /// 认证用户名
        /// </summary>
        private readonly string _username;

        /// <summary>
        /// 认证密码
        /// </summary>
        private readonly string _password;

        /// <summary>
        /// 客户端随机数
        /// </summary>
        private string _cnonce;

        /// <summary>
        /// 请求计数器
        /// </summary>
        private int _nc = 1;

        #endregion

        #region 构造

        public DigestAuthHandler(string username, string password)
        {
            _username = username;
            _password = password;
            _cnonce = GenerateNonce(); // 初始化时生成客户端随机数
        }

        #endregion

        #region 重写

        /// <summary>
        /// 重写请求发送方法，实现摘要认证流程
        /// </summary>
        /// <remarks>
        /// 1. 首次发送无认证头的请求
        /// 2. 收到401后解析WWW-Authenticate头
        /// 3. 生成Digest认证头
        /// 4. 重新发送带认证头的请求
        /// </remarks>
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            // 强制关闭长连接，确保每次请求都重新协商认证
            request.Headers.ConnectionClose = true;

            // 发送初始请求（无认证头
            var response = await base.SendAsync(request, cancellationToken);

            // 处理401未授权响应
            if(response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                // 解析WWW-Authenticate头中的认证参数
                var authHeader = response.Headers.WwwAuthenticate.ToString();
                var parameters = ParseAuthHeader(authHeader);

                if(!parameters.ContainsKey("realm") || !parameters.ContainsKey("nonce"))
                {
                    throw new InvalidOperationException("无效的WWW-Authenticate头，缺少必要参数");
                }

                // 生成Digest认证头并添加到新请求
                var authValue = GenerateDigestHeader(request, parameters);
                request.Headers.Authorization = new AuthenticationHeaderValue("Digest", authValue);

                // 释放原始401响应资源
                response.Dispose();

                // 重新发送带认证头的请求
                response = await base.SendAsync(request, cancellationToken);
            }
            return response;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 生成客户端随机数（cnonce）
        /// </summary>
        /// <returns>16进制格式的8字节随机字符串</returns>
        private string GenerateNonce()
        {
            var bytes = new byte[8];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(bytes);
            return BitConverter.ToString(bytes).Replace("-", "").ToLower();
        }

        /// <summary>
        /// 解析WWW-Authenticate头参数
        /// </summary>
        /// <param name="header">认证头内容 如：Digest realm="<EMAIL>", nonce="dcd98b7102..."</param>
        /// <returns>认证参数键值字典</returns>
        private Dictionary<string, string> ParseAuthHeader(string header)
        {
            var parameters = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            var parts = header.Split(new[] { ' ' }, 2);
            if(parts.Length != 2 || !parts[0].Equals("Digest", StringComparison.OrdinalIgnoreCase))
            {
                throw new ArgumentException("无效的认证头格式");
            }

            foreach(var param in parts[1].Split(','))
            {
                var pair = param.Trim().Split(new[] { '=' }, 2);
                if(pair.Length != 2)
                {
                    continue;
                }

                parameters[pair[0].Trim()] = pair[1].Trim().Trim('"');
            }
            return parameters;
        }

        /// <summary>
        /// 生成Digest认证头内容
        /// </summary>
        /// <param name="request">HTTP请求对象</param>
        /// <param name="parameters">认证参数字典</param>
        /// <returns>认证头值字符串</returns>
        private string GenerateDigestHeader(HttpRequestMessage request, Dictionary<string, string> parameters)
        {
            var uri = request.RequestUri.PathAndQuery;
            var method = request.Method.Method;
            var realm = parameters["realm"];
            var nonce = parameters["nonce"];

            // 处理可选参数
            // 质量保护策略
            var qop = parameters.TryGetValue("qop", out var qopVal) ? qopVal : null;
            // 哈希算法
            var algorithm = parameters.TryGetValue("algorithm", out var algVal) ? algVal : "MD5";

            // 计算HA1（根据算法类型处理）
            var ha1 = ComputeHash($"{_username}:{realm}:{_password}");
            if(algorithm == "MD5-sess")
            {
                ha1 = ComputeHash($"{ha1}:{nonce}:{_cnonce}");
            }

            // 计算HA2
            var ha2 = ComputeHash($"{method}:{uri}");

            // 计算Response
            var response = qop != null
                ? ComputeHash($"{ha1}:{nonce}:{_nc:x8}:{_cnonce}:{qop}:{ha2}")
                : ComputeHash($"{ha1}:{nonce}:{ha2}");
            // 构建认证头
            var header = new StringBuilder()
                .Append($"username=\"{_username}\", ")
                .Append($"realm=\"{realm}\", ")
                .Append($"nonce=\"{nonce}\", ")
                .Append($"uri=\"{uri}\", ")
                .Append($"response=\"{response}\"");

            // 添加质量保护参数
            if(qop != null)
            {
                header.Append($", qop={qop}, nc={_nc:x8}, cnonce=\"{_cnonce}\"");
            }
            // 添加可选参数
            if(parameters.TryGetValue("opaque", out var opaque))
            {
                header.Append($", opaque=\"{opaque}\"");
            }
            if(algorithm != "MD5")
            {
                header.Append($", algorithm={algorithm}");
            }

            // 递增计数器（线程安全）
            Interlocked.Increment(ref _nc);
            return header.ToString();
        }

        /// <summary>
        /// 计算MD5哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>32位小写十六进制哈希值</returns>
        private string ComputeHash(string input)
        {
            using var md5 = MD5.Create();
            var bytes = Encoding.UTF8.GetBytes(input);
            return BitConverter.ToString(md5.ComputeHash(bytes))
                .Replace("-", "").ToLower();
        }

        #endregion
    }
}