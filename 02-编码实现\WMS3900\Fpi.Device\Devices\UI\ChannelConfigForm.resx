<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnRemove.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnRemove.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnRemove.Location" type="System.Drawing.Point, System.Drawing">
    <value>296, 6</value>
  </data>
  <data name="btnRemove.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnRemove.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnRemove.Text" xml:space="preserve">
    <value>移除</value>
  </data>
  <data name="&gt;&gt;btnRemove.Name" xml:space="preserve">
    <value>btnRemove</value>
  </data>
  <data name="&gt;&gt;btnRemove.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnRemove.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnRemove.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAdd.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAdd.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 6</value>
  </data>
  <data name="btnAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAdd.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnAdd.Text" xml:space="preserve">
    <value>添加</value>
  </data>
  <data name="&gt;&gt;btnAdd.Name" xml:space="preserve">
    <value>btnAdd</value>
  </data>
  <data name="&gt;&gt;btnAdd.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAdd.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnAdd.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 7</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>确定(&amp;E)</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pnlFunc.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="pnlFunc.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 415</value>
  </data>
  <data name="pnlFunc.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 37</value>
  </data>
  <data name="pnlFunc.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Name" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlFunc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="columnHeader15.Text" xml:space="preserve">
    <value>通道编号</value>
  </data>
  <data name="columnHeader15.Width" type="System.Int32, mscorlib">
    <value>145</value>
  </data>
  <data name="columnHeader16.Text" xml:space="preserve">
    <value>通道名称</value>
  </data>
  <data name="columnHeader16.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="columnHeader17.Text" xml:space="preserve">
    <value>对应数值变量</value>
  </data>
  <data name="columnHeader17.Width" type="System.Int32, mscorlib">
    <value>130</value>
  </data>
  <data name="columnHeader18.Text" xml:space="preserve">
    <value>源单位</value>
  </data>
  <data name="columnHeader18.Width" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="lvOutValue.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvOutValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10.5pt</value>
  </data>
  <data name="lvOutValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lvOutValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 415</value>
  </data>
  <data name="lvOutValue.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lvOutValue.Name" xml:space="preserve">
    <value>lvOutValue</value>
  </data>
  <data name="&gt;&gt;lvOutValue.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvOutValue.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;lvOutValue.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="columnHeader4.Text" xml:space="preserve">
    <value>通道编号</value>
  </data>
  <data name="columnHeader4.Width" type="System.Int32, mscorlib">
    <value>145</value>
  </data>
  <data name="columnHeader5.Text" xml:space="preserve">
    <value>通道名称</value>
  </data>
  <data name="columnHeader5.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="columnHeader6.Text" xml:space="preserve">
    <value>对应状态变量</value>
  </data>
  <data name="columnHeader6.Width" type="System.Int32, mscorlib">
    <value>130</value>
  </data>
  <data name="lvInSwitch.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvInSwitch.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10.5pt</value>
  </data>
  <data name="lvInSwitch.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lvInSwitch.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 415</value>
  </data>
  <data name="lvInSwitch.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lvInSwitch.Name" xml:space="preserve">
    <value>lvInSwitch</value>
  </data>
  <data name="&gt;&gt;lvInSwitch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvInSwitch.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;lvInSwitch.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>通道编号</value>
  </data>
  <data name="columnHeader1.Width" type="System.Int32, mscorlib">
    <value>145</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>通道名称</value>
  </data>
  <data name="columnHeader2.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="columnHeader3.Text" xml:space="preserve">
    <value>对应数值变量</value>
  </data>
  <data name="columnHeader3.Width" type="System.Int32, mscorlib">
    <value>130</value>
  </data>
  <data name="columnHeader10.Text" xml:space="preserve">
    <value>源单位</value>
  </data>
  <data name="columnHeader10.Width" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="lvInValue.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvInValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10.5pt</value>
  </data>
  <data name="lvInValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lvInValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 415</value>
  </data>
  <data name="lvInValue.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lvInValue.Name" xml:space="preserve">
    <value>lvInValue</value>
  </data>
  <data name="&gt;&gt;lvInValue.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvInValue.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;lvInValue.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="columnHeader7.Text" xml:space="preserve">
    <value>通道编号</value>
  </data>
  <data name="columnHeader7.Width" type="System.Int32, mscorlib">
    <value>145</value>
  </data>
  <data name="columnHeader8.Text" xml:space="preserve">
    <value>通道名称</value>
  </data>
  <data name="columnHeader8.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="columnHeader9.Text" xml:space="preserve">
    <value>对应状态变量</value>
  </data>
  <data name="columnHeader9.Width" type="System.Int32, mscorlib">
    <value>130</value>
  </data>
  <data name="lvOutSwitch.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvOutSwitch.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 10.5pt</value>
  </data>
  <data name="lvOutSwitch.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lvOutSwitch.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 415</value>
  </data>
  <data name="lvOutSwitch.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lvOutSwitch.Name" xml:space="preserve">
    <value>lvOutSwitch</value>
  </data>
  <data name="&gt;&gt;lvOutSwitch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvOutSwitch.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;lvOutSwitch.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panel2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 415</value>
  </data>
  <data name="panel2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;panel2.Name" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel2.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>554, 415</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>554, 452</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>设备通道配置</value>
  </data>
  <data name="&gt;&gt;columnHeader15.Name" xml:space="preserve">
    <value>columnHeader15</value>
  </data>
  <data name="&gt;&gt;columnHeader15.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader16.Name" xml:space="preserve">
    <value>columnHeader16</value>
  </data>
  <data name="&gt;&gt;columnHeader16.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader17.Name" xml:space="preserve">
    <value>columnHeader17</value>
  </data>
  <data name="&gt;&gt;columnHeader17.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader18.Name" xml:space="preserve">
    <value>columnHeader18</value>
  </data>
  <data name="&gt;&gt;columnHeader18.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Name" xml:space="preserve">
    <value>columnHeader4</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Name" xml:space="preserve">
    <value>columnHeader5</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Name" xml:space="preserve">
    <value>columnHeader6</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Name" xml:space="preserve">
    <value>columnHeader1</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Name" xml:space="preserve">
    <value>columnHeader2</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Name" xml:space="preserve">
    <value>columnHeader3</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader10.Name" xml:space="preserve">
    <value>columnHeader10</value>
  </data>
  <data name="&gt;&gt;columnHeader10.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader7.Name" xml:space="preserve">
    <value>columnHeader7</value>
  </data>
  <data name="&gt;&gt;columnHeader7.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader8.Name" xml:space="preserve">
    <value>columnHeader8</value>
  </data>
  <data name="&gt;&gt;columnHeader8.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader9.Name" xml:space="preserve">
    <value>columnHeader9</value>
  </data>
  <data name="&gt;&gt;columnHeader9.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ChannelConfigForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>