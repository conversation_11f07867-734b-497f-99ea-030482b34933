﻿using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Sunny.UI;

namespace Fpi.Entrance.UI
{
    public partial class FrmEntranceManager : UIForm
    {
        #region 属性、字段

        #endregion

        #region 构造

        public FrmEntranceManager()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmEntranceManager_Load(object sender, EventArgs e)
        {
            SetDataGridViewHead();
            InitlvEntranceList();
        }

        #region 增删改

        private void btnAdd_Click(object sender, EventArgs e)
        {
            FrmEntranceEdit form = new FrmEntranceEdit();
            if(form.ShowDialog() == DialogResult.OK)
            {
                EntranceManager.GetInstance().Entrances.AddNode(form.Entrance);
                this.InitlvEntranceList();
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is BaseNETEntrance entrance)
            {
                if(new FrmEntranceEdit(entrance).ShowDialog() == DialogResult.OK)
                {
                    this.InitlvEntranceList();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择需要编辑的门禁！");
            }
        }

        private void dgvData_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is BaseNETEntrance entrance)
            {
                if(new FrmEntranceEdit(entrance).ShowDialog() == DialogResult.OK)
                {
                    this.InitlvEntranceList();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择需要编辑的门禁！");
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if(this.dgvData.SelectedRows.Count > 0 && dgvData.SelectedRows[0].Tag is BaseNETEntrance entrance)
            {
                if(FpiMessageBox.ShowQuestion($"是否确认删除选中的摄像机[{entrance.name}]?") == DialogResult.Yes)
                {
                    EntranceManager.GetInstance().Entrances.Remove(entrance);
                    this.InitlvEntranceList();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择需要删除的门禁!");
            }
        }

        #endregion

        #region 保存

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                EntranceManager.GetInstance().Save();

                Task.Run(() =>
                {
                    foreach(var item in EntranceManager.GetInstance().GetAllEntrances())
                    {
                        if(item is YSEntrance yS)
                        {
                            if(yS.IsReceiceEnable)
                            {
                                yS.InitEntranceAsync();
                            }
                            else
                            {
                                yS.CleanEntranceAsync();
                            }
                        }
                    }
                });

                FpiMessageBox.ShowInfo("保存成功！");
                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("保存失败：" + ex.Message);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if(FpiMessageBox.ShowQuestion("确认取消编辑？") == DialogResult.Yes)
            {
                EntranceManager.GetInstance().ReLoad();
                DialogResult = DialogResult.Cancel;
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置表头
        /// </summary>
        private void SetDataGridViewHead()
        {
            dgvData.ClearColumns();
            dgvData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            var col = dgvData.AddColumn("序号", "num");
            col.FillWeight = 50;
            col = dgvData.AddColumn("名称", "name");
            col.FillWeight = 300;
            col = dgvData.AddColumn("描述信息", "desc");
            col.FillWeight = 440;
        }

        /// <summary>
        /// 初始化门禁列表
        /// </summary>
        private void InitlvEntranceList()
        {
            dgvData.Rows.Clear();

            foreach(BaseNETEntrance camera in EntranceManager.GetInstance().GetAllEntrances())
            {
                int index = dgvData.Rows.Add();
                DataGridViewRow dr = dgvData.Rows[index];
                dr.Tag = camera;
                dr.Cells[0].Value = index + 1;
                dr.Cells[1].Value = camera.name;
                dr.Cells[2].Value = camera.Description;
            }
        }

        #endregion
    }
}