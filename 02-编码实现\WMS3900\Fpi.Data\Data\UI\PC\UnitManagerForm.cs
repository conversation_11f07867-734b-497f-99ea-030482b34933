﻿//==================================================================================================
//类名：     UnitManagerForm   
//创建人:    hongbing_mao
//创建时间:  2013-1-7 19:11:51
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;
using System.ComponentModel;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Data.UI.PC.ValueNodeViews;
using Fpi.UI.Common.PC;
using Fpi.Xml;

namespace Fpi.Data.UI.PC
{
    /// <summary>
    /// 单位管理界面
    /// </summary>
    public partial class UnitManagerForm : Form
    {
        private UnitManager clone = null;
        /// <summary>
        /// 构造
        /// </summary>
        public UnitManagerForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void UnitManagerForm_Load(object sender, EventArgs e)
        {
            clone = UnitManager.GetInstance().Clone() as UnitManager;
            if(clone.UnitGroups == null)
            {
                clone.UnitGroups = new NodeList();
            }
            foreach(UnitGroup ug in clone.UnitGroups)
            {
                ListViewItem lvi = new ListViewItem(ug.name);
                lvi.Tag = ug;
                lvUnitType.Items.Add(lvi);
            }
        }
        /// <summary>
        /// 单位类型切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lvUnitType_SelectedIndexChanged(object sender, EventArgs e)
        {
            lvUnit.Items.Clear();
            if(lvUnitType.SelectedItems.Count > 0)
            {
                ListViewItem lvi = lvUnitType.SelectedItems[0];
                UnitGroup ug = lvi.Tag as UnitGroup;
                if(ug.Units != null)
                {
                    foreach(Unit unit in ug.Units)
                    {
                        ListViewItem item = new ListViewItem(unit.name);
                        item.SubItems.Add(unit.Trans);
                        item.SubItems.Add(unit.ReverseTrans);
                        item.Tag = unit;
                        lvUnit.Items.Add(item);
                    }
                }
            }
        }
        #region UnitType Edit
        /// <summary>
        /// 类型编辑菜单显示处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void menuUnitType_Opening(object sender, CancelEventArgs e)
        {
            if(this.lvUnitType.SelectedItems.Count == 0)
            {
                this.tsmDeleteUnitType.Visible = false;
                this.tsmEditUnitType.Visible = false;
            }
            else
            {
                this.tsmDeleteUnitType.Visible = true;
                this.tsmEditUnitType.Visible = true;
            }
        }
        /// <summary>
        /// 添加类型
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmAddUnitType_Click(object sender, EventArgs e)
        {
            FormEditUnitType form = new FormEditUnitType(clone, null);
            if(form.ShowDialog() == DialogResult.OK)
            {
                ListViewItem lvi = new ListViewItem(form.UnitGroup.name);
                lvi.Tag = form.UnitGroup;
                lvUnitType.Items.Add(lvi);
            }
        }
        /// <summary>
        /// 编辑类型
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmEditUnitType_Click(object sender, EventArgs e)
        {
            FormEditUnitType form = new FormEditUnitType(clone, this.lvUnitType.SelectedItems[0].Tag as UnitGroup);
            if(form.ShowDialog() == DialogResult.OK)
            {
                ListViewItem lvi = this.lvUnitType.SelectedItems[0];
                lvi.Text = form.UnitGroup.name;
            }
        }
        /// <summary>
        /// 删除类型
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmDeleteUnitType_Click(object sender, EventArgs e)
        {
            if(FpiMessageBox.ShowQuestion("确定删除该单位类型吗？") == DialogResult.Yes)
            {
                ListViewItem item = this.lvUnitType.SelectedItems[0];
                clone.UnitGroups.Remove(item.Tag);
                item.Remove();
            }
        }
        #endregion
        #region Unit Edit
        /// <summary>
        /// 单位编辑菜单显示处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void menuUnit_Opening(object sender, CancelEventArgs e)
        {
            if(this.lvUnitType.SelectedItems.Count == 0)
            {
                e.Cancel = true;
                return;
            }
            if(this.lvUnit.SelectedItems.Count == 0)
            {
                this.tsmDelUnit.Visible = false;
                this.tsmEditUnit.Visible = false;
            }
            else
            {
                this.tsmDelUnit.Visible = true;
                this.tsmEditUnit.Visible = true;
            }
        }
        /// <summary>
        /// 添加单位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmAddUnit_Click(object sender, EventArgs e)
        {
            FormEditUnit form = new FormEditUnit(clone, null);
            if(form.ShowDialog() == DialogResult.OK)
            {
                ListViewItem item = new ListViewItem(form.Unit.name);
                item.SubItems.Add(form.Unit.Trans);
                item.SubItems.Add(form.Unit.ReverseTrans);
                item.Tag = form.Unit;
                this.lvUnit.Items.Add(item);
                UnitGroup ug = this.lvUnitType.SelectedItems[0].Tag as UnitGroup;
                if(ug.Units == null)
                {
                    ug.Units = new NodeList();
                }
                ug.Units.Add(form.Unit);
            }
        }
        /// <summary>
        /// 删除单位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmDelUnit_Click(object sender, EventArgs e)
        {
            if(FpiMessageBox.ShowQuestion("确定删除该单位吗？") == DialogResult.Yes)
            {
                ListViewItem item = this.lvUnit.SelectedItems[0];
                clone.DeleteUnit((item.Tag as Unit).id);
                item.Remove();
            }
        }
        /// <summary>
        /// 编辑单位
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsmEditUnit_Click(object sender, EventArgs e)
        {
            ListViewItem item = this.lvUnit.SelectedItems[0];
            FormEditUnit form = new FormEditUnit(clone, item.Tag as Unit);
            if(form.ShowDialog() == DialogResult.OK)
            {
                item.SubItems[1].Text = form.Unit.Trans;
                item.SubItems[2].Text = form.Unit.ReverseTrans;
            }
        }
        #endregion
        /// <summary>
        /// 确定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            clone.Save();
            clone.Save(UnitManager.GetInstance());
        }


    }
}