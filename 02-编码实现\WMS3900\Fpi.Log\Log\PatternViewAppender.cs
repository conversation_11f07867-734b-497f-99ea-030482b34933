﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.Log.Config;
using log4net.Appender;
using log4net.Core;

namespace Fpi.Log
{
    /// <summary>
    /// 信息栏输出附着器
    /// </summary>
    public class PatternViewAppender : AppenderSkeleton
    {
        #region 字段属性

        /// <summary>
        /// 存储所有子界面引用
        /// </summary>
        public static List<ILogView> LogViews = new List<ILogView>();

        #endregion

        #region 公共方法

        /// <summary>
        /// 输出
        /// </summary>
        /// <param name="loggingEvent"></param>
        protected override void Append(LoggingEvent loggingEvent)
        {
            if(!LogManager.GetInstance().ViewOutput(loggingEvent.Level.Name))
            {
                return;
            }

            try
            {
                LogOut(loggingEvent.Level, loggingEvent);
            }
            catch(Exception ex)
            {
                ErrorHandler.Error("Failed to append to View", ex);
            }
        }

        #endregion

        #region 私有方法

        private void LogOut(Level level, LoggingEvent loggingEvent)
        {
            try
            {
                // 失效界面
                var removeList = new List<ILogView>();

                foreach(ILogView view in LogViews)
                {
                    // 界面失效
                    if(view == null || (view is Form && ((Form)view).IsDisposed))
                    {
                        removeList.Add(view);
                    }
                    // 界面有效
                    else
                    {
                        if(loggingEvent != null)
                        {
                            view.OutputInfo(GetLevelName(level), loggingEvent);
                        }
                    }
                }

                // 移除失效界面
                foreach(ILogView view in removeList)
                {
                    LogViews.Remove(view);
                }
            }
            catch(Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }

        private static string GetLevelName(Level level)
        {
            var node = LogManager.GetInstance().LogLevels[level.Name];
            return node == null ? "未知信息" : node.name;
        }

        #endregion
    }
}