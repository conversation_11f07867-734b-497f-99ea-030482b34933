﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Fpi.Util.Interfaces.Initialize;
using Fpi.Xml;

namespace Fpi.Entrance
{
    public class EntranceManager : BaseNode, IInitialization
    {
        #region 字段属性

        /// <summary>
        /// 门禁列表
        /// </summary>
        public NodeList Entrances = new NodeList();

        #endregion

        #region 构造

        private EntranceManager()
        {
            loadXml();
        }

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static EntranceManager _instance;
        public static EntranceManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new EntranceManager();
                }
            }
            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 通过设备ID查找设备
        /// </summary>
        /// <param name="entranceId"></param>
        /// <returns></returns>
        public BaseNETEntrance GetEntranceById(string entranceId)
        {
            return Entrances.FindNode(entranceId) as BaseNETEntrance;
        }

        /// <summary>
        /// 通过设备名称查找设备
        /// </summary>
        /// <param name="entranceName"></param>
        /// <returns></returns>
        public BaseNETEntrance GetEntranceByName(string entranceName)
        {
            return Entrances.FindNodeByName(entranceName) as BaseNETEntrance;
        }

        /// <summary>
        /// 获得所有设备列表
        /// </summary>
        /// <returns></returns>
        public List<BaseNETEntrance> GetAllEntrances()
        {
            var list = new List<BaseNETEntrance>();
            foreach(BaseNETEntrance vn in Entrances)
            {
                list.Add(vn);
            }
            list.Sort((x, y) => string.Compare(x.name, y.name, StringComparison.Ordinal));
            return list;
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReLoad()
        {
            lock(SyncObj)
            {
                _instance = null;
                GetInstance();
            }
        }



        #endregion

        #region 事件

        public void Initialize()
        {
            Task.Run(() =>
            {
                Task.Delay(60 * 1000).Wait();
                foreach(var item in GetAllEntrances())
                {
                    if(item is YSEntrance yS && yS.IsReceiceEnable)
                    {
                        yS.InitEntranceAsync();
                    }
                }
            });
        }

        #endregion
    }
}