﻿using System;
using System.IO;
using System.Net;
using System.Windows.Forms;
using Fpi.Camera.DH;
using Fpi.Camera.HIK;
using Fpi.Camera.YS;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.Util.Extensions;
using Sunny.UI;

namespace Fpi.Camera.UI
{
    /// <summary>
    /// 网络摄像机属性配置界面
    /// </summary>
    public partial class FrmCameraEdit : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 是否是编辑模式
        /// </summary>
        private bool _editModel { get; set; }

        /// <summary>
        /// 对应摄像机
        /// </summary>
        public BaseNETCamera Camera { get; set; }

        #endregion

        #region 构造

        public FrmCameraEdit()
        {
            InitializeComponent();
            EnumOperate.BandEnumToCmb(cmbType, typeof(eCameraType));
            cmbType.SelectedIndex = 0;
        }

        public FrmCameraEdit(BaseNETCamera camera) : this()
        {
            _editModel = true;
            this.Camera = camera;
            if(this.Camera is HikvisonCamera)
            {
                this.cmbType.SelectedValue = 0;
            }
            else if(this.Camera is DHIPCCamera)
            {
                this.cmbType.SelectedValue = 1;
            }
            else if(this.Camera is YSCamera)
            {
                this.cmbType.SelectedValue = 2;
            }
            else
            {
                this.cmbType.SelectedValue = 0;
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ParamConfigForm_Load(object sender, EventArgs e)
        {
            cmbType.Enabled = txtID.Enabled = !_editModel;

            if(Camera == null)
            {
                Camera = new HikvisonCamera();
            }

            LoadConfig();

            this.cmbType.SelectedIndexChanged += this.cmbType_SelectedIndexChanged;
        }

        private void cmbType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(!_editModel)
            {
                switch(cmbType.SelectedValue)
                {
                    case 0:
                        if(!(Camera is HikvisonCamera))
                        {
                            Camera = new HikvisonCamera();
                        }
                        break;

                    case 1:
                        if(!(Camera is DHIPCCamera))
                        {
                            Camera = new DHIPCCamera();
                        }
                        break;

                    case 2:
                        if(!(Camera is YSCamera))
                        {
                            Camera = new YSCamera();
                        }
                        break;

                    default:
                        if(!(Camera is HikvisonCamera))
                        {
                            Camera = new HikvisonCamera();
                        }
                        break;
                }

                LoadConfig();
            }
        }

        private void LoadConfig()
        {
            if(Camera != null)
            {
                txtID.Text = Camera.id;
                txtName.Text = Camera.name;
                txtIP.Text = Camera.Ip;
                txtPort.Text = Camera.Port;
                txtUser.Text = Camera.User;
                txtPassWord.Text = Camera.Pwd;
                txtDesc.Text = Camera.Description;

                txtPicSavePath.Text = FileExtension.GetRelativePath(Camera.PicSavePath);
                txtVideoSavePath.Text = FileExtension.GetRelativePath(Camera.VideoSavePath);
                this.ckbDs.Checked = Camera.IsDS;
                txtDsPort.Text = Camera.DsPort.ToString();

                txtID.Select();
            }
        }

        /// <summary>
        /// 保存参数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                Camera.id = txtID.Text;
                Camera.name = txtName.Text;

                Camera.Ip = txtIP.Text;
                Camera.Port = txtPort.Text;
                Camera.User = txtUser.Text;
                Camera.Pwd = txtPassWord.Text;
                Camera.Description = txtDesc.Text;

                Camera.PicSavePath = txtPicSavePath.Text;
                Camera.VideoSavePath = txtVideoSavePath.Text;
                Camera.IsDS = this.ckbDs.Checked;
                Camera.DsPort = int.Parse(txtDsPort.Text);

                DialogResult = DialogResult.OK;

                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 截图文件路径
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnPicSavePath_Click(object sender, EventArgs e)
        {
            // 相对路径
            // 获取绝对路径
            var filePath = FileExtension.GetAbsolutePath(txtPicSavePath.Text);

            // 空路径
            // 无效绝对路径
            if(string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                filePath = Application.StartupPath;
            }

            var dilog = new FolderBrowserDialog
            {
                Description = "请选择截图文件保存路径！",
                SelectedPath = filePath
            };
            DialogResult result = dilog.ShowDialog();
            if(result == DialogResult.OK || result == DialogResult.Yes)
            {
                txtPicSavePath.Text = FileExtension.GetRelativePath(dilog.SelectedPath);
            }
        }

        /// <summary>
        /// 录像文件路径
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnVideoSavePath_Click(object sender, EventArgs e)
        {
            // 相对路径
            // 获取绝对路径
            var filePath = FileExtension.GetAbsolutePath(txtVideoSavePath.Text);

            // 空路径
            // 无效绝对路径
            if(string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                filePath = Application.StartupPath;
            }

            var dilog = new FolderBrowserDialog
            {
                Description = "请选择录像文件保存路径！",
                SelectedPath = filePath
            };
            DialogResult result = dilog.ShowDialog();
            if(result == DialogResult.OK || result == DialogResult.Yes)
            {
                txtVideoSavePath.Text = FileExtension.GetRelativePath(dilog.SelectedPath);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查输入参数的合法性
        /// </summary>
        private void Check()
        {
            if(string.IsNullOrEmpty(cmbType.Text))
            {
                throw new Exception("摄像机类型不能为空！");
            }
            if(string.IsNullOrEmpty(txtID.Text))
            {
                throw new Exception("摄像机ID不能为空！");
            }
            if(string.IsNullOrEmpty(txtName.Text))
            {
                throw new Exception("摄像机名称不能为空！");
            }
            if(string.IsNullOrEmpty(txtIP.Text))
            {
                throw new Exception("摄像机IP不能为空！");
            }
            if(!IPAddress.TryParse(txtIP.Text, out _))
            {
                throw new Exception("摄像机IP格式错误！");
            }
            if(!uint.TryParse(txtPort.Text, out uint port) || port < 1 || port > 65535)
            {
                throw new Exception("摄像机端口号不可为空，且端口号应为1-65535范围内的整数！");
            }
            if(string.IsNullOrEmpty(txtUser.Text))
            {
                throw new Exception("用户名不能为空！");
            }
            if(string.IsNullOrEmpty(txtPassWord.Text))
            {
                throw new Exception("密码不能为空！");
            }

            var path = txtPicSavePath.Text;
            if(string.IsNullOrEmpty(path))
            {
                throw new Exception("截图文件保存路径不能为空！");
            }
            // 获取绝对路径
            path = FileExtension.GetAbsolutePath(path);
            if(!Directory.Exists(path))
            {
                throw new Exception("截图文件保存路径不存在，请确认输入路径是否正确！");
            }

            path = txtVideoSavePath.Text;
            if(string.IsNullOrEmpty(path))
            {
                throw new Exception("视频文件保存路径不能为空！");
            }
            // 获取绝对路径
            path = FileExtension.GetAbsolutePath(path);
            if(!Directory.Exists(path))
            {
                throw new Exception("视频文件保存路径不存在，请确认输入路径是否正确！");
            }

            if(ckbDs.Checked && !uint.TryParse(txtDsPort.Text, out uint _))
            {
                throw new Exception("刻录机通道请输入整数！");
            }
            // 新增设备，校验ID是否重复
            if(!_editModel && NETCameraManager.GetInstance().GetCameraById(txtID.Text) != null)
            {
                throw new Exception("此摄像机ID已存在，请选择其他ID新建测量摄像机！");
            }
        }

        #endregion
    }
}