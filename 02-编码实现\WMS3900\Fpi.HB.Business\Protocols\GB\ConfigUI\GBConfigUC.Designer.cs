﻿namespace Fpi.HB.Business.Protocols.GB.ConfigUI
{
    partial class GBConfigUC
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbBase = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txtMN = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cmbST = new System.Windows.Forms.ComboBox();
            this.nuReCount = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.nuWarnTime = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.nuOverTime = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.txtServerAddress = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPwd = new System.Windows.Forms.TextBox();
            this.rdInitiative = new System.Windows.Forms.RadioButton();
            this.label13 = new System.Windows.Forms.Label();
            this.rdPassive = new System.Windows.Forms.RadioButton();
            this.chkNeedQN = new System.Windows.Forms.CheckBox();
            this.label11 = new System.Windows.Forms.Label();
            this.nuCmdInterval = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chkNeedFlag = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.gbReport = new System.Windows.Forms.GroupBox();
            this.chkState = new System.Windows.Forms.CheckBox();
            this.dtpDay = new System.Windows.Forms.DateTimePicker();
            this.chkMinute = new System.Windows.Forms.CheckBox();
            this.cmbSteteSpan = new System.Windows.Forms.ComboBox();
            this.cmbSpan = new System.Windows.Forms.ComboBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.chkReportAlarm = new System.Windows.Forms.CheckBox();
            this.chkDay = new System.Windows.Forms.CheckBox();
            this.chkHour = new System.Windows.Forms.CheckBox();
            this.btnSet = new System.Windows.Forms.Button();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkFromDb = new System.Windows.Forms.CheckBox();
            this.gbBase.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuReCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuWarnTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuOverTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuCmdInterval)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.gbReport.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbBase
            // 
            this.gbBase.Controls.Add(this.label1);
            this.gbBase.Controls.Add(this.txtMN);
            this.gbBase.Controls.Add(this.label10);
            this.gbBase.Controls.Add(this.label2);
            this.gbBase.Controls.Add(this.cmbST);
            this.gbBase.Controls.Add(this.nuReCount);
            this.gbBase.Controls.Add(this.label3);
            this.gbBase.Controls.Add(this.nuWarnTime);
            this.gbBase.Controls.Add(this.label9);
            this.gbBase.Controls.Add(this.nuOverTime);
            this.gbBase.Controls.Add(this.label6);
            this.gbBase.Controls.Add(this.txtServerAddress);
            this.gbBase.Controls.Add(this.label5);
            this.gbBase.Controls.Add(this.txtPwd);
            this.gbBase.Location = new System.Drawing.Point(6, 11);
            this.gbBase.Name = "gbBase";
            this.gbBase.Size = new System.Drawing.Size(251, 210);
            this.gbBase.TabIndex = 15;
            this.gbBase.TabStop = false;
            this.gbBase.Text = "基本";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(11, 48);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "系统编码";
            // 
            // txtMN
            // 
            this.txtMN.Location = new System.Drawing.Point(95, 18);
            this.txtMN.MaxLength = 16;
            this.txtMN.Name = "txtMN";
            this.txtMN.Size = new System.Drawing.Size(135, 21);
            this.txtMN.TabIndex = 2;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(11, 21);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(77, 12);
            this.label10.TabIndex = 0;
            this.label10.Text = "唯一标识MN号";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(11, 74);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "访问密码";
            // 
            // cmbST
            // 
            this.cmbST.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbST.FormattingEnabled = true;
            this.cmbST.Items.AddRange(new object[] {
            "20",
            "21",
            "22",
            "23",
            "31",
            "32",
            "33",
            "34",
            "35",
            "36",
            "37",
            "38",
            "41",
            "91"});
            this.cmbST.Location = new System.Drawing.Point(95, 45);
            this.cmbST.Name = "cmbST";
            this.cmbST.Size = new System.Drawing.Size(135, 20);
            this.cmbST.TabIndex = 1;
            // 
            // nuReCount
            // 
            this.nuReCount.Location = new System.Drawing.Point(95, 125);
            this.nuReCount.Maximum = new decimal(new int[] {
            99,
            0,
            0,
            0});
            this.nuReCount.Name = "nuReCount";
            this.nuReCount.Size = new System.Drawing.Size(135, 21);
            this.nuReCount.TabIndex = 3;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(11, 101);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "上位机地址";
            // 
            // nuWarnTime
            // 
            this.nuWarnTime.Location = new System.Drawing.Point(95, 179);
            this.nuWarnTime.Maximum = new decimal(new int[] {
            99999,
            0,
            0,
            0});
            this.nuWarnTime.Name = "nuWarnTime";
            this.nuWarnTime.Size = new System.Drawing.Size(135, 21);
            this.nuWarnTime.TabIndex = 3;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(11, 154);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(53, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "超时时间";
            // 
            // nuOverTime
            // 
            this.nuOverTime.Location = new System.Drawing.Point(95, 152);
            this.nuOverTime.Maximum = new decimal(new int[] {
            99999,
            0,
            0,
            0});
            this.nuOverTime.Name = "nuOverTime";
            this.nuOverTime.Size = new System.Drawing.Size(135, 21);
            this.nuOverTime.TabIndex = 3;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(11, 181);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "超限报警时间";
            // 
            // txtServerAddress
            // 
            this.txtServerAddress.Location = new System.Drawing.Point(95, 98);
            this.txtServerAddress.Name = "txtServerAddress";
            this.txtServerAddress.Size = new System.Drawing.Size(135, 21);
            this.txtServerAddress.TabIndex = 2;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(11, 127);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "重发次数";
            // 
            // txtPwd
            // 
            this.txtPwd.Location = new System.Drawing.Point(95, 71);
            this.txtPwd.Name = "txtPwd";
            this.txtPwd.PasswordChar = '*';
            this.txtPwd.Size = new System.Drawing.Size(135, 21);
            this.txtPwd.TabIndex = 2;
            // 
            // rdInitiative
            // 
            this.rdInitiative.AutoSize = true;
            this.rdInitiative.Location = new System.Drawing.Point(18, 84);
            this.rdInitiative.Name = "rdInitiative";
            this.rdInitiative.Size = new System.Drawing.Size(203, 16);
            this.rdInitiative.TabIndex = 0;
            this.rdInitiative.Text = "主动模式(定时扫描缺失项并上报)";
            this.rdInitiative.UseVisualStyleBackColor = true;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(35, 107);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(161, 12);
            this.label13.TabIndex = 5;
            this.label13.Text = "要求上报指令含请求编号(QN)";
            // 
            // rdPassive
            // 
            this.rdPassive.AutoSize = true;
            this.rdPassive.Location = new System.Drawing.Point(18, 30);
            this.rdPassive.Name = "rdPassive";
            this.rdPassive.Size = new System.Drawing.Size(107, 16);
            this.rdPassive.TabIndex = 0;
            this.rdPassive.Text = "被动模式(默认)";
            this.rdPassive.UseVisualStyleBackColor = true;
            // 
            // chkNeedQN
            // 
            this.chkNeedQN.AutoSize = true;
            this.chkNeedQN.Location = new System.Drawing.Point(21, 101);
            this.chkNeedQN.Name = "chkNeedQN";
            this.chkNeedQN.Size = new System.Drawing.Size(204, 16);
            this.chkNeedQN.TabIndex = 4;
            this.chkNeedQN.Text = "上报统计值指令需要请求编号(QN)";
            this.chkNeedQN.UseVisualStyleBackColor = true;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(19, 34);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(89, 12);
            this.label11.TabIndex = 0;
            this.label11.Text = "指令间隔(毫秒)";
            // 
            // nuCmdInterval
            // 
            this.nuCmdInterval.Location = new System.Drawing.Point(114, 30);
            this.nuCmdInterval.Maximum = new decimal(new int[] {
            20000,
            0,
            0,
            0});
            this.nuCmdInterval.Minimum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nuCmdInterval.Name = "nuCmdInterval";
            this.nuCmdInterval.Size = new System.Drawing.Size(73, 21);
            this.nuCmdInterval.TabIndex = 3;
            this.nuCmdInterval.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(35, 53);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(161, 12);
            this.label12.TabIndex = 5;
            this.label12.Text = "即收到服务端指令后开始补缺";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rdInitiative);
            this.groupBox1.Controls.Add(this.rdPassive);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.label12);
            this.groupBox1.Location = new System.Drawing.Point(6, 227);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(251, 140);
            this.groupBox1.TabIndex = 17;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "数据补缺模式";
            // 
            // chkNeedFlag
            // 
            this.chkNeedFlag.AutoSize = true;
            this.chkNeedFlag.Location = new System.Drawing.Point(21, 69);
            this.chkNeedFlag.Name = "chkNeedFlag";
            this.chkNeedFlag.Size = new System.Drawing.Size(204, 16);
            this.chkNeedFlag.TabIndex = 4;
            this.chkNeedFlag.Text = "实时数据指令需要因子状态(Flag)";
            this.chkNeedFlag.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkNeedFlag);
            this.groupBox2.Controls.Add(this.chkNeedQN);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.nuCmdInterval);
            this.groupBox2.Location = new System.Drawing.Point(263, 227);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(258, 140);
            this.groupBox2.TabIndex = 18;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "协议选项";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(101, 39);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "分钟间隔";
            // 
            // gbReport
            // 
            this.gbReport.Controls.Add(this.chkState);
            this.gbReport.Controls.Add(this.dtpDay);
            this.gbReport.Controls.Add(this.chkMinute);
            this.gbReport.Controls.Add(this.cmbSteteSpan);
            this.gbReport.Controls.Add(this.cmbSpan);
            this.gbReport.Controls.Add(this.label7);
            this.gbReport.Controls.Add(this.label8);
            this.gbReport.Controls.Add(this.label14);
            this.gbReport.Controls.Add(this.label4);
            this.gbReport.Controls.Add(this.chkReportAlarm);
            this.gbReport.Controls.Add(this.chkDay);
            this.gbReport.Controls.Add(this.chkHour);
            this.gbReport.Location = new System.Drawing.Point(263, 11);
            this.gbReport.Name = "gbReport";
            this.gbReport.Size = new System.Drawing.Size(258, 210);
            this.gbReport.TabIndex = 13;
            this.gbReport.TabStop = false;
            this.gbReport.Text = "主动上报";
            // 
            // chkState
            // 
            this.chkState.AutoSize = true;
            this.chkState.Location = new System.Drawing.Point(21, 143);
            this.chkState.Name = "chkState";
            this.chkState.Size = new System.Drawing.Size(156, 16);
            this.chkState.TabIndex = 7;
            this.chkState.Text = "上报仪器状态参数及报警";
            this.chkState.UseVisualStyleBackColor = true;
            // 
            // dtpDay
            // 
            this.dtpDay.CustomFormat = "HH:mm";
            this.dtpDay.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpDay.Location = new System.Drawing.Point(160, 116);
            this.dtpDay.Name = "dtpDay";
            this.dtpDay.ShowUpDown = true;
            this.dtpDay.Size = new System.Drawing.Size(73, 21);
            this.dtpDay.TabIndex = 6;
            this.dtpDay.Value = new System.DateTime(2008, 10, 27, 0, 0, 0, 0);
            // 
            // chkMinute
            // 
            this.chkMinute.AutoSize = true;
            this.chkMinute.Location = new System.Drawing.Point(21, 20);
            this.chkMinute.Name = "chkMinute";
            this.chkMinute.Size = new System.Drawing.Size(108, 16);
            this.chkMinute.TabIndex = 4;
            this.chkMinute.Text = "上报分钟统计值";
            this.chkMinute.UseVisualStyleBackColor = true;
            // 
            // cmbSteteSpan
            // 
            this.cmbSteteSpan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbSteteSpan.FormattingEnabled = true;
            this.cmbSteteSpan.Items.AddRange(new object[] {
            "5",
            "10",
            "20",
            "30"});
            this.cmbSteteSpan.Location = new System.Drawing.Point(160, 160);
            this.cmbSteteSpan.Name = "cmbSteteSpan";
            this.cmbSteteSpan.Size = new System.Drawing.Size(73, 20);
            this.cmbSteteSpan.TabIndex = 1;
            // 
            // cmbSpan
            // 
            this.cmbSpan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbSpan.FormattingEnabled = true;
            this.cmbSpan.Items.AddRange(new object[] {
            "1",
            "2",
            "5",
            "10",
            "20",
            "30"});
            this.cmbSpan.Location = new System.Drawing.Point(160, 35);
            this.cmbSpan.Name = "cmbSpan";
            this.cmbSpan.Size = new System.Drawing.Size(73, 20);
            this.cmbSpan.TabIndex = 1;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(101, 80);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(113, 12);
            this.label7.TabIndex = 5;
            this.label7.Text = "整点上报小时统计值";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(101, 121);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(53, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "上报时刻";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(101, 164);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(53, 12);
            this.label14.TabIndex = 0;
            this.label14.Text = "分钟间隔";
            // 
            // chkReportAlarm
            // 
            this.chkReportAlarm.AutoSize = true;
            this.chkReportAlarm.Location = new System.Drawing.Point(21, 184);
            this.chkReportAlarm.Name = "chkReportAlarm";
            this.chkReportAlarm.Size = new System.Drawing.Size(120, 16);
            this.chkReportAlarm.TabIndex = 4;
            this.chkReportAlarm.Text = "上报实时超标报警";
            this.chkReportAlarm.UseVisualStyleBackColor = true;
            // 
            // chkDay
            // 
            this.chkDay.AutoSize = true;
            this.chkDay.Location = new System.Drawing.Point(21, 102);
            this.chkDay.Name = "chkDay";
            this.chkDay.Size = new System.Drawing.Size(96, 16);
            this.chkDay.TabIndex = 4;
            this.chkDay.Text = "上报日统计值";
            this.chkDay.UseVisualStyleBackColor = true;
            // 
            // chkHour
            // 
            this.chkHour.AutoSize = true;
            this.chkHour.Location = new System.Drawing.Point(21, 61);
            this.chkHour.Name = "chkHour";
            this.chkHour.Size = new System.Drawing.Size(108, 16);
            this.chkHour.TabIndex = 4;
            this.chkHour.Text = "上报小时统计值";
            this.chkHour.UseVisualStyleBackColor = true;
            // 
            // btnSet
            // 
            this.btnSet.Location = new System.Drawing.Point(95, 14);
            this.btnSet.Name = "btnSet";
            this.btnSet.Size = new System.Drawing.Size(103, 23);
            this.btnSet.TabIndex = 3;
            this.btnSet.Text = "输出因子配置";
            this.btnSet.UseVisualStyleBackColor = true;
            this.btnSet.Click += new System.EventHandler(this.btnSet_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkFromDb);
            this.groupBox3.Controls.Add(this.btnSet);
            this.groupBox3.Location = new System.Drawing.Point(6, 373);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(515, 44);
            this.groupBox3.TabIndex = 19;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "输出选项";
            // 
            // chkFromDb
            // 
            this.chkFromDb.AutoSize = true;
            this.chkFromDb.Location = new System.Drawing.Point(278, 18);
            this.chkFromDb.Name = "chkFromDb";
            this.chkFromDb.Size = new System.Drawing.Size(144, 16);
            this.chkFromDb.TabIndex = 5;
            this.chkFromDb.Text = "实时数据从数据库获取";
            this.chkFromDb.UseVisualStyleBackColor = true;
            // 
            // GBConfigUC
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(528, 419);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.gbBase);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.gbReport);
            this.Name = "GBConfigUC";
            this.gbBase.ResumeLayout(false);
            this.gbBase.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuReCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuWarnTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuOverTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuCmdInterval)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.gbReport.ResumeLayout(false);
            this.gbReport.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox gbBase;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cmbST;
        private System.Windows.Forms.NumericUpDown nuReCount;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown nuWarnTime;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown nuOverTime;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox txtServerAddress;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPwd;
        private System.Windows.Forms.RadioButton rdInitiative;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.RadioButton rdPassive;
        private System.Windows.Forms.CheckBox chkNeedQN;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown nuCmdInterval;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox chkNeedFlag;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.GroupBox gbReport;
        private System.Windows.Forms.DateTimePicker dtpDay;
        private System.Windows.Forms.CheckBox chkMinute;
        private System.Windows.Forms.ComboBox cmbSpan;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.CheckBox chkReportAlarm;
        private System.Windows.Forms.CheckBox chkDay;
        private System.Windows.Forms.CheckBox chkHour;
        private System.Windows.Forms.TextBox txtMN;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Button btnSet;
        private System.Windows.Forms.CheckBox chkState;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox chkFromDb;
        private System.Windows.Forms.ComboBox cmbSteteSpan;
        private System.Windows.Forms.Label label14;
    }
}
