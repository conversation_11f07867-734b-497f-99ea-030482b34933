using System;
using System.Drawing;
using System.Windows.Forms;
using Fpi.Communication.Commands.Config;
using Fpi.Util.Reflection;

namespace Fpi.Communication.UI.PC.CommandForms
{
    public partial class CommandPanel : Form
    {
        public CommandPanel()
        {
            InitializeComponent();
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
        private string[] commandIds = null;

        public CommandPanel(string[] cmdIds)
            : this()
        {
            commandIds = new string[cmdIds.Length];
            for(int i = 0; i < cmdIds.Length; i++)
            {
                if(cmdIds[i].LastIndexOf(',') >= 0)
                {
                    commandManager = cmdIds[i].Substring(0, cmdIds[i].LastIndexOf(','));
                }
                commandIds[i] = cmdIds[i].Substring(cmdIds[i].LastIndexOf(',') + 1);
            }
        }

        private string commandManager = "";

        public CommandPanel(string cmdId)
            : this()
        {
            if(cmdId.LastIndexOf(',') >= 0)
            {
                commandManager = cmdId.Substring(0, cmdId.LastIndexOf(','));
            }
            commandIds = new string[1];
            commandIds[0] = cmdId.Substring(cmdId.LastIndexOf(',') + 1);
        }

        private void CommandPanel_Load(object sender, EventArgs e)
        {
            OneCommandPanel commandPanel = null;
            CommandManager managerInstance = this.commandManager.Trim() != ""
                ? (CommandManager)ReflectionHelper.CreateInstance(this.commandManager)
                : CommandManager.GetInstance();
            if(commandIds.Length == 1)
            {
                CommandDesc commandDesc = (CommandDesc)managerInstance.commandDescs[commandIds[0]];
                this.Text = commandDesc.name;
                commandPanel = new OneCommandPanel();
                commandPanel.CommandId = commandIds[0];

                if(((commandDesc.commandExtends.FindNode("0x55") != null) && (commandDesc.commandExtends.FindNode("0x66") != null))
                    || (commandDesc.commandExtends.FindNode("0xff") != null))
                {
                    commandPanel.ExtendId = ExtendType.ReadWrite;
                }
                else if(commandDesc.commandExtends.FindNode("0x55") != null)
                {
                    commandPanel.ExtendId = ExtendType.Read;

                }
                else if(commandDesc.commandExtends.FindNode("0x66") != null)
                {
                    commandPanel.ExtendId = ExtendType.Write;

                }
                if(this.commandManager.Trim() != "")
                {
                    commandPanel.CommandManager = commandManager;
                }
                commandPanel.Location = new Point(0, 0);
                commandPanel.Dock = DockStyle.Fill;
                this.Controls.Add(commandPanel);
                this.ClientSize = new Size(commandPanel.GetNeededWidth(), commandPanel.GetNeededHeight());
                commandPanel.Init();

            }
            else
            {
                int posY = 5;
                int width = 0;
                int height = 0;
                for(int i = 0; i < commandIds.Length; i++)
                {
                    GroupBox groupBox = new GroupBox();
                    commandPanel = new OneCommandPanel();
                    commandPanel.CommandId = commandIds[i];

                    CommandDesc commandDesc = (CommandDesc)managerInstance.commandDescs[commandIds[0]];
                    if(((commandDesc.commandExtends.FindNode("0x55") != null) && (commandDesc.commandExtends.FindNode("0x66") != null))
                        || (commandDesc.commandExtends.FindNode("0xff") != null))
                    {
                        commandPanel.ExtendId = ExtendType.ReadWrite;
                    }
                    else if(commandDesc.commandExtends.FindNode("0x55") != null)
                    {
                        commandPanel.ExtendId = ExtendType.Read;

                    }
                    else if(commandDesc.commandExtends.FindNode("0x66") != null)
                    {
                        commandPanel.ExtendId = ExtendType.Write;

                    }
                    if(this.commandManager.Trim() != "")
                    {
                        commandPanel.CommandManager = commandManager;
                    }
                    commandPanel.Location = new Point(2, 12);
                    commandPanel.Dock = DockStyle.Fill;
                    groupBox.Height = commandPanel.GetNeededHeight() + 20;
                    groupBox.Location = new Point(5, posY);
                    groupBox.Text = managerInstance.commandDescs[commandIds[i]].name;
                    groupBox.Controls.Add(commandPanel);
                    this.Controls.Add(groupBox);
                    int temp = commandPanel.GetNeededWidth();
                    if(temp > width)
                    {
                        width = temp;
                    }

                    posY += groupBox.Height + 5;
                    height += groupBox.Height + 5;
                }
                this.ClientSize = new Size(width + 5, height + 10);
                foreach(Control ctrl in this.Controls)
                {
                    if(ctrl is GroupBox)
                    {
                        ctrl.Width = width;
                        (ctrl.Controls[0] as OneCommandPanel).Init();
                    }
                }

            }
        }
    }
}