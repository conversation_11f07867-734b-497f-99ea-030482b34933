﻿//==================================================================================================
//类名：     BaseServer   
//创建人:    hongbing_mao
//创建时间:  2013-1-11 11:34:55
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;
using System.Reflection;
using Fpi.Util.Interfaces.Initialize;

namespace Fpi.Data.FuncServer
{
    /// <summary>
    /// 服务基类
    /// </summary>
    public abstract class BaseServer : IInitialization
    {
        /// <summary>
        /// 构造
        /// </summary>
        public BaseServer()
        {
            MethodInfo[] methods = this.GetType().GetMethods(BindingFlags.Static | BindingFlags.Public);
            foreach(MethodInfo method in methods)
            {
                object[] arrtibutes = method.GetCustomAttributes(typeof(ServiceAttribute), true);
                if(arrtibutes != null && arrtibutes.Length > 0)
                {
                    ServiceAttribute attr = arrtibutes[0] as ServiceAttribute;
                    Service srv = new Service();
                    srv.SrvName = method.Name;
                    srv.SrvFriendlyName = attr.FriendlyName;
                    srv.SrvDescription = attr.Description;
                    srv.ParamDescription = attr.ParamDescription;

                    ParameterInfo[] parameters = method.GetParameters();
                    if(parameters != null && parameters.Length > 0)
                    {
                        Type[] paramsType = new Type[parameters.Length];
                        for(int i = 0; i < parameters.Length; i++)
                        {
                            paramsType[i] = parameters[i].ParameterType;
                        }
                        srv.ParamType = paramsType;
                    }
                    srv.SupportType = this.GetType();
                    ServiceManager.GetInstance().RegisterService(srv);
                }
            }
        }

        #region IInitialization 成员

        public void Initialize()
        {

        }

        #endregion
    }
}
