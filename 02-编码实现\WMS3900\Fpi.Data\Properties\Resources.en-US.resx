﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="EnableInputProcessor" xml:space="preserve">
    <value>It has already started inputting processor ， but hasn't selected the concretion  realize</value>
  </data>
  <data name="SetFormula" xml:space="preserve">
    <value>Set the formulae and unable to set the value:{0}</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Node_Del" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAVFJREFUOE+tkjFO
        w0AQRS1RQLfbEdHsCiHF3WyHRTPbJdBMjpAj5Ag+go+QMqWPkCO4TYHkI/gGyx9DIuIEbCQsjWLt/P/m
        Z7w3GR55k5A/5rv8KW8P74dWz/708ILXMcbEzJ28yuo3s4h4XvLmTAPjGlWHIiYFyULKIcQRWWiiLKXD
        73YI8NpUiAJ6iEiNsiokZstL6XtUUEWA/ZgSkM03yJZfOHDBVW8mqjTJ6H4ACahG/xJMnZbLqZ5kPtJh
        iiGEFD8hrXMTJp/Mz+QxsVFAXwqZ07TPi2m92echEWKbBxecc7WCcH7++YbLwBQPceO919h7vFtjTF94
        1+13DpqrSyRiFW3dzOnSWlQ4Co8QnDUKugpAY2VnPml0TCnVdHGZNOHctehdpjD3prTWJoD25vaK4ItG
        uBMAnNKdhsAYsrtM48XMZGOXZaw/etf+T/ABASBzBnL8dsYAAAAASUVORK5CYII=
</value>
  </data>
  <data name="NotFindIdGroup" xml:space="preserve">
    <value>It can't find the DataGroup number of {0}</value>
  </data>
  <data name="Group_Del" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAYxJREFUOE+lkS9T
        w0AQxe8bEIlLZAfDSRwncT2JPAeOOHDEgWtk5LnWEQnuZB2RlZF13Dd4vL2kNNPpUGbIzJv82/fbt3tK
        /ff6/HjQ7arGod6WC/MnthjjtgXioCjqCfQ13pbP1UmIFB6T53ffiKqjWrw8uAQXM2I40Jho64Et/zMR
        NhXQlYhrKjhUpUMCSKcBsB8BcWLsR2PnWGYR3y26ppgAGHEKwLRr44CKYse4pmjuVxqhzlA6OyYYAbK8
        oTMlkaWzAJ5sUmxFZgC8ZvAE4HEelK8lwWTmnXnDWTkzK/eQhgBfIAqwJOBubhIgHeNPdAJHc4otc/sh
        xY/uDeyVHkaoX3eAMfah+d0gMnZsNEAjnEEoC+hZsQMwau9T3F6W1Oo0Z8eogdsONVUVyQhLiOEz77rI
        9oDqqUTJc3VcjLUGhvGkQ8EiKdwZ61kGx/c4U4i3ZgCcunCTR2hCaML1WSb1yFUVzgnJVf2rH/M8E2Nk
        547JpsV9rkKbqdMpcKlsL2NcKH3Y7StX9hsQ8AGiqZ2NAAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="NotValueNode" xml:space="preserve">
    <value>{0} isn't numerical value type variable</value>
  </data>
  <data name="Remove" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAd1JREFUOE+FkyFz
        20AQhfUPbBZosZRJ0MzHaiaxhlmsZhJriyQWsxia9WBYjsWsy2rWZTVcVrMcLNLr3jl2HKd1b+bNzUj7
        vn2zKyXJhYMvs7JvZ/x0W6fnZfhYWGSD6pI/waeZxecK/W0tT22dH4qjeZoD1wldBISXqIsS8xJ9XQg+
        TNJ+XjiYFH6U2P+aefOVRBjeLYEb7VhkHuNodv80O2IE2WUFv7OAZ/KeIV0D5EN4BfyYZG9mEoHBeDjO
        tl6N7H+xx6JCjN1UEIVwgFwP3kJOAbwhUUUTxtr5xkCJ8HYJThUyGsjD6Azi1i8J+r4XH2JrNyjE7zzA
        RJ4cqG2F3g3hrhJ5NYszAOO3mpYdwt1/J+ZpCvnJbB3Jt3HWPFydJbCPvI/ABDxazetJzYQNEesWOMTf
        etTtHR87d/cdmlUFd7+E0wK76nQDHXjRQLQj5gZS5iD9oES8bkhgygpHgNtalJUBdu5ZFl6eIQolMwQp
        iDUc6UICIJ+eAJp1CaMT3gP25kaBQmVMQ9bCrWw0/xVQOQVMDUzY71qnvW3iHSAHZe9n1uqGohy/ThAA
        uckxyQYmQOxi311cGFoOt0gjPMQ+aFLMmuMMgjnN0ziUAFG5YDiVPnsxnP0EfwBtn9Xlb9kSygAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="Search" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAVtJREFUOE/Nki12
        g0AQx3MDuEFzA3DFBYkLrnHgWgcStytxu7IOZBx7g6ysREaurBxZN51ZAg0vqcmr6Ly3gpnhN//52Gz+
        0lRvXdUaiF6Uu+aqztp7/pvau4My8IUYRNUK8Jt/BeAqUhvM33qMc41RJoAT9NHiPf9NdfluEam6sQ5r
        bXGbSeSkxf/hUPbj4l8BhBpCTizlj4L0QCpoFmVjsG7JX5MygjK4EEO4AkiqCADI/dszeBAnBokYa2qL
        zcdGwJyAszoPUd1JcgU2OzrUJJOrhanESg2SAePZ0YMpxoBkinmA7i14iZfhzTJDSgqehVti1FKcadwS
        OExqjOnbA/yQyNwnoJ0HRUnXPXJLHHcOpiG31s9jAnCP3B/9zBJLSRv4VwBe2dyCV3m8DHJuoWj6sWx6
        TEuNvHt+TzsB9JZdF8LA/lU53g7H+VLp3P2lPmz7qlsf06Okb4/oi1FL11wVAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Compute" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAaBJREFUOE+NkzFS
        6zAYhE1L83wE3yCm43Uu6VCZoUElHX/pLirdodIdKtOhMh0qKXUE3+AlJ9i3v5wQGAJBMzuWJ9nv31WU
        CxcmONtcVL9cr6tHs9vtXvTrfy5xVbmRAEJun/415xgvz6suisXkBbm30H0lBIQEiP8ZouYUHeImIw9S
        zP66JYDG8Ab4DWCG05DZLNhOHtObwA2umFd/F6ayNB0ALhLS5091Suy1LeacGHs0qOsa93eLtlS2bgZ0
        kiFrVglHSDEH88X8eL/o3s/L9PMZtDbBEGIGwI6USzhrVopG1v7NTYC+axLjtgghIG3YOfPA9rE5eY79
        canBs3t97dF0lIkljQJ0RT93Pmk+THTam1O7npIJLSUiMEujqVgt6cHWJ++JJnjXQy7T22VkklBq1UxV
        xITfXrQcGqiiPz6Tvg8N0tgguJr7GqGnuPdCce+lmqFqRuYtI+D45OERAP72asLGFADWXQFsQwtr9gCd
        psajBOCNU3MRzYgUzWosGluY7gBgTKW6h71sBVEtZ+mkoptZajzo3B/w7Of/AQjntixwdCRCAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="SystemPrompt" xml:space="preserve">
    <value>System prompt</value>
  </data>
  <data name="TwoDataCompute" xml:space="preserve">
    <value>It need two sets data at least to proceed fitting calculation</value>
  </data>
  <data name="Add" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAZhJREFUOE+dk6Fy
        4zAQhq9PcH2EvEH1BhFrWAXLLFgWwbAamsXQrAvDsqxmFQyrYFkFyyJY9ndXqae9O3furjvzj2ds/d+/
        u7bPfnyz7u6PPh6y+Za92T73bpOw3T2G/wY020xuE3FLKe0fnr/u4O4BjpkQuQVTC01aV3PCehvT/vE4
        b+aYoApDRqCCeIjIqUfoCL5L8BvG/nB0s62rcaq2j7BBQQUtxWp2YsYr0Kwp/RWgoHZQyCQGjxKw6xGd
        rSP9UfXAVJKk1Uu69YR+J89kF+naYL1c8j8DFBJltNKLebVAjoyrm9v5Eeg+nVpIERgJeMlAKSjDKTkN
        0n7KMCv/MUK7a2VRHiyzMfUgOUya1gVkScSNRXYGceORcwGNvwH4ieC8lTTZcBWh5HeILsyeIwooSXPx
        MAMIo4O9ngAncxBgjq52E4nAg3xQSb+JGYBnAawsrDlHGj3KU6hXhUy6uGyI5A1Vcfp1Bwow1mB58dMq
        hLpTemZZGhlwt6hwXdyk5VXz8ROpeWEWdasKEbEaPkvuffnXvQFAzLhB0lSrDgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Group" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAUNJREFUOE+9ky1y
        wzAUhH0EH0Gw0LCsukF1BN0ggmEVS1gEA8USFsOUGZbVMNAwUDfY7vNflNYtCKhndjzjefvt6mlcFP/9
        aOMw6aHsyfzyurI94PN9VdXHgFynw07/RhcAzWZ860KM6VoDqUYSdYTFgNPhzf9VkZBKIIUMLynye9yL
        /A/tNkP9GYDUsEGuoRGukQoAW+HigdZxzMI7i7mdJA3m2zGQMmM3GlvLMYN2r74BWDEH3KWK+cJUMX9Q
        Z4MmlHDWZA1GgCxwSKak8oI51RrNtoQ1OSBIg+zMd2amSzKrizkdK9S+hNE6a0BAf43zwqbamfk8mLs4
        APRzdQOE7QTIay+YBcAFxrVC9aRyAIe7yEU5dFxSV3OQw21UaGhogmLqpBLBKShV3gP82sHxbi23a4zu
        K0qKDKpyWQ/9SEumLx5Fzg0wwF1pAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Make sure to delect: {0} ?</value>
  </data>
  <data name="UnknowAlarmId" xml:space="preserve">
    <value>Unknown alarm limit number : {0}</value>
  </data>
  <data name="ExistId" xml:space="preserve">
    <value>Already exist this number : {0} !</value>
  </data>
  <data name="UnknowUnitId" xml:space="preserve">
    <value>Unknown unit number : {0}</value>
  </data>
  <data name="ConfigSave" xml:space="preserve">
    <value>The configuration saved， whether effect immediately ?</value>
  </data>
  <data name="NotConfigUnit" xml:space="preserve">
    <value>Not configured unit transition formulae</value>
  </data>
  <data name="EnableOutputProcessor" xml:space="preserve">
    <value>It has already started outputting processor ， but hasn't selected the concretion  realize</value>
  </data>
  <data name="UnknowScopeId" xml:space="preserve">
    <value>Unknown Range number : {0}</value>
  </data>
  <data name="Edit" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAASVJREFUOE+Vkq1y
        wzAQhJU36SP4EfoIfgTDQMNAwULDQMFCw0LDQpUFChYalm13lTlXlpXJVDPfOP65vb29nNw/D3oHlXwl
        57roTm76SNgx8574dxHhA7mSaQFCALxH7Fwm99bH9Vl/ACOtgLBiDD3Qv2DqX48CuWODh8XmoOxY/24V
        j2/zvbuOOlpR3b1VHG8JewGGVAp0HSCwxhxYOXP8BpZIAV86oIAFJQfbkYBg8gpM7xbmfRTgimqBcA15
        TVYs0U3gkw4upQPuN9Ga2DmodmsCM22Ml/AX4kgBzSYerdGea4SjAFdiAvmqORWWriU3Fgs+HMbSARO1
        D585eCpQd80FNXJwbjjQH8TQqjJMXJZr9gJMVKk24ayad4OdVTycp20Lv0oXpblEMTJFAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="Node" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAASdJREFUOE+Vkc1x
        gzAQhV2CS3AJdIA6QEffxC2+hZt9M7d4coES1EHoIHRgOjAdRBVE2Y+siSYTT2Bn3mi1+95b/Ww2f4eR
        cvegt7jcL2YKMRMwtUpEGLD3gq3Wrdbg3mtTCxKBgCYxKolaK8gEta7U2M+xlawUdIlBn/TJEdlUlOYI
        CUhG89TA6wD6DNspZo9RhRhBIgatlbJmKoTXCmrdzwZ3R9wBwWp+E9VsFq5KXBMGW4/RlD6uEkJ2TfSu
        DcFWfTT7dp2Baz4rxMXl1ppDF7PVBhcRH691/vy+M3u/zsC9hDE/dIFjI/7GwivIo/ni1If86Y1fmmJ6
        A7nGv4/oXj8yexqiiPnKKYrzTWpiUHaRKz00Kc7XiikcNbM/x+U090dkxfwLbG58NZ8pt+QAAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="IdNameEmpty" xml:space="preserve">
    <value>The number or name shouldn't be blank</value>
  </data>
  <data name="ExistGroupVarNode" xml:space="preserve">
    <value>The node id is not unique,please modify and save</value>
  </data>
</root>