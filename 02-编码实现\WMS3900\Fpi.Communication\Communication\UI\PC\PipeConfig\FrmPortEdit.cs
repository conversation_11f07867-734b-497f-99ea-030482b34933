using System;
using System.Drawing;
using System.Windows.Forms;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Manager;
using Fpi.Communication.Properties;
using Fpi.Communication.Protocols;
using Fpi.Communication.Protocols.Interfaces;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Reflection;
using Fpi.Xml;

namespace Fpi.Communication.UI.PC.PipeConfig
{
    public partial class FrmPortEdit : Form
    {
        private FrmPortEdit()
        {
            InitializeComponent();
        }
        public FrmPortEdit(Pipe pipe, Protocol protocol)
            : this()
        {
            this.protocol = protocol;
            this.pipe = pipe;
        }

        private Pipe pipe;
        private Protocol protocol;

        private void FormPortEdit_Load(object sender, EventArgs e)
        {
            BuildPortList();
            BuildCustom();
        }

        private void BuildPortList()
        {
            IPort[] ports = protocol.Parser.Ports;
            foreach(IPort port in ports)
            {
                this.lsbPorts.Items.Add(new PortItem(pipe, protocol, port));
            }
            if(protocol.Receiver != null)
            {
                this.lsbPorts.Items.Add(new PortItem(pipe, protocol, protocol.Receiver));
            }

            if(this.lsbPorts.Items.Count > 0)
            {
                this.lsbPorts.SelectedIndex = 0;
            }
        }

        private void BuildCustom()
        {
            if(protocol.Sender == null || !protocol.Sender.CanExtended)
            {
                this.gbSender.Enabled = false;
            }

            if(protocol.Receiver == null || !protocol.Receiver.CanExtended)
            {
                this.gbReceiver.Enabled = false;
            }

            if(this.gbSender.Enabled)
            {
                this.txtImpSender.Text = pipe.GetCustomSender(protocol.GetType().FullName);
            }

            if(this.gbReceiver.Enabled)
            {
                this.txtImpReceiver.Text = pipe.GetCustomReceiver(protocol.GetType().FullName);
            }
        }

        private void lsbPorts_SelectedIndexChanged(object sender, EventArgs e)
        {
            PortItem pi = this.lsbPorts.SelectedItem as PortItem;
            this.pnlPorts.Controls.Clear();
            if(pi.View != null)
            {
                this.pnlPorts.Controls.Add(pi.View);
            }
            else
            {
                this.pnlPorts.Controls.Add(CreateLabel());
            }
        }

        private Label CreateLabel()
        {
            Label lbl = new Label();
            lbl.Text = Resources.TheItemNotNeedConfig;
            lbl.Location = new Point(30, 30);
            return lbl;
        }

        private void btnSender_Click(object sender, EventArgs e)
        {
            string typeName = this.txtImpSender.Text;
            ReflectionExplore form = new ReflectionExplore(typeof(ISender));
            form.SelectedType = ReflectionHelper.FindType(typeName);

            if(form.ShowDialog() == DialogResult.OK)
            {
                this.txtImpSender.Text = (form.SelectedType != null ? form.SelectedType.FullName : null);
            }
        }

        private void btnReceiver_Click(object sender, EventArgs e)
        {
            string typeName = this.txtImpReceiver.Text;
            ReflectionExplore form = new ReflectionExplore(typeof(IPortOwner));
            form.SelectedType = ReflectionHelper.FindType(pipe.GetCustomReceiver(typeName));

            if(form.ShowDialog() == DialogResult.OK)
            {
                this.txtImpReceiver.Text = (form.SelectedType != null ? form.SelectedType.FullName : null);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                SavePortProperty();
                SaveCustom();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        private void SavePortProperty()
        {
            foreach(PortItem pi in this.lsbPorts.Items)
            {
                if(pi.View != null)
                {
                    try
                    {
                        pi.View.Check();
                        pi.View.Save();
                    }
                    catch(Exception ex)
                    {
                        this.lsbPorts.SelectedItem = pi;
                        throw ex;
                    }
                }
            }
        }

        private void SaveCustom()
        {
            string impProtocol = protocol.GetType().FullName;
            if(this.gbSender.Enabled)
            {
                pipe.SetCustomSender(impProtocol, this.txtImpSender.Text);
            }

            if(this.gbReceiver.Enabled)
            {
                pipe.SetCustomReceiver(impProtocol, this.txtImpReceiver.Text);
            }
        }

        private class PortItem
        {
            private Pipe pipe;
            private Protocol protocol;
            private IPortOwner port;
            private BaseConfigureView view;

            public PortItem(Pipe pipe, Protocol protocol, IPortOwner port)
            {
                this.pipe = pipe;
                this.protocol = protocol;
                this.port = port;
            }

            public override string ToString()
            {
                return port.GetType().Name;
            }

            public override bool Equals(object obj)
            {
                return obj is PortItem && (obj as PortItem).port.GetType().FullName.Equals(port.GetType().FullName);
            }

            public override int GetHashCode()
            {
                return port.GetType().FullName.GetHashCode();
            }

            public BaseConfigureView View
            {
                get
                {
                    if(view == null)
                    {
                        view = CreateView();
                    }
                    return view;
                }
            }

            private BaseConfigureView CreateView()
            {
                if(port is IConfigView)
                {
                    IConfigView icv = port as IConfigView;
                    if(icv.ConfigView != null)
                    {
                        Property portProperty = pipe.GetPortProperty(protocol.GetType().FullName, port.GetType().FullName);
                        icv.ConfigView.LoadObj(portProperty);
                    }
                    return icv.ConfigView;
                }
                return null;
            }

        }

    }
}