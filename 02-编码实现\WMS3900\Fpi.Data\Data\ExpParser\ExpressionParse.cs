﻿namespace Fpi.Data.ExpParser
{
    using System;
    using System.Collections.Generic;

    public class ExpressionParse
    {
        private Evaluator _eval;
        private string _expression;
        private Link_OP _link_OP;

        public ExpressionParse() : this("")
        {
        }

        public ExpressionParse(string expression)
        {
            this._expression = string.Empty;
            this._link_OP = null;
            this._eval = new Evaluator();
            this._expression = expression;
        }

        private void Analyze()
        {
            if(this._link_OP == null)
            {
                if(this._expression.Trim().Length <= 0)
                {
                    throw new Exception("Error! 表达式为空");
                }
                this._link_OP = new PhraseAnalyzer(this._expression).Analyze();
            }
        }

        public bool Check(ref string mes)
        {
            bool flag = false;
            try
            {
                this.Analyze();
                EDataType type = new SyntaxAnalyzer().Execute(this._link_OP.Head, this._link_OP.Tail);
                mes = "Success!";
                flag = true;
            }
            catch(Exception exception)
            {
                mes = exception.Message;
            }
            return flag;
        }
        public bool CheckBool(ref string mes)
        {
            bool flag = false;
            try
            {
                this.Analyze();
                EDataType type = new SyntaxAnalyzer().Execute(this._link_OP.Head, this._link_OP.Tail);
                if(type != EDataType.Dbool)
                {
                    throw new Exception("返回值必须为Bool型!");
                }
                mes = "Success!";
                flag = true;
            }
            catch(Exception exception)
            {
                mes = exception.Message;
            }
            return flag;
        }

        public IOperand Execute()
        {
            this.Analyze();
            return this._eval.ExpressionEvaluate(this._link_OP.Head, this._link_OP.Tail);
        }

        public string ExecuteToString()
        {
            return this.Execute().ToString();
        }

        public string Expression
        {
            get => this._expression;
            set
            {
                this._expression = value.Trim();
                this._link_OP = null;
            }
        }

        public List<string> Words
        {
            get
            {
                this.Analyze();
                TOKENLink head = this._link_OP.Head;
                List<string> list = new List<string>();
                while(head != null)
                {
                    Operator tag;
                    switch(head.Token.Type)
                    {
                        case ETokenType.token_operand:
                            list.Add(((TOKEN<IOperand>)head.Token).Tag.ToString());
                            goto Label_0134;

                        case ETokenType.token_operator:
                            tag = ((TOKEN<Operator>)head.Token).Tag;
                            if((tag.Value != "+") && !(tag.Value == "-"))
                            {
                                break;
                            }
                            list.Add(tag.Value + "  " + tag.Type.ToString());
                            goto Label_0134;

                        case ETokenType.token_keyword:
                            list.Add(((TOKEN<KeyWord>)head.Token).Tag.Value);
                            goto Label_0134;

                        case ETokenType.token_separator:
                            list.Add(((TOKEN<Separator>)head.Token).Tag.Value.ToString());
                            goto Label_0134;

                        default:
                            goto Label_0134;
                    }
                    list.Add(tag.Value);
                    Label_0134:
                    head = head.Next;
                }
                return list;
            }
        }
    }
}

