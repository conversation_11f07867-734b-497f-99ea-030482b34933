﻿using System;
using Fpi.Timers;
using Fpi.Xml;

namespace Fpi.HB.Business.Tasks
{
    /// <summary>
    /// 自定义任务基类。包含一个定时器，周期性执行任务。
    /// </summary>
    public abstract class CustomTask : IdNameNode, ITimerAction
    {
        #region 字段属性

        /// <summary>
        /// 该任务是否已启用
        /// </summary>
        public bool IsValid;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description;

        /// <summary>
        /// 任务执行定时器
        /// </summary>
        public Timer timer;

        #endregion

        #region 公共方法（待重写）

        public abstract void DoTask();

        #endregion

        #region ITimerAction 成员

        public void OnTimer(string taskId)
        {
            try
            {
                DoTask();
            }
            catch(Exception ex)
            {
                TaskLogHelper.WriteTaskLog($"[{name}]任务执行出错：{ex.Message}");
            }
        }

        #endregion
    }
}