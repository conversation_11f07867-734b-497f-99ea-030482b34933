﻿using System;
using System.Windows.Forms;
using Fpi.Xml;

namespace Fpi.Devices.UI
{
    public partial class OtherParamPanel : UserControl, ICommParam
    {
        private Device _device;

        public OtherParamPanel()
        {
            InitializeComponent();
        }

        public OtherParamPanel(Device device)
            : this()
        {
            this._device = device;
        }

        private void SerialPortParamPanel_Load(object sender, EventArgs e)
        {
            if(this._device != null)
            {
                Property otherParam = this._device.GetProperty("OtherParam");
                if(otherParam != null)
                {
                    this.txtOther.Text = otherParam.GetPropertyValue("other", "");
                }
            }
        }

        #region ICommParam 成员

        public void SaveCommParam()
        {
            if(this._device != null)
            {
                Property otherParam = this._device.GetProperty("OtherParam");

                if(otherParam == null)
                {
                    otherParam = new Property("OtherParam", "其他通信参数");
                    this._device.AddProperty(otherParam);
                }

                otherParam.SetProperty("other", this.txtOther.Text);
            }
        }

        #endregion
    }
}
