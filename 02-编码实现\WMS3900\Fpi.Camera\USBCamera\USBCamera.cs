﻿using System;
using System.Drawing;
using System.Linq;
using System.Threading;
using AForge.Video;
using AForge.Video.DirectShow;

namespace Fpi.Camera
{
    /// <summary>
    /// USB摄像机
    /// </summary>
    public class USBCamera
    {
        #region 字段属性

        /// <summary>
        /// 摄像机标识号
        /// </summary>
        public string MonikerString { get; set; }

        /// <summary>
        /// 摄像机名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 对应摄像机实例
        /// </summary>
        public VideoCaptureDevice VideoSource { get; set; }

        ///// <summary>
        ///// 录像帮助类
        ///// </summary>
        //public VideoFileWriter VideoWriter { get; set; }

        /// <summary>
        /// 实时抓取的图像
        /// </summary>
        public Bitmap CurrentGrabImage { get; set; }

        /// <summary>
        /// 设备正在被使用
        /// </summary>
        public bool IsPreviewing => VideoSource != null && VideoSource.IsRunning;

        /// <summary>
        /// 设备正在录像中
        /// </summary>
        public bool IsRecording { get; set; }

        #endregion

        #region 构造

        public USBCamera(string monikerString)
        {
            MonikerString = monikerString;

            // 截取标识符
            int index = MonikerString.IndexOf("pid");
            if(index != -1)
            {
                Name = MonikerString.Substring(index, Math.Min(8, MonikerString.Length - index));
            }

            VideoSource = new VideoCaptureDevice(MonikerString);

            //VideoWriter = new VideoFileWriter();

            // 默认设置分辨率为最低
            //SetMinResolution();

            // 默认设置分辨率为最高
            SetMaxResolution();
        }

        public USBCamera()
        {
        }

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return Name;
        }

        #endregion

        #region 预览相关

        /// <summary>
        /// 开始预览
        /// </summary>
        /// <returns></returns>
        public void StartPreview()
        {
            if(!IsPreviewing)
            {
                // 设置NewFrame事件处理
                VideoSource.NewFrame -= new NewFrameEventHandler(videoSource_NewFrame);
                VideoSource.NewFrame += new NewFrameEventHandler(videoSource_NewFrame);
                // 开始播放
                VideoSource.Start();
            }
        }

        /// <summary>
        /// 停止预览
        /// </summary>
        public void StopPreview()
        {
            VideoSource.SignalToStop();
            VideoSource.WaitForStop();
            VideoSource.Stop();

            VideoSource.NewFrame -= new NewFrameEventHandler(videoSource_NewFrame);

            CurrentGrabImage?.Dispose();

            //if(VideoWriter.IsOpen)
            //{
            //    VideoWriter.Close();
            //}
        }

        #endregion

        #region 抓拍

        /// <summary>
        /// 抓拍
        /// </summary>
        /// <returns></returns>
        public Bitmap ScreenShot()
        {
            Bitmap shotImage;

            bool isPreviewing = IsPreviewing;
            // 当前正在预览中
            if(IsPreviewing)
            {
                //// 设置摄像机分辨率为最高分辨率
                //SetMaxResolution();

                //VideoSource.SignalToStop();
                //VideoSource.WaitForStop();
                //VideoSource.Stop();
                //VideoSource.Start();

                //Thread.Sleep(3000);

                shotImage = CurrentGrabImage;

                //// 恢复摄像机分辨率为最低分辨率
                //SetMinResolution();

                //VideoSource.SignalToStop();
                //VideoSource.WaitForStop();
                //VideoSource.Stop();
                //VideoSource.Start();

            }
            // 当前不在预览中
            else
            {
                //// 设置摄像机分辨率为最高分辨率
                //SetMaxResolution();

                StartPreview();

                Thread.Sleep(3000);

                shotImage = CurrentGrabImage;

                StopPreview();

                //// 恢复摄像机分辨率为最低分辨率
                //SetMinResolution();
            }

            return shotImage;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 视频帧到达事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="eventArgs"></param>
        private void videoSource_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            try
            {
                CurrentGrabImage?.Dispose();

                CurrentGrabImage = (Bitmap)eventArgs.Frame.Clone();

                //// 处理录像事件
                //if(VideoWriter != null && VideoWriter.IsOpen)
                //{
                //    VideoWriter.WriteVideoFrame(CurrentGrabImage);

                //    CameraLogHelper.Info(DateTime.Now.ToString("HH-mm-ss-fff"));
                //}
            }
            catch(Exception e)
            {
                CameraLogHelper.Info($"获取摄像机[{Name}]最新图像出错：{e.Message}");
            }
        }

        /// <summary>
        /// 设置最大清晰度
        /// </summary>
        public void SetMaxResolution()
        {
            if(VideoSource != null)
            {
                // 获取设备支持的视频分辨率
                VideoCapabilities[] resolutions = VideoSource.VideoCapabilities;
                if(resolutions.Length > 0)
                {
                    // 设置为第一个支持30帧的分辨率
                    VideoSource.VideoResolution = VideoSource.VideoCapabilities.First(res => res.AverageFrameRate == 30);

                    if(VideoSource.VideoResolution == null)
                    {
                        VideoSource.VideoResolution = resolutions[0];
                    }
                }
            }
        }

        /// <summary>
        /// 设置最低清晰度
        /// </summary>
        public void SetMinResolution()
        {
            if(VideoSource != null)
            {
                // 获取设备支持的视频分辨率
                VideoCapabilities[] resolutions = VideoSource.VideoCapabilities;
                if(resolutions.Length > 0)
                {
                    VideoSource.VideoResolution = resolutions[resolutions.Length - 1];
                }
            }
        }

        #endregion
    }
}