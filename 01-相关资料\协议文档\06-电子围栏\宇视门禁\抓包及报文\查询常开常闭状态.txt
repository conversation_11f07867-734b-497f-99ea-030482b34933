GET /LAPI/V1.0/PACS/Controller/OpenDoorTemplates HTTP/1.1
User-Agent: PostmanRuntime/7.28.4
Accept: */*
Postman-Token: 1cbb068f-340b-4e65-8156-608c3e020627
Host: *************
Accept-Encoding: gzip, deflate, br
Connection: keep-alive

HTTP/1.1 200 Ok
Content-Length: 1290
Content-Type: text/plain
Connection: close
X-Frame-Options: SAMEORIGIN

{
"Response": {
	"ResponseURL": "/LAPI/V1.0/PACS/Controller/OpenDoorTemplates",
	"CreatedID": -1, 
	"ResponseCode": 0,
 	"SubResponseCode": 0,
 	"ResponseString": "Succeed",
	"StatusCode": 0,
	"StatusString": "Succeed",
	"Data": {
	"Type":	0,
	"SafetyTipsEnabled":	1,
	"DayNum":	7,
	"Day":	[{
			"ID":	0,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}, {
			"ID":	1,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}, {
			"ID":	2,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}, {
			"ID":	3,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}, {
			"ID":	4,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}, {
			"ID":	5,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}, {
			"ID":	6,
			"SectionNum":	1,
			"TimeSection":	[{
					"Begin":	"00:00:00",
					"End":	"23:59:59",
					"OpenDoorType":	1
				}]
		}]
}
	}
}
