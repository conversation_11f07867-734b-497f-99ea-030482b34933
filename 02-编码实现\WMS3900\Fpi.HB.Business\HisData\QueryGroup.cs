﻿using System.Collections.Generic;
using Fpi.Data.Config;
using Fpi.Xml;

namespace Fpi.HB.Business.HisData
{
    /// <summary>
    /// 查询分组类
    /// </summary>
    public class QueryGroup : IdNameNode
    {
        public NodeList QueryNodes = new NodeList();

        /// <summary>
        /// 获取测量组对应测量因子
        /// </summary>
        /// <param name="queryGroup"></param>
        /// <returns></returns>
        public List<ValueNode> GetQueryGroupValueNode()
        {
            List<ValueNode> valueNodeList = new List<ValueNode>();
            foreach(QueryNode queryNode in QueryNodes)
            {
                ValueNode valueNode = DataManager.GetInstance().GetValueNodeById(queryNode.id);
                if(valueNode != null)
                {
                    valueNodeList.Add(valueNode);
                }
            }
            return valueNodeList;
        }
    }
}