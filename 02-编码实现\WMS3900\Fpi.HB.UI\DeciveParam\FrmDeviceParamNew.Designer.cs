﻿namespace Fpi.HB.UI
{
    partial class FrmDeviceParamNew
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lineDeviceListTitle = new Sunny.UI.UILine();
            this.pnlDeviceList = new Sunny.UI.UIPanel();
            this.mainMenu = new Sunny.UI.UINavMenu();
            this.mainTab = new Sunny.UI.UITabControl();
            this.uiLine1 = new Sunny.UI.UILine();
            this.pnlDeviceList.SuspendLayout();
            this.SuspendLayout();
            // 
            // lineDeviceListTitle
            // 
            this.lineDeviceListTitle.BackColor = System.Drawing.Color.Transparent;
            this.lineDeviceListTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lineDeviceListTitle.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lineDeviceListTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lineDeviceListTitle.Location = new System.Drawing.Point(0, 0);
            this.lineDeviceListTitle.MinimumSize = new System.Drawing.Size(1, 1);
            this.lineDeviceListTitle.Name = "lineDeviceListTitle";
            this.lineDeviceListTitle.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.lineDeviceListTitle.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.lineDeviceListTitle.Size = new System.Drawing.Size(300, 32);
            this.lineDeviceListTitle.TabIndex = 2;
            this.lineDeviceListTitle.Text = "设备列表";
            this.lineDeviceListTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlDeviceList
            // 
            this.pnlDeviceList.Controls.Add(this.mainMenu);
            this.pnlDeviceList.Controls.Add(this.lineDeviceListTitle);
            this.pnlDeviceList.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlDeviceList.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlDeviceList.Location = new System.Drawing.Point(0, 0);
            this.pnlDeviceList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDeviceList.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDeviceList.Name = "pnlDeviceList";
            this.pnlDeviceList.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlDeviceList.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlDeviceList.Size = new System.Drawing.Size(300, 600);
            this.pnlDeviceList.TabIndex = 0;
            this.pnlDeviceList.Text = null;
            this.pnlDeviceList.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // mainMenu
            // 
            this.mainMenu.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.mainMenu.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.mainMenu.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mainMenu.DrawMode = System.Windows.Forms.TreeViewDrawMode.OwnerDrawAll;
            this.mainMenu.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.mainMenu.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.mainMenu.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.mainMenu.FullRowSelect = true;
            this.mainMenu.HideSelection = false;
            this.mainMenu.HotTracking = true;
            this.mainMenu.HoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(230)))), ((int)(((byte)(230)))));
            this.mainMenu.ItemHeight = 50;
            this.mainMenu.Location = new System.Drawing.Point(0, 32);
            this.mainMenu.MenuStyle = Sunny.UI.UIMenuStyle.Custom;
            this.mainMenu.Name = "mainMenu";
            this.mainMenu.ScrollBarColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.mainMenu.ScrollBarHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.mainMenu.ScrollBarPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.mainMenu.ScrollFillColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.mainMenu.SecondBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(235)))), ((int)(((byte)(235)))));
            this.mainMenu.SelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.mainMenu.SelectedColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.mainMenu.ShowSecondBackColor = true;
            this.mainMenu.Size = new System.Drawing.Size(300, 568);
            this.mainMenu.TabControl = this.mainTab;
            this.mainMenu.TabIndex = 4;
            this.mainMenu.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // mainTab
            // 
            this.mainTab.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mainTab.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.mainTab.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.mainTab.ItemSize = new System.Drawing.Size(0, 1);
            this.mainTab.Location = new System.Drawing.Point(300, 32);
            this.mainTab.MainPage = "";
            this.mainTab.MenuStyle = Sunny.UI.UIMenuStyle.White;
            this.mainTab.Name = "mainTab";
            this.mainTab.SelectedIndex = 0;
            this.mainTab.Size = new System.Drawing.Size(500, 568);
            this.mainTab.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.mainTab.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.mainTab.TabIndex = 4;
            this.mainTab.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.mainTab.TabUnSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.mainTab.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.mainTab.TabVisible = false;
            this.mainTab.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // uiLine1
            // 
            this.uiLine1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.uiLine1.Dock = System.Windows.Forms.DockStyle.Top;
            this.uiLine1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLine1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine1.Location = new System.Drawing.Point(300, 0);
            this.uiLine1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine1.Name = "uiLine1";
            this.uiLine1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uiLine1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uiLine1.Size = new System.Drawing.Size(500, 32);
            this.uiLine1.TabIndex = 3;
            this.uiLine1.Text = "设备参数状态";
            this.uiLine1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // FrmDeviceParam
            // 
            this.AllowShowTitle = false;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.ControlBox = false;
            this.Controls.Add(this.mainTab);
            this.Controls.Add(this.uiLine1);
            this.Controls.Add(this.pnlDeviceList);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.Margin = new System.Windows.Forms.Padding(3);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(0, 0);
            this.MinimizeBox = false;
            this.Name = "FrmDeviceParam";
            this.Padding = new System.Windows.Forms.Padding(0);
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.ShowTitle = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "设备参数状态查询";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 800, 600);
            this.Load += new System.EventHandler(this.DeviceParamForm_Load);
            this.pnlDeviceList.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UILine lineDeviceListTitle;
        private Sunny.UI.UIPanel pnlDeviceList;
        private Sunny.UI.UILine uiLine1;
        private Sunny.UI.UINavMenu mainMenu;
        private Sunny.UI.UITabControl mainTab;
    }
}