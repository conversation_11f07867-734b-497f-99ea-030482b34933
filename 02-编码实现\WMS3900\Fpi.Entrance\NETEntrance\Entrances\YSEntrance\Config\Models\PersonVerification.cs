﻿using System.Collections.Generic;
using System.ComponentModel;

namespace Fpi.Entrance.YS.Models
{
    /// <summary>
    /// 人员核验通知数据结构
    /// </summary>
    public class PersonVerification
    {
        /// <summary>
        /// 订阅者描述信息（URL格式）
        /// </summary>
        [Description("订阅者描述信息，以URL格式体现")]
        public string Reference { get; set; }

        /// <summary>
        /// 通知记录序号
        /// </summary>
        [Description("通知记录序号")]
        public uint Seq { get; set; }

        /// <summary>
        /// 智能识别终端序列号
        /// </summary>
        [Description("智能识别终端编码(智能识别终端序列号)")]
        public string DeviceCode { get; set; }

        /// <summary>
        /// 通知上报时间（UTC秒）
        /// </summary>
        [Description("通知上报时间，UTC格式，单位秒")]
        public long Timestamp { get; set; }

        /// <summary>
        /// 通知类型（0：实时 1：历史）
        /// </summary>
        [Description("通知类型 0：实时通知 1：历史通知")]
        public uint NotificationType { get; set; }

        /// <summary>
        /// 人脸信息列表
        /// </summary>
        [Description("人脸信息列表")]
        public List<FaceInfo> FaceInfoList { get; set; } = new List<FaceInfo>();

        /// <summary>
        /// 卡证信息列表
        /// </summary>
        [Description("卡信息列表")]
        public List<CardInfo> CardInfoList { get; set; } = new List<CardInfo>();

        /// <summary>
        /// 闸机信息列表
        /// </summary>
        [Description("闸机信息列表")]
        public List<GateInfo> GateInfoList { get; set; } = new List<GateInfo>();

        /// <summary>
        /// 库比对信息列表
        /// </summary>
        [Description("库比对信息列表")]
        public List<LibMatchInfo> LibMatInfoList { get; set; } = new List<LibMatchInfo>();
    }

    /// <summary>
    /// 人脸采集信息
    /// </summary>
    public class FaceInfo
    {
        /// <summary>
        /// 记录ID（从1开始递增）
        /// </summary>
        [Description("记录ID")]
        public uint ID { get; set; }

        /// <summary>
        /// 采集来源（1：智能终端 2：门禁卡 3：身份证 4：闸机）
        /// </summary>
        [Description("采集来源 1：智能识别终端采集 2：门禁卡 3：身份证 4：闸机")]
        public uint CapSrc { get; set; }

        /// <summary>
        /// 体温（℃），0表示未检测
        /// </summary>
        [Description("体温（摄氏度）未检测为0")]
        public float Temperature { get; set; }

        /// <summary>
        /// 口罩标识（0：未知 1：未戴 2：已戴）
        /// </summary>
        [Description("口罩标识 0：未知 1：未戴 2：已戴")]
        public int MaskFlag { get; set; }

        /// <summary>
        /// 全景图信息
        /// </summary>
        [Description("全景图信息")]
        public ImageInfo PanoImage { get; set; }

        /// <summary>
        /// 人脸图信息
        /// </summary>
        [Description("人脸图信息")]
        public ImageInfo FaceImage { get; set; }

        /// <summary>
        /// 人脸坐标区域（归一化坐标系0-10000）
        /// </summary>
        [Description("人脸坐标区域")]
        public FaceArea FaceArea { get; set; }
    }

    /// <summary>
    /// 卡证信息（门禁卡/身份证）
    /// </summary>
    public class CardInfo
    {
        /// <summary>
        /// 卡类型（0：身份证 1：门禁卡 2：二维码）
        /// </summary>
        [Description("卡类型 0：身份证 1：门禁卡 2：二维码")]
        public uint CardType { get; set; }

        /// <summary>
        /// 身份证信息（当CardType=0时有效）
        /// </summary>
        [Description("身份证信息")]
        public IDCardInfo IdentityCard { get; set; }

        /// <summary>
        /// 门禁卡号（当CardType=1时有效）
        /// </summary>
        [Description("门禁卡号")]
        public string CardNumber { get; set; }

        /// <summary>
        /// 二维码内容（当CardType=2时有效）
        /// </summary>
        [Description("二维码内容")]
        public string QRCode { get; set; }
    }

    /// <summary>
    /// 闸机通行信息
    /// </summary>
    public class GateInfo
    {
        /// <summary>
        /// 通行方向（0：进 1：出）
        /// </summary>
        [Description("通行方向 0：进入 1：离开")]
        public uint Direction { get; set; }

        /// <summary>
        /// 通行时间戳（UTC秒）
        /// </summary>
        [Description("通行时间戳")]
        public uint PassTime { get; set; }
    }

    /// <summary>
    /// 比对信息
    /// </summary>
    public class LibMatchInfo
    {
        /// <summary>
        /// 比对状态码
        /// </summary>
        [Description("比对状态码 0：成功 1：失败 2：黑名单等")]
        public int MatchStatus { get; set; }

        /// <summary>
        /// 匹配人员信息
        /// </summary>
        [Description("匹配人员信息")]
        public MatchPersonInfo MatchPersonInfo { get; set; } // 修改属性类型

        /// <summary>
        /// 比对置信度
        /// </summary>
        [Description("比对置信度")]
        public int Confidence { get; set; }
    }

    /// <summary>
    /// 匹配人员信息
    /// </summary>
    public class MatchPersonInfo
    {
        /// <summary>
        /// 人员编码
        /// </summary>
        [Description("人员编码")]
        public string PersonCode { get; set; }

        /// <summary>
        /// 人员姓名
        /// </summary>
        [Description("人员姓名")]
        public string PersonName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [Description("性别 0:未知 1:男 2:女")]
        public int Gender { get; set; }
        
        /// <summary>
        /// 性别
        /// </summary>
        [Description("卡号")]
        public string CardID { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        [Description("身份证号")]
        public string IdentityNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 嵌套数据类型
    /// </summary>
    public class ImageInfo
    {
        /// <summary>
        /// 文件名
        /// </summary>
        [Description("文件名")]
        public string Name { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        [Description("文件大小（字节）")]
        public uint Size { get; set; }

        /// <summary>
        /// Base64编码数据
        /// </summary>
        [Description("Base64编码数据")]
        public string Data { get; set; }
    }

    /// <summary>
    /// 人脸区域
    /// </summary>
    public class FaceArea
    {
        /// <summary>
        /// 左上角X坐标
        /// </summary>
        [Description("左上角X坐标")]
        public uint LeftTopX { get; set; }

        /// <summary>
        /// 左上角Y坐标
        /// </summary>
        [Description("左上角Y坐标")]
        public uint LeftTopY { get; set; }

        /// <summary>
        /// 右下角X坐标
        /// </summary>
        [Description("右下角X坐标")]
        public uint RightBottomX { get; set; }

        /// <summary>
        /// 右下角Y坐标
        /// </summary>
        [Description("右下角Y坐标")]
        public uint RightBottomY { get; set; }
    }

    /// <summary>
    /// ID卡信息
    /// </summary>
    public class IDCardInfo
    {
        /// <summary>
        /// 姓名
        /// </summary>
        [Description("姓名")]
        public string Name { get; set; }

        /// <summary>
        /// 性别（1：男 2：女）
        /// </summary>
        [Description("性别（1：男 2：女）")]
        public uint Gender { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        [Description("身份证号")]
        public string IdentityNo { get; set; }
    }
}
