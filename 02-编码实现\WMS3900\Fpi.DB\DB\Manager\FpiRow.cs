﻿//==================================================================================================
//类名：     FpiRow   
//创建人:    hongbing_mao
//创建时间:  2013-1-15 16:49:25
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System.Collections.Generic;

namespace Fpi.DB.Manager
{
    /// <summary>
    /// 行数据
    /// </summary>
    public class FpiRow
    {
        private Dictionary<string, object> values = new Dictionary<string, object>();
        /// <summary>
        /// 构造
        /// </summary>
        public FpiRow()
        {

        }
        /// <summary>
        /// 设置字段值
        /// </summary>
        /// <param name="field"></param>
        /// <param name="value"></param>
        public void SetFieldValue(string field, object value)
        {
            if(string.IsNullOrEmpty(field))
            {
                return;
            }

            string innerField = field.ToLower();
            if(values.ContainsKey(innerField))
            {
                values[innerField] = value;
            }
            else
            {
                values.Add(innerField, value);
            }
        }

        /// <summary>
        /// 设置字段值
        /// </summary>
        /// <param name="field"></param>
        /// <param name="value"></param>
        public void SetFieldValue(FpiColumn column, string value)
        {
            if(column == null)
            {
                return;
            }

            string innerField = column.Name.ToLower();
            if(values.ContainsKey(innerField))
            {
                values[innerField] = value;
            }
            else
            {
                values.Add(innerField, value);
            }
        }
        /// <summary>
        /// 获取字段值
        /// </summary>
        /// <param name="field"></param>
        /// <returns></returns>
        public object GetFieldValue(string field)
        {
            string innerField = field.ToLower();
            return values.ContainsKey(innerField) ? values[innerField] : null;
        }

    }
}
