﻿namespace Fpi.HB.Business
{
    partial class FrmDataAndParamBackUp
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmDataAndParamBackUp));
            this.folderDialog = new System.Windows.Forms.FolderBrowserDialog();
            this.btnSave = new System.Windows.Forms.Button();
            this.ofDialog = new System.Windows.Forms.OpenFileDialog();
            this.folderDialogXml = new System.Windows.Forms.FolderBrowserDialog();
            this.btnCancel = new System.Windows.Forms.Button();
            this.gbBackup = new System.Windows.Forms.GroupBox();
            this.chkIsBackUpDB = new System.Windows.Forms.CheckBox();
            this.btnReconver = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.pbStep = new System.Windows.Forms.ProgressBar();
            this.lbInfo = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnBackup = new System.Windows.Forms.Button();
            this.txtBackPath = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.btnSel = new System.Windows.Forms.Button();
            this.txtFileName = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.gbXmlBackup = new System.Windows.Forms.GroupBox();
            this.chkIsBackUpConfig = new System.Windows.Forms.CheckBox();
            this.btnXmlReconver = new System.Windows.Forms.Button();
            this.btnXmlSel = new System.Windows.Forms.Button();
            this.btnXmlBackup = new System.Windows.Forms.Button();
            this.txtXmlBackPath = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.txtURL = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.textStationName = new System.Windows.Forms.TextBox();
            this.txtStationID = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.nuOverTime = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.chkIsOpenUpload = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.nuReCount = new System.Windows.Forms.NumericUpDown();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.btnPlan = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.gbBackup.SuspendLayout();
            this.gbXmlBackup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuOverTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuReCount)).BeginInit();
            this.panel1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnSave.Location = new System.Drawing.Point(443, 436);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "保存";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // ofDialog
            // 
            this.ofDialog.Filter = "bak 文件|*.bak";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(519, 436);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // gbBackup
            // 
            this.gbBackup.Controls.Add(this.chkIsBackUpDB);
            this.gbBackup.Controls.Add(this.label3);
            this.gbBackup.Controls.Add(this.label2);
            this.gbBackup.Controls.Add(this.txtBackPath);
            this.gbBackup.Controls.Add(this.btnSel);
            this.gbBackup.Controls.Add(this.txtFileName);
            this.gbBackup.Controls.Add(this.label1);
            this.gbBackup.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbBackup.Location = new System.Drawing.Point(0, 0);
            this.gbBackup.Name = "gbBackup";
            this.gbBackup.Size = new System.Drawing.Size(611, 124);
            this.gbBackup.TabIndex = 6;
            this.gbBackup.TabStop = false;
            this.gbBackup.Text = "备份数据库参数配置";
            // 
            // chkIsBackUpDB
            // 
            this.chkIsBackUpDB.AutoSize = true;
            this.chkIsBackUpDB.Location = new System.Drawing.Point(18, 96);
            this.chkIsBackUpDB.Name = "chkIsBackUpDB";
            this.chkIsBackUpDB.Size = new System.Drawing.Size(99, 21);
            this.chkIsBackUpDB.TabIndex = 12;
            this.chkIsBackUpDB.Text = "是否启用备份";
            this.chkIsBackUpDB.UseVisualStyleBackColor = true;
            // 
            // btnReconver
            // 
            this.btnReconver.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnReconver.Location = new System.Drawing.Point(505, 19);
            this.btnReconver.Name = "btnReconver";
            this.btnReconver.Size = new System.Drawing.Size(89, 23);
            this.btnReconver.TabIndex = 7;
            this.btnReconver.Text = "数据恢复";
            this.btnReconver.UseVisualStyleBackColor = true;
            this.btnReconver.Click += new System.EventHandler(this.btnReconver_Click);
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.ForeColor = System.Drawing.Color.Maroon;
            this.label3.Location = new System.Drawing.Point(451, 66);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(145, 17);
            this.label3.TabIndex = 6;
            this.label3.Text = "*备份文件名自动附加时间";
            // 
            // pbStep
            // 
            this.pbStep.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pbStep.Location = new System.Drawing.Point(9, 52);
            this.pbStep.Name = "pbStep";
            this.pbStep.Size = new System.Drawing.Size(583, 23);
            this.pbStep.TabIndex = 3;
            // 
            // lbInfo
            // 
            this.lbInfo.Location = new System.Drawing.Point(69, 19);
            this.lbInfo.Name = "lbInfo";
            this.lbInfo.Size = new System.Drawing.Size(252, 20);
            this.lbInfo.TabIndex = 2;
            this.lbInfo.Text = "------";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(47, 66);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(56, 17);
            this.label2.TabIndex = 5;
            this.label2.Text = "文件名：";
            // 
            // btnBackup
            // 
            this.btnBackup.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBackup.Location = new System.Drawing.Point(403, 19);
            this.btnBackup.Name = "btnBackup";
            this.btnBackup.Size = new System.Drawing.Size(89, 23);
            this.btnBackup.TabIndex = 0;
            this.btnBackup.Text = "手动备份";
            this.btnBackup.UseVisualStyleBackColor = true;
            this.btnBackup.Click += new System.EventHandler(this.btnBackup_Click);
            // 
            // txtBackPath
            // 
            this.txtBackPath.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtBackPath.CanEmpty = false;
            this.txtBackPath.DigitLength = 2;
            this.txtBackPath.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtBackPath.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtBackPath.IsValidCheck = false;
            this.txtBackPath.Label = "数据库备份目录";
            this.txtBackPath.Location = new System.Drawing.Point(112, 29);
            this.txtBackPath.MaxValue = null;
            this.txtBackPath.MinValue = null;
            this.txtBackPath.Name = "txtBackPath";
            this.txtBackPath.ReadOnly = true;
            this.txtBackPath.Size = new System.Drawing.Size(430, 23);
            this.txtBackPath.TabIndex = 1;
            // 
            // btnSel
            // 
            this.btnSel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSel.Location = new System.Drawing.Point(548, 29);
            this.btnSel.Name = "btnSel";
            this.btnSel.Size = new System.Drawing.Size(46, 23);
            this.btnSel.TabIndex = 0;
            this.btnSel.Text = "...";
            this.btnSel.UseVisualStyleBackColor = true;
            this.btnSel.Click += new System.EventHandler(this.btnSel_Click);
            // 
            // txtFileName
            // 
            this.txtFileName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFileName.CanEmpty = false;
            this.txtFileName.DigitLength = 2;
            this.txtFileName.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtFileName.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtFileName.IsValidCheck = false;
            this.txtFileName.Label = "数据库文件名";
            this.txtFileName.Location = new System.Drawing.Point(112, 63);
            this.txtFileName.MaxLength = 50;
            this.txtFileName.MaxValue = null;
            this.txtFileName.MinValue = null;
            this.txtFileName.Name = "txtFileName";
            this.txtFileName.Size = new System.Drawing.Size(333, 23);
            this.txtFileName.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(35, 32);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(68, 17);
            this.label1.TabIndex = 2;
            this.label1.Text = "备份目录：";
            // 
            // gbXmlBackup
            // 
            this.gbXmlBackup.Controls.Add(this.chkIsBackUpConfig);
            this.gbXmlBackup.Controls.Add(this.btnXmlSel);
            this.gbXmlBackup.Controls.Add(this.txtXmlBackPath);
            this.gbXmlBackup.Controls.Add(this.label5);
            this.gbXmlBackup.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbXmlBackup.Location = new System.Drawing.Point(0, 124);
            this.gbXmlBackup.Name = "gbXmlBackup";
            this.gbXmlBackup.Size = new System.Drawing.Size(611, 86);
            this.gbXmlBackup.TabIndex = 7;
            this.gbXmlBackup.TabStop = false;
            this.gbXmlBackup.Text = "备份配置文件参数设置";
            // 
            // chkIsBackUpConfig
            // 
            this.chkIsBackUpConfig.AutoSize = true;
            this.chkIsBackUpConfig.Location = new System.Drawing.Point(18, 60);
            this.chkIsBackUpConfig.Name = "chkIsBackUpConfig";
            this.chkIsBackUpConfig.Size = new System.Drawing.Size(99, 21);
            this.chkIsBackUpConfig.TabIndex = 12;
            this.chkIsBackUpConfig.Text = "是否启用备份";
            this.chkIsBackUpConfig.UseVisualStyleBackColor = true;
            // 
            // btnXmlReconver
            // 
            this.btnXmlReconver.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnXmlReconver.Location = new System.Drawing.Point(505, 16);
            this.btnXmlReconver.Name = "btnXmlReconver";
            this.btnXmlReconver.Size = new System.Drawing.Size(89, 23);
            this.btnXmlReconver.TabIndex = 9;
            this.btnXmlReconver.Text = "配置恢复";
            this.btnXmlReconver.UseVisualStyleBackColor = true;
            this.btnXmlReconver.Click += new System.EventHandler(this.btnXmlReconver_Click);
            // 
            // btnXmlSel
            // 
            this.btnXmlSel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnXmlSel.Location = new System.Drawing.Point(548, 24);
            this.btnXmlSel.Name = "btnXmlSel";
            this.btnXmlSel.Size = new System.Drawing.Size(46, 23);
            this.btnXmlSel.TabIndex = 8;
            this.btnXmlSel.Text = "...";
            this.btnXmlSel.UseVisualStyleBackColor = true;
            this.btnXmlSel.Click += new System.EventHandler(this.btnXmlSel_Click);
            // 
            // btnXmlBackup
            // 
            this.btnXmlBackup.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnXmlBackup.Location = new System.Drawing.Point(403, 16);
            this.btnXmlBackup.Name = "btnXmlBackup";
            this.btnXmlBackup.Size = new System.Drawing.Size(89, 23);
            this.btnXmlBackup.TabIndex = 8;
            this.btnXmlBackup.Text = "手动备份";
            this.btnXmlBackup.UseVisualStyleBackColor = true;
            this.btnXmlBackup.Click += new System.EventHandler(this.btnXmlBackup_Click);
            // 
            // txtXmlBackPath
            // 
            this.txtXmlBackPath.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtXmlBackPath.CanEmpty = false;
            this.txtXmlBackPath.DigitLength = 2;
            this.txtXmlBackPath.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtXmlBackPath.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtXmlBackPath.IsValidCheck = false;
            this.txtXmlBackPath.Label = "配置文件备份目录";
            this.txtXmlBackPath.Location = new System.Drawing.Point(112, 26);
            this.txtXmlBackPath.MaxValue = null;
            this.txtXmlBackPath.MinValue = null;
            this.txtXmlBackPath.Name = "txtXmlBackPath";
            this.txtXmlBackPath.ReadOnly = true;
            this.txtXmlBackPath.Size = new System.Drawing.Size(430, 23);
            this.txtXmlBackPath.TabIndex = 4;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(35, 29);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(68, 17);
            this.label5.TabIndex = 3;
            this.label5.Text = "备份目录：";
            // 
            // txtURL
            // 
            this.txtURL.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtURL.Location = new System.Drawing.Point(106, 100);
            this.txtURL.MaxLength = 200;
            this.txtURL.Name = "txtURL";
            this.txtURL.Size = new System.Drawing.Size(488, 23);
            this.txtURL.TabIndex = 16;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(5, 104);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(91, 17);
            this.label6.TabIndex = 15;
            this.label6.Text = "上传接口(url)：";
            // 
            // textStationName
            // 
            this.textStationName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textStationName.Location = new System.Drawing.Point(106, 61);
            this.textStationName.MaxLength = 40;
            this.textStationName.Name = "textStationName";
            this.textStationName.Size = new System.Drawing.Size(488, 23);
            this.textStationName.TabIndex = 13;
            // 
            // txtStationID
            // 
            this.txtStationID.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtStationID.Location = new System.Drawing.Point(106, 24);
            this.txtStationID.MaxLength = 40;
            this.txtStationID.Name = "txtStationID";
            this.txtStationID.Size = new System.Drawing.Size(488, 23);
            this.txtStationID.TabIndex = 14;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(35, 66);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(68, 17);
            this.label7.TabIndex = 7;
            this.label7.Text = "项目名称：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(35, 29);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(68, 17);
            this.label8.TabIndex = 8;
            this.label8.Text = "项目编号：";
            // 
            // nuOverTime
            // 
            this.nuOverTime.Location = new System.Drawing.Point(335, 137);
            this.nuOverTime.Maximum = new decimal(new int[] {
            99999,
            0,
            0,
            0});
            this.nuOverTime.Name = "nuOverTime";
            this.nuOverTime.Size = new System.Drawing.Size(74, 23);
            this.nuOverTime.TabIndex = 12;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(276, 141);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(68, 17);
            this.label9.TabIndex = 9;
            this.label9.Text = "超时时间：";
            // 
            // chkIsOpenUpload
            // 
            this.chkIsOpenUpload.AutoSize = true;
            this.chkIsOpenUpload.Location = new System.Drawing.Point(18, 139);
            this.chkIsOpenUpload.Name = "chkIsOpenUpload";
            this.chkIsOpenUpload.Size = new System.Drawing.Size(99, 21);
            this.chkIsOpenUpload.TabIndex = 12;
            this.chkIsOpenUpload.Text = "是否开启上传";
            this.chkIsOpenUpload.UseVisualStyleBackColor = true;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(120, 141);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(68, 17);
            this.label10.TabIndex = 10;
            this.label10.Text = "重发次数：";
            // 
            // nuReCount
            // 
            this.nuReCount.Location = new System.Drawing.Point(181, 137);
            this.nuReCount.Maximum = new decimal(new int[] {
            99,
            0,
            0,
            0});
            this.nuReCount.Name = "nuReCount";
            this.nuReCount.Size = new System.Drawing.Size(76, 23);
            this.nuReCount.TabIndex = 11;
            // 
            // panel1
            // 
            this.panel1.AutoScroll = true;
            this.panel1.Controls.Add(this.groupBox3);
            this.panel1.Controls.Add(this.groupBox2);
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Controls.Add(this.gbXmlBackup);
            this.panel1.Controls.Add(this.gbBackup);
            this.panel1.Controls.Add(this.btnCancel);
            this.panel1.Controls.Add(this.btnSave);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(611, 662);
            this.panel1.TabIndex = 17;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.groupBox5);
            this.groupBox3.Controls.Add(this.groupBox4);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.groupBox3.Location = new System.Drawing.Point(0, 504);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(611, 158);
            this.groupBox3.TabIndex = 19;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "手动操作";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.btnPlan);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox2.Location = new System.Drawing.Point(0, 374);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(611, 51);
            this.groupBox2.TabIndex = 18;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "备份计划配置";
            // 
            // btnPlan
            // 
            this.btnPlan.Location = new System.Drawing.Point(14, 20);
            this.btnPlan.Name = "btnPlan";
            this.btnPlan.Size = new System.Drawing.Size(103, 23);
            this.btnPlan.TabIndex = 17;
            this.btnPlan.Text = "备份计划配置";
            this.btnPlan.UseVisualStyleBackColor = true;
            this.btnPlan.Click += new System.EventHandler(this.btnPlan_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.txtURL);
            this.groupBox1.Controls.Add(this.txtStationID);
            this.groupBox1.Controls.Add(this.nuReCount);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.nuOverTime);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.textStationName);
            this.groupBox1.Controls.Add(this.label9);
            this.groupBox1.Controls.Add(this.chkIsOpenUpload);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 210);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(611, 164);
            this.groupBox1.TabIndex = 17;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "上传参数配置";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label4);
            this.groupBox4.Controls.Add(this.lbInfo);
            this.groupBox4.Controls.Add(this.btnReconver);
            this.groupBox4.Controls.Add(this.btnBackup);
            this.groupBox4.Controls.Add(this.pbStep);
            this.groupBox4.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox4.Location = new System.Drawing.Point(3, 19);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(605, 85);
            this.groupBox4.TabIndex = 20;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "数据库备份";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.btnXmlReconver);
            this.groupBox5.Controls.Add(this.btnXmlBackup);
            this.groupBox5.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox5.Location = new System.Drawing.Point(3, 104);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(605, 49);
            this.groupBox5.TabIndex = 21;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "配置文件备份";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(3, 22);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(68, 17);
            this.label4.TabIndex = 8;
            this.label4.Text = "备份信息：";
            // 
            // FrmDataAndParamBackUp
            // 
            this.AcceptButton = this.btnSave;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(611, 662);
            this.Controls.Add(this.panel1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmDataAndParamBackUp";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "数据及系统配置(参数)备份";
            this.Load += new System.EventHandler(this.FormBackup_Load);
            this.gbBackup.ResumeLayout(false);
            this.gbBackup.PerformLayout();
            this.gbXmlBackup.ResumeLayout(false);
            this.gbXmlBackup.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuOverTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuReCount)).EndInit();
            this.panel1.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.FolderBrowserDialog folderDialog;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.OpenFileDialog ofDialog;
        private System.Windows.Forms.FolderBrowserDialog folderDialogXml;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.GroupBox gbBackup;
        private System.Windows.Forms.Button btnReconver;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ProgressBar pbStep;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnBackup;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtBackPath;
        private System.Windows.Forms.Button btnSel;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtFileName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox gbXmlBackup;
        private System.Windows.Forms.Button btnXmlReconver;
        private System.Windows.Forms.Button btnXmlSel;
        private System.Windows.Forms.Button btnXmlBackup;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtXmlBackPath;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtURL;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox textStationName;
        private System.Windows.Forms.TextBox txtStationID;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown nuOverTime;
        private System.Windows.Forms.CheckBox chkIsBackUpDB;
        private System.Windows.Forms.CheckBox chkIsBackUpConfig;
        private System.Windows.Forms.CheckBox chkIsOpenUpload;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown nuReCount;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label lbInfo;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button btnPlan;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label4;
    }
}