﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using Fpi.DB.SqlUtil;
using Fpi.Log;
using Fpi.Util.Compress;
using Fpi.Util.Security;
using Fpi.Xml;

namespace Fpi.DB.Manager
{
    /// <summary>
    /// 数据库类
    /// </summary>
    public class FpiDataBase
    {
        #region Properties

        /// <summary>
        /// 数据库名
        /// </summary>

        /// <summary>
        /// 数据库表列表
        /// </summary>
        private List<FpiTable> fpiTables = new List<FpiTable>();

        /// <summary>
        /// 临时数据库文件目录
        /// </summary>
        private string tempDbPath = string.Empty;

        /// <summary>
        /// 备份或恢复是否在执行
        /// </summary>
        private bool running = false;

        /// <summary>
        /// 备份进度委托
        /// </summary>
        /// <param name="index">当前值</param>
        /// <param name="count">最大值</param>
        /// <param name="info">信息</param>
        public delegate void BackupReportHander(int index, int count, string info);

        /// <summary>
        /// 备份中事件
        /// </summary>
        public event BackupReportHander OnBackuping;

        public delegate void BackupCompletedHander();

        /// <summary>
        /// 备份完成事件
        /// </summary>
        public event BackupCompletedHander OnBackupCompleted;

        /// <summary>
        /// 恢复进度委托
        /// </summary>
        /// <param name="index">当前值</param>
        /// <param name="count">最大值</param>
        /// <param name="info">信息</param>
        public delegate void RestoreReportHander(int index, int count, string info);

        /// <summary>
        /// 恢复中事件
        /// </summary>
        public event RestoreReportHander OnRestoring;

        public delegate void RestoreCompletedHander();

        /// <summary>
        /// 恢复完成事件
        /// </summary>
        public event RestoreCompletedHander OnRestoreCompleted;

        public delegate void ErrorHander(Exception ex);

        /// <summary>
        /// 报告出错事件
        /// </summary>
        public event ErrorHander ErrorEvent;
        public string DBNAME { get; } = string.Empty;
        #endregion

        #region 构造

        /// <summary>
        /// 构造
        /// </summary>
        private FpiDataBase()
        {
            tempDbPath = ConstConfig.AppPath + @"\dbtempfiles\";
            if(!string.IsNullOrEmpty(ConstConfig.GetValue("Database")))
            {
                DBNAME = ConstConfig.GetValue("Database");
            }


        }

        private static object syncObj = new object();
        private static FpiDataBase instance = null;
        public static FpiDataBase GetInstance()
        {
            lock(syncObj)
            {
                if(instance == null)
                {
                    instance = new FpiDataBase();
                }
            }
            return instance;
        }

        #endregion

        #region 表操作

        /// <summary>
        /// 添加表
        /// </summary>
        /// <param name="table"></param>
        public void AddTable(FpiTable table)
        {
            this.fpiTables.Add(table);
        }
        /// <summary>
        /// 创建表
        /// </summary>
        public void CreateTable()
        {
            foreach(FpiTable table in this.fpiTables)
            {
                table.CreateTable();
            }
        }
        /// <summary>
        /// 通过表名查找表
        /// </summary>
        /// <param name="tablename"></param>
        /// <returns></returns>
        public FpiTable FindTableByName(string tablename)
        {
            foreach(FpiTable table in this.fpiTables)
            {
                if(table.TableName == tablename)
                {
                    return table;
                }
            }
            return null;
        }
        #endregion

        #region 备份方法

        /// <summary>
        /// 异步备份数据库
        /// </summary>
        /// <param name="file">备份文件的完整路径（含文件名）</param>
        public void StartBackup(string file)
        {
            if(running)
            {
                return;//已经在运行
            }

            Thread th = new Thread(new ParameterizedThreadStart(BackupRun));
            th.Start(file);
        }

        /// <summary>
        /// 备份方法
        /// </summary>
        /// <param name="file">备份文件的完整路径（含文件名）</param>
        private void BackupRun(object file)
        {
            try
            {
                running = true;
                Backup(file.ToString());
            }
            catch(Exception ex)
            {
                if(ErrorEvent != null)
                {
                    ErrorEvent(ex);
                }
            }
            running = false;
        }

        /// <summary>
        /// 备份数据库
        /// </summary>
        /// <param name="fileName">备份文件的完整路径（含文件名）</param>
        public void Backup(string fileName)
        {
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();


            if(!Directory.Exists(tempDbPath))
            {
                Directory.CreateDirectory(tempDbPath);
            }
            if(OnBackuping != null)
            {
                OnBackuping(0, 1, "开始备份");
            }
            //备份表结构
            BackupDbStructure();

            //备份表数据
            BackupTableData();

            //压缩表数据
            if(OnBackuping != null)
            {
                OnBackuping(1, 1, "压缩文件");
            }
            CompressUtil.ZipFiles(tempDbPath, fileName, false);

            JudgeHistoryCount(new DirectoryInfo(new FileInfo(fileName).DirectoryName), "bak", 3);

            //删除临时数据
            if(OnBackuping != null)
            {
                OnBackuping(1, 1, "清除历史");
            }
            Directory.Delete(tempDbPath, true);

            //备份完成
            if(OnBackupCompleted != null)
            {
                OnBackupCompleted();
            }
            stopwatch.Stop();

            LogUtil.Debug("watch", "备份数据库时间：==" + stopwatch.ElapsedMilliseconds);
        }

        #endregion

        #region 恢复方法

        /// <summary>
        /// 异步恢复数据库
        /// </summary>
        /// <param name="zipfile">备份文件的完整路径（含文件名）</param>
        public void StartRestore(string zipfile)
        {
            Thread th = new Thread(new ParameterizedThreadStart(RestoreRun));
            th.Priority = ThreadPriority.Highest;
            th.Start(zipfile);
        }

        /// <summary>
        /// 恢复方法
        /// </summary>
        /// <param name="zipfile">备份文件的完整路径（含文件名）</param>
        private void RestoreRun(object zipfile)
        {
            try
            {
                Restore(zipfile.ToString());
            }
            catch(Exception ex)
            {
                if(ErrorEvent != null)
                {
                    ErrorEvent(ex);
                }
            }
        }

        /// <summary>
        /// 恢复数据库
        /// </summary>
        /// <param name="file">备份文件的完整路径（含文件名）</param>
        public void Restore(string zipfile)
        {
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();

            if(!Directory.Exists(tempDbPath))
            {
                Directory.CreateDirectory(tempDbPath);
            }
            //解压备份数据到临时目录
            if(OnRestoring != null)
            {
                OnRestoring(0, 1, "解压文件");
            }

            CompressUtil.DecompressToFiles(zipfile, tempDbPath);

            //如果没有数据库则创建
            DbFactory.GetInstance().CreateDb(DBNAME);

            //恢复数据库结构
            List<FpiTable> tableList = RestoreDbStructrue();

            //恢复数据
            RestoreTableData(tableList);

            //删除临时目录
            if(OnRestoring != null)
            {
                OnRestoring(1, 1, "清除临时文件");
            }

            Directory.Delete(tempDbPath, true);

            //恢复完成
            if(OnRestoreCompleted != null)
            {
                OnRestoreCompleted();
            }
            stopwatch.Stop();
            LogUtil.Debug("watch", "恢复数据库时间：==" + stopwatch.ElapsedMilliseconds);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 备份表结构
        /// </summary>
        /// <param name="path"></param>
        private void BackupDbStructure()
        {
            StreamWriter sw = null;
            string structFileName = tempDbPath + "structure";
            try
            {
                sw = new StreamWriter(structFileName);
                foreach(FpiTable table in fpiTables.ToArray())
                {
                    // sw.WriteLine("table:" + table.TableName);
                    sw.WriteLine(AesCryptHelper.GetInstance().Encrypt("table:" + table.TableName));
                    foreach(FpiColumn column in table.FpiColumns.ToArray())
                    {
                        StringBuilder sb = new StringBuilder();
                        sb.Append(column.Name);
                        sb.Append("\t");
                        sb.Append(column.PrimaryKey);
                        sb.Append("\t");
                        sb.Append(column.CanNull);
                        sb.Append("\t");
                        sb.Append(column.AutoColumn);
                        sb.Append("\t");
                        sb.Append(column.Type);
                        sb.Append("\t");
                        sb.Append(column.Length);
                        sb.Append("\t");
                        sb.Append(column.IndexName);
                        //sw.WriteLine(sb.ToString());
                        sw.WriteLine(AesCryptHelper.GetInstance().Encrypt(sb.ToString()));
                    }
                }
            }
            catch(Exception ex)
            {
                throw ex;
            }
            finally
            {
                sw.Flush();
                sw.Close();
                sw = null;
            }
        }

        /// <summary>
        /// 恢复表结构
        /// </summary>
        private List<FpiTable> RestoreDbStructrue()
        {
            List<FpiTable> tableList = new List<FpiTable>();
            string structFileName = tempDbPath + "structure";
            StreamReader sr = null;
            try
            {
                sr = new StreamReader(structFileName);
                string line = string.Empty;
                FpiTable table = null;
                //while (null != (line = sr.ReadLine()))
                string str = sr.ReadLine();

                while(null != (line = (str == null) ? null : (AesCryptHelper.GetInstance().Decrypt(str) == str ? str : AesCryptHelper.GetInstance().Decrypt(str))))
                {
                    if(line.StartsWith("table:"))
                    {
                        if(table != null)
                        {
                            table.DropTable();
                            table.CreateTable();
                            tableList.Add(table);
                        }
                        table = new FpiTable(line.Substring(6).Trim());
                    }
                    else
                    {
                        string[] columns = line.Split('\t');
                        FpiColumn column = new FpiColumn();
                        column.Name = columns[0];
                        column.PrimaryKey = bool.Parse(columns[1]);
                        column.CanNull = bool.Parse(columns[2]);
                        column.AutoColumn = bool.Parse(columns[3]);
                        column.Type = (ColumnType)Enum.Parse(typeof(ColumnType), columns[4]);
                        column.Length = int.Parse(columns[5]);
                        column.IndexName = columns[6];
                        if(table != null)
                        {
                            table.AddColumn(column);
                        }
                    }

                    str = sr.ReadLine();
                }
                if(table != null)
                {
                    table.DropTable();
                    table.CreateTable();
                    tableList.Add(table);
                }
            }
            catch(Exception ex)
            {
            }
            finally
            {
                if(sr != null)
                {
                    sr.Close();
                }
            }
            return tableList;
        }

        /// <summary>
        /// 备份指定表数据
        /// </summary>
        private void BackupTableData()
        {
            // 数据输出到临时目录
            int tabIndex = 1;
            foreach(FpiTable table in fpiTables.ToArray())
            {
                int rowcount = table.GetRecordCount();
                string tableText = "(" + tabIndex.ToString() + "/" + fpiTables.Count.ToString() + ")";
                tabIndex++;
                if(OnBackuping != null)
                {
                    OnBackuping(0, rowcount, tableText + string.Format("备份表{0}", table.TableName));
                }

                if(rowcount < 1)
                {
                    continue;
                }

                string tablefile = tempDbPath + table.TableName;
                StreamWriter sw = null;
                StringBuilder sb = new StringBuilder();
                StringBuilder sbInsert = new StringBuilder();
                StringBuilder sbValues = new StringBuilder();
                object[] values = new object[table.ColumnCount];

                string selectPreSql = table.BuildSelectString(false);
                string selectSql = string.Empty;

                int step = rowcount / 100;
                if(rowcount % 100 != 0)
                {
                    step++;
                }
                int reportnum = step;//报告进度点
                int index = 1;
                int readTimes = rowcount / 10000;//读数据库分次
                if(rowcount % 10000 != 0)
                {
                    readTimes++;
                }
                try
                {
                    sw = new StreamWriter(tablefile);
                    sw.WriteLine(rowcount.ToString());

                    for(int i = 1; i <= readTimes; i++)
                    {
                        int itemcount = 0;
                        List<FpiRow> rows = table.Search(null, "id", OrderType.Asc, 10000, i, out itemcount);
                        foreach(FpiRow row in rows.ToArray())
                        {
                            if(OnBackuping != null && index >= reportnum)
                            {
                                reportnum += step;
                                OnBackuping(index, rowcount, tableText + string.Format("备份表{0}", table.TableName));
                            }
                            //sb.Remove(0, sb.Length);

                            // sbInsert.Remove(0, sbInsert.Length);

                            // sbValues.Remove(0, sbValues.Length);
                            sb = new StringBuilder();
                            sbInsert = new StringBuilder();
                            sbValues = new StringBuilder();
                            sbInsert.Append("insert into ").Append(table.TableName).Append("(");

                            foreach(FpiColumn column in table.FpiColumns.ToArray())
                            {

                                object value = row.GetFieldValue(column.Name);

                                if(value == null || value is DBNull)
                                {

                                    sb.Append("DBNull");
                                }
                                else
                                {
                                    sb.Append(value.ToString());
                                }
                                sb.Append("\t");
                            }
                            sb.Remove(sb.Length - 1, 1);


                            //sw.WriteLine(sb.ToString());
                            sw.WriteLine(AesCryptHelper.GetInstance().Encrypt(sb.ToString().ToString()));
                            //sw.WriteLine(sb.ToString());
                            sw.Flush();
                            index++;
                        }
                    }
                }
                catch(Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    sw.Flush();
                    sw.Close();
                    sw = null;
                    GC.Collect();
                }
            }
        }

        /// <summary>
        /// 恢复表数据
        /// </summary>
        private void RestoreTableData(List<FpiTable> tableList)
        {
            StreamReader sr = null;
            int tableIndex = 1;
            foreach(FpiTable table in tableList)
            {
                try
                {
                    string tableText = "(" + tableIndex.ToString() + "/" + tableList.Count.ToString() + ")";
                    tableIndex++;
                    if(OnRestoring != null)
                    {
                        OnRestoring(0, 1, tableText + string.Format("恢复表{0}", table.TableName));
                    }
                    string fileName = tempDbPath + table.TableName;
                    if(!File.Exists(fileName))
                    {
                        continue;
                    }
                    FileInfo file = new FileInfo(fileName);
                    sr = new StreamReader(file.FullName);
                    string str = sr.ReadLine();
                    int rowcount = int.Parse(str);

                    LogUtil.Debug("watch", table.TableName + "数据" + rowcount + "条");

                    int index = 1;
                    int step = rowcount / 100 + 1;

                    int reportnum = step;
                    List<string> insertSqlList = new List<string>();
                    // while (null != (str = sr.ReadLine()))
                    string read = sr.ReadLine();


                    while(null != (str = (null == read ? null : (AesCryptHelper.GetInstance().Decrypt(read) == read ? read : AesCryptHelper.GetInstance().Decrypt(read)))))
                    {

                        if(OnRestoring != null && index >= reportnum)
                        {
                            reportnum += step;
                            OnRestoring(index, rowcount, tableText + string.Format("恢复表{0}", file.Name));
                        }

                        string[] values = str.Split('\t');
                        FpiRow row = new FpiRow();
                        for(int i = 0; i < values.Length; i++)
                        {
                            row.SetFieldValue(table.FpiColumns[i], values[i]);
                        }
                        insertSqlList.Add(table.GetInsertSql(row));
                        if(insertSqlList.Count > 1000)
                        {
                            DbAccess.BatExecuteNonQuery(insertSqlList);
                            insertSqlList.Clear();

                        }
                        index++;
                        read = sr.ReadLine();




                    }

                    if(insertSqlList.Count > 0)
                    {
                        DbAccess.BatExecuteNonQuery(insertSqlList);
                        insertSqlList.Clear();
                    }

                }
                catch(Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    if(sr != null)
                    {
                        sr.Close();
                        sr = null;
                    }
                }

            }
        }

        #endregion

        /// <summary>
        /// 判断文件份数
        /// </summary>
        /// <param name="dirpath">源文件路径</param>
        /// <param name="nametype">文件类型</param>
        /// <param name="exstr">文件后缀</param>
        public static void JudgeHistoryCount(DirectoryInfo dirpath, string exstr, int count)
        {
            string oldfilename = "";
            DateTime oldtime = new DateTime();
            bool isfirst = true;
            List<FileInfo> fileInfoList = new List<FileInfo>();
            FileInfo[] fileInfos = dirpath.GetFiles();
            if(fileInfos == null || fileInfos.Length < count + 1)
            {
                return;
            }

            foreach(FileInfo item in fileInfos)
            {
                if(item.FullName.Contains(exstr))
                {
                    fileInfoList.Add(item);
                }
            }
            foreach(FileInfo file in fileInfoList)
            {
                if(isfirst)
                {
                    oldfilename = file.FullName;
                    oldtime = file.CreationTime;
                    isfirst = false;
                }
                else
                {
                    if(file.CreationTime < oldtime)
                    {
                        oldfilename = file.FullName;
                        oldtime = file.CreationTime;
                    }
                }
            }
            File.Delete(oldfilename);
        }
    }
}
