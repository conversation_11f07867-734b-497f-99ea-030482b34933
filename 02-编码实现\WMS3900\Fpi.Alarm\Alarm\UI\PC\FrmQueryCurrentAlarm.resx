<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnClear.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnClear.Location" type="System.Drawing.Point, System.Drawing">
    <value>724, 4</value>
  </data>
  <data name="btnClear.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>94, 30</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnClear.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="btnClear.Text" xml:space="preserve">
    <value>报警确认</value>
  </data>
  <data name="&gt;&gt;btnClear.Name" xml:space="preserve">
    <value>btnClear</value>
  </data>
  <data name="&gt;&gt;btnClear.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnClear.Parent" xml:space="preserve">
    <value>pnlTop</value>
  </data>
  <data name="&gt;&gt;btnClear.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnRefresh_.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="btnRefresh_.Location" type="System.Drawing.Point, System.Drawing">
    <value>834, 4</value>
  </data>
  <data name="btnRefresh_.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="btnRefresh_.Size" type="System.Drawing.Size, System.Drawing">
    <value>94, 30</value>
  </data>
  <data name="btnRefresh_.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="btnRefresh_.Text" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="&gt;&gt;btnRefresh_.Name" xml:space="preserve">
    <value>btnRefresh_</value>
  </data>
  <data name="&gt;&gt;btnRefresh_.Type" xml:space="preserve">
    <value>Sunny.UI.UIButton, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;btnRefresh_.Parent" xml:space="preserve">
    <value>pnlTop</value>
  </data>
  <data name="&gt;&gt;btnRefresh_.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pnlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="pnlTop.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="pnlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 35</value>
  </data>
  <data name="pnlTop.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="pnlTop.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="pnlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>938, 39</value>
  </data>
  <data name="pnlTop.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="pnlTop.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlTop.Name" xml:space="preserve">
    <value>pnlTop</value>
  </data>
  <data name="&gt;&gt;pnlTop.Type" xml:space="preserve">
    <value>Sunny.UI.UIPanel, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;pnlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlTop.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dgvAlarmInfo.ColumnHeadersHeight" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="dgvAlarmInfo.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="dgvAlarmInfo.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="dgvAlarmInfo.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 74</value>
  </data>
  <data name="dgvAlarmInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>938, 520</value>
  </data>
  <data name="dgvAlarmInfo.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;dgvAlarmInfo.Name" xml:space="preserve">
    <value>dgvAlarmInfo</value>
  </data>
  <data name="&gt;&gt;dgvAlarmInfo.Type" xml:space="preserve">
    <value>Sunny.UI.UIDataGridView, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;dgvAlarmInfo.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dgvAlarmInfo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>940, 595</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAASmZ8/76Vlv8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAa5zD/x6J6P9LeqP/yJaT/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAEu0/v9Rtf//IInp/0t6ov/GlZL/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAUbf+/1Gz//8dh+b/Tnqg/8qXkv8AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABRt/7/TrL//x+J5v9Oe6L/uZSX/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFK4/v9Lsf//J4fZ/19qdv8AAAAAsIV//8Cf
        lP/An5b/vJiO/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVb3//7XW7f+/nZL/u5uM/+fa
        wv///+P////l//362v/Yw7P/tY2F/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzqeV//3u
        vv///9j////a////2////+b////7/+rd3P+ug3//AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGg
        kf/73Kj//vfQ////2////+P////4/////f////3/xqmc/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGg
        kf/+46z/8cSR//zyyv///93////k////9/////f////p/+7ly/+5lIz/AAAAAAAAAAAAAAAAAAAAAAAA
        AADCoZH//+au/+61gf/33K7//v3Y////3////+P////k////4P/z7NL/u5aO/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAvJeM//vnt//0x5H/8smU//jluf/+/Nj////d////3P///+D/4tK6/7aOhv8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADZw6n///7l//fcuP/yyZT/9dSl//rovf/99Mn//fvW/7aQif8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAtY2F/+je3f///vL/+dij//TEjP/51J///eq4/9C0n/+4kIb/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACtgn//yaqe/+/gt//v37L/586s/7iQhv+4kIb/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC6lor/u5iM/7eRiP8AAAAAAAAAAAAA
        AAAAAAAAn/8AAA//AAAH/wAAg/8AAMH/AADhDwAA8AMAAPwBAAD8AQAA+AAAAPgAAAD4AAAA/AEAAPwB
        AAD+AwAA/48AAA==
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 35, 1, 1</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>当前报警查询</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FrmQueryCurrentAlarm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>Sunny.UI.UIForm, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
</root>