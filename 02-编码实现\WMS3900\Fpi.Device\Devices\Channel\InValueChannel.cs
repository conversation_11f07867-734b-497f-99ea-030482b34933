﻿//==================================================================================================
//类名：     InSimulateDataChannel   
//创建人:    曹旭
//创建时间:  2012-9-26 15:11:07
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using System;

namespace Fpi.Devices.Channel
{
    public class InValueChannel : ValueChannel
    {
        public InValueChannel()
        {
            this.ChannelType = eDeviceChannelType.InValue;
        }

        /// <summary>
        /// 设置数据通道实时数据（将采集到得数据设置到变量上）
        /// </summary>
        public override void SetRealValue()
        {
            if(this.ValueNode == null)
            {
                return;
            }

            double value = double.NaN;

            if(this.ChannelValue != null)
            {
                value = this.DataProcessor != null
                    ? this.DataProcessor.InputProcessData(Convert.ToDouble(this.ChannelValue))
                    : Convert.ToDouble(this.ChannelValue);
            }

            if(string.IsNullOrEmpty(this.UnitId))
            {
                this.ValueNode.SetValue(value);
            }
            else
            {
                this.ValueNode.SetValue(value, this.UnitId);
            }
        }
    }

    //public class ValidDataAndState
    //{
    //    /// <summary>
    //    /// 周期有效数据值
    //    /// </summary>
    //    public double CycleValue
    //    {
    //        get;
    //        set;
    //    }

    //    /// <summary>
    //    /// 测量值标示（具体含义由业务层通过自定义枚举赋值）
    //    /// </summary>
    //    public int NodeState
    //    {
    //        get;
    //        set;
    //    }
    //}
}
