using System;
using System.Windows.Forms;
using Fpi.Communication.Commands.Config;
using Fpi.Communication.Properties;
using Fpi.UI.Common.PC;
using Fpi.Util.Reflection;
using Fpi.Xml;

namespace Fpi.Communication.UI.PC.CommandForms
{
    public partial class CommandExplore : Form
    {
        public CommandExplore()
        {
            InitializeComponent();

            Type baseType = typeof(CommandManager);
            IdNameNode node = new IdNameNode(baseType.FullName, baseType.Name);
            node.Tag = baseType;
            this.cmbAsm.Items.Add(node);

            Type[] cmdTypes = ReflectionHelper.GetChildTypes(baseType);
            if(cmdTypes != null)
            {
                foreach(Type type in cmdTypes)
                {
                    node = new IdNameNode(type.FullName, type.Name);
                    node.Tag = type;
                    this.cmbAsm.Items.Add(node);
                }
            }
        }

        public string CommandString { get; set; }

        private void cmbAsm_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.listView.Items.Clear();
            IdNameNode node = this.cmbAsm.SelectedItem as IdNameNode;
            Type type = node.Tag as Type;

            string strMng = string.Empty;
            string strCmd = string.Empty;
            if(!string.IsNullOrEmpty(CommandString))
            {
                int index = CommandString.LastIndexOf(',');
                strMng = CommandString.Substring(0, index);
                strCmd = CommandString.Substring(index + 1);
            }

            try
            {
                CommandManager cmdMng = (CommandManager)ReflectionHelper.CreateInstance(type);
                if(cmdMng.commandDescs != null)
                {
                    foreach(IdNameNode cmd in cmdMng.commandDescs)
                    {
                        ListViewItem lvi = new ListViewItem(new string[] { cmd.id, cmd.name });
                        lvi.Tag = cmd;
                        this.listView.Items.Add(lvi);

                        if(type.FullName == strMng && cmd.id == strCmd)
                        {
                            lvi.Checked = true;
                        }
                    }
                }
            }
            catch
            {
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if(this.listView.CheckedItems.Count > 0)
                {
                    IdNameNode mng = this.cmbAsm.SelectedItem as IdNameNode;
                    Type type = mng.Tag as Type;
                    IdNameNode cmd = this.listView.CheckedItems[0].Tag as IdNameNode;

                    CommandString = type.FullName + "," + cmd.id;
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }
    }
}