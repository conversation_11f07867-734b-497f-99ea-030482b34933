GET /LAPI/V1.0/PeopleLibraries/TimeTemplates/0 HTTP/1.1
cache-control: no-cache
Postman-Token: 921167ef-cdec-42d6-aef7-2c53f40b9333
User-Agent: PostmanRuntime/6.4.1
Accept: */*
Host: *************
accept-encoding: gzip, deflate
Connection: keep-alive

HTTP/1.1 200 Ok
Content-Length: 5547
Content-Type: text/plain
Connection: close
X-Frame-Options: SAMEORIGIN

{
"Response": {
	"ResponseURL": "/LAPI/V1.0/PeopleLibraries/TimeTemplates/0",
	"CreatedID": -1, 
	"ResponseCode": 0,
 	"SubResponseCode": 0,
 	"ResponseString": "Succeed",
	"StatusCode": 0,
	"StatusString": "Succeed",
	"Data": {
	"ID":	0,
	"Name":	"default",
	"LastChange":	0,
	"WeekPlan":	{
		"Enabled":	1,
		"Num":	7,
		"Days":	[{
				"ID":	0,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"11:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"13:00:00",
						"End":	"21:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"22:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}, {
				"ID":	1,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"02:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"07:00:00",
						"End":	"11:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"13:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}, {
				"ID":	2,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"02:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"06:00:00",
						"End":	"08:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"09:00:00",
						"End":	"12:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"13:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}, {
				"ID":	3,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"02:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"06:00:00",
						"End":	"13:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"14:00:00",
						"End":	"17:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"18:00:00",
						"End":	"20:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"21:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}, {
				"ID":	4,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"09:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"11:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}, {
				"ID":	5,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"03:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"06:00:00",
						"End":	"15:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"16:00:00",
						"End":	"17:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"18:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}, {
				"ID":	6,
				"Num":	8,
				"TimeSectionInfos":	[{
						"Begin":	"00:00:00",
						"End":	"10:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"12:00:00",
						"End":	"19:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"21:00:00",
						"End":	"23:59:59",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}, {
						"Begin":	"24:00:00",
						"End":	"24:00:00",
						"ArmingType":	0
					}]
			}]
	},
	"Exception":	{
		"Enabled":	0,
		"Num":	0,
		"ExceptionDays":	[]
	}
}
	}
}
