using System;

namespace Fpi.Communication.Exceptions
{
    /// <summary>
    /// 
    /// </summary>
    public class PortException : CommunicationException
    {
        public PortException()
            : base()
        {
        }

        public PortException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public PortException(string message)
            : base(message)
        {
        }
    }
}