﻿using System;
using System.Collections.Generic;
using System.Linq;
using AForge.Video.DirectShow;

namespace Fpi.Camera
{
    /// <summary>
    /// USB摄像机管理类
    /// </summary>
    internal class USBCameraManager
    {
        #region 字段属性

        /// <summary>
        /// USB摄像机列表
        /// </summary>
        public List<USBCamera> CameraList { get; set; } = new List<USBCamera>();

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static USBCameraManager _instance;
        public static USBCameraManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new USBCameraManager();
                }
            }
            return _instance;
        }

        private USBCameraManager()
        {
            Initialize();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化USB摄像机列表
        /// </summary>
        public void InitCameraList()
        {
            // 获取所有视频输入设备
            FilterInfoCollection VideoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

            if(VideoDevices.Count == 0)
            {
                throw new Exception("未检测到任何USB摄像机设备！");
            }

            foreach(FilterInfo VideoDevice in VideoDevices)
            {
                CameraList.Add(new USBCamera(VideoDevice.MonikerString));
            }
        }

        /// <summary>
        /// 清空USB摄像机列表
        /// </summary>
        public void ClearCameraList()
        {
            foreach(var camera in CameraList)
            {
                // 停止已有视频源
                if(camera.IsPreviewing)
                {
                    camera.StopPreview();
                }
            }

            CameraList.Clear();
        }

        /// <summary>
        /// 刷新USB摄像机列表
        /// </summary>
        public void RefreshCameraList()
        {
            ClearCameraList();
            InitCameraList();
        }

        /// <summary>
        /// 根据PID获取USB摄像机
        /// </summary>
        /// <param name="pid"></param>
        /// <returns></returns>
        public USBCamera GetUSBCameraByPID(string pid)
        {
            return CameraList.FirstOrDefault(camera => camera.Name == pid);
        }

        #endregion

        #region Initialize

        public void Initialize()
        {
            try
            {
                InitCameraList();
            }
            catch(Exception e)
            {
                CameraLogHelper.Info($"初始化USB摄像机列表出错：{e.Message}");
            }
        }

        #endregion
    }
}