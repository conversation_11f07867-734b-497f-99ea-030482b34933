using System;

namespace Fpi.Communication.Exceptions
{
    /// <summary>
    ///
    /// </summary>
    public class WebException : CommunicationException
    {
        public WebException()
            : base()
        {
        }

        public WebException(string message)
            : base(message)
        {
        }

        public WebException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
    }
}