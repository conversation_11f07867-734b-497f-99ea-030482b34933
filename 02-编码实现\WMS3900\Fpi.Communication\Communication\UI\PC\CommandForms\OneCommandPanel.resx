<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;writePanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;readPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;writePanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="readPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>364, 68</value>
  </data>
  <data name="&gt;&gt;readPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnWrite.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnRead.Location" type="System.Drawing.Point, System.Drawing">
    <value>205, 10</value>
  </data>
  <data name="buttonPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 237</value>
  </data>
  <data name="&gt;&gt;buttonPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnRead.Text" xml:space="preserve">
    <value>读取</value>
  </data>
  <data name="&gt;&gt;writePanel.Name" xml:space="preserve">
    <value>writePanel</value>
  </data>
  <data name="writePanel.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="btnWrite.Text" xml:space="preserve">
    <value>设置</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="readPanel.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnRead.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonPanel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;writePanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="writePanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>364, 169</value>
  </data>
  <data name="buttonPanel.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;btnWrite.Name" xml:space="preserve">
    <value>btnWrite</value>
  </data>
  <data name="&gt;&gt;btnRead.Parent" xml:space="preserve">
    <value>buttonPanel</value>
  </data>
  <data name="&gt;&gt;btnWrite.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;readPanel.Name" xml:space="preserve">
    <value>readPanel</value>
  </data>
  <data name="&gt;&gt;btnRead.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnWrite.Parent" xml:space="preserve">
    <value>buttonPanel</value>
  </data>
  <data name="buttonPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>364, 42</value>
  </data>
  <data name="btnRead.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="&gt;&gt;readPanel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;btnWrite.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>364, 279</value>
  </data>
  <data name="&gt;&gt;buttonPanel.Name" xml:space="preserve">
    <value>buttonPanel</value>
  </data>
  <data name="btnWrite.Location" type="System.Drawing.Point, System.Drawing">
    <value>286, 10</value>
  </data>
  <data name="btnWrite.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnWrite.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="&gt;&gt;btnRead.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="writePanel.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnRead.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="readPanel.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="&gt;&gt;btnRead.Name" xml:space="preserve">
    <value>btnRead</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>OneCommandPanel</value>
  </data>
  <data name="writePanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 68</value>
  </data>
  <data name="&gt;&gt;buttonPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="readPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;buttonPanel.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>