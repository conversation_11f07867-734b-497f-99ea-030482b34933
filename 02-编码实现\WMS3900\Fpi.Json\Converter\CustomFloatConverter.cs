﻿using System;
using Newtonsoft.Json;

namespace Fpi.Json.Converter
{
    /// <summary>
    /// 自定义浮点数转换器
    /// </summary>
    public class CustomFloatConverter : JsonConverter
    {
        private readonly int _decimalPlaces;

        public CustomFloatConverter(int decimalPlaces)
        {
            _decimalPlaces = decimalPlaces;
        }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(float) || objectType == typeof(double) ||
                   objectType == typeof(decimal) ||
                   objectType == typeof(float?) || objectType == typeof(double?) ||
                   objectType == typeof(decimal?);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            throw new NotImplementedException("Unnecessary because CanRead is false. The method should never be called.");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            if(value is float floatValue)
            {
                writer.WriteValue(Math.Round(floatValue, _decimalPlaces, MidpointRounding.AwayFromZero));
            }
            else if(value is double doubleValue)
            {
                writer.WriteValue(Math.Round(doubleValue, _decimalPlaces, MidpointRounding.AwayFromZero));
            }
            else if(value is decimal decimalValue)
            {
                writer.WriteValue(Math.Round(decimalValue, _decimalPlaces, MidpointRounding.AwayFromZero));
            }
            else if(value is float? && value != null)
            {
                writer.WriteValue(Math.Round((float)value, _decimalPlaces, MidpointRounding.AwayFromZero));
            }
            else if(value is double? && value != null)
            {
                writer.WriteValue(Math.Round((double)value, _decimalPlaces, MidpointRounding.AwayFromZero));
            }
            else if(value is decimal? && value != null)
            {
                writer.WriteValue(Math.Round((decimal)value, _decimalPlaces, MidpointRounding.AwayFromZero));
            }
            else
            {
                writer.WriteNull();
            }
        }
    }
}