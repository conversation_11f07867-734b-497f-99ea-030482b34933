﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Adam" xml:space="preserve">
    <value>亚当模块</value>
  </data>
  <data name="Channel" xml:space="preserve">
    <value>通道</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>关闭</value>
  </data>
  <data name="Config" xml:space="preserve">
    <value>配置</value>
  </data>
  <data name="ConfigSave" xml:space="preserve">
    <value>配置已保存,并在下次启动生效!</value>
  </data>
  <data name="DeleteDeviceAsk" xml:space="preserve">
    <value>是否确定删除选中设备?</value>
  </data>
  <data name="DeviceId" xml:space="preserve">
    <value>设备Id</value>
  </data>
  <data name="DeviceName" xml:space="preserve">
    <value>设备名称</value>
  </data>
  <data name="DeviceType" xml:space="preserve">
    <value>设备类型</value>
  </data>
  <data name="ExistIdDevice" xml:space="preserve">
    <value>已存在编号为 {0} 的设备</value>
  </data>
  <data name="Hub" xml:space="preserve">
    <value>集线器</value>
  </data>
  <data name="IOImpl" xml:space="preserve">
    <value>IO实现</value>
  </data>
  <data name="IOTypeConflict" xml:space="preserve">
    <value>I/O类型冲突:{0}</value>
  </data>
  <data name="NotAllowEmpty" xml:space="preserve">
    <value>{0}不允许为空,请重新输入</value>
  </data>
  <data name="NotSelectDevice" xml:space="preserve">
    <value>未选中需要删除的设备</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>打开</value>
  </data>
  <data name="ReadSwitchInFail" xml:space="preserve">
    <value>读取开关量输入失败</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Search" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAVtJREFUOE/Nki12
        g0AQx3MDuEFzA3DFBYkLrnHgWgcStytxu7IOZBx7g6ysREaurBxZN51ZAg0vqcmr6Ly3gpnhN//52Gz+
        0lRvXdUaiF6Uu+aqztp7/pvau4My8IUYRNUK8Jt/BeAqUhvM33qMc41RJoAT9NHiPf9NdfluEam6sQ5r
        bXGbSeSkxf/hUPbj4l8BhBpCTizlj4L0QCpoFmVjsG7JX5MygjK4EEO4AkiqCADI/dszeBAnBokYa2qL
        zcdGwJyAszoPUd1JcgU2OzrUJJOrhanESg2SAePZ0YMpxoBkinmA7i14iZfhzTJDSgqehVti1FKcadwS
        OExqjOnbA/yQyNwnoJ0HRUnXPXJLHHcOpiG31s9jAnCP3B/9zBJLSRv4VwBe2dyCV3m8DHJuoWj6sWx6
        TEuNvHt+TzsB9JZdF8LA/lU53g7H+VLp3P2lPmz7qlsf06Okb4/oi1FL11wVAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Setting" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="SimuTransDigital" xml:space="preserve">
    <value>模数转换对应关系须符合逻辑</value>
  </data>
  <data name="SystemPrompt" xml:space="preserve">
    <value>系统提示</value>
  </data>
  <data name="UnitEmpty" xml:space="preserve">
    <value>单位不能为空</value>
  </data>
  <data name="WriteSwitchInFail" xml:space="preserve">
    <value>写入开关量输出失败</value>
  </data>
</root>