﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Fpi.Devices.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Fpi.Devices.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 亚当模块 的本地化字符串。
        /// </summary>
        internal static string Adam {
            get {
                return ResourceManager.GetString("Adam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通道 的本地化字符串。
        /// </summary>
        internal static string Channel {
            get {
                return ResourceManager.GetString("Channel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关闭 的本地化字符串。
        /// </summary>
        internal static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 配置 的本地化字符串。
        /// </summary>
        internal static string Config {
            get {
                return ResourceManager.GetString("Config", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 配置已保存,并在下次启动生效! 的本地化字符串。
        /// </summary>
        internal static string ConfigSave {
            get {
                return ResourceManager.GetString("ConfigSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 是否确定删除选中设备? 的本地化字符串。
        /// </summary>
        internal static string DeleteDeviceAsk {
            get {
                return ResourceManager.GetString("DeleteDeviceAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备Id 的本地化字符串。
        /// </summary>
        internal static string DeviceId {
            get {
                return ResourceManager.GetString("DeviceId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备名称 的本地化字符串。
        /// </summary>
        internal static string DeviceName {
            get {
                return ResourceManager.GetString("DeviceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备类型 的本地化字符串。
        /// </summary>
        internal static string DeviceType {
            get {
                return ResourceManager.GetString("DeviceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的设备 的本地化字符串。
        /// </summary>
        internal static string ExistIdDevice {
            get {
                return ResourceManager.GetString("ExistIdDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 集线器 的本地化字符串。
        /// </summary>
        internal static string Hub {
            get {
                return ResourceManager.GetString("Hub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 IO实现 的本地化字符串。
        /// </summary>
        internal static string IOImpl {
            get {
                return ResourceManager.GetString("IOImpl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 I/O类型冲突:{0} 的本地化字符串。
        /// </summary>
        internal static string IOTypeConflict {
            get {
                return ResourceManager.GetString("IOTypeConflict", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0}不允许为空,请重新输入 的本地化字符串。
        /// </summary>
        internal static string NotAllowEmpty {
            get {
                return ResourceManager.GetString("NotAllowEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未选中需要删除的设备 的本地化字符串。
        /// </summary>
        internal static string NotSelectDevice {
            get {
                return ResourceManager.GetString("NotSelectDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开 的本地化字符串。
        /// </summary>
        internal static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 读取开关量输入失败 的本地化字符串。
        /// </summary>
        internal static string ReadSwitchInFail {
            get {
                return ResourceManager.GetString("ReadSwitchInFail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap Search {
            get {
                object obj = ResourceManager.GetObject("Search", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 设置 的本地化字符串。
        /// </summary>
        internal static string Setting {
            get {
                return ResourceManager.GetString("Setting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 模数转换对应关系须符合逻辑 的本地化字符串。
        /// </summary>
        internal static string SimuTransDigital {
            get {
                return ResourceManager.GetString("SimuTransDigital", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统提示 的本地化字符串。
        /// </summary>
        internal static string SystemPrompt {
            get {
                return ResourceManager.GetString("SystemPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 单位不能为空 的本地化字符串。
        /// </summary>
        internal static string UnitEmpty {
            get {
                return ResourceManager.GetString("UnitEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 写入开关量输出失败 的本地化字符串。
        /// </summary>
        internal static string WriteSwitchInFail {
            get {
                return ResourceManager.GetString("WriteSwitchInFail", resourceCulture);
            }
        }
    }
}
