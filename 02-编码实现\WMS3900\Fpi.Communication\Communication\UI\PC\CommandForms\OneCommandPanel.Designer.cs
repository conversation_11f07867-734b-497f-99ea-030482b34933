﻿namespace Fpi.Communication.UI.PC.CommandForms
{
    partial class OneCommandPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(OneCommandPanel));
            this.buttonPanel = new System.Windows.Forms.Panel();
            this.btnWrite = new System.Windows.Forms.Button();
            this.btnRead = new System.Windows.Forms.Button();
            this.writePanel = new System.Windows.Forms.Panel();
            this.readPanel = new System.Windows.Forms.Panel();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonPanel
            // 
            this.buttonPanel.AccessibleDescription = null;
            this.buttonPanel.AccessibleName = null;
            resources.ApplyResources(this.buttonPanel, "buttonPanel");
            this.buttonPanel.BackgroundImage = null;
            this.buttonPanel.Controls.Add(this.btnWrite);
            this.buttonPanel.Controls.Add(this.btnRead);
            this.buttonPanel.Font = null;
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.buttonPanel_Paint);
            // 
            // btnWrite
            // 
            this.btnWrite.AccessibleDescription = null;
            this.btnWrite.AccessibleName = null;
            resources.ApplyResources(this.btnWrite, "btnWrite");
            this.btnWrite.BackgroundImage = null;
            this.btnWrite.Font = null;
            this.btnWrite.Name = "btnWrite";
            this.btnWrite.UseVisualStyleBackColor = true;
            this.btnWrite.Click += new System.EventHandler(this.btnWrite_Click);
            // 
            // btnRead
            // 
            this.btnRead.AccessibleDescription = null;
            this.btnRead.AccessibleName = null;
            resources.ApplyResources(this.btnRead, "btnRead");
            this.btnRead.BackgroundImage = null;
            this.btnRead.Font = null;
            this.btnRead.Name = "btnRead";
            this.btnRead.UseVisualStyleBackColor = true;
            this.btnRead.Click += new System.EventHandler(this.btnRead_Click);
            // 
            // writePanel
            // 
            this.writePanel.AccessibleDescription = null;
            this.writePanel.AccessibleName = null;
            resources.ApplyResources(this.writePanel, "writePanel");
            this.writePanel.BackColor = System.Drawing.SystemColors.Control;
            this.writePanel.BackgroundImage = null;
            this.writePanel.Font = null;
            this.writePanel.Name = "writePanel";
            // 
            // readPanel
            // 
            this.readPanel.AccessibleDescription = null;
            this.readPanel.AccessibleName = null;
            resources.ApplyResources(this.readPanel, "readPanel");
            this.readPanel.BackColor = System.Drawing.SystemColors.Control;
            this.readPanel.BackgroundImage = null;
            this.readPanel.Font = null;
            this.readPanel.Name = "readPanel";
            this.readPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.readPanel_Paint);
            // 
            // OneCommandPanel
            // 
            this.AccessibleDescription = null;
            this.AccessibleName = null;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImage = null;
            this.Controls.Add(this.writePanel);
            this.Controls.Add(this.readPanel);
            this.Controls.Add(this.buttonPanel);
            this.Name = "OneCommandPanel";
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel buttonPanel;
        private System.Windows.Forms.Button btnWrite;
        private System.Windows.Forms.Button btnRead;
        private System.Windows.Forms.Panel writePanel;
        private System.Windows.Forms.Panel readPanel;

    }
}
