﻿namespace Fpi.HB.UI
{
    partial class FrmDeviceParam
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmDeviceParam));
            this.imageList = new System.Windows.Forms.ImageList(this.components);
            this.tvDevices = new Sunny.UI.UITreeView();
            this.lineDeviceListTitle = new Sunny.UI.UILine();
            this.pnlDeviceList = new Sunny.UI.UIPanel();
            this.uiLine1 = new Sunny.UI.UILine();
            this.pnlDeviceUI = new Sunny.UI.UIPanel();
            this.pnlDeviceList.SuspendLayout();
            this.SuspendLayout();
            // 
            // imageList
            // 
            this.imageList.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList.ImageStream")));
            this.imageList.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList.Images.SetKeyName(0, "equip");
            this.imageList.Images.SetKeyName(1, "select");
            // 
            // tvDevices
            // 
            this.tvDevices.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tvDevices.FillColor = System.Drawing.Color.White;
            this.tvDevices.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tvDevices.Location = new System.Drawing.Point(0, 32);
            this.tvDevices.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.tvDevices.MinimumSize = new System.Drawing.Size(1, 1);
            this.tvDevices.MultiLanguageSupport = false;
            this.tvDevices.Name = "tvDevices";
            this.tvDevices.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.tvDevices.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.tvDevices.ScrollBarStyleInherited = false;
            this.tvDevices.ShowLines = true;
            this.tvDevices.ShowText = false;
            this.tvDevices.Size = new System.Drawing.Size(300, 568);
            this.tvDevices.Style = Sunny.UI.UIStyle.Custom;
            this.tvDevices.TabIndex = 0;
            this.tvDevices.Text = "设备列表";
            this.tvDevices.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.tvDevices.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.tvDevices_AfterSelect);
            // 
            // lineDeviceListTitle
            // 
            this.lineDeviceListTitle.BackColor = System.Drawing.Color.Transparent;
            this.lineDeviceListTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lineDeviceListTitle.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lineDeviceListTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lineDeviceListTitle.Location = new System.Drawing.Point(0, 0);
            this.lineDeviceListTitle.MinimumSize = new System.Drawing.Size(1, 1);
            this.lineDeviceListTitle.Name = "lineDeviceListTitle";
            this.lineDeviceListTitle.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.lineDeviceListTitle.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.lineDeviceListTitle.Size = new System.Drawing.Size(300, 32);
            this.lineDeviceListTitle.TabIndex = 2;
            this.lineDeviceListTitle.Text = "设备列表";
            this.lineDeviceListTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlDeviceList
            // 
            this.pnlDeviceList.Controls.Add(this.tvDevices);
            this.pnlDeviceList.Controls.Add(this.lineDeviceListTitle);
            this.pnlDeviceList.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlDeviceList.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlDeviceList.Location = new System.Drawing.Point(0, 0);
            this.pnlDeviceList.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDeviceList.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDeviceList.Name = "pnlDeviceList";
            this.pnlDeviceList.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlDeviceList.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlDeviceList.Size = new System.Drawing.Size(300, 600);
            this.pnlDeviceList.TabIndex = 0;
            this.pnlDeviceList.Text = null;
            this.pnlDeviceList.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiLine1
            // 
            this.uiLine1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.uiLine1.Dock = System.Windows.Forms.DockStyle.Top;
            this.uiLine1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLine1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine1.Location = new System.Drawing.Point(300, 0);
            this.uiLine1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine1.Name = "uiLine1";
            this.uiLine1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uiLine1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uiLine1.Size = new System.Drawing.Size(500, 32);
            this.uiLine1.TabIndex = 3;
            this.uiLine1.Text = "设备参数状态";
            this.uiLine1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlDeviceUI
            // 
            this.pnlDeviceUI.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlDeviceUI.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlDeviceUI.Location = new System.Drawing.Point(300, 32);
            this.pnlDeviceUI.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDeviceUI.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDeviceUI.Name = "pnlDeviceUI";
            this.pnlDeviceUI.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlDeviceUI.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlDeviceUI.Size = new System.Drawing.Size(500, 568);
            this.pnlDeviceUI.TabIndex = 0;
            this.pnlDeviceUI.Text = null;
            this.pnlDeviceUI.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // FrmDeviceParamold
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.ControlBox = false;
            this.Controls.Add(this.pnlDeviceUI);
            this.Controls.Add(this.uiLine1);
            this.Controls.Add(this.pnlDeviceList);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(3, 3, 3, 3);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmDeviceParamold";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "设备参数状态查询";
            this.Load += new System.EventHandler(this.DeviceParamForm_Load);
            this.pnlDeviceList.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.ImageList imageList;
        private Sunny.UI.UITreeView tvDevices;
        private Sunny.UI.UILine lineDeviceListTitle;
        private Sunny.UI.UIPanel pnlDeviceList;
        private Sunny.UI.UILine uiLine1;
        private Sunny.UI.UIPanel pnlDeviceUI;
    }
}