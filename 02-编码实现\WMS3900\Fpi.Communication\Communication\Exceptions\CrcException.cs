using System;

namespace Fpi.Communication.Exceptions
{
    /// <summary>
    /// 
    /// </summary>
    public class CrcException : CommunicationException
    {
        public CrcException()
            : base()
        {
        }

        public CrcException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public CrcException(string message)
            : base(message)
        {
        }
    }
}