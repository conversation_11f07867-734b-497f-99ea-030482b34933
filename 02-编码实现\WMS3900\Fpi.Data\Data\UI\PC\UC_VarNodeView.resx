<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;chkInputValid.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;nuValidTime.Name" xml:space="preserve">
    <value>nuValidTime</value>
  </data>
  <data name="&gt;&gt;btnInput.Name" xml:space="preserve">
    <value>btnInput</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnOutput.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnOutput.Name" xml:space="preserve">
    <value>btnOutput</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="nuValidTime.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 82</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnOutput.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="chkOutputValid.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnOutput.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>74, 57</value>
  </data>
  <data name="groupBox9.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 174</value>
  </data>
  <data name="chkOutputValid.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtImpInput.Parent" xml:space="preserve">
    <value>gbCustom</value>
  </data>
  <data name="gbCustom.Size" type="System.Drawing.Size, System.Drawing">
    <value>350, 50</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="txtNodeName.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="chkOutputValid.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 21</value>
  </data>
  <data name="txtImpInput.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 19</value>
  </data>
  <data name="gbCustom.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 114</value>
  </data>
  <data name="&gt;&gt;txtImpOutput.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtNodeName.Name" xml:space="preserve">
    <value>txtNodeName</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="chkOutputValid.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="groupBox9.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;btnOutput.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="nuValidTime.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;chkInputValid.Parent" xml:space="preserve">
    <value>gbCustom</value>
  </data>
  <data name="&gt;&gt;gbCustom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;txtImpInput.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gbCustom.Name" xml:space="preserve">
    <value>gbCustom</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnOutput.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 21</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="txtImpOutput.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 19</value>
  </data>
  <data name="btnOutput.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtImpInput.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnOutput.Location" type="System.Drawing.Point, System.Drawing">
    <value>304, 19</value>
  </data>
  <data name="&gt;&gt;txtNodeId.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnOutput.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtImpOutput.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="chkInputValid.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 20</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="txtNodeName.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 53</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>变量命名</value>
  </data>
  <data name="&gt;&gt;gbCustom.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtNodeId.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnInput.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtNodeId.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 12</value>
  </data>
  <data name="btnInput.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>有效时间(秒)</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>UC_VarNodeView</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;txtNodeId.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="chkOutputValid.Text" xml:space="preserve">
    <value>启用</value>
  </data>
  <data name="&gt;&gt;groupBox9.Name" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;groupBox9.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gbCustom.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="chkInputValid.Text" xml:space="preserve">
    <value>启用</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>74, 28</value>
  </data>
  <data name="txtNodeId.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 24</value>
  </data>
  <data name="&gt;&gt;nuValidTime.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chkInputValid.Name" xml:space="preserve">
    <value>chkInputValid</value>
  </data>
  <data name="&gt;&gt;chkOutputValid.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="chkInputValid.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtNodeName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtNodeName.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;chkOutputValid.Name" xml:space="preserve">
    <value>chkOutputValid</value>
  </data>
  <data name="btnInput.Location" type="System.Drawing.Point, System.Drawing">
    <value>304, 19</value>
  </data>
  <data name="txtImpOutput.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 230</value>
  </data>
  <data name="&gt;&gt;nuValidTime.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox9.Text" xml:space="preserve">
    <value>输出处理器</value>
  </data>
  <data name="&gt;&gt;txtImpOutput.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="gbCustom.Text" xml:space="preserve">
    <value>输入处理器</value>
  </data>
  <data name="&gt;&gt;chkInputValid.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtNodeId.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;groupBox9.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="nuValidTime.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="btnInput.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtNodeId.Name" xml:space="preserve">
    <value>txtNodeId</value>
  </data>
  <data name="txtImpInput.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnInput.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>变量编号</value>
  </data>
  <data name="&gt;&gt;txtImpInput.Name" xml:space="preserve">
    <value>txtImpInput</value>
  </data>
  <data name="&gt;&gt;btnOutput.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>50, 86</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtImpOutput.Name" xml:space="preserve">
    <value>txtImpOutput</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkOutputValid.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chkOutputValid.Parent" xml:space="preserve">
    <value>groupBox9</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;gbCustom.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnInput.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtNodeName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtImpOutput.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox9.Size" type="System.Drawing.Size, System.Drawing">
    <value>350, 50</value>
  </data>
  <data name="txtImpInput.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 21</value>
  </data>
  <data name="btnInput.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 21</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nuValidTime.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnInput.Parent" xml:space="preserve">
    <value>gbCustom</value>
  </data>
  <data name="chkInputValid.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnInput.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chkInputValid.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="txtNodeName.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>