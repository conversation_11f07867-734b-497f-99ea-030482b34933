﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{1007E2B0-01AA-4BC4-9753-29E75615C1A0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.Camera</RootNamespace>
    <AssemblyName>Fpi.Camera</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>
    </DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AForge">
      <HintPath>..\FpiDLL\AForge.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Controls">
      <HintPath>..\FpiDLL\AForge.Controls.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Imaging">
      <HintPath>..\FpiDLL\AForge.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Math">
      <HintPath>..\FpiDLL\AForge.Math.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video">
      <HintPath>..\FpiDLL\AForge.Video.dll</HintPath>
    </Reference>
    <Reference Include="AForge.Video.DirectShow">
      <HintPath>..\FpiDLL\AForge.Video.DirectShow.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="NETCamera\Cameras\YSCamera\YSCamera.cs" />
    <Compile Include="NETCamera\Config\Enums.cs" />
    <Compile Include="NETCamera\Config\NETCameraManager.cs" />
    <Compile Include="NETCamera\Config\BaseNETCamera.cs" />
    <Compile Include="NETCamera\UI\FrmBaseControl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETCamera\UI\FrmBaseControl.Designer.cs">
      <DependentUpon>FrmBaseControl.cs</DependentUpon>
    </Compile>
    <Compile Include="USBCamera\UI\FrmUSBCameraPreview.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="USBCamera\UI\FrmUSBCameraPreview.designer.cs">
      <DependentUpon>FrmUSBCameraPreview.cs</DependentUpon>
    </Compile>
    <Compile Include="NETCamera\UI\FrmNETCameraPreview.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETCamera\UI\FrmNETCameraPreview.designer.cs">
      <DependentUpon>FrmNETCameraPreview.cs</DependentUpon>
    </Compile>
    <Compile Include="NETCamera\Cameras\DHCamera\DHIPCCamera.cs" />
    <Compile Include="NETCamera\Cameras\DHCamera\UI\FrmDHCameraControl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETCamera\Cameras\DHCamera\UI\FrmDHCameraControl.designer.cs">
      <DependentUpon>FrmDHCameraControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\Helper\CameraLogHelper.cs" />
    <Compile Include="NETCamera\Cameras\HIKCamera\ErrorCode.cs" />
    <Compile Include="NETCamera\Cameras\HIKCamera\HikvisonCamera.cs" />
    <Compile Include="NETCamera\Cameras\HIKCamera\UI\FrmHIKCameraControl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETCamera\Cameras\HIKCamera\UI\FrmHIKCameraControl.designer.cs">
      <DependentUpon>FrmHIKCameraControl.cs</DependentUpon>
    </Compile>
    <Compile Include="NETCamera\UI\FrmCameraEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETCamera\UI\FrmCameraEdit.designer.cs">
      <DependentUpon>FrmCameraEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\PlayerItem.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Common\UI\PlayerItem.designer.cs">
      <DependentUpon>PlayerItem.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_LogView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_LogView.designer.cs">
      <DependentUpon>UC_LogView.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\UI\UC_Preview.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Common\UI\UC_Preview.designer.cs">
      <DependentUpon>UC_Preview.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="NETCamera\UI\FrmCameraManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETCamera\UI\FrmCameraManager.Designer.cs">
      <DependentUpon>FrmCameraManager.cs</DependentUpon>
    </Compile>
    <Compile Include="USBCamera\USBCameraManager.cs" />
    <Compile Include="USBCamera\USBCamera.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %2811%29.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Alarm\Fpi.Alarm.csproj">
      <Project>{E714875C-0EC1-4C0F-8571-D0F631430C82}</Project>
      <Name>Fpi.Alarm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Camera.Common\Fpi.ExternalSDK.csproj">
      <Project>{4d6863d0-017a-4584-8db3-47349251c62c}</Project>
      <Name>Fpi.ExternalSDK</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2d502016-b3b3-43ff-9bae-ad1d2a18d42e}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="NETCamera\UI\FrmBaseControl.resx">
      <DependentUpon>FrmBaseControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="USBCamera\UI\FrmUSBCameraPreview.resx">
      <DependentUpon>FrmUSBCameraPreview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NETCamera\UI\FrmNETCameraPreview.resx">
      <DependentUpon>FrmNETCameraPreview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NETCamera\Cameras\DHCamera\UI\FrmDHCameraControl.resx">
      <DependentUpon>FrmDHCameraControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NETCamera\Cameras\HIKCamera\UI\FrmHIKCameraControl.resx">
      <DependentUpon>FrmHIKCameraControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NETCamera\UI\FrmCameraEdit.resx">
      <DependentUpon>FrmCameraEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_LogView.resx">
      <DependentUpon>UC_LogView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\UI\UC_Preview.resx">
      <DependentUpon>UC_Preview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="NETCamera\UI\FrmCameraManager.resx">
      <DependentUpon>FrmCameraManager.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %282%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %282%291.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %283%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %284%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %285%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %287%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %288%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %289%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\右.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\中.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\左.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\监控 %2810%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\[feiq]中.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\4.png" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>