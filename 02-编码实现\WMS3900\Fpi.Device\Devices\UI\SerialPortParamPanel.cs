﻿using System;
using System.Windows.Forms;
using Fpi.Communication.Buses;
using Fpi.Util.WinApiUtil.CommDataType;
using Fpi.Xml;

namespace Fpi.Devices.UI
{
    public partial class SerialPortParamPanel : UserControl, ICommParam
    {
        private readonly Device _device;

        public SerialPortParamPanel()
        {
            InitializeComponent();
        }

        public SerialPortParamPanel(Device device)
            : this()
        {
            this._device = device;
        }

        private void SerialPortParamPanel_Load(object sender, EventArgs e)
        {
            this.InitUI();

            if(this._device != null)
            {
                Property proCommParam = this._device.GetProperty("SerialPortParam");
                if(proCommParam != null)
                {
                    string port = proCommParam.GetPropertyValue("port", "COM1");
                    string baud = proCommParam.GetPropertyValue("baud", "9600");
                    string dataBit = proCommParam.GetPropertyValue("dataBit", "8");
                    string stopBit = proCommParam.GetPropertyValue("stopBit", "1");
                    string parity = proCommParam.GetPropertyValue("parity", "0");

                    this.cmbComm.Text = port;
                    this.cmbBaud.Text = baud;
                    this.cmbDataBit.Text = dataBit;
                    this.cmbStopBit.Text = stopBit;
                    this.cmbParity.SelectedIndex = int.Parse(parity);
                }
                else
                {
                    this.cmbBaud.Text = "9600";
                    this.cmbDataBit.Text = "8";
                    this.cmbStopBit.Text = "1";
                    this.cmbParity.SelectedIndex = 0;
                }
            }
        }

        private void InitUI()
        {
            //串口
            this.cmbComm.Items.AddRange(CommBus.GetCommArray());

            //波特率
            Array array = Enum.GetValues(typeof(BaudRates));
            foreach(int i in array)
            {
                this.cmbBaud.Items.Add(i.ToString());
            }

            //数据位
            array = Enum.GetValues(typeof(DataBits));
            foreach(int i in array)
            {
                this.cmbDataBit.Items.Add(i.ToString());
            }

            //停止位
            array = Enum.GetValues(typeof(StopBits));
            foreach(int i in array)
            {
                if(i == 0)
                {
                    this.cmbStopBit.Items.Add("1");
                }
                else if(i == 1)
                {
                    this.cmbStopBit.Items.Add("1.5");
                }
                else
                {
                    this.cmbStopBit.Items.Add(i.ToString());
                }
            }

            //校验位
            array = Enum.GetNames(typeof(Parity));
            Array values = Enum.GetValues(typeof(Parity));
            for(int i = 0; i < array.Length; i++)
            {
                int id = (int)values.GetValue(i);
                string name = (string)array.GetValue(i);
                IdNameNode node = new IdNameNode(id.ToString(), name);
                this.cmbParity.Items.Add(node);
            }
        }

        #region ICommParam 成员

        public void SaveCommParam()
        {
            if(this._device != null)
            {
                if(cmbComm.SelectedIndex == -1)
                {
                    throw new Exception("串口号未配置！");
                }
                if(cmbBaud.SelectedIndex == -1)
                {
                    throw new Exception("波特率未配置！");
                }
                if(cmbDataBit.SelectedIndex == -1)
                {
                    throw new Exception("数据位未配置！");
                }
                if(cmbStopBit.SelectedIndex == -1)
                {
                    throw new Exception("停止位未配置！");
                }
                if(cmbParity.SelectedIndex == -1)
                {
                    throw new Exception("校验位未配置！");
                }
                string port = this.cmbComm.SelectedItem.ToString();
                string baud = this.cmbBaud.SelectedItem.ToString();
                string dataBit = this.cmbDataBit.SelectedItem.ToString();
                string stopBit = this.cmbStopBit.SelectedItem.ToString();
                string parity = (this.cmbParity.SelectedItem as IdNameNode).id;

                Property proCommParam = this._device.GetProperty("SerialPortParam");

                if(proCommParam == null)
                {
                    proCommParam = new Property("SerialPortParam", "串口通信参数");
                    this._device.AddProperty(proCommParam);
                }

                proCommParam.SetProperty("port", port);
                proCommParam.SetProperty("baud", baud);
                proCommParam.SetProperty("dataBit", dataBit);
                proCommParam.SetProperty("stopBit", stopBit);
                proCommParam.SetProperty("parity", parity);
            }
        }

        #endregion
    }
}
