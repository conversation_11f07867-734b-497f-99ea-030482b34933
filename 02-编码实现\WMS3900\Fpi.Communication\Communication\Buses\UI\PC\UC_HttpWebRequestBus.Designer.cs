﻿namespace Fpi.Communication.Buses.UI.PC
{
    partial class UC_HttpWebRequestBus
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.txtUrl = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.txtHttpHeaders = new System.Windows.Forms.TextBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.chkNeedSaveLog = new System.Windows.Forms.CheckBox();
            this.label3 = new System.Windows.Forms.Label();
            this.cmbContentTypes = new System.Windows.Forms.ComboBox();
            this.SuspendLayout();
            // 
            // txtUrl
            // 
            this.txtUrl.BackColor = System.Drawing.SystemColors.Window;
            this.txtUrl.CanEmpty = false;
            this.txtUrl.DigitLength = 2;
            this.txtUrl.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtUrl.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtUrl.IsValidCheck = false;
            this.txtUrl.Label = "服务器";
            this.txtUrl.Location = new System.Drawing.Point(81, 13);
            this.txtUrl.MaxLength = 1024;
            this.txtUrl.MaxValue = null;
            this.txtUrl.MinValue = null;
            this.txtUrl.Name = "txtUrl";
            this.txtUrl.Size = new System.Drawing.Size(244, 21);
            this.txtUrl.TabIndex = 3;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(37, 17);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "服务器";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(25, 43);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "标头信息";
            this.toolTip1.SetToolTip(this.label2, "key1=\"value1\";key2=\"value2\";\r\n以 \"; 作为两个键值对之间的分隔符\r\n以 =\" 作为键值之间的分隔符");
            // 
            // txtHttpHeaders
            // 
            this.txtHttpHeaders.Location = new System.Drawing.Point(81, 43);
            this.txtHttpHeaders.Multiline = true;
            this.txtHttpHeaders.Name = "txtHttpHeaders";
            this.txtHttpHeaders.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtHttpHeaders.Size = new System.Drawing.Size(244, 85);
            this.txtHttpHeaders.TabIndex = 5;
            this.toolTip1.SetToolTip(this.txtHttpHeaders, "key1=\"value1\";key2=\"value2\";\r\n必须包含分割符[=\"]和[\";]，否则视为无效\r\n取值为<key,value>键值对，自动去除分割符");
            // 
            // chkNeedSaveLog
            // 
            this.chkNeedSaveLog.AutoSize = true;
            this.chkNeedSaveLog.Location = new System.Drawing.Point(81, 173);
            this.chkNeedSaveLog.Name = "chkNeedSaveLog";
            this.chkNeedSaveLog.Size = new System.Drawing.Size(72, 16);
            this.chkNeedSaveLog.TabIndex = 16;
            this.chkNeedSaveLog.Text = "保存日志";
            this.toolTip1.SetToolTip(this.chkNeedSaveLog, "是否打印通讯日志");
            this.chkNeedSaveLog.UseVisualStyleBackColor = true;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(25, 143);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 17;
            this.label3.Text = "连接类型";
            // 
            // cmbContentTypes
            // 
            this.cmbContentTypes.FormattingEnabled = true;
            this.cmbContentTypes.Items.AddRange(new object[] {
            "application/json",
            "application/x-www-form-urlencoded"});
            this.cmbContentTypes.Location = new System.Drawing.Point(81, 140);
            this.cmbContentTypes.Name = "cmbContentTypes";
            this.cmbContentTypes.Size = new System.Drawing.Size(244, 20);
            this.cmbContentTypes.TabIndex = 19;
            // 
            // UC_HttpWebRequestBus
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.cmbContentTypes);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.chkNeedSaveLog);
            this.Controls.Add(this.txtHttpHeaders);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.txtUrl);
            this.Controls.Add(this.label1);
            this.Name = "UC_HttpWebRequestBus";
            this.Size = new System.Drawing.Size(360, 199);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Fpi.UI.Common.PC.Controls.FpiTextBox txtUrl;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtHttpHeaders;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.CheckBox chkNeedSaveLog;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cmbContentTypes;
    }
}
