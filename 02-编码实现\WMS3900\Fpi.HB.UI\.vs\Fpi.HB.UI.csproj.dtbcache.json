{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.UI", "ProjectFileName": "Fpi.HB.UI.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "DeciveParam\\FrmDeviceParam.cs"}, {"SourceFile": "DeciveParam\\FrmDeviceParam.Designer.cs"}, {"SourceFile": "DeciveParam\\FrmDeviceParamNew.cs"}, {"SourceFile": "DeciveParam\\FrmDeviceParamNew.Designer.cs"}, {"SourceFile": "DeciveParam\\UC_NoParamPanel.cs"}, {"SourceFile": "DeciveParam\\UC_NoParamPanel.Designer.cs"}, {"SourceFile": "HisData\\FrmQueryConfig.cs"}, {"SourceFile": "HisData\\FrmQueryConfig.Designer.cs"}, {"SourceFile": "HisData\\UC_QueryConfig.cs"}, {"SourceFile": "HisData\\UC_QueryConfig.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Remote\\FrmRemoteConfig.cs"}, {"SourceFile": "Remote\\FrmRemoteConfig.Designer.cs"}, {"SourceFile": "Remote\\UC_RemoteConfig.cs"}, {"SourceFile": "Remote\\UC_RemoteConfig.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.FlowChart\\bin\\Debug\\Fpi.UI.FlowChart.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.FlowChart\\bin\\Debug\\Fpi.UI.FlowChart.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.UI\\bin\\Debug\\SunnyUI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\WinFormsUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\ZedGraph.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.UI\\bin\\Debug\\Fpi.HB.UI.dll", "OutputItemRelativePath": "Fpi.HB.UI.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}