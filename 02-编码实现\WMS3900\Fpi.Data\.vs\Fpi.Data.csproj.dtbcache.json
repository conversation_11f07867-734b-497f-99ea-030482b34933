{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data", "ProjectFileName": "Fpi.Data.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Data\\Config\\AlarmLimit.cs"}, {"SourceFile": "Data\\Config\\ArrayNode.cs"}, {"SourceFile": "Data\\Config\\CalK.cs"}, {"SourceFile": "Data\\Config\\DataDealer.cs"}, {"SourceFile": "Data\\Config\\DataHelper.cs"}, {"SourceFile": "Data\\Config\\DataManager.cs"}, {"SourceFile": "Data\\Config\\Enums.cs"}, {"SourceFile": "Data\\Config\\Fixup.cs"}, {"SourceFile": "Data\\Config\\Scope.cs"}, {"SourceFile": "Data\\Config\\StateNode.cs"}, {"SourceFile": "Data\\Config\\UnitGroup.cs"}, {"SourceFile": "Data\\Config\\UnitManager.cs"}, {"SourceFile": "Data\\Config\\ValueNode.cs"}, {"SourceFile": "Data\\Config\\VarNode.cs"}, {"SourceFile": "Data\\Config\\VarNodeTemplate.cs"}, {"SourceFile": "Data\\Config\\Unit.cs"}, {"SourceFile": "Data\\Config\\Indice.cs"}, {"SourceFile": "Data\\Exceptions\\DataException.cs"}, {"SourceFile": "Data\\ExpParser\\Define.cs"}, {"SourceFile": "Data\\ExpParser\\EDataType.cs"}, {"SourceFile": "Data\\ExpParser\\EDFAState.cs"}, {"SourceFile": "Data\\ExpParser\\EKeyword.cs"}, {"SourceFile": "Data\\ExpParser\\EOperatorType.cs"}, {"SourceFile": "Data\\ExpParser\\ETokenType.cs"}, {"SourceFile": "Data\\ExpParser\\Evaluator.cs"}, {"SourceFile": "Data\\ExpParser\\ExpressionParse.cs"}, {"SourceFile": "Data\\FuncServer\\BaseServer.cs"}, {"SourceFile": "Data\\FuncServer\\ServiceAttribute.cs"}, {"SourceFile": "Data\\FuncServer\\ServiceManager.cs"}, {"SourceFile": "Data\\FuncServer\\Service.cs"}, {"SourceFile": "Data\\ExpParser\\Grammar.cs"}, {"SourceFile": "Data\\ExpParser\\GrammarAnalyzer.cs"}, {"SourceFile": "Data\\ExpParser\\IOperand.cs"}, {"SourceFile": "Data\\ExpParser\\IToken.cs"}, {"SourceFile": "Data\\ExpParser\\KeyValueList.cs"}, {"SourceFile": "Data\\ExpParser\\KeyWord.cs"}, {"SourceFile": "Data\\ExpParser\\Link_OP.cs"}, {"SourceFile": "Data\\ExpParser\\Operand.cs"}, {"SourceFile": "Data\\ExpParser\\Operator.cs"}, {"SourceFile": "Data\\ExpParser\\PhraseAnalyzer.cs"}, {"SourceFile": "Data\\ExpParser\\Separator.cs"}, {"SourceFile": "Data\\ExpParser\\SyntaxAnalyzer.cs"}, {"SourceFile": "Data\\ExpParser\\TOKEN.cs"}, {"SourceFile": "Data\\ExpParser\\TOKENLink.cs"}, {"SourceFile": "Data\\ExpParser\\ToolBox.cs"}, {"SourceFile": "Data\\ExpParser\\Unknown.cs"}, {"SourceFile": "Data\\Interfaces\\IDataDealer.cs"}, {"SourceFile": "Data\\Interfaces\\IDataFilter.cs"}, {"SourceFile": "Data\\Interfaces\\IExtendFunction.cs"}, {"SourceFile": "Data\\Interfaces\\INameChanged.cs"}, {"SourceFile": "Data\\Interfaces\\INodeConfigView.cs"}, {"SourceFile": "Data\\Interfaces\\ISimuData.cs"}, {"SourceFile": "Data\\Interfaces\\IDataProvider.cs"}, {"SourceFile": "Data\\UI\\PC\\ExpressEditorForm.cs"}, {"SourceFile": "Data\\UI\\PC\\ExpressEditorForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ExpressesEditorForm.cs"}, {"SourceFile": "Data\\UI\\PC\\ExpressesEditorForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\FormConfigSingleNode.cs"}, {"SourceFile": "Data\\UI\\PC\\FormConfigSingleNode.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\FormVarExplore.cs"}, {"SourceFile": "Data\\UI\\PC\\FormVarExplore.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ServiceEditForm.cs"}, {"SourceFile": "Data\\UI\\PC\\ServiceEditForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\UC_StateNodeView.cs"}, {"SourceFile": "Data\\UI\\PC\\UC_StateNodeView.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\UC_ValueNodeView.cs"}, {"SourceFile": "Data\\UI\\PC\\UC_ValueNodeView.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\UC_VarNodeView.cs"}, {"SourceFile": "Data\\UI\\PC\\UC_VarNodeView.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\UnitManagerForm.cs"}, {"SourceFile": "Data\\UI\\PC\\UnitManagerForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditUnit.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditUnit.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditScope.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditScope.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditAlarmLimit.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditAlarmLimit.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormComputeCalk.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormComputeCalk.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormSelectUnit.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormSelectUnit.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditUnitType.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditUnitType.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditIndice.cs"}, {"SourceFile": "Data\\UI\\PC\\ValueNodeViews\\FormEditIndice.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\AlarmNodeEditorForm.cs"}, {"SourceFile": "Data\\UI\\PC\\AlarmNodeEditorForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\VarNodeEditorForm2.cs"}, {"SourceFile": "Data\\UI\\PC\\VarNodeEditorForm2.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\VarNodeEditorForm.cs"}, {"SourceFile": "Data\\UI\\PC\\VarNodeEditorForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\VarNodeInfo.cs"}, {"SourceFile": "Data\\UI\\PC\\TemplateExpEditorForm.cs"}, {"SourceFile": "Data\\UI\\PC\\TemplateExpEditorForm.Designer.cs"}, {"SourceFile": "Data\\UI\\PC\\TemplateVarNodeEditorForm.cs"}, {"SourceFile": "Data\\UI\\PC\\TemplateVarNodeEditorForm.Designer.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll", "OutputItemRelativePath": "Fpi.Data.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}