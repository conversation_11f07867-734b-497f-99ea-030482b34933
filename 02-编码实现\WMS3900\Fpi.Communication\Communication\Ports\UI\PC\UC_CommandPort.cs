using System;
using Fpi.Communication.Commands;
using Fpi.Communication.Commands.Config;
using Fpi.Communication.Converter;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Reflection;
using Fpi.Xml;

namespace Fpi.Communication.Ports.UI.PC
{
    public partial class UC_CommandPort : BaseConfigureView//UserControl
    {
        public UC_CommandPort()
        {
            InitializeComponent();
            BindUI();
        }

        private void BindUI()
        {
            this.cmbManager.Items.Add(typeof(CommandManager).FullName);
            Type[] managerList = ReflectionHelper.GetChildTypes(typeof(CommandManager));
            foreach(Type type in managerList)
            {
                this.cmbManager.Items.Add(type.FullName);
            }

            Type[] converterList = ReflectionHelper.GetChildTypes(typeof(IDataConvertable));
            foreach(Type type in converterList)
            {
                this.cmbConverter.Items.Add(type.FullName);
            }
        }

        protected override void ShowConfig(object obj)
        {
            BaseNode configNode = configObj as BaseNode;
            string manager = configNode.GetPropertyValue(CommandPort.PropertyName_Manager, typeof(CommandManager).FullName);
            string convert = configNode.GetPropertyValue(CommandPort.PropertyName_Converter, typeof(DataConverter).FullName);

            this.cmbManager.SelectedIndex = this.cmbManager.Items.IndexOf(manager);
            this.cmbConverter.SelectedIndex = this.cmbConverter.Items.IndexOf(convert);
        }

        public override object Save()
        {
            string manager = this.cmbManager.Text;
            string convert = this.cmbConverter.Text;

            BaseNode configNode = configObj as BaseNode;
            configNode.SetProperty(CommandPort.PropertyName_Manager, manager);
            configNode.SetProperty(CommandPort.PropertyName_Converter, convert);

            return configNode;
        }
    }
}