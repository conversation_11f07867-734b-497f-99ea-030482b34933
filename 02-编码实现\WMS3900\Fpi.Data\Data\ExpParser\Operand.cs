﻿namespace Fpi.Data.ExpParser
{
    using System.Runtime.InteropServices;

    [StructLayout(LayoutKind.Sequential)]
    public struct Operand<T> : IOperand
    {
        private EDataType _type;
        private T _value;
        public Operand(EDataType type, T value)
        {
            this._type = type;
            this._value = value;
        }

        public T TValue => this._value;
        public EDataType Type => this._type;
        public object Value => this._value;
        public override string ToString()
        {
            return this._value.ToString();
        }
    }
}

