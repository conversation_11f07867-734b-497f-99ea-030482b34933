﻿//==================================================================================================
//类名：     ServiceAttribute   
//创建人:    hongbing_mao
//创建时间:  2013-1-11 13:04:28
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;

namespace Fpi.Data.FuncServer
{
    /// <summary>
    /// 服务属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public class ServiceAttribute : Attribute
    {
        /// <summary>
        /// 友好别名
        /// </summary>
        public string FriendlyName { get; set; }

        /// <summary>
        /// 服务方法描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 服务方法参数描述
        /// </summary>
        public string[] ParamDescription { get; set; }

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="friendlyName">友好别名</param>
        /// <param name="description">服务方法描述</param>
        /// <param name="paramDes">服务方法参数描述</param>
        public ServiceAttribute(string friendlyName, string description, params string[] paramDes)
        {
            FriendlyName = friendlyName;
            Description = description;
            ParamDescription = paramDes;
        }
    }
}
