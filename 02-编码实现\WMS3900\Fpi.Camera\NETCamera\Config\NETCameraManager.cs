﻿using System;
using System.Collections.Generic;
using Fpi.Util.Interfaces.Initialize;
using Fpi.Xml;

namespace Fpi.Camera
{
    /// <summary>
    /// 摄像机管理类
    /// </summary>
    public class NETCameraManager : BaseNode, IInitialization
    {
        #region 字段属性

        /// <summary>
        /// 摄像机列表
        /// </summary>
        public NodeList Cameras = new NodeList();

        #endregion

        #region 构造

        private NETCameraManager()
        {
            loadXml();
        }

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static NETCameraManager _instance;
        public static NETCameraManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new NETCameraManager();
                }
            }
            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 通过设备ID查找设备
        /// </summary>
        /// <param name="cameraId"></param>
        /// <returns></returns>
        public BaseNETCamera GetCameraById(string cameraId)
        {
            return Cameras.FindNode(cameraId) as BaseNETCamera;
        }

        /// <summary>
        /// 通过设备名称查找设备
        /// </summary>
        /// <param name="cameraName"></param>
        /// <returns></returns>
        public BaseNETCamera GetCameraByName(string cameraName)
        {
            return Cameras.FindNodeByName(cameraName) as BaseNETCamera;
        }

        /// <summary>
        /// 获得所有设备列表
        /// </summary>
        /// <returns></returns>
        public List<BaseNETCamera> GetAllCameras()
        {
            var list = new List<BaseNETCamera>();
            foreach(BaseNETCamera vn in Cameras)
            {
                list.Add(vn);
            }
            list.Sort((x, y) => string.Compare(x.name, y.name, StringComparison.Ordinal));
            return list;
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <returns></returns>
        public override bool Save()
        {
            OnResetCameraList();
            return base.Save();
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReLoad()
        {
            lock(SyncObj)
            {
                _instance = null;
                GetInstance();
                OnResetCameraList();
            }
        }

        /// <summary>
        /// 清理所有设备SDK
        /// </summary>
        public void ClearnAllCamera()
        {
            // 停止录像
            foreach(var camera in GetAllCameras())
            {
                try
                {
                    camera.StopRecording();
                    camera.StopRealPlay();
                    camera.StopNoPreviewRealPlay();
                    camera.Logout();
                }
                catch { }
            }

            // 清理SDK
            foreach(var camera in GetAllCameras())
            {
                try
                {
                    camera.CleanSDK();
                }
                catch { }
            }
        }

        #endregion

        #region 事件

        public delegate void ResetCameraListHandler();

        /// <summary>
        /// 摄像机列表重置通知
        /// </summary>
        public event ResetCameraListHandler ResetCameraListEvent;

        /// <summary>
        /// 摄像机列表重置通知
        /// </summary>
        private void OnResetCameraList()
        {
            if(ResetCameraListEvent != null)
            {
                ResetCameraListEvent();
            }
        }

        #endregion

        #region IInitialization

        /// <summary>
        /// 初始化时，调用各厂商SDK Init方法。关闭软件时调用Cleanup方法
        /// </summary>
        public void Initialize()
        {

        }

        #endregion
    }
}