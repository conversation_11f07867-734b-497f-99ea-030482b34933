﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E714875C-0EC1-4C0F-8571-D0F631430C82}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>Fpi.Alarm</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>Fpi.Alarm</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>pdbonly</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Alarm\AlarmConfig\AlarmCode.cs" />
    <Compile Include="Alarm\AlarmConfig\AlarmGrade.cs" />
    <Compile Include="Alarm\AlarmConfig\AlarmGroup.cs" />
    <Compile Include="Alarm\AlarmConfig\AlarmManager.cs" />
    <Compile Include="Alarm\AlarmConfig\AlarmSource.cs" />
    <Compile Include="Alarm\AlarmInfo.cs" />
    <Compile Include="Alarm\AlarmSave\DbSaveAlarm.cs" />
    <Compile Include="Alarm\AlarmSave\ISaveAlarm.cs" />
    <Compile Include="Alarm\AlarmState.cs" />
    <Compile Include="Alarm\UI\PC\FrmQueryHistoryAlarm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FrmQueryHistoryAlarm.Designer.cs">
      <DependentUpon>FrmQueryHistoryAlarm.cs</DependentUpon>
    </Compile>
    <Compile Include="Alarm\UI\PC\FrmQueryCurrentAlarm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FrmQueryCurrentAlarm.Designer.cs">
      <DependentUpon>FrmQueryCurrentAlarm.cs</DependentUpon>
    </Compile>
    <Compile Include="Alarm\UI\PC\FrmConfigAlarm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FrmConfigAlarm.Designer.cs">
      <DependentUpon>FrmConfigAlarm.cs</DependentUpon>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmCode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmCode.Designer.cs">
      <DependentUpon>FormEditAlarmCode.cs</DependentUpon>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmGrade.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmGrade.Designer.cs">
      <DependentUpon>FormEditAlarmGrade.cs</DependentUpon>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmGroup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmGroup.Designer.cs">
      <DependentUpon>FormEditAlarmGroup.cs</DependentUpon>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmSource.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Alarm\UI\PC\FormEditAlarmSource.Designer.cs">
      <DependentUpon>FormEditAlarmSource.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Alarm\UI\PC\FrmQueryHistoryAlarm.resx">
      <DependentUpon>FrmQueryHistoryAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FrmQueryCurrentAlarm.en-US.resx">
      <DependentUpon>FrmQueryCurrentAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FrmQueryCurrentAlarm.resx">
      <DependentUpon>FrmQueryCurrentAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FrmConfigAlarm.en-US.resx">
      <DependentUpon>FrmConfigAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FrmConfigAlarm.resx">
      <DependentUpon>FrmConfigAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmCode.en-US.resx">
      <DependentUpon>FormEditAlarmCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmCode.resx">
      <DependentUpon>FormEditAlarmCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmGrade.en-US.resx">
      <DependentUpon>FormEditAlarmGrade.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmGrade.resx">
      <DependentUpon>FormEditAlarmGrade.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmGroup.en-US.resx">
      <DependentUpon>FormEditAlarmGroup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmGroup.resx">
      <DependentUpon>FormEditAlarmGroup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmSource.en-US.resx">
      <DependentUpon>FormEditAlarmSource.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Alarm\UI\PC\FormEditAlarmSource.resx">
      <DependentUpon>FormEditAlarmSource.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.en-US.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <SubType>Designer</SubType>
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Edit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Remove.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Add.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2D502016-B3B3-43FF-9BAE-AD1D2A18D42E}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>