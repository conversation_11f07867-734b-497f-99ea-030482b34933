using System;

namespace Fpi.Communication.Exceptions
{
    /// <summary>
    /// 
    /// </summary>
    public class DataFormatException : CommunicationException
    {
        public DataFormatException()
            : base()
        {
        }

        public DataFormatException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public DataFormatException(string message)
            : base(message)
        {
        }
    }

}