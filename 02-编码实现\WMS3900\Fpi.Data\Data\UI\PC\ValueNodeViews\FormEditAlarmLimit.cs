using System;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Properties;

namespace Fpi.Data.UI.PC.ValueNodeViews
{
    internal partial class FormEditAlarmLimit : Form
    {
        public FormEditAlarmLimit()
        {
            InitializeComponent();
        }
        public FormEditAlarmLimit(AlarmLimit alarmLimit)
            : this()
        {
            this.AlarmLimit = alarmLimit;
        }

        public AlarmLimit AlarmLimit { get; private set; }

        private void FormEditAlarmLimit_Load(object sender, EventArgs e)
        {
            if(AlarmLimit != null)
            {
                this.txtId.Text = AlarmLimit.id;
                this.txtName.Text = AlarmLimit.name;
                this.nuMin.Value = (decimal)AlarmLimit.alarmLower;
                this.nuMax.Value = (decimal)AlarmLimit.alarmUpper;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        private void Save()
        {
            if(string.IsNullOrEmpty(this.txtId.Text) || string.IsNullOrEmpty(this.txtName.Text))
            {
                throw new Exception(Resources.IdNameEmpty);
            }

            if(AlarmLimit == null)
            {
                AlarmLimit = new AlarmLimit();
            }

            AlarmLimit.id = this.txtId.Text;
            AlarmLimit.name = this.txtName.Text;
            AlarmLimit.alarmLower = (double)this.nuMin.Value;
            AlarmLimit.alarmUpper = (double)this.nuMax.Value;
        }
    }
}