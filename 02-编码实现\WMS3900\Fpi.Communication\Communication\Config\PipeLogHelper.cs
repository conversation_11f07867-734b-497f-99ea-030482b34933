using Fpi.Log;
using Fpi.Util.Sundry;

namespace Fpi.Communication.Manager
{
    public class PipeLogHelper
    {
        private PipeLogHelper()
        {
        }

        private const string MsgType = "PipeMessage";

        public static void TraceMsg(string msg)
        {
            LogUtil.Debug(MsgType, msg);
        }

        public static void TraceSendMsg(byte[] sendData)
        {
            string strBytes = StringUtil.BytesToString(sendData);
            TraceSendMsg(strBytes);
        }

        public static void TraceRecvMsg(byte[] recvData)
        {
            string strBytes = StringUtil.BytesToString(recvData);
            TraceRecvMsg(strBytes);
        }

        public static void TraceSendMsg(string sendData)
        {
            LogUtil.Debug(MsgType, "Send:" + sendData);
        }

        public static void TraceRecvMsg(string recvData)
        {
            LogUtil.Debug(MsgType, "Recv:" + recvData);
        }
    }
}