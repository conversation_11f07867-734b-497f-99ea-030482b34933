﻿using System;
using System.Data;
using System.Data.Odbc;
using Fpi.DB.Manager;

namespace Fpi.DB
{
    /// <summary>
    /// 针对MySQL ODBC连接的工厂
    /// </summary>
    public class OdbcFactory : IDbFactory
    {
        public OdbcFactory()
        {
            ConnectString = DataBaseManager.GetInstance().GetDataBaseConStr();
        }
        public OdbcFactory(string constr)
        {
            ConnectString = constr;
        }

        #region IDbFactory 成员
        public string ConnectString { get; }

        public IDbConnection CreateConnection()
        {
            OdbcConnection conn = new OdbcConnection(this.ConnectString);
            conn.ConnectionTimeout = 15;  //默认15，单位秒
            return conn;
        }

        public IDbDataAdapter CreateDataAdapter()
        {
            return new OdbcDataAdapter();
        }

        public IDataParameter CreateDataParameter()
        {
            return new OdbcParameter();
        }

        #region 平台以前的方法，为了兼容而保留

        /// <summary>
        /// 创建数据库
        /// </summary>
        /// <param name="dbName"></param>
        /// <returns></returns>
        public bool CreateDb(string dbName)
        {
            string connectString = this.ConnectString;
            int index = connectString.IndexOf("DATABASE", StringComparison.OrdinalIgnoreCase);
            connectString = connectString.Substring(0, index);

            IDbConnection conn = null;
            try
            {
                //DRIVER={MySQL ODBC 5.1 Driver};SERVER=localhost;OPTION=3;UID=root;PASSWORD=***;
                conn = new OdbcConnection(connectString);
                conn.Open();

                string sql = "CREATE DATABASE IF NOT EXISTS " + dbName + " DEFAULT CHARACTER SET utf8";

                IDbCommand command = conn.CreateCommand();
                command.CommandText = sql;

                command.ExecuteNonQuery();
            }
            finally
            {
                if(null != conn)
                {
                    conn.Close();
                }
            }

            return true;
        }

        #endregion

        #endregion
    }
}
