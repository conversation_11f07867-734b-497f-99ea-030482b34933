﻿using System.ComponentModel;

namespace Fpi.Devices
{
    /// <summary>
    /// 设备类别
    /// </summary>
    public enum eDeviceType : byte
    {
        /// <summary>
        /// IO设备型
        /// </summary>
        IO = 1,

        /// <summary>
        /// 监测设备
        /// </summary>
        WMS,

        /// <summary>
        /// 监测辅助设备
        /// </summary>
        AUXILI,

        /// <summary>
        /// 外部设备（动环设备）
        /// </summary>
        EXTER
    }

    /// <summary>
    /// 设备通信类型
    /// </summary>
    public enum eDeviceCommType : byte
    {
        /// <summary>
        /// 通过串口采集数据
        /// </summary>
        COM = 1,

        /// <summary>
        /// 作为网络客户端采集数据
        /// </summary>
        NET_C,

        /// <summary>
        /// 作为网络服务端采集数据
        /// </summary>
        NET_S,

        /// <summary>
        /// 从文件采集数据（从文件采集数据）
        /// </summary>
        FILE,

        /// <summary>
        /// 通过SP串口
        /// </summary>
        COM_SP,

        /// <summary>
        /// 其他通信参数（例如PCI模拟量采集卡，通过调用驱动采集）
        /// </summary>
        OTHER
    }

    /// <summary>
    /// 通道数据类型
    /// </summary>
    public enum eDeviceChannelType
    {
        /// <summary>
        /// 模拟输入
        /// </summary>
        InValue,

        /// <summary>
        /// 模拟输出
        /// </summary>
        OutValue,

        /// <summary>
        /// 开关输入
        /// </summary>
        InSwitch,

        /// <summary>
        /// 开关输出
        /// </summary>
        OutSwitch
    }

    /// <summary>
    /// 仪表测量类型
    /// </summary>
    public enum eDeviceMeasureType
    {
        /// <summary>
        /// 所有设备
        /// </summary>
        [Description("所有设备")]
        ALL,

        /// <summary>
        /// PLC
        /// </summary>
        [Description("PLC")]
        PLC,

        /// <summary>
        /// 高锰酸盐
        /// </summary>
        [Description("高锰酸盐")]
        CODMn,

        /// <summary>
        /// 氨氮
        /// </summary>
        [Description("氨氮")]
        NH4,

        /// <summary>
        /// 总磷
        /// </summary>
        [Description("总磷")]
        TP,

        /// <summary>
        /// 总氮
        /// </summary>
        [Description("总氮")]
        TN,

        /// <summary>
        /// 化学需氧量
        /// </summary>
        [Description("化学需氧量")]
        COD,

        /// <summary>
        /// 多参数
        /// </summary>
        [Description("多参数")]
        MulParam,

        /// <summary>
        /// GPS
        /// </summary>
        [Description("GPS")]
        GPS,

        /// <summary>
        /// 质控设备
        /// </summary>
        [Description("质控设备")]
        QCD,

        /// <summary>
        /// 采样器
        /// </summary>
        [Description("采样器")]
        Sample,

        /// <summary>
        /// 外部设备（动环设备）
        /// </summary>
        [Description("动环设备")]
        Exter,

        /// <summary>
        /// 其他设备
        /// </summary>
        [Description("其他设备")]
        Other
    }
}