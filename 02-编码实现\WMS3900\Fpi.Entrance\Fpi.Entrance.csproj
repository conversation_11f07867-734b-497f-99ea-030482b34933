﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1A61D7D4-AEDD-487F-84D9-7AE75B3B1AEF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.Entrance</RootNamespace>
    <AssemblyName>Fpi.Entrance</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI">
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI">
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\Helper\EntranceLogHelper.cs" />
    <Compile Include="Common\Helper\ImageHelper.cs" />
    <Compile Include="NETEntrance\Config\BaseNETEntrance.cs" />
    <Compile Include="NETEntrance\Config\EntranceManager.cs" />
    <Compile Include="NETEntrance\Config\Enum.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Controllers\YSSubscriptionHelper.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Controllers\DigestAuthHandler.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Controllers\PushNotificationService.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Models\CombinedEvent.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Models\DoorContactAlarm.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Models\PersonVerification.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Models\RemoteControlResponse.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Models\SubscriptionResponse.cs" />
    <Compile Include="NETEntrance\Entrances\YSEntrance\Config\Models\SubUnSubscriptionResponse.cs" />
    <Compile Include="NETEntrance\UI\FrmFaceInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETEntrance\UI\FrmFaceInfo.Designer.cs">
      <DependentUpon>FrmFaceInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="NETEntrance\Entrances\YSEntrance\YSEntrance.cs" />
    <Compile Include="NETEntrance\UI\FrmEntranceEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETEntrance\UI\FrmEntranceEdit.Designer.cs">
      <DependentUpon>FrmEntranceEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="NETEntrance\UI\FrmEntranceManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NETEntrance\UI\FrmEntranceManager.Designer.cs">
      <DependentUpon>FrmEntranceManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{C238E665-75B4-4EDA-B574-A37F2794BA54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2D502016-B3B3-43FF-9BAE-AD1D2A18D42E}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="NETEntrance\UI\FrmFaceInfo.resx">
      <DependentUpon>FrmFaceInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NETEntrance\UI\FrmEntranceEdit.resx">
      <DependentUpon>FrmEntranceEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NETEntrance\UI\FrmEntranceManager.resx">
      <DependentUpon>FrmEntranceManager.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>