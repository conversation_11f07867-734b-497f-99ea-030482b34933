﻿//==================================================================================================
//类名：     ServiceEditForm   
//创建人:    hongbing_mao
//创建时间:  2013-1-11 13:24:07
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Fpi.Data.FuncServer;
using Fpi.UI.Common.PC;
using Fpi.UI.Common.PC.Controls;

namespace Fpi.Data.UI.PC
{
    /// <summary>
    /// 服务方法选择编辑窗口
    /// </summary>
    public partial class ServiceEditForm : Form
    {
        /// <summary>
        /// 调用使用串
        /// </summary>
        public string InvokeStr { get; set; }

        private List<FpiTextBox> textBoxs = new List<FpiTextBox>();
        /// <summary>
        /// 构造
        /// </summary>
        public ServiceEditForm()
        {
            InitializeComponent();
        }
        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ServiceEditForm_Load(object sender, EventArgs e)
        {
            foreach(Service service in ServiceManager.GetInstance().Services)
            {
                ListViewItem lvi = new ListViewItem();
                if(!string.IsNullOrEmpty(service.SrvFriendlyName))
                {
                    lvi.Text = service.SrvFriendlyName;
                }
                lvi.SubItems.Add(service.SrvDescription);
                lvi.Tag = service;
                this.lvServices.Items.Add(lvi);
            }

        }
        /// <summary>
        /// 服务方法切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lvServices_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(this.lvServices.SelectedItems == null || this.lvServices.SelectedItems.Count < 1)
            {
                return;
            }
            Service service = this.lvServices.SelectedItems[0].Tag as Service;
            this.textBoxs.Clear();
            this.gbParam.Controls.Clear();

            if(service.ParamType != null && service.ParamType.Length > 0)
            {
                int x = 10;
                int y = 20;
                int width = gbParam.Width - 20;
                for(int i = 0; i < service.ParamType.Length; i++)
                {
                    Label lb = new Label();
                    lb.Width = width;
                    lb.Location = new Point(x, y);
                    if(service.ParamDescription != null && service.ParamDescription.Length > i)
                    {
                        lb.Text = service.ParamDescription[i];
                    }
                    if(string.IsNullOrEmpty(lb.Text))
                    {
                        lb.Text = string.Format("第{0}参数", i + 1);
                    }
                    this.gbParam.Controls.Add(lb);
                    y += lb.Height;
                    FpiTextBox text = new FpiTextBox();
                    text.Label = lb.Text;
                    text.Location = new Point(x, y);
                    y += text.Height + 10;
                    if(service.ParamType[i] == typeof(int))
                    {
                        text.InputType = TextInputType.Int;
                        text.CanEmpty = false;
                    }
                    else if(service.ParamType[i] == typeof(float) || service.ParamType[i] == typeof(double))
                    {
                        text.InputType = TextInputType.Float;
                        text.CanEmpty = false;
                    }
                    else
                    {
                        text.InputType = TextInputType.String;
                    }
                    this.textBoxs.Add(text);
                    this.gbParam.Controls.Add(text);
                }
            }
        }
        /// <summary>
        /// 确定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            if(this.lvServices.SelectedItems == null || this.lvServices.SelectedItems.Count < 1)
            {
                FpiMessageBox.ShowInfo("至少选择一个方法!");
                this.DialogResult = DialogResult.None;
                return;
            }
            foreach(FpiTextBox ctr in textBoxs)
            {
                try
                {
                    ctr.Check();
                }
                catch(Exception ex)
                {
                    FpiMessageBox.ShowInfo(ex.Message);
                    this.DialogResult = DialogResult.None;
                    return;
                }
            }
            Service service = this.lvServices.SelectedItems[0].Tag as Service;
            StringBuilder sb = new StringBuilder();
            sb.Append("\"");
            sb.Append(service.SrvName);
            sb.Append("\"");
            if(service.ParamType != null && service.ParamType.Length > 0)
            {
                for(int i = 0; i < service.ParamType.Length; i++)
                {
                    sb.Append(",");
                    FpiTextBox ctr = textBoxs[i];
                    if(ctr.InputType == TextInputType.String)
                    {
                        sb.Append("\"");
                        sb.Append(ctr.Text);
                        sb.Append("\"");
                    }
                    else
                    {
                        sb.Append(ctr.Text);
                    }
                }
            }
            this.InvokeStr = sb.ToString();
        }


    }
}