<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="folderDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="ofDialog.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>143, 17</value>
  </metadata>
  <metadata name="folderDialogXml.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>244, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>62</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAQAMDAAAAEAIACoJQAARgAAACAgAAABACAAqBAAAO4lAAAYGAAAAQAgAIgJAACWNgAAEBAAAAEA
        IABoBAAAHkAAACgAAAAwAAAAYAAAAAEAIAAAAAAAgCUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAABcHABUSBQAoEgYAKBIGACgSBgAoEgYAKBIGACgSBgAoEgYAKBIG
        ACgSBgAoEgYAKBIGACgSBgAoEgYAKBIGACgSBgAoEgYAKBIGACgSBgAoEgYAKBIGACgSBgAoEgYAKBIG
        ACgSBgAoEgYAKBIGACgSBgAoEgYAKBEFACgQBQAVAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAShwUS488Fu+SMAH8kTAA/JEwAPyRMAD8kTAA/JEw
        APyRMAD8kTAA/JEwAPyRMAD8kTAA/JEwAPyRMAD8kTAA/JEwAPyRMAD8kTAA/JEwAPyRMAD8kTAA/JEw
        APyRMAD8kTAA/JEwAPyRMAD8kTAA/JEwAPyRMAD8kTAA/JEwAPx2JwDmJwwAPAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAjzsVxGUxf/+leJH/1ayZ/8uZ
        gf+zaUb/nDwN/5cyAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gz
        Af+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+XMgD/bCMAnwAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoUoe0pQx
        B/8tD7X+SRuO/7NzX/7o0cf/+/j2/uTLv//HkHb+p1Ip/5gzAf6YMwD/lzIA/pgzAP+XMgD+mDMA/5cy
        AP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgz
        AP+XMgD+fCkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAokoe0pgzAf+OLxH/GgnT/xYI2/9xJkP/sWQ//+LHuv/+/v7//v7+/+3c1P/Kln7/pk4l/5cy
        Af+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gz
        AP+YMwH/mDMA/5gzAf+YMwD/fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAP+XMgD+hCwi/w0F6v4CAf3/PxWX/pAwDv+uXzn+4sa5//7+
        /v7//////v39/ubOxP++fmD+nDsM/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgz
        Af+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/3EmQv8DAfr/AAD//xYH
        2/91Jzz/mDMB/7FlQf/o0sj/////////////////+PHt/9Cjjf+lTiP/lzIA/5gzAf+YMwD/mDMB/5gz
        AP+YMwH/mDMB/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/fSkAqgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAP+XMgD+mDMA/5cy
        AP5WHW//AQH+/gAA//8CAfv+SxmC/5UyBf6YMwL/undX/vHl3//+/v7+//////7+/v79/Pz/3bus/q5e
        OP+XMwH+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+fSkAqgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgz
        Af+YMwD/mDMB/5gzAf+XMgL/NRKn/wEB//8BAf//AQH//yQMw/+HLR7/mDMA/5s6C//LmYH/+/j2////
        //////////////7+/v/jx7v/sWQ//5cyAf+YMwH/mDMB/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gz
        Af+YMwD/fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAokoe0pgzAP+XMgD+mDMA/5cyAP6YMwD/kDAO/hgI2P8AAP7+AAD//wAA/v4NBer/biVI/pgz
        AP+XMgD+pk8l/+PIu/7+/v7//v7+/v/////+/v7+/v7+/+PJvP6vYDr/lzIB/pgzAP+XMgD+mDMA/5cy
        AP6YMwD/lzIA/pgzAP+XMgD+fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/3spMv8FAvf/AQH//wAA
        //8BAf//AgH7/1Abev+XMgL/mDMB/5g0Av+/gWP/+PLv///////////////////////+/f3/3r+w/6hT
        K/+XMgH/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+XMgD+mDMA/5cyAP6YMwD/lzIA/pgz
        AP9WHXD+AQH//wAA/v4AAP//AAD+/gAA//81Eqj+kjEL/5cyAP6YMwH/pEof/uTJvf/+/v7+//////7+
        /v7//////v7+/v37+v/Uq5f+oEQX/5cyAP6YMwD/lzIA/pgzAP+XMgD+fSkAqgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gz
        AP+YMwH/mDMA/5gzAf+XMgL/Kg65/wAA//8BAf//AAD//wEB//8AAP//IQvJ/4ouGP+YMwH/mDMA/5g0
        A//Iknj//Pr6////////////////////////////+PLv/8WNcv+aNwf/mDMA/5gzAf+YMwD/fSkAqgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgz
        AP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+hy0d/wsE7f4AAP//AAD+/gAA//8AAP7+AAD//xUH
        3f6BKyf/lzIA/pgzAP+XMgD+sGI9//Tp5P7//////v7+/v/////+/v7+//////7+/v7t3NX/smdD/pcy
        AP+XMgD+fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwH/mDMB/2AgXv8BAf7/AAD//wEB
        //8AAP//AQH//wAA//8OBen/eikz/5gzAf+YMwD/mDMB/6FGGv/nz8T/////////////////////////
        /////////v79/9q2pf+hRRn/fisCqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAf+XMgD+mDMB/5cy
        Af4tD7X/AAD+/gAA//8AAP7+AQH//wAA/v4BAf//CgTu/nUnO/+XMgD+mDMA/5cyAP6bOQn/2rWk/v//
        ///+/v7+//////7+/v7//////v7+/v/////38O3+qYBtqwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAf+YMwH/mDMA/5gz
        Af+YMwH/mDMB/5gzAf+GLR//CAPx/wEB//8BAf//AQH//wAA//8BAf//AAD//wgD8f90Jz3/mDMB/5gz
        AP+YMwH/mDMC/8+fif/+/v7/////////////////////////////////1NPSrgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAP+XMgD+mDMA/5cy
        AP6YMwD/lzIA/pgzAP+XMgD+mDMB/5cyAP6YMwD/VRxx/gAA//8AAP7+AAD//wAA/v4AAP//AAD+/gAA
        //8JA/D+eCg3/5cyAP6YMwD/lzIA/pgzAf/Iknn+/v7+//7+/v7//////v7+/v/////+/v7+1dXVrgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgz
        Af+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP+YMwH/lTIF/x0Kz/8AAP//AQH//wAA
        //8BAf//AAD//wEB//8AAP//DATs/34qLf+YMwH/mDMA/5gzAf+XMgH/xo50//7+/v//////////////
        ////////1dXVrgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAokoe0pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/nQn
        Pf8BAf3+AAD//wAA/v4AAP//AAD+/gAA//8AAP7+AAD//xIG4v6HLR7/lzIA/pgzAP+XMgD+mDMB/8qX
        fv7+/v7//v7+/v/////+/v7+1dXVrgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gz
        AP+YMwH/mDMA/5gzAf82Eqb/AQH//wAA//8BAf//AAD//wEB//8AAP//AQH//wAA//8cCtD/jzAP/5gz
        Af+YMwD/mDMB/5gzAf/Uqpb/////////////////1dXVrgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgz
        AP+XMgD+mDMB/5cyAP6YMwD/lzIA/pgzAP+GLR/+BwL0/wAA/v4AAP//AAD+/gAA//8AAP7+AAD//wAA
        /v4AAP//LhCz/pYyA/+XMgD+mDMA/5cyAP6ZNgX/4se6/v/////+/v7+1dXVrgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gz
        AP+YMwH/mDMA/5gzAf+YMwH/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/SBiH/wAA//8BAf//AAD//wEB
        //8AAP//AQH//wAA//8BAf//AQH//0oZg/+YMwD/mDMB/5gzAP+YMwH/n0IU//Ll4P//////1dXVrgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgz
        AP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+ji8Q/wwE
        7P4AAP//AAD+/gAA//8AAP7+AAD//wAA/v4AAP//AAD+/gIB/P9rJE3+mDMA/5cyAP6YMwD/lzIA/q9h
        O//9+/v+1dXVrgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAf+YMwH/mDMA/5gzAf+YMwH/mDMB/5gzAP+YMwH/mDMA/5gz
        Af+YMwD/mDMB/1Ebd/8BAf//AAD//wEB//8BAf//AQH//wAA//8BAf//AQH//wEB//8MBOv/hy0d/5gz
        AP+YMwH/mDMA/5cyAf/Onoj/1dTUrgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cy
        AP6YMwD/lzIA/pgzAP+XMgD+mDMA/5EwDP4PBef/AAD+/gAA//8AAP7+AAD//wAA/v4AAP//AAD+/gAA
        //8AAP7+Jw2+/5YyA/6YMwD/lzIA/pgzAP+bOQr+v6qgrgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gz
        Af+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP9SHHb/AAD//wEB//8AAP//AQH//wAA
        //8BAf//AAD//wEB//8AAP//AQH//1Qccv+YMwH/mDMA/5gzAf+YMwD/hjsWqwAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+XMgD+mDMA/5cy
        AP6YMwD/lzIA/pgzAP+XMgD+mDMB/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6QMA3/DATs/gAA
        //8AAP7+AAD//wAA/v4AAP//AAD+/gEB//8AAP7+AAD//wYC9f6BKyf/lzIA/pgzAP+XMgD+fSkAqgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgz
        Af+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gz
        AP+YMwH/ShmD/wEB//8AAP//AQH//wAA//8BAf//AAD//wEB//8AAP//AQH//wAA//8nDb//lzIC/5gz
        Af+YMwD/fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAokoe0pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMA/5cyAP6YMwD/lzIA/pgz
        AP+XMgD+mDMA/5cyAP6YMwD/ii4Y/gYC9v8AAP7+AAD//wAA/v4AAP//AAD+/gAA//8AAP7+AAD//wAA
        /v4AAP//YCBf/pgzAP+XMgD+fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/5gzAf+YMwH/mDMB/5gz
        AP+YMwH/mDMA/5gzAf+YMwD/mDMB/5gzAP+YMwH/mDMA/zoTn/8AAP//AQH//wAA//8BAf//AQH//wEB
        //8AAP//AQH//wAA//8BAf//DwXn/48wEP+YMwD/fSkAqgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAokoe0pgzAf+XMgD+mDMA/5cyAP6YMwD/lzIA/pgz
        Af+XMgD+mDMB/5cyAP6YMwD/lzIA/pgzAP+XMgD+mDMB/5cyAP6YMwH/lzIA/nopM/8BAf7+AAD//wAA
        /v4BAf//AAD+/gEB//8AAP7+AAD//wAA/v4AAP//AAD+/kYXiv+XMgD+fSkAqgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAApE4jz5gzAf+YMwD/mDMB/5gz
        Af+YMwH/mDMA/5gzAf+YMwH/mDMB/5gzAf+YMwH/mDMA/5gzAf+YMwH/mDMB/5gzAP+YMwH/mDMA/5cy
        Af8hC8j/AQH//wAA//8BAf//AQH//wEB//8BAf//AQH//wAA//8BAf//AQH//wUC9/+FLCH/eygApgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAr2U/hJw7
        DP6YNAL+mDQC/5g0Av6YNAL/mDQC/pg0Av+YNAL+mDQC/5g0Av6YNAL/mDQC/pg0Av+YNAL+mDQC/5g0
        Av6YNAL/mDQC/pg0Av9eIGX+AQH//wAA/v4AAP//AAD+/gEB//8AAP7+AAD//wAA/v4AAP//AAD+/gAA
        //86FaL+ZyMMWgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAtG9MArRsSVKxaUVvsWlFb7FpRW+xaUVvsWlFb7FpRW+xaUVvsWlFb7FpRW+xaUVvsWlFb7Fp
        RW+xaUVvsWlFb7FpRW+xaUVvsWlFb7FpRW+VXm5wHBz89AAA//8BAf//AAD//wEB//8AAP//AQH//wAA
        //8BAf//AAD//wEB//8CAcSoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANzb4pQEB/v4AAP//AAD+/gAA
        //8AAP7+AAD//wAA/v4AAP//AAD+/gAA9PMAAJ8eAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQ0P3SgoK
        //8BAf//AAD//wEB//8AAP//AQH//wAA//8BAf//AAD+/wAAtHwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAARETyByMj/OwAAP//AAD+/gEB//8AAP7+AAD//wAA/v4AAP//AADj2wAAmgkAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAADQ0+aEBAf//AQH//wEB//8AAP//AQH//wAA//8AAP7+AACnTwAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEJC9lIHB///AAD+/gAA//8AAP7+AAD//wEB
        /v4AAM+2AACTAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEND8w4dHf74AQH//wEB
        //8AAP//AQH//wAA+PcAAJwpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAsLPq/AAD+/gAA//8AAP7+AAD+/wAAvYsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAA6OvR7AgL//wEB//8AAP//AADo5AAAmA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAQO88DAz+/gEB//8AAP7+AACwXgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/P+kJHR399QEB//8AANXEAACUAgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKSn3xwAA
        +/sAAKM1AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAMzP1kwEBwZoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAANzfjTxERoBYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/AAAAAP8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4A
        AAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/
        AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4A
        AAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/
        AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4AAAAAfwAA/gAAAAB/AAD+AAAAAH8AAP4A
        AAAA/wAA////8AD/AAD////wAf8AAP////AB/wAA////+AP/AAD////4A/8AAP////gH/wAA/////A//
        AAD////8D/8AAP////wf/wAA/////B//AAD////+P/8AAP////5//wAA/////n//AAAoAAAAIAAAAEAA
        AAABACAAAAAAAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyEABBOhMAbjoT
        AG46EwBuOhMAbjoTAG46EwBuOhMAbjoTAG46EwBuOhMAbjoTAG46EwBuOhMAbjoTAG46EwBuOhMAbjoT
        AG46EwBuOhMAbjoTAG4hCgA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWSoUIYA5
        Svuqa2H/vX1e/qZPJP+YMwH/lzIA/5cyAP6XMgD/lzIA/5cyAP+XMgD+lzIA/5cyAP+XMgD/lzIA/pcy
        AP+XMgD/lzIA/5cyAP6XMgD/lzIA/4suAPUpDQARAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACESCo3lzQG/z0Umv9vP4f+1KyZ/+7e1//Uqpf/tGpH/po2Bv+YMwH/mDMB/5cyAP6YMwH/mDMB/5gz
        Af+XMgD+mDMB/5gzAf+YMwH/lzIA/pgzAf+YMwH/lzIA/0YXABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAIZJKzeZNAP+kTEL/h8LzP4rD7j+kUI5/tGlkP779/b+9u3p/tKnkv6qWDD+mDMB/pcy
        AP6XMgD+lzIA/pcyAP6XMgD+lzIA/pcyAP6XMgD+lzIA/pcyAP6XMgD+RxcAHAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAhkkrN5k0A/+YMwH/hi0e/g4F5/8KBO//YSBd/6NJHf7XsJ3//fz8//7+
        /v/n0MX+unhX/5k2Bf+YMwH/lzIA/pgzAf+YMwH/mDMB/5cyAP6YMwH/mDMB/5cyAP9HFwAcAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACGSSs3mTQD/5gzAf+XMgD+ciZA/wMB+/8BAf7/NxKk/pAw
        Df+rWTH/5s7D//7+/v7/////8eTd/8GFaP+aOAj+mDMB/5gzAf+YMwH/lzIA/pgzAf+YMwH/lzIA/0cX
        ABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIZJKzeZNAP/mDMB/5cyAP6YMwH/UBt6/wEB
        //8AAP7+GAjX/34qK/+YMwL/v4Fj/vjx7v////////////Lm4f7AgmX/mTUF/5gzAf+XMgD+mDMB/5gz
        Af+XMgD/RxcAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhkkrN5k0A/6XMgD+lzIA/pcy
        AP6WMgP+KA68/gAA/v4AAP7+CAPy/mkjUP6XMgD+okcb/uHEtv7+/v7+/v7+/v7+/v7u3tb+tm9N/pgz
        Af6XMgD+lzIA/pcyAP5HFwAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACGSSs3mTQD/5gz
        Af+XMgD+mDMB/5gzAf+HLRz/CwTt/gEB//8BAf//AgH8/1QcdP6XMgH/mDMC/8SLcP/8+fj+////////
        ///+/v7/4sa5/qhTKv+YMwH/lzIA/0cXABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIZJ
        KzeZNAP/mDMB/5cyAP6YMwH/mDMB/5gzAf9iIVz+AQH+/wEB//8BAf//AQH+/kQXjv+XMgL/mDMB/65g
        Ov706uX////////////+/v7+/Pr5/86fiP+bOgv/SBkCHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAhkkrN5k0A/+YMwH/lzIA/pgzAf+YMwH/mDMB/5cyAf4uELL/AQH//wEB//8AAP7+AQH//zwU
        m/+WMgP/lzIA/qNJHf/r2ND///////7+/v7///////////Dj3f9mUkkdAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACGSSs3mTQD/pcyAP6XMgD+lzIA/pcyAP6XMgD+lzIA/oYtHv4IA/L+AAD+/gAA
        /v4AAP7+AAD+/jwUmv6XMgL+lzIA/p4+EP7mzsL+/v7+/v7+/v7+/v7+/v7+/n19fR8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAIZJKzeZNAP/mDMB/5cyAP6YMwH/mDMB/5gzAf+XMgD+mDMB/1Mc
        dP8BAf//AAD+/gEB//8BAf//AQH//0UXjP6YMwH/mDMB/5w7DP/mzsP+///////////+/v7/fX19HwAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhkkrN5k0A/+YMwH/lzIA/pgzAf+YMwH/mDMB/5cy
        AP6YMwH/lDEG/xkI1v8AAP7+AQH//wEB//8BAf//AQH+/lYdcP+YMwH/mDMB/50+D/7s2dD///////7+
        /v99fX0fAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACGSSs3mTQD/5gzAf+XMgD+mDMB/5gz
        Af+YMwH/lzIA/pgzAf+YMwH/ayRM/wEB/v4BAf//AQH//wEB//8AAP7+AgH7/20kSv+YMwH/lzIA/qNK
        Hv/27en//v7+/319fR8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIZJKzeZNAP+lzIA/pcy
        AP6XMgD+lzIA/pcyAP6XMgD+lzIA/pcyAP6XMgH+Jw2+/gAA/v4AAP7+AAD+/gAA/v4AAP7+CwTt/oQs
        Iv6XMgD+lzIA/rNqRv79/fz+fX19HwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhkkrN5k0
        A/+YMwH/lzIA/pgzAf+YMwH/mDMB/5cyAP6YMwH/mDMB/5gzAf91Jzv+AQH+/wEB//8BAf//AAD+/gEB
        //8BAf//IAvJ/5QxBv6YMwH/lzIB/9Ook/97eXgfAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACGSSs3mTQD/5gzAf+XMgD+mDMB/5gzAf+YMwH/lzIA/pgzAf+YMwH/mDMB/5gzAf4rDrj/AQH//wEB
        //8AAP7+AQH//wEB//8BAf//ShmE/pgzAf+YMwH/nT4P/15CNR0AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAIZJKzeZNAP/mDMB/5cyAP6YMwH/mDMB/5gzAf+XMgD+mDMB/5gzAf+YMwH/lzIA/nMn
        Pv8BAf7/AQH//wAA/v4BAf//AQH//wEB//8DAfr+eikz/5gzAf+XMgD/SBgAHAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAhkkrN5k0A/6XMgD+lzIA/pcyAP6XMgD+lzIA/pcyAP6XMgD+lzIA/pcy
        AP6XMgD+lzIB/iIMx/4AAP7+AAD+/gAA/v4AAP7+AAD+/gAA/v4fCsz+ljIE/pcyAP5HFwAcAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACGSSs3mTQD/5gzAf+XMgD+mDMB/5gzAf+YMwH/lzIA/pgz
        Af+YMwH/mDMB/5cyAP6YMwH/ZCFY/wEB//8AAP7+AQH//wEB//8BAf//AAD+/gEB//9aHmn/lzIA/0cX
        ABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIdMLzWZNQP/mDMB/5cyAP6YMwH/mDMB/5gz
        Af+XMgD+mDMB/5gzAf+YMwH/lzIA/pgzAf+UMQb/DwXm/wAA/v4BAf//AQH//wEB//8AAP7+AQH//w0F
        6f+PLw//RRYAGgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhVE3CqlVK8ehRxrzoUca86FH
        GvOhRxrzoUca86FHGvOhRxrzoUca86FHGvOhRxrzoUca86FHGvNTKZf3AQH+/gEB//8BAf//AQH//wAA
        /v4BAf//AQH//zkUhcQ3EgUCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF9MuSkKCv7+AAD+/gAA
        /v4AAP7+AAD+/gAA/v4AAPr5AgCWKgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACEh
        /M0BAf//AQH//wEB//8AAP7+AAD//wAAxY4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAANzf3eQEB//8BAf//AQH//wAA/v4AAO3nAACCDwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAABBQeEqCQn+/gEB//8BAf//AAD+/gAAtWEAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcHPzfAAD+/gAA/v4AAN/IAABoAgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACoq95wBAf//AAD8/AAAnjcAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOjrwXwEB//8AAMudAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA5OcwnCAjy7wAA
        ixcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACkp
        lQIbG75iAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPgAAB/wAAAP8AAAD/AA
        AA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AA
        AA/wAAAP8AAAD/AAAA/wAAAP///AH///4D///+A////gf///8H////D////x////8f////P/KAAAABgA
        AAAwAAAAAQAgAAAAAABgCQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACUNBhNSHgaKUhsAklIb
        AJJSGwCSUhsAklIbAJJSGwCSUhsAklIbAJJSGwCSUhsAklIbAJJSGwCSUhsAklIbAJJKGACIEQUADwAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAH47G2VzO3P/p3WC/82chP+3cE3/nDoL/5gzAP+YMwD/mDMA/5gz
        AP+YMwD/mDMA/5gzAf+YMwH/mDMA/5gzAP+XMgD/URoAUgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItF
        ImmVMgX/MBCv/1kohf/IlYD/8+fi/9u4p/+1bEn/mTUD/5gzAP+YMwD/mDMA/5gzAP+YMwD/mDMA/5gz
        AP+YMwD/Wx4AVQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwD/ji8R/xcI2f8jDMT/ijky/86f
        iP/7+Pf/8eTe/8aOc/+dPQ7/mDMA/5gzAf+YMwH/mDMA/5gzAP+YMwD/Wx4AVQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAItFImmYMwD/mDMA/30qLv8GAvX/CQPw/2YiVP+lTiP/4cO1//7+/v/38e3/ypd//50+
        D/+YMwH/mDMA/5gzAP+YMwD/Wx4AVQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwD/mDMA/5gz
        AP9bH2b/AAD//wEB/v9HGIn/ljIE/718Xf/48e7///////bu6//Ei2//mjcG/5gzAP+YMwD/Wx4AVQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwD/mDMA/5gzAP+XMgH/LxCx/wAA//8AAP//MBCv/5Ix
        Cv+kSx//6NHG////////////79/Y/7RrSP+XMgD/Wx4AVQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItF
        ImmYMwH/mDMA/5gzAf+YMwH/ii4Y/wwE7P8AAP//AQH//yQMwv+PMA//mjcH/9avnP////////////7+
        /v/cuqr/az0nVQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwH/mDMA/5gzAf+YMwH/mDMB/18g
        Yf8AAP//AQH//wEB//8iC8f/kDAO/5gzAf/LmID//v7+////////////nJqaVwAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAItFImmYMwD/mDMA/5gzAP+YMwH/mDMB/5cyAv8lDML/AAD//wAA//8AAP//Jw2+/5Mx
        CP+XMgH/yZV8//7+/v//////nZ2dVwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwD/mDMA/5gz
        AP+YMwD/mDMA/5gzAP97KTL/AgH8/wAA//8AAP//AAD//zcSpP+XMgH/mDMB/9Oplf//////nZ2dVwAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwD/mDMA/5gzAP+YMwH/mDMB/5gzAP+YMwD/OROh/wAA
        //8AAP//AAD//wEB//9UHHP/mDMA/5k2Bf/n0MX/nZ2dVwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItF
        ImmYMwD/mDMA/5gzAP+YMwH/mDMB/5gzAP+YMwD/hCwh/wQC+f8AAP//AAD//wEB//8DAfr/dyg4/5gz
        AP+mTyX/lY6KVwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwD/mDMA/5gzAP+YMwD/mDMA/5gz
        AP+YMwD/mDMA/zwUm/8AAP//AAD//wAA//8AAP//FwjZ/5IxCv+YMwD/YSkOVQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAItFImmYMwD/mDMA/5gzAP+YMwH/mDMB/5gzAP+YMwD/mDMA/4ErJ/8CAfz/AAD//wEB
        //8BAf//AAD//0cYiP+YMwD/Wx4AVQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAItFImmYMwH/mDMA/5gz
        Af+YMwH/mDMB/5gzAf+YMwD/mDMB/5gzAf8tD7T/AQH//wEB//8BAf//AQH//wQC+f+BKyf/Wx4AVQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAJBQMFSZNQT+mDMB/5gzAf+YMwH/mDMB/5gzAf+YMwH/mDMB/5gz
        Af9sJEz/AQH//wEB//8BAf//AQH//wAA//8xEa7+URsDQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACGUzkwhVI4N4VSODeFUjg3hVI4N4VSODeFUjg3hVI4N4VSODeAVGM3FRX85QEB//8BAf//AAD//wAA
        //8BANWuAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAALS35jwAA//8AAP//AAD//wAA+PYAAHshAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANjbIPAIC
        //8BAf//AQH//wAAwoEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJCR9AxMT/e0BAf//AADt4AAAWwoAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAACIi+K0AAP7+AACmVAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAw6nEAANy9AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAC4uszgEBIcsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA
        BwDgAAcA4AAHAOAABwDgAAcA4AAHAOAABwDgAAcA4AAHAOAABwDgAAcA4AAHAOAABwDgAAcA4AAHAOAA
        BwDgAAcA8AAPAP/8DwD//B8A//wfAP/+PwD//n8A//5/ACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAEgeGFd3Qy+2bSoJtmkjALZpIwC2aSMAtmkjALZpIwC2aSMAtmkj
        ALZpIwC2NxIAUAAAAAAAAAAAAAAAAAAAAACOPhebVyR+/qB2mP/VrJn+vn9g/5w8DP6YMwD/lzIA/pgz
        AP+XMgD+mDMA/28kAI0AAAAAAAAAAAAAAAAAAAAAjz8Xm5MxCP8jDMT/Tx+H/8SNdv/y5+H/0aSO/6NJ
        HP+YMwH/mDMA/5gzAf9vJQCNAAAAAAAAAAAAAAAAAAAAAI8/F5uXMgD+hSwf/woE7v4oDb3/lkIt/t6/
        r//7+Pf+0aWP/59CFP6YMwD/byUAjQAAAAAAAAAAAAAAAAAAAACPPxebmDMA/5gzAf9jIVn/AQH//xYH
        2/+CLCX/woZp//z5+P/38O3/wohr/3AnA40AAAAAAAAAAAAAAAAAAAAAjz8Xm5cyAP6YMwD/lzIB/i8Q
        sP8AAP7+DwXm/4ArKP6xZT//+PLv/v////+0rKiOAAAAAAAAAAAAAAAAAAAAAI8/F5uYMwD/mDMB/5gz
        AP+GLR//BwL0/wEB//8SBuL/hy0c/65eOP/69fP/vb29jwAAAAAAAAAAAAAAAAAAAACPPxeblzIA/pgz
        AP+XMgD+mDMA/0oZgv4BAf//AAD+/h8KzP+TMQn+uXVT/729vY8AAAAAAAAAAAAAAAAAAAAAjz8Xm5gz
        Af+YMwH/mDMA/5gzAf+PMA//CwTt/wAA//8BAf//QBWU/5gyAf+SaFSOAAAAAAAAAAAAAAAAAAAAAI8/
        F5uXMgD+mDMA/5cyAP6YMwD/lzIA/ksZgf8AAP7+AAD//wEB/f5xJkH/byUAjQAAAAAAAAAAAAAAAAAA
        AACQQBiamDMA/5gzAf+YMwD/mDMB/5gzAP+KLhj/BAL4/wEB//8AAP//GgnU/2wkA40AAAAAAAAAAAAA
        AAAAAAAAcUEpNIxIJnqMSCZ6jEgmeoxIJnqMSCZ6jEgmei8g08cAAP//AAD+/gAA/f0eCko8AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAmJrFRAQH//wAA//8AAM2hAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHBxfCgkJ/vcAAPbxAABRGQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZGfm+AACtdAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAISHEXgAA
        MAUAAAAAAAAAAAAAAAAAAAAAwAMAAMADAADAAwAAwAMAAMADAADAAwAAwAMAAMADAADAAwAAwAMAAMAD
        AADAAwAA/4cAAP+HAAD/zwAA/88AAA==
</value>
  </data>
</root>