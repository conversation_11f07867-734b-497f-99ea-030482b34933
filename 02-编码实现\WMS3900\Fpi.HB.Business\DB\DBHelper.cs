﻿using System;
using System.Collections.Generic;
using System.Text;
using Fpi.Data.Config;
using Fpi.DB;
using Fpi.DB.Manager;
using Fpi.DB.SqlUtil;

namespace Fpi.HB.Business.DB
{
    public static class DBHelper
    {
        #region 属性字段

        private static readonly object lockObj = new object();

        #endregion

        #region 实时数据存储

        /// <summary>
        /// 写实时数据到数据库
        /// </summary>
        public static void WriteRealDataToDb(DateTime time)
        {
            lock(lockObj)
            {
                _saveRealData(time);
            }
        }

        /// <summary>
        /// 写实时数据到数据库
        /// </summary>
        /// <param name="time"></param>
        private static void _saveRealData(DateTime time)
        {
            string searchSql = "select count(*) from " + DBConfig.REAL_DATA_TABLE + " where datatime='" + time.ToString(DBConfig.DATETIME_FORMAT) + "'";
            int count = DbAccess.QueryRecordCount(searchSql);
            if(count > 0)
            {
                UpdateRealData(time);
            }
            else
            {
                //插入新数据
                InsertRealData(time);
            }
        }

        private static void UpdateRealData(DateTime time)
        {
            var sb = new StringBuilder();
            sb.Append("update ").Append(DBConfig.REAL_DATA_TABLE).Append(" set ");
            // 各测量点及有效性
            foreach(ValueNode valueNode in DataManager.GetInstance().GetAllValueNodes())
            {
                string valueNodeId = valueNode.id.Trim();
                string valueNodeData = "";
                int valueNodeFlag = 0;

                double value = valueNode.GetValue();

                valueNodeData = double.IsNaN(value) || double.IsInfinity(value) ? "null" : value.ToString("G10");
                valueNodeFlag = valueNode.State;

                sb.Append(DBConfig.PREFIX_F).Append(valueNodeId).Append(" = ").Append(valueNodeData).Append(",");
                sb.Append(DBConfig.PREFIX_F).Append(valueNodeId).Append(DBConfig.POSTFIX).Append(" = ").Append(valueNodeFlag).Append(",");
            }
            sb.Remove(sb.Length - 1, 1);
            sb.Append(" where datatime = '").Append(time.ToString(DBConfig.DATETIME_FORMAT)).Append("'");
            DbAccess.ExecuteNonQuery(sb.ToString());
        }

        private static void InsertRealData(DateTime time)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("insert into ").Append(DBConfig.REAL_DATA_TABLE).Append("(datatime");

            // 各测量点及有效性
            foreach(VarNode varNode in DataManager.GetInstance().VarNodes)
            {
                if(varNode is ValueNode)
                {
                    string valueNodeId = varNode.id.Trim();
                    sb.Append(", ").Append(DBConfig.PREFIX_F).Append(valueNodeId);
                    sb.Append(", ").Append(DBConfig.PREFIX_F).Append(valueNodeId).Append(DBConfig.POSTFIX);
                }
            }

            // 水质级别
            sb.Append(") values('").Append(time.ToString(DBConfig.DATETIME_FORMAT)).Append("'");

            // 各测量点及有效性具体值
            foreach(VarNode varNode in DataManager.GetInstance().VarNodes)
            {
                if(varNode is ValueNode)
                {
                    ValueNode valueNode = varNode as ValueNode;

                    double value = valueNode.GetValue();
                    int nodeFlag = valueNode.State;
                    if(double.IsNaN(value) || double.IsInfinity(value))
                    {
                        sb.Append(", null");
                    }
                    else
                    {
                        sb.Append(", ").Append(value.ToString("G10"));
                    }

                    sb.Append(", ").Append(nodeFlag);
                }
            }
            sb.Append(")");

            DbAccess.ExecuteNonQuery(sb.ToString());
        }

        #endregion

        #region 数据查询

        /// <summary>
        /// 获取因子最近数据库保存的值
        /// </summary>
        /// <param name="fullNodeIds"></param>
        /// <returns></returns>
        public static Dictionary<string, double> GetLatestValues(List<string> fullNodeIds)
        {
            Dictionary<string, double> values = new Dictionary<string, double>();

            List<DBNode> tableNodes = new List<DBNode>();

            foreach(string fullnodeid in fullNodeIds)
            {
                DBNode dbnode = new DBNode(fullnodeid);
                tableNodes.Add(dbnode);
            }

            FpiTable fpitable = FpiDataBase.GetInstance().FindTableByName(DBConfig.REAL_DATA_TABLE);
            if(fpitable != null)
            {
                FpiRow row = fpitable.GetFirstRow("datatime", OrderType.Desc);
                foreach(DBNode dbnode in tableNodes)
                {
                    float dataValue;
                    try
                    {
                        dataValue = Convert.ToSingle(row?.GetFieldValue(DBConfig.PREFIX_F + dbnode.NodeId));
                    }
                    catch
                    {
                        dataValue = float.NaN;
                    }
                    dbnode.Value = dataValue;

                    if(values.ContainsKey(dbnode.FullNodeID))
                    {
                        values[dbnode.FullNodeID] = dbnode.Value;
                    }
                    else
                    {
                        values.Add(dbnode.FullNodeID, dbnode.Value);
                    }
                }
            }

            return values;
        }

        /// <summary>
        /// 获取因子历史统计值
        /// </summary>
        /// <param name="fullNodeIds"></param>
        /// <param name="begin"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public static Dictionary<string, DBNode> GetStatValues(List<string> fullNodeIds, DateTime begin, DateTime end)
        {
            string startTime = begin.ToString(DBConfig.DATETIME_FORMAT);
            string endTime = end.ToString(DBConfig.DATETIME_FORMAT);
            Dictionary<string, DBNode> values = new Dictionary<string, DBNode>();

            try
            {
                List<DBNode> tableNodes = new List<DBNode>();

                foreach(string fullnodeid in fullNodeIds)
                {
                    DBNode dbnode = new DBNode(fullnodeid);
                    tableNodes.Add(dbnode);
                }

                StringBuilder sb = new StringBuilder("select ");

                foreach(DBNode dbnode in tableNodes)
                {
                    string colname = DBConfig.PREFIX_F + dbnode.NodeId;
                    sb.Append("min(").Append(colname).Append("),");
                    sb.Append("max(").Append(colname).Append("),");
                    sb.Append("avg(").Append(colname).Append("),");
                }
                sb.Remove(sb.Length - 1, 1);
                sb.Append(" from ").Append(DBConfig.REAL_DATA_TABLE).Append(" where datatime>='").Append(startTime).Append("' and datatime<'").Append(endTime).Append("'");

                float[] results = DbAccess.ExecuteQueryReturnFloatValues(sb.ToString());
                // 列索引
                int colIndex = 0;
                foreach(DBNode dbnode in tableNodes)
                {
                    dbnode.Min = results[colIndex++];
                    dbnode.Max = results[colIndex++];
                    dbnode.Avg = results[colIndex++];
                    if(values.ContainsKey(dbnode.FullNodeID))
                    {
                        values[dbnode.FullNodeID] = dbnode;
                    }
                    else
                    {
                        values.Add(dbnode.FullNodeID, dbnode);
                    }
                }
            }
            catch(Exception ex)
            {
                Log.LogUtil.Error("exception", ex.StackTrace + "\r\n" + ex.Message);
                throw ex;
            }
            return values;
        }

        #endregion
    }
}
