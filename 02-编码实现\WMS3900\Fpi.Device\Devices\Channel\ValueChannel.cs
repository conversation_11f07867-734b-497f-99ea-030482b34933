﻿//==================================================================================================
//类名：     SimulateDataChannel   
//创建人:    曹旭
//创建时间:  2012-9-24 15:19:29
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using Fpi.Data.Config;
using Fpi.Util.Reflection;
using Fpi.Xml;

namespace Fpi.Devices.Channel
{
    public class ValueChannel : DataChannel
    {
        public string ProcessorImp;

        public string UnitId;

        public string ProcessorParam;

        public ValueNode ValueNode { get; set; } = null;

        public Processor DataProcessor
        {
            get;
            set;
        }

        public override BaseNode Init(System.Xml.XmlNode node)
        {
            base.Init(node);

            this.ValueNode = (ValueNode)this.GetVarNode();

            if(!string.IsNullOrEmpty(this.ProcessorImp))
            {
                this.DataProcessor = (Processor)ReflectionHelper.CreateInstance(this.ProcessorImp, new object[] { this.ProcessorParam });
            }

            return this;
        }

        public Unit GetUnit()
        {
            return !string.IsNullOrEmpty(this.UnitId) ? GetVarNode() is ValueNode vn ? vn.GetUnit(this.UnitId) : null : null;
        }
    }
}
