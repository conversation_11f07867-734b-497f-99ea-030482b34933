﻿using System.Collections.Generic;


namespace Fpi.Data.ExpParser
{
    public static class Define
    {
        public static Dictionary<EKeyword, KeyWord> KeyWords;
        public static Dictionary<EOperatorType, Operator> Operators;

        static Define()
        {
            var dictionary = new Dictionary<EOperatorType, Operator>
            {
                {EOperatorType.LeftParen, new Operator(0, 0x63, EOperatorType.LeftParen, "(")},
                {EOperatorType.RightParen, new Operator(0, 0x63, EOperatorType.RightParen, ")")},
                {EOperatorType.Plus, new Operator(2, 0x2c, EOperatorType.Plus, "+")},
                {EOperatorType.Minus, new Operator(2, 0x2c, EOperatorType.Minus, "-")},
                {EOperatorType.Multiply, new Operator(2, 0x37, EOperatorType.Multiply, "*")},
                {EOperatorType.Divide, new Operator(2, 0x37, EOperatorType.Divide, "/")},
                {EOperatorType.Mod, new Operator(2, 0x37, EOperatorType.Mod, "%")},
                {EOperatorType.Positive, new Operator(1, 0x4d, EOperatorType.Positive, "+")},
                {EOperatorType.Negative, new Operator(1, 0x4d, EOperatorType.Negative, "-")},
                {EOperatorType.LessThan, new Operator(2, 0x21, EOperatorType.LessThan, "<")},
                {EOperatorType.GreaterThan, new Operator(2, 0x21, EOperatorType.GreaterThan, ">")},
                {EOperatorType.Equal, new Operator(2, 0x16, EOperatorType.Equal, "=")},
                {EOperatorType.NotEqual, new Operator(2, 0x16, EOperatorType.NotEqual, "<>")},
                {EOperatorType.LessEqual, new Operator(2, 0x21, EOperatorType.LessEqual, "<=")},
                {EOperatorType.GreaterEqual, new Operator(2, 0x21, EOperatorType.GreaterEqual, ">=")}
            };
            Operators = dictionary;
            var dictionary2 = new Dictionary<EKeyword, KeyWord>
            {
                {EKeyword.IF, new KeyWord(EKeyword.IF, EDataType.Dunknown, "if")},
                {EKeyword.CASE, new KeyWord(EKeyword.CASE, EDataType.Dunknown, "case")},
                {EKeyword.AND, new KeyWord(EKeyword.AND, EDataType.Dbool, "and")},
                {EKeyword.OR, new KeyWord(EKeyword.OR, EDataType.Dbool, "or")},
                {EKeyword.TRUE, new KeyWord(EKeyword.TRUE, EDataType.Dbool, "true")},
                {EKeyword.FALSE, new KeyWord(EKeyword.FALSE, EDataType.Dbool, "false")},
                {EKeyword.NOT, new KeyWord(EKeyword.NOT, EDataType.Dbool, "not")},
                {EKeyword.Len, new KeyWord(EKeyword.Len, EDataType.Dint, "len")},
                {EKeyword.NowDate, new KeyWord(EKeyword.NowDate, EDataType.Ddatetime, "nowdate")},
                {EKeyword.ToInt, new KeyWord(EKeyword.ToInt, EDataType.Dint, "toint")},
                {EKeyword.ToDouble, new KeyWord(EKeyword.ToDouble, EDataType.Ddouble, "todouble")},
                {EKeyword.ToDateTime, new KeyWord(EKeyword.ToDateTime, EDataType.Ddatetime, "todatetime")},
                {EKeyword.ToString, new KeyWord(EKeyword.ToString, EDataType.Dstring, "tostring")},
                {EKeyword.GetNodeValue, new KeyWord(EKeyword.GetNodeValue, EDataType.Ddouble, "getnodevalue")},
                {EKeyword.GetNodeIntValue, new KeyWord(EKeyword.GetNodeValue, EDataType.Dint, "getnodeintvalue")},
                {EKeyword.GetNodeBoolValue, new KeyWord(EKeyword.GetNodeBoolValue, EDataType.Dbool, "getnodeboolvalue")},
                {EKeyword.GetAllNodeBoolValueOR, new KeyWord(EKeyword.GetAllNodeBoolValueOR, EDataType.Dbool, "getallnodeboolvalueor")},
                {EKeyword.GetAlarmNodeBoolValue, new KeyWord(EKeyword.GetAlarmNodeBoolValue, EDataType.Dbool, "getalarmnodeboolvalue")},
                {EKeyword.DoubleNaN, new KeyWord(EKeyword.DoubleNaN, EDataType.Dbool, "doublenan")},
                {EKeyword.IsAlarmNode, new KeyWord(EKeyword.IsAlarmNode, EDataType.Dbool, "isalarmnode")},
                {EKeyword.IsNodeCycleValueAlarm, new KeyWord(EKeyword.IsNodeCycleValueAlarm, EDataType.Dbool, "isalarmnodetag")},
                {EKeyword.Invoke, new KeyWord(EKeyword.Invoke, EDataType.Dunknown, "func")}
            };
            KeyWords = dictionary2;
        }
    }
}

