using Fpi.Communication.Commands;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Ports.FpiPorts;
using Fpi.Communication.Ports.SyncPorts;
using Fpi.Communication.Protocols;

namespace Fpi.Devices.DeviceProtocols
{
    public class FpiBoardParser : Parser
    {
        protected override IPort[] ConstructPorts()
        {
            IPort[] ports = new IPort[4];
            ports[0] = new InsertPort();
            ports[1] = new SyncPort();
            ports[2] = new FpiRouterPort();
            ports[3] = new CommandPort();
            return ports;
        }
    }
}
