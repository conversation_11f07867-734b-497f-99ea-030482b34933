﻿using System.Windows.Forms;

namespace Fpi.Devices.Channel
{
    public partial class ConfigRoundPanel : UserControl, IParamConfig
    {
        public ConfigRoundPanel()
        {
            InitializeComponent();
        }

        #region IParamConfig 成员

        public string SaveConfig()
        {
            return rdoBtn1.Checked ? this.txtRound.Text + ",0" : rdoBtn2.Checked ? this.txtRound.Text + ",1" : this.txtRound.Text + ",0";
        }

        public bool CheakParam()
        {
            int coeff;

            return int.TryParse(this.txtRound.Text, out coeff) && (rdoBtn1.Checked || rdoBtn2.Checked);
        }

        public void ShowParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length == 2)
            {
                this.txtRound.Text = paramStrs[0];
                if(paramStrs[1] == "1")
                {
                    rdoBtn2.Checked = true;
                }
                else
                {
                    rdoBtn1.Checked = true;
                }
            }
        }

        #endregion
    }
}
