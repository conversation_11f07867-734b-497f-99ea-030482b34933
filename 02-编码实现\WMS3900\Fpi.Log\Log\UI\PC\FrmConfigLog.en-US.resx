﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 16</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnOK.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>308, 16</value>
  </data>
  <data name="btnCancel.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 400</value>
  </data>
  <data name="panel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>408, 51</value>
  </data>
  <data name="chkList.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 25</value>
  </data>
  <data name="chkList.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 5, 3, 5</value>
  </data>
  <data name="chkList.Size" type="System.Drawing.Size, System.Drawing">
    <value>238, 452</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lsbMsgTypes.ItemHeight" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="lsbMsgTypes.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 25</value>
  </data>
  <data name="lsbMsgTypes.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lsbMsgTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 364</value>
  </data>
  <data name="splitContainer1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 6</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 15</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Log types</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 6</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 15</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Log output mode</value>
  </data>
  <data name="splitContainer1.Size" type="System.Drawing.Size, System.Drawing">
    <value>408, 400</value>
  </data>
  <data name="splitContainer1.SplitterDistance" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="splitContainer1.SplitterWidth" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 15</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>408, 451</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Log configuration</value>
  </data>
</root>