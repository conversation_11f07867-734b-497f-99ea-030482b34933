﻿using System;
using System.Collections.Generic;
using Fpi.Data.Config;
using Fpi.Util.EnumRelated;

namespace Fpi.HB.Business.Tasks
{
    /// <summary>
    /// 数据模拟任务（开发阶段用）
    /// </summary>
    public class DateSimulateTask : CustomTask
    {
        #region 字段属性

        private static readonly Random _rd = new Random();

        /// <summary>
        /// 累计流量因子的上一次值存储字典
        /// </summary>
        private static readonly Dictionary<string, double> _lastTotalFlowValues = new Dictionary<string, double>();

        #endregion

        #region 构造

        public DateSimulateTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "数据模拟任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "DateSimulateTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "数据模拟任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "数据模拟任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            foreach(ValueNode valueNode in DataManager.GetInstance().GetAllValueNodes())
            {
                // 优化State字段生成逻辑：95%概率返回N状态，5%概率返回其他状态
                if(_rd.NextDouble() < 0.95)
                {
                    valueNode.State = (int)eValueNodeState.N;
                }
                else
                {
                    valueNode.State = EnumOperate.GetRandomEnum(typeof(eValueNodeState));
                }

                if(valueNode.Fixup == null)
                {
                    try
                    {
                        double v;

                        // 特殊处理累计流量因子（编码e01204）
                        if(valueNode.id == "e01204")
                        {
                            v = GenerateMonotonicIncreasingValue(valueNode.id);
                        }
                        else
                        {
                            // 其他因子按原逻辑生成
                            int i = 0;
                            if(valueNode.DefaultAlarmLimit != null)
                            {
                                do
                                {
                                    v = _rd.Next((int)valueNode.DefaultAlarmLimit.alarmLower * 100, (int)Math.Ceiling(valueNode.DefaultAlarmLimit.alarmUpper) * 100) * 0.01;
                                    i++;
                                } while(valueNode.IsOverAlarmLimit(v) && i < 10);
                            }
                            else
                            {
                                v = _rd.Next(100, 10000) * 0.01;
                            }
                        }

                        valueNode.SetValue(v);
                    }
                    catch
                    {
                        // ignored
                    }
                }
            }
        }

        /// <summary>
        /// 为累计流量因子生成单调递增的值
        /// </summary>
        /// <param name="nodeId">因子ID</param>
        /// <returns>单调递增的值</returns>
        private double GenerateMonotonicIncreasingValue(string nodeId)
        {
            // 获取上一次的值，如果不存在则初始化为0
            if(!_lastTotalFlowValues.ContainsKey(nodeId))
            {
                _lastTotalFlowValues[nodeId] = 0.0;
            }

            // 生成0-4之间的随机增量
            double increment = _rd.NextDouble() * 4; // 0-4之间的随机数

            // 计算新值并更新存储
            double newValue = _lastTotalFlowValues[nodeId] + increment;
            _lastTotalFlowValues[nodeId] = newValue;

            return newValue;
        }

        #endregion
    }
}