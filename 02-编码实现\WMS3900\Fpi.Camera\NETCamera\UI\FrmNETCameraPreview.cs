﻿/*==================================================================================================
** 类 名 称:MainPreviewForm
** 创 建 人:xiaopeng_liu
** 当前版本：V1.0.0
** CLR 版本:4.0.30319.42000
** 创建时间:2017/4/18 10:40:41

** 修改人		修改时间		修改后版本		修改内容


** 功能描述：视频监控主界面
 
==================================================================================================
 Copyright @2017. Focused Photonics Inc. All rights reserved.
==================================================================================================*/
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Threading;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.UI.PC.DockForms;
using Fpi.Util.Picture;

namespace Fpi.Camera.UI
{
    /// <summary>
    /// 视频监控主界面
    /// </summary>
    public partial class FrmNETCameraPreview : BaseMainView
    {
        #region 字段属性

        /// <summary>
        /// 右侧视频显示界面
        /// </summary>
        private UC_Preview _previewPanel;

        #endregion

        #region 构造

        /// <summary>
        /// 视频监控主界面
        /// </summary>
        public FrmNETCameraPreview()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 加载初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmNETCameraPreview_Load(object sender, EventArgs e)
        {
            InitPreviewPanel();
            trkBarSpeed.Value = 4;
            // 新线程连接摄像机，防止主界面卡顿
            new Thread(InitCameraPreview).Start();
            NETCameraManager.GetInstance().ResetCameraListEvent += FrmNETCameraPreview_ResetCameraListEvent;
        }

        /// <summary>
        /// 摄像机列表发生变化，重置界面
        /// </summary>
        private void FrmNETCameraPreview_ResetCameraListEvent()
        {
            _previewPanel.Init();
        }

        /// <summary>
        /// 界面第一次加载时，初始化摄像机
        /// </summary>
        private void InitCameraPreview()
        {
            foreach(BaseNETCamera camera in NETCameraManager.GetInstance().GetAllCameras())
            {
                //CameraLogin(camera);
            }
        }

        /// <summary>
        /// 初始化右侧视频显示界面
        /// </summary>
        private void InitPreviewPanel()
        {
            PreviewPanel.Controls.Clear();
            _previewPanel = new UC_Preview { Dock = DockStyle.Fill };
            _previewPanel.CurrentPlayerItemChanged += previewPanel_CurrentPlayerItemChanged;
            PreviewPanel.Controls.Add(_previewPanel);
        }

        /// <summary>
        /// 右面板当前选中项变化
        /// </summary>
        private void previewPanel_CurrentPlayerItemChanged()
        {
            gbShowCtrl.Enabled = gbManualControl.Enabled = _previewPanel.CurrentPlayerItem != null;
            // 当前选中项不为空且已连接，显示断开
            string btnConAndBreakTxt = _previewPanel.CurrentPlayerItem != null && _previewPanel.CurrentCamera.m_PreviewRealPlayHandle != IntPtr.Zero
                ? "断开"
                : "连接";
            // 更新显示
            if(btnConAndBreak.InvokeRequired)
            {
                Action<string> updateTxt = str => { btnConAndBreak.Text = str; };
                btnConAndBreak.Invoke(updateTxt, btnConAndBreakTxt);
            }
            else
            {
                btnConAndBreak.Text = btnConAndBreakTxt;
            }
            // 当前选中项不为空且已连接，显示断开
            string btnRecordTxt = _previewPanel.CurrentPlayerItem != null && _previewPanel.CurrentCamera.IsRecording
                ? "停止录像"
                : "开始录像";
            // 更新显示
            if(btnRecord.InvokeRequired)
            {
                Action<string> updateTxt = str => { btnRecord.Text = str; };
                btnRecord.Invoke(updateTxt, btnRecordTxt);
            }
            else
            {
                btnRecord.Text = btnRecordTxt;
            }
        }

        /// <summary>
        /// 退出界面，清理事件绑定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmNETCameraPreview_FormClosed(object sender, FormClosedEventArgs e)
        {
            NETCameraManager.GetInstance().ResetCameraListEvent -= FrmNETCameraPreview_ResetCameraListEvent;
        }

        private void button_Paint(object sender, PaintEventArgs e)
        {
            Button button = sender as Button;
            button.Cursor = Cursors.Hand;
            Bitmap bmpBob = (Bitmap)button.BackgroundImage.Clone();
            Bitmap target = PictureProcessing.ZoomImage(bmpBob, button.Height, button.Width);
            GraphicsPath graphicsPath = PictureProcessing.CalculateControlGraphicsPath(target);

            button.Region = new Region(graphicsPath);
        }

        #endregion

        #region 私有方法

        #region 手动控制

        /// <summary>
        /// 连接/断开
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConAndBreak_Click(object sender, EventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                if(_previewPanel.CurrentCamera.IsRecording)
                {
                    FpiMessageBox.ShowInfo("录像中时不可切换连接状态！");
                    return;
                }

                // 未预览，开始预览
                if(_previewPanel.CurrentCamera.m_PreviewRealPlayHandle == IntPtr.Zero)
                {
                    CameraLogin(_previewPanel.CurrentCamera);
                }
                // 已预览，停止预览
                else
                {
                    CameraLogout(_previewPanel.CurrentCamera);
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请先在左侧选中待操作摄像机！");
            }
        }

        /// <summary>
        /// 注册相机
        /// </summary>
        /// <param name="camera"></param>
        private void CameraLogin(BaseNETCamera camera)
        {
            try
            {
                // 初始化
                camera.InitSDK();

                // 未登录，先登录
                if(IntPtr.Zero == camera.m_LoginID)
                {
                    camera.Login();
                }

                // 开始预览
                camera.StartRealPlay();
                // 刷新界面
                _previewPanel.RefreshCurrentPlayerItem();
                //
                if(btnConAndBreak.InvokeRequired)
                {
                    Action<string> updateText = str => { btnConAndBreak.Text = str; };
                    btnConAndBreak.Invoke(updateText, "断开");
                }
                else
                {
                    btnConAndBreak.Text = "断开";
                }

                //CameraLogHelper.Info(camera.name + ":--连接摄像机成功!");
                logView.Log(camera.name + ":--连接摄像机成功!");
            }
            catch(Exception ex)
            {
                //CameraLogHelper.Info(ex.Message);
                logView.Log(ex.Message);
                MessageNotifier.ShowError(ex.Message);
                try
                {
                    // 停止预览
                    camera.StopRealPlay();
                    //// 注销
                    //camera.Logout();
                }
                catch(Exception)
                {
                    // ignored
                }
            }
        }

        /// <summary>
        /// 注销相机
        /// </summary>
        /// <param name="camera"></param>
        private void CameraLogout(BaseNETCamera camera)
        {
            // 未登录
            try
            {
                // 停止预览
                camera.StopRealPlay();

                // 注销
                //camera.Logout();

                // 刷新界面
                _previewPanel.RefreshCurrentPlayerItem();
                btnConAndBreak.Text = "连接";
                //CameraLogHelper.Info(camera.name + ":--断开摄像机成功!");
                logView.Log(camera.name + ":--断开摄像机成功!");
            }
            catch(Exception ex)
            {
                //CameraLogHelper.Info(ex.Message);
                logView.Log(ex.Message);
                MessageNotifier.ShowError(ex.Message);
            }
        }

        #region 云台控制

        private void btnLeft_MouseDown(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.LEFT, 0, trkBarSpeed.Value);

            }
            else
            {
                FpiMessageBox.ShowInfo("请先在左侧选中待操作摄像机！");
            }
        }

        private void btnLeft_MouseUp(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.LEFT, 1, trkBarSpeed.Value);

            }
        }

        private void btnUp_MouseDown(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.UP, 0, trkBarSpeed.Value);

            }
            else
            {
                FpiMessageBox.ShowInfo("请先在左侧选中待操作摄像机！");
            }
        }

        private void btnUp_MouseUp(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.UP, 1, trkBarSpeed.Value);
            }
        }

        private void btnRight_MouseDown(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.RIGHT, 0, trkBarSpeed.Value);
            }
            else
            {
                FpiMessageBox.ShowInfo("请先在左侧选中待操作摄像机！");
            }

        }

        private void btnRight_MouseUp(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.RIGHT, 1, trkBarSpeed.Value);
            }
        }

        private void btnDown_MouseDown(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.DOWN, 0, trkBarSpeed.Value);

            }
            else
            {
                FpiMessageBox.ShowInfo("请先在左侧选中待操作摄像机！");
            }
        }

        private void btnDown_MouseUp(object sender, MouseEventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.DOWN, 1, trkBarSpeed.Value);
            }
        }

        /// <summary>
        /// 雨刷开
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnWiperOpen_Click(object sender, EventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                _previewPanel.CurrentCamera.PTZControlWithSpeed(ControlType.WIPER, 0, trkBarSpeed.Value);

            }
            else
            {
                FpiMessageBox.ShowInfo("请先在左侧选中待操作摄像机！");
            }
        }

        #endregion

        /// <summary>
        /// 最大化/正常显示当前选中面板
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMax_Click(object sender, EventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                if(btnMax.Text == "最大化")
                {
                    _previewPanel.MaxShowCurrentPlayerItem();
                    btnMax.Text = "正常显示";
                }
                else
                {
                    _previewPanel.NormalShowCurrentPlayerItem();
                    btnMax.Text = "最大化";
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请先在右侧选中待操作摄像机！");
            }
        }

        #endregion

        #region 截图/录像

        /// <summary>
        /// 截图
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnScreenShot_Click(object sender, EventArgs e)
        {
            try
            {
                if(_previewPanel.CurrentCamera != null)
                {
                    bool flag = _previewPanel.CurrentCamera.ScreenShot(out string picFileName);
                    if(flag)
                    {
                        string str = $":--截图成功，文件：{picFileName}";
                        logView.Log(str);
                        FpiMessageBox.ShowInfo(str);
                    }
                    else
                    {
                        throw new Exception($"截图失败, error code={_previewPanel.CurrentCamera.GetLastError()}");
                    }
                }
                else
                {
                    throw new Exception("请选择需要控制的摄像机！");
                }
            }
            catch(Exception ex)
            {
                logView.Log(ex.Message);
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 录像
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRecord_Click(object sender, EventArgs e)
        {
            if(_previewPanel.CurrentCamera != null)
            {
                try
                {
                    if(!_previewPanel.CurrentCamera.IsRecording)
                    {
                        if(_previewPanel.CurrentCamera.StartRecording())
                        {
                            string info = _previewPanel.CurrentCamera + ":--开始录像成功!";
                            logView.Log(info);
                            btnRecord.Text = "停止录像";
                        }
                    }
                    else
                    {
                        if(_previewPanel.CurrentCamera.StopRecording())
                        {
                            string info = _previewPanel.CurrentCamera + ":--停止录像成功!";
                            logView.Log(info);
                            btnRecord.Text = "开始录像";
                        }
                    }
                }
                catch(Exception ex)
                {
                    logView.Log(ex.Message);
                    FpiMessageBox.ShowError(ex.Message);
                }
            }
            else
            {
                FpiMessageBox.ShowError("请选择需要控制的摄像机！");
            }
        }

        #endregion

        #endregion
    }
}