﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Fpi.Alarm.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Fpi.Alarm.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap Add {
            get {
                object obj = ResourceManager.GetObject("Add", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 报警产生时间 的本地化字符串。
        /// </summary>
        public static string AddAlarmTime {
            get {
                return ResourceManager.GetString("AddAlarmTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警码不可为空 的本地化字符串。
        /// </summary>
        public static string AlarmCodeEmpty {
            get {
                return ResourceManager.GetString("AlarmCodeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警等级编号与命名不可为空 的本地化字符串。
        /// </summary>
        public static string AlarmGradeCodeEmpty {
            get {
                return ResourceManager.GetString("AlarmGradeCodeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警等级不可为空 的本地化字符串。
        /// </summary>
        public static string AlarmGradeEmpty {
            get {
                return ResourceManager.GetString("AlarmGradeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 该报警等级被引用,不能被删除 的本地化字符串。
        /// </summary>
        public static string AlarmGradeUsed {
            get {
                return ResourceManager.GetString("AlarmGradeUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 时间:{0} 报警:{1} 的本地化字符串。
        /// </summary>
        public static string AlarmInfo {
            get {
                return ResourceManager.GetString("AlarmInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警名称不可为空 的本地化字符串。
        /// </summary>
        public static string AlarmNameEmpty {
            get {
                return ResourceManager.GetString("AlarmNameEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警过期 的本地化字符串。
        /// </summary>
        public static string AlarmOutDate {
            get {
                return ResourceManager.GetString("AlarmOutDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 处置后的报警将转为历史报警，是否确定处置该报警? 的本地化字符串。
        /// </summary>
        public static string AlarmProcess {
            get {
                return ResourceManager.GetString("AlarmProcess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已刷新当前报警 的本地化字符串。
        /// </summary>
        public static string AlarmRefresh {
            get {
                return ResourceManager.GetString("AlarmRefresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警消除时间 的本地化字符串。
        /// </summary>
        public static string AlarmRemoveTime {
            get {
                return ResourceManager.GetString("AlarmRemoveTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警信息保存到数据库发生异常 的本地化字符串。
        /// </summary>
        public static string AlarmSaveToDBError {
            get {
                return ResourceManager.GetString("AlarmSaveToDBError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 该报警源被引用,不能被删除 的本地化字符串。
        /// </summary>
        public static string AlarmSourceUsed {
            get {
                return ResourceManager.GetString("AlarmSourceUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 最近报警时间 的本地化字符串。
        /// </summary>
        public static string AlarmUpdateTime {
            get {
                return ResourceManager.GetString("AlarmUpdateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 起止时间错误 的本地化字符串。
        /// </summary>
        public static string BeginEndTimeError {
            get {
                return ResourceManager.GetString("BeginEndTimeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 创建报警数据库异常 的本地化字符串。
        /// </summary>
        public static string CreateAlarmDBError {
            get {
                return ResourceManager.GetString("CreateAlarmDBError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 创建报警信息数据表异常 的本地化字符串。
        /// </summary>
        public static string CreateAlarmTableError {
            get {
                return ResourceManager.GetString("CreateAlarmTableError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确定删除报警码:{0}? 的本地化字符串。
        /// </summary>
        public static string DeleteAlarmCode {
            get {
                return ResourceManager.GetString("DeleteAlarmCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确定删除报警等级:{0}? 的本地化字符串。
        /// </summary>
        public static string DeleteAlarmGrade {
            get {
                return ResourceManager.GetString("DeleteAlarmGrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确定删除报警组:{0}? 的本地化字符串。
        /// </summary>
        public static string DeleteAlarmGroup {
            get {
                return ResourceManager.GetString("DeleteAlarmGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确定删除报警源:{0}? 的本地化字符串。
        /// </summary>
        public static string DeleteAlarmSource {
            get {
                return ResourceManager.GetString("DeleteAlarmSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap Edit {
            get {
                object obj = ResourceManager.GetObject("Edit", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的报警码 的本地化字符串。
        /// </summary>
        public static string ExistAlarmCode {
            get {
                return ResourceManager.GetString("ExistAlarmCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的报警等级 的本地化字符串。
        /// </summary>
        public static string ExistAlarmGrade {
            get {
                return ResourceManager.GetString("ExistAlarmGrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的报警组 的本地化字符串。
        /// </summary>
        public static string ExistAlarmGroup {
            get {
                return ResourceManager.GetString("ExistAlarmGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已存在编号为 {0} 的报警源 的本地化字符串。
        /// </summary>
        public static string ExistAlarmSource {
            get {
                return ResourceManager.GetString("ExistAlarmSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 首次报警时间 的本地化字符串。
        /// </summary>
        public static string FirstAlarmTime {
            get {
                return ResourceManager.GetString("FirstAlarmTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 查询历史报警出错:{0} 的本地化字符串。
        /// </summary>
        public static string QueryAlarmError {
            get {
                return ResourceManager.GetString("QueryAlarmError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        public static System.Drawing.Bitmap Remove {
            get {
                object obj = ResourceManager.GetObject("Remove", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 请选择报警码 的本地化字符串。
        /// </summary>
        public static string SelectAlarmCode {
            get {
                return ResourceManager.GetString("SelectAlarmCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请选择报警等级 的本地化字符串。
        /// </summary>
        public static string SelectAlarmGrade {
            get {
                return ResourceManager.GetString("SelectAlarmGrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请输入报警源 的本地化字符串。
        /// </summary>
        public static string SelectAlarmSource {
            get {
                return ResourceManager.GetString("SelectAlarmSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请选择查询时间类型 的本地化字符串。
        /// </summary>
        public static string SelectQueryTime {
            get {
                return ResourceManager.GetString("SelectQueryTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统退出 的本地化字符串。
        /// </summary>
        public static string SystemExit {
            get {
                return ResourceManager.GetString("SystemExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统提示 的本地化字符串。
        /// </summary>
        public static string SystemPrompt {
            get {
                return ResourceManager.GetString("SystemPrompt", resourceCulture);
            }
        }
    }
}
