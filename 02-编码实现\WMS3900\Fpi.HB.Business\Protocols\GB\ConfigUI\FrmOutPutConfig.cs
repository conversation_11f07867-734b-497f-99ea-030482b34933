using System;
using System.Windows.Forms;
using Fpi.Communication.Manager;
using Fpi.HB.Business.Protocols.GB.Config;

namespace Fpi.HB.Business.Protocols.GB.ConfigUI
{
    public partial class FrmOutPutConfig : Form
    {
        private Pipe pipe = null;
        private SingleCfg singleConfig = null;
        private OutputNode outputNode = null;

        public FrmOutPutConfig(Pipe pipe, SingleCfg config)
        {
            InitializeComponent();

            this.pipe = pipe;
            this.singleConfig = config;
            this.InitForm();
        }

        private void InitForm()
        {
            this.outputNode = new OutputNode(this.pipe, singleConfig);

            this.outputNode.Dock = DockStyle.Fill;

            this.panel1.Controls.Add(outputNode);
        }

        private void btnSure_Click(object sender, EventArgs e)
        {
            if(this.outputNode != null)
            {
                if(!this.outputNode.Save())
                {
                    this.DialogResult = DialogResult.None;
                    return;
                }
            }

            this.Close();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}