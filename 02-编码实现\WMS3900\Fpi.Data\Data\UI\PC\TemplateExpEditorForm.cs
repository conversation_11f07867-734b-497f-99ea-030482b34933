﻿using System;
using System.Windows.Forms;
using Fpi.Data.ExpParser;
using Fpi.UI.Common.PC;

namespace Fpi.Data.UI.PC
{
    /// <summary>
    /// 逻辑表达式编辑界面
    /// </summary>
    public partial class TemplateExpEditorForm : Form
    {
        private ExpressionParse ex = new ExpressionParse();

        /// <summary>
        /// 表达式串
        /// </summary>
        public string Expression { get; set; } = string.Empty;
        /// <summary>
        /// 关键字集合
        /// </summary>
        private string[] functions = new string[] { "IF", "CASE", "AND", "OR", "Not", "TRUE", "FALSE", "NowDate", "Len", "ToInt", "ToDouble", "ToDateTime", "ToString", "GetNodeValue" };
        /// <summary>
        /// 关键字描述集合
        /// </summary>
        private string[] descriptions = new string[]
        {
            "IF(JudgeExpression,FirstExpression,SecondExpression)\r\nJudgeExpression 为 true 返回 FirstExpression 否则 返回 SecondExpression",
            "CASE(JudgeExpression1,ResultExpression1,JudgeExpression2,ResultExpression2,...)\r\nJudgeExpression1 为 true 返回 ResultExpression1 以此类推",
            "AND(JudgeExpression,JudgeExpression,...)\r\n所有 JudgeExpression 表达式为 true 返回 true 否则 返回 false",
            "OR(JudgeExpression,JudgeExpression,...)\r\n一个 JudgeExpression 表达式为 true 返回 true 否则 返回 false",
            "Not(JudgeExpression)\r\nJudgeExpression 表达式为 true 返回 fase 否则 返回 true",
            "TRUE() 返回 true",
            "FALSE() 返回 false",
            "NowDate() 返回 当前时间 datetime类型",
            "Len(Expression) 返回值长度 int类型",
            "ToInt(Expression) 值转换 返回 int类型",
            "ToDouble(Expression) 值转换 返回 double类型",
            "ToDateTime(Expression) 值转换 返回 datetime类型",
            "ToString(Expression)|ToString(Expression,Formatstring)值转换 返回 string类型" ,
            "GetNodeValue(nodeid,unitid) 获取变量值函数 返回 double类型"
        };
        /// <summary>
        /// 关键字默认生成串集合
        /// </summary>
        private string[] defaultExpress = new string[]
        {
            "If(JudgeExpression,FirstExpression,SecondExpression)",
            "Case(JudgeExpression1,ResultExpression1,JudgeExpression2,ResultExpression2)",
            "And(JudgeExpression,JudgeExpression,...)",
            "Or(JudgeExpression,JudgeExpression,...)",
            "Not(JudgeExpression)",
            "True()",
            "False()",
            "NowDate()",
            "Len(Expression)",
            "ToInt(Expression)",
            "ToDouble(Expression)",
            "ToDateTime(Expression)",
            "ToString(Expression)",
            "GetNodeValue(nodeid,unitid)"

        };

        /// <summary>
        /// 构造
        /// </summary>
        public TemplateExpEditorForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void TemplateExpEditorForm_Load(object sender, EventArgs e)
        {
            this.textBox_Expression.Text = Expression;
            this.lbFunction.Items.Clear();
            foreach(string func in functions)
            {
                this.lbFunction.Items.Add(func);
            }
        }

        /// <summary>
        /// 验证表达式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button_check_Click(object sender, EventArgs e)
        {
            ex.Expression = this.textBox_Expression.Text.Trim();
            string mes = "";
            if(ex.Check(ref mes))
            {
                FpiMessageBox.ShowInfo("验证通过!");
            }
            else
            {
                FpiMessageBox.ShowError("验证失败：" + mes);
            }
        }

        /// <summary>
        /// 双击关键字生成表达式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lbFunction_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if(lbFunction.SelectedIndex > -1 && lbFunction.SelectedIndex < descriptions.Length)
            {
                if(lbFunction.SelectedItem.ToString() == "GetNodeValue")
                {
                    TemplateVarNodeEditorForm nodeForm = new TemplateVarNodeEditorForm();
                    nodeForm.MultiSelect = false;
                    if(nodeForm.ShowDialog() == DialogResult.OK)
                    {
                        string exp = lbFunction.SelectedItem.ToString() + "(\"" + nodeForm.VarNode.FullNodeId + "\",\"" + nodeForm.VarNode.UnitName + "\")";
                        Clipboard.SetDataObject(exp);
                        textBox_Expression.Paste();
                    }
                }
                else
                {
                    Clipboard.SetDataObject(defaultExpress[lbFunction.SelectedIndex]);
                    textBox_Expression.Paste();
                }
            }
        }
        /// <summary>
        /// 确定保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOk_Click(object sender, EventArgs e)
        {
            ex.Expression = this.textBox_Expression.Text.Trim();
            string mes = "";
            if(!ex.Check(ref mes))
            {
                FpiMessageBox.ShowError("验证失败：" + mes);
                DialogResult = DialogResult.None;

            }
            else
            {
                Expression = this.textBox_Expression.Text;
                DialogResult = DialogResult.OK;
            }
        }

        /// <summary>
        /// 选择关键字
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lbFunction_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.lbDes.Text = lbFunction.SelectedIndex > -1 && lbFunction.SelectedIndex < descriptions.Length ? descriptions[lbFunction.SelectedIndex] : "";
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }



    }
}
