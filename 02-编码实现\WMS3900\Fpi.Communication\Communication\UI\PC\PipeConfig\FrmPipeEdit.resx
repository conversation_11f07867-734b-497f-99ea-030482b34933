<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pnlFunc.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pnlFunc.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 314</value>
  </data>
  <data name="pnlFunc.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 37</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pnlFunc.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Name" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlFunc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>318, 7</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>确定(&amp;E)</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 7</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>取消(&amp;C)</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cmbProtocol.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cmbProtocol.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 97</value>
  </data>
  <data name="cmbProtocol.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="cmbProtocol.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>63, 0</value>
  </data>
  <data name="cmbProtocol.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 30, 2</value>
  </data>
  <data name="cmbProtocol.Size" type="System.Drawing.Size, System.Drawing">
    <value>284, 22</value>
  </data>
  <data name="cmbProtocol.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="&gt;&gt;cmbProtocol.Name" xml:space="preserve">
    <value>cmbProtocol</value>
  </data>
  <data name="&gt;&gt;cmbProtocol.Type" xml:space="preserve">
    <value>Sunny.UI.UIComboBox, SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;cmbProtocol.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;cmbProtocol.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cmbBus.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="cmbBus.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 65</value>
  </data>
  <data name="cmbBus.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="cmbBus.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>63, 0</value>
  </data>
  <data name="cmbBus.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 30, 2</value>
  </data>
  <data name="cmbBus.Size" type="System.Drawing.Size, System.Drawing">
    <value>284, 22</value>
  </data>
  <data name="cmbBus.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;cmbBus.Name" xml:space="preserve">
    <value>cmbBus</value>
  </data>
  <data name="&gt;&gt;cmbBus.Type" xml:space="preserve">
    <value>Sunny.UI.UIComboBox, SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;cmbBus.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;cmbBus.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="chkReconnect.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chkReconnect.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt</value>
  </data>
  <data name="chkReconnect.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 239</value>
  </data>
  <data name="chkReconnect.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 16</value>
  </data>
  <data name="chkReconnect.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chkReconnect.Text" xml:space="preserve">
    <value>断开时自动重连</value>
  </data>
  <data name="&gt;&gt;chkReconnect.Name" xml:space="preserve">
    <value>chkReconnect</value>
  </data>
  <data name="&gt;&gt;chkReconnect.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkReconnect.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;chkReconnect.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="chkLst.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 128</value>
  </data>
  <data name="chkLst.Size" type="System.Drawing.Size, System.Drawing">
    <value>284, 100</value>
  </data>
  <data name="chkLst.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;chkLst.Name" xml:space="preserve">
    <value>chkLst</value>
  </data>
  <data name="&gt;&gt;chkLst.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckedListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkLst.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;chkLst.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 12</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>通道编号</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtId.Label" xml:space="preserve">
    <value />
  </data>
  <data name="txtId.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 8</value>
  </data>
  <data name="txtId.Size" type="System.Drawing.Size, System.Drawing">
    <value>284, 21</value>
  </data>
  <data name="txtId.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtId.Name" xml:space="preserve">
    <value>txtId</value>
  </data>
  <data name="&gt;&gt;txtId.Type" xml:space="preserve">
    <value>Fpi.UI.Common.PC.Controls.FpiTextBox, Fpi.UI.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;txtId.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;txtId.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 70</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>通讯链路</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="btnPort.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnPort.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="btnPort.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btnPort.Location" type="System.Drawing.Point, System.Drawing">
    <value>423, 98</value>
  </data>
  <data name="btnPort.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 21</value>
  </data>
  <data name="btnPort.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="btnPort.ToolTip" xml:space="preserve">
    <value>通讯协议内部参数配置</value>
  </data>
  <data name="&gt;&gt;btnPort.Name" xml:space="preserve">
    <value>btnPort</value>
  </data>
  <data name="&gt;&gt;btnPort.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnPort.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;btnPort.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 41</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>通道命名</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btnProtocol.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnProtocol.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="btnProtocol.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btnProtocol.Location" type="System.Drawing.Point, System.Drawing">
    <value>395, 98</value>
  </data>
  <data name="btnProtocol.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 21</value>
  </data>
  <data name="btnProtocol.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="btnProtocol.ToolTip" xml:space="preserve">
    <value>通讯协议用户参数配置</value>
  </data>
  <data name="&gt;&gt;btnProtocol.Name" xml:space="preserve">
    <value>btnProtocol</value>
  </data>
  <data name="&gt;&gt;btnProtocol.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnProtocol.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;btnProtocol.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 102</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>通讯协议</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="btnBus.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnBus.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="btnBus.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Popup</value>
  </data>
  <data name="btnBus.Location" type="System.Drawing.Point, System.Drawing">
    <value>395, 66</value>
  </data>
  <data name="btnBus.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 21</value>
  </data>
  <data name="btnBus.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="btnBus.ToolTip" xml:space="preserve">
    <value>通讯链路参数配置</value>
  </data>
  <data name="&gt;&gt;btnBus.Name" xml:space="preserve">
    <value>btnBus</value>
  </data>
  <data name="&gt;&gt;btnBus.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnBus.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;btnBus.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 128</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>对端设备</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="txtName.Label" xml:space="preserve">
    <value />
  </data>
  <data name="txtName.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 37</value>
  </data>
  <data name="txtName.Size" type="System.Drawing.Size, System.Drawing">
    <value>284, 21</value>
  </data>
  <data name="txtName.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtName.Name" xml:space="preserve">
    <value>txtName</value>
  </data>
  <data name="&gt;&gt;txtName.Type" xml:space="preserve">
    <value>Fpi.UI.Common.PC.Controls.FpiTextBox, Fpi.UI.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;txtName.Parent" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;txtName.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="pnlMain.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="pnlMain.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="pnlMain.Size" type="System.Drawing.Size, System.Drawing">
    <value>476, 266</value>
  </data>
  <data name="pnlMain.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;pnlMain.Name" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;pnlMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlMain.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;pnlMain.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chkValid.Name" xml:space="preserve">
    <value>chkValid</value>
  </data>
  <data name="&gt;&gt;chkValid.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkValid.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;chkValid.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="panel2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="panel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>宋体, 9pt, style=Bold</value>
  </data>
  <data name="panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>476, 28</value>
  </data>
  <data name="panel2.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;panel2.Name" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel2.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 17</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>476, 294</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 314</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chkValid.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chkValid.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 6</value>
  </data>
  <data name="chkValid.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 16</value>
  </data>
  <data name="chkValid.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chkValid.Text" xml:space="preserve">
    <value>启用该通道</value>
  </data>
  <data name="&gt;&gt;chkValid.Name" xml:space="preserve">
    <value>chkValid</value>
  </data>
  <data name="&gt;&gt;chkValid.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chkValid.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;chkValid.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>482, 351</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>通道编辑</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FrmPipeEdit</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>