<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnRefresh.Location" type="System.Drawing.Point, System.Drawing">
    <value>821, 526</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnRefresh.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnRefresh.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnRefresh.Text" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="colAlarmCode.Text" xml:space="preserve">
    <value>alarm code</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="colAlarmCode.Width" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="colAlarmName.Text" xml:space="preserve">
    <value>alarm name</value>
  </data>
  <data name="colAlarmSource.Text" xml:space="preserve">
    <value>alarm source</value>
  </data>
  <data name="colAlarmGrade.Text" xml:space="preserve">
    <value>alarm grade</value>
  </data>
  <data name="colAlarmGrade.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="colFirstTime.Text" xml:space="preserve">
    <value>alarm occur time</value>
  </data>
  <data name="colFirstTime.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="colAlarmDesc.Text" xml:space="preserve">
    <value>alarm description</value>
  </data>
  <data name="colAlarmDesc.Width" type="System.Int32, mscorlib">
    <value>245</value>
  </data>
  <data name="lsvCurrent.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 4</value>
  </data>
  <data name="lsvCurrent.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lsvCurrent.Size" type="System.Drawing.Size, System.Drawing">
    <value>904, 513</value>
  </data>
  <data name="tabPage1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="tabPage1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tabPage1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tabPage1.Size" type="System.Drawing.Size, System.Drawing">
    <value>917, 570</value>
  </data>
  <data name="tabPage1.Text" xml:space="preserve">
    <value>Current alarm</value>
  </data>
  <data name="cmbCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>107, 16</value>
  </data>
  <data name="cmbCode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 23</value>
  </data>
  <data name="btnQuery.Location" type="System.Drawing.Point, System.Drawing">
    <value>588, 84</value>
  </data>
  <data name="btnQuery.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="btnQuery.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 29</value>
  </data>
  <data name="btnQuery.Text" xml:space="preserve">
    <value>Query</value>
  </data>
  <data name="dtpEnd.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 82</value>
  </data>
  <data name="dtpEnd.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="dtpEnd.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 21</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 55</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 15</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>Alarm scale</value>
  </data>
  <data name="cmbDateType.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 14</value>
  </data>
  <data name="cmbDateType.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbDateType.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 23</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 19</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 15</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Time type</value>
  </data>
  <data name="cmbSource.Location" type="System.Drawing.Point, System.Drawing">
    <value>107, 89</value>
  </data>
  <data name="cmbSource.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbSource.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 23</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 86</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 15</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="cmbGrade.Location" type="System.Drawing.Point, System.Drawing">
    <value>107, 51</value>
  </data>
  <data name="cmbGrade.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="cmbGrade.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 23</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 20</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 15</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Alarm name</value>
  </data>
  <data name="lblBegin.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 48</value>
  </data>
  <data name="lblBegin.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 15</value>
  </data>
  <data name="lblBegin.Text" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="dtpBegin.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 48</value>
  </data>
  <data name="dtpBegin.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="dtpBegin.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 21</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 90</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 15</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Alarm source</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 1</value>
  </data>
  <data name="groupBox1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>901, 124</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="columnHeader3.Text" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="columnHeader8.Text" xml:space="preserve">
    <value>alarm grade</value>
  </data>
  <data name="columnHeader8.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="columnHeader4.Text" xml:space="preserve">
    <value>alarm occur time</value>
  </data>
  <data name="columnHeader4.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="columnHeader7.Text" xml:space="preserve">
    <value>Alarm remove time</value>
  </data>
  <data name="columnHeader7.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="columnHeader6.Text" xml:space="preserve">
    <value>Alarm description</value>
  </data>
  <data name="lsvHistory.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 132</value>
  </data>
  <data name="lsvHistory.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="lsvHistory.Size" type="System.Drawing.Size, System.Drawing">
    <value>904, 419</value>
  </data>
  <data name="tabPage2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 24</value>
  </data>
  <data name="tabPage2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tabPage2.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tabPage2.Size" type="System.Drawing.Size, System.Drawing">
    <value>917, 570</value>
  </data>
  <data name="tabPage2.Text" xml:space="preserve">
    <value>History alarm</value>
  </data>
  <data name="tabControl1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9pt</value>
  </data>
  <data name="tabControl1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>925, 598</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 15</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>925, 598</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAASmZ8/76Vlv8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAa5zD/x6J6P9LeqP/yJaT/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAEu0/v9Rtf//IInp/0t6ov/GlZL/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAUbf+/1Gz//8dh+b/Tnqg/8qXkv8AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABRt/7/TrL//x+J5v9Oe6L/uZSX/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFK4/v9Lsf//J4fZ/19qdv8AAAAAsIV//8Cf
        lP/An5b/vJiO/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVb3//7XW7f+/nZL/u5uM/+fa
        wv///+P////l//362v/Yw7P/tY2F/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzqeV//3u
        vv///9j////a////2////+b////7/+rd3P+ug3//AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGg
        kf/73Kj//vfQ////2////+P////4/////f////3/xqmc/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGg
        kf/+46z/8cSR//zyyv///93////k////9/////f////p/+7ly/+5lIz/AAAAAAAAAAAAAAAAAAAAAAAA
        AADCoZH//+au/+61gf/33K7//v3Y////3////+P////k////4P/z7NL/u5aO/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAvJeM//vnt//0x5H/8smU//jluf/+/Nj////d////3P///+D/4tK6/7aOhv8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAADZw6n///7l//fcuP/yyZT/9dSl//rovf/99Mn//fvW/7aQif8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAtY2F/+je3f///vL/+dij//TEjP/51J///eq4/9C0n/+4kIb/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACtgn//yaqe/+/gt//v37L/586s/7iQhv+4kIb/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC6lor/u5iM/7eRiP8AAAAAAAAAAAAA
        AAAAAAAAn/8AAA//AAAH/wAAg/8AAMH/AADhDwAA8AMAAPwBAAD8AQAA+AAAAPgAAAD4AAAA/AEAAPwB
        AAD+AwAA/48AAA==
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Query Alarm</value>
  </data>
</root>