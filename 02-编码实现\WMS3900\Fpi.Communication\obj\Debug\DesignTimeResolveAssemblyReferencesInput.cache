   .winmd.dll.exe    JE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Resources\Remove.pngGE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Resources\Add.pngJE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Resources\Modify.pngHE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Resources\Edit.pngJE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Resources\Search.pngaE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Instrument\bin\Debug\Fpi.Instrument.dllSE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Log\bin\Debug\Fpi.Log.dll_E:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.UI.Common\bin\Debug\Fpi.UI.Common.dllUE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Util\bin\Debug\Fpi.Util.dllSE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Xml\bin\Debug\Fpi.Xml.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dllOE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\FpiDLL\SunnyUI.Common.dllHE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\FpiDLL\SunnyUI.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Core.dllbC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Design.dll]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.dlleC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Drawing.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.ServiceProcess.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Web.Extensions.dllkC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Xml.dllKE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\FpiDLL\WinFormsUI.dll       SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}
{RawFileName}RE:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Communication\bin\Debug\     B{Registry:Software\Microsoft\.NETFramework,v4.8,AssemblyFoldersEx}{E:\01-数采软件\01-WMS3900\02-编码实现\WMS3900\Fpi.Communication\obj\Debug\DesignTimeResolveAssemblyReferences.cache   SC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\[C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\Facades\.NETFramework,Version=v4.8.NET Framework 4.8v4.8msil
v4.0.30319         