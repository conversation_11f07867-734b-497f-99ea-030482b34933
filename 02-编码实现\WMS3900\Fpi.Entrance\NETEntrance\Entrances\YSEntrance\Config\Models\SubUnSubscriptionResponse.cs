﻿namespace Fpi.Entrance.YS.Models
{
    /// <summary>
    /// 门禁订阅回应报文类
    /// </summary>
    public class UnSubscriptionResponse
    {
        public SubUnSubscriptionResponse Response { get; set; }
    }

    /// <summary>
    /// 删除门禁回应报文
    /// </summary>
    public class SubUnSubscriptionResponse
    {
        /// <summary>
        /// /// <summary>
        /// 响应的 URL 地址。
        /// 通常用于指示请求的实际处理路径。      
        /// </summary>
        public string ResponseURL { get; set; }

        /// <summary>
        /// 响应代码。
        /// 表示操作的总体结果状态。
        /// 通常为 0 表示成功，非零值表示失败或异常。
        /// </summary>
        public string ResponseCode { get; set; }

        /// <summary>
        /// 子响应代码。
        /// 提供更细粒度的操作结果信息。
        /// 通常为 0 表示无额外错误，非零值表示特定的子错误类型。
        /// </summary>
        public string SubResponseCode { get; set; }

        /// <summary>
        /// 响应字符串。
        /// 对操作结果的简短描述，例如 "Succeed" 或 "Failed"。
        /// </summary>
        public string ResponseString { get; set; }

        /// <summary>
        /// 状态码。
        /// 表示操作的具体状态。
        /// 通常与 <see cref="ResponseCode"/> 类似，但可能用于区分不同层次的状态。
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// 状态字符串。
        /// 对操作状态的详细描述，例如 "Succeed" 或 "Invalid Arguments"。
        /// </summary>
        public string StatusString { get; set; }

        /// <summary>
        /// 数据内容。
        /// 包含操作返回的具体数据（如果有）。
        /// 可能是一个对象、数组或其他数据结构，具体取决于接口定义。
        /// 如果没有返回数据，则可能为 null。
        /// </summary>
        public object Data { get; set; }
    }
}
