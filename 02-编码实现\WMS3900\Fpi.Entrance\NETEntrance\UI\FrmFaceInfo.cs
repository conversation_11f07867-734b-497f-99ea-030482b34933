﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Sunny.UI;

namespace Fpi.Entrance.UI
{
    /// <summary>
    /// 显示图像
    /// </summary>
    public partial class FrmFaceInfo : UIForm
    {
        #region 构造

        public FrmFaceInfo()
        {
            InitializeComponent();
        }

        public FrmFaceInfo(string str) : this()
        {
            try
            {
                Image image = ImageHelper.ConvertBase64ToImage(str);
                ImageHelper.FillPictureBox(picFace, image);
            }
            catch(Exception ex)
            {
                this.Controls.Clear();
                var label = new UILabel
                {
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("微软雅黑", 12),
                    AutoSize = false,
                    Dock = DockStyle.Fill,
                    Text = ex.Message
                };
                this.Controls.Add(label);
            }
        }

        #endregion
    }
}
