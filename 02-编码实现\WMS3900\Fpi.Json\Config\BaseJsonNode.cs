﻿using System;

namespace Fpi.Json
{
    /// <summary>
    /// 基础Json对象
    /// </summary>
    [Serializable]
    public class BaseJsonNode
    {
        #region 公共方法

        #region 加载保存

        /// <summary>
        /// 从磁盘加载序列化的数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        protected void loadJson()
        {
            try
            {
                FpiJsonHelper.LoadDataFromDisk(this);
            }
            catch
            {
                // Ignore
            }
        }

        /// <summary>
        /// 序列化数据到磁盘
        /// </summary>
        /// <returns></returns>
        public bool Save()
        {
            return FpiJsonHelper.SaveDataToDisk(this);
        }

        #endregion

        #endregion
    }
}