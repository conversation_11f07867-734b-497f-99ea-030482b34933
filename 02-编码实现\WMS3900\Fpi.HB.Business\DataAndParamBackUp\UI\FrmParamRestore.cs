﻿using System;
using System.ComponentModel;
using System.IO;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.Xml;

namespace Fpi.HB.Business
{
    public partial class FrmParamRestore : Form
    {
        public FrmParamRestore()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 执行恢复
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                txtConfigResetFile.Check();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
                return;
            }
            if(DialogResult.Yes != FpiMessageBox.ShowQuestion("恢复数据将丢失备份之后的数据,确认继续吗？"))
            {
                return;
            }
            try
            {
                if(txtConfigResetFile.Text == "")
                {
                    FpiMessageBox.ShowWarning("请选择恢复目录！！");
                    return;
                }
                if(FpiMessageBox.ShowQuestion("请确认是否恢复配置文件，恢复配置文件需要重新启动软件，请确认是否继续？") != DialogResult.Yes)
                {
                    return;
                }

                this.btnRestore.Enabled = false;
                if(Directory.Exists(ConstConfig.XmlPath))
                {

                    FileInfo[] fileInfos = new DirectoryInfo(ConstConfig.XmlPath).GetFiles();
                    if(fileInfos != null)
                    {
                        foreach(FileInfo item in fileInfos)
                        {
                            if(!item.Name.Contains("BuildLibraryConfig"))
                            {
                                item.Attributes = FileAttributes.Normal;
                                item.Delete();
                            }
                        }
                        Directory.Delete(ConstConfig.XmlPath, true);
                    }
                }
                if(!Directory.Exists(ConstConfig.XmlPath))
                {
                    Directory.CreateDirectory(ConstConfig.XmlPath);
                }
                Fpi.Util.Compress.CompressUtil.DecompressToFiles(txtConfigResetFile.Text, ConstConfig.XmlPath);

                FpiMessageBox.ShowInfo("系统配置(参数)恢复完成,软件即将重启！");
                Environment.Exit(0);
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowInfo(ex.Message);
                this.btnRestore.Enabled = true;
            }
            this.btnRestore.Enabled = true;

        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="e"></param>
        protected override void OnClosing(CancelEventArgs e)
        {
            if(!btnRestore.Enabled)
            {
                FpiMessageBox.ShowInfo("正在恢复数据不能关闭窗口!");
                e.Cancel = true;
                return;
            }
            base.OnClosing(e);
        }

        private void btnXMLresetFile_Click(object sender, EventArgs e)
        {
            OpenFileDialog open = new OpenFileDialog();
            open.CheckFileExists = true;
            open.CheckPathExists = true;
            open.Multiselect = false;
            open.InitialDirectory = "";
            open.Filter = "数据文件|*.zip";

            if(open.ShowDialog() == DialogResult.OK)
            {
                txtConfigResetFile.Text = open.FileName;
            }
            else
            {
                return;
            }
        }
    }
}