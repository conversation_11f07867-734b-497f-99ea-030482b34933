using System.Collections;
using Fpi.Xml;

namespace Fpi.Instruments
{
    public class InstrumentManager : BaseNode
    {
        public NodeList instrumentTypes = new NodeList();
        public NodeList instruments = new NodeList();

        private InstrumentManager()
        {
            loadXml();
        }

        private static readonly object syncObj = new object();
        private static InstrumentManager _instance = null;
        public static InstrumentManager GetInstance()
        {
            lock(syncObj)
            {
                if(_instance == null)
                {
                    _instance = new InstrumentManager();
                }
            }
            return _instance;
        }
        public static void ReLoad()
        {
            lock(syncObj)
            {
                _instance = null;
            }
        }

        public Instrument[] GetInstruments(string type)
        {
            ArrayList list = new ArrayList();
            foreach(Instrument ins in instruments)
            {
                if(ins.type.Equals(type))
                {
                    list.Add(ins);
                }
            }

            return (Instrument[])list.ToArray(typeof(Instrument));
        }

        public Instrument GetInstrument(int address)
        {
            int count = instruments.GetCount();
            for(int i = 0; i < count; i++)
            {
                Instrument ins = (Instrument)instruments[i];
                if(ins.address == address)
                {
                    return ins;
                }
            }
            return null;
        }

        public Instrument GetInstrument(string insId)
        {
            return this.instruments != null ? this.instruments[insId] as Instrument : null;
        }

        public InstrumentType GetInstrumentType(string insId)
        {
            if(this.instruments != null)
            {
                if(this.instrumentTypes != null && this.instruments[insId] is Instrument ins)
                {
                    return this.instrumentTypes[ins.type] as InstrumentType;
                }
            }
            return null;
        }
    }
}