using System;
using Fpi.Communication.Properties;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Extensions;
using Fpi.Xml;

namespace Fpi.Communication.Buses.UI.PC
{
    public partial class UC_ServerSocketBus : BaseConfigureView// System.Windows.Forms.UserControl
    {
        public UC_ServerSocketBus()
        {
            InitializeComponent();
            InitUI();
        }


        protected override void ShowConfig(object obj)
        {
            this.ParentForm.Text = Resources.TcpServerConfigOption;

            BaseNode configNode = configObj as BaseNode;
            string strPort = configNode.GetPropertyValue(TcpServer.PropertyName_Port, string.Empty);

            int port = 0;
            int.TryParse(strPort, out port);
            this.nuPort.Value = port;

            this.chkNeedSaveLog.Checked = configNode.GetPropertyValue(BaseBus.PropertyName_NeedSaveLog, string.Empty).TryValue<bool>(true);
        }

        public override void Check()
        {
            base.Check();
            if(this.nuPort.Value == 0)
            {
                throw new Exception(Resources.ConfigListenPort);
            }
        }


        public override object Save()
        {
            string port = this.nuPort.Value.ToString();
            BaseNode configNode = configObj as BaseNode;
            configNode.SetProperty(TcpServer.PropertyName_Port, port);
            configNode.SetProperty(BaseBus.PropertyName_NeedSaveLog, this.chkNeedSaveLog.Checked.ToString());
            return configNode;
        }

        private void InitUI()
        {
        }
    }
}