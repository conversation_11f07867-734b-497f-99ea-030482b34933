﻿namespace Fpi.Data.ExpParser
{
    using System;

    public class Evaluator
    {
        private readonly Grammar _gmm = null;
        private readonly ToolBox _toolBox = null;

        public Evaluator()
        {
            this._gmm = new Grammar(this);
            this._toolBox = new ToolBox();
        }

        public IOperand ExpressionEvaluate(TOKENLink startLink, TOKENLink endLink)
        {
            TOKENLink link2;
            IOperand operand;
            TOKENLink link3;
            TOKENLink next = startLink;
            goto Label_0210;
            Label_0155:
            link3 = new TOKENLink(new TOKEN<IOperand>(ETokenType.token_operand, operand, next.Token.Index));
            if(link2 != null)
            {
                if(next.Prev != null)
                {
                    link3.Prev = next.Prev;
                    next.Prev.Next = link3;
                }
                if(link2.Next != null)
                {
                    link3.Next = link2.Next;
                    link2.Next.Prev = link3;
                }
                if(next == startLink)
                {
                    startLink = link3;
                }
                if(link2 == endLink)
                {
                    endLink = link3;
                }
                next = link3;
            }
            Label_01F6:
            if(next == endLink)
            {
                return startLink == endLink ? ((TOKEN<IOperand>)next.Token).Tag : this.MathEvaluate(startLink, endLink);
            }
            next = next.Next;
            Label_0210:
            if(next.Token.Type != ETokenType.token_keyword)
            {
                goto Label_01F6;
            }
            link2 = null;
            operand = null;
            switch(((TOKEN<KeyWord>)next.Token).Tag.Type)
            {
                case EKeyword.IF:
                    operand = this._gmm.Key_IF(next, out link2);
                    goto Label_0155;
                case EKeyword.CASE:
                    operand = this._gmm.Key_CASE(next, out link2);
                    goto Label_0155;
                case EKeyword.AND:
                    operand = this._gmm.Key_AND(next, out link2);
                    goto Label_0155;

                case EKeyword.OR:
                    operand = this._gmm.Key_OR(next, out link2);
                    goto Label_0155;

                case EKeyword.NOT:
                    operand = this._gmm.Key_Not(next, out link2);
                    goto Label_0155;

                case EKeyword.TRUE:
                    operand = this._gmm.Key_True(next, out link2);
                    goto Label_0155;

                case EKeyword.FALSE:
                    operand = this._gmm.Key_False(next, out link2);
                    goto Label_0155;

                case EKeyword.ToString:
                    operand = this._gmm.Key_ToString(next, out link2);
                    goto Label_0155;

                case EKeyword.ToDateTime:
                    operand = this._gmm.Key_ToDateTime(next, out link2);
                    goto Label_0155;

                case EKeyword.ToInt:
                    operand = this._gmm.Key_ToInt(next, out link2);
                    goto Label_0155;

                case EKeyword.ToDouble:
                    operand = this._gmm.Key_ToDouble(next, out link2);
                    goto Label_0155;

                case EKeyword.Len:
                    operand = this._gmm.Key_Len(next, out link2);
                    goto Label_0155;

                case EKeyword.NowDate:
                    operand = this._gmm.Key_NowDate(next, out link2);
                    goto Label_0155;
                case EKeyword.GetNodeValue:
                    operand = this._gmm.Key_GetNodeValue(next, out link2);
                    goto Label_0155;
                case EKeyword.GetNodeIntValue:
                    operand = this._gmm.Key_GetNodeIntValue(next, out link2);
                    goto Label_0155;
                case EKeyword.GetNodeBoolValue:
                    operand = this._gmm.Key_GetNodeBoolValue(next, out link2);
                    goto Label_0155;
                case EKeyword.GetAllNodeBoolValueOR:
                    operand = this._gmm.Key_GetAllNodeBoolValueOR(next, out link2);
                    goto Label_0155;
                case EKeyword.GetAlarmNodeBoolValue:
                    operand = this._gmm.Key_GetAlarmNodeBoolValue(next, out link2);
                    goto Label_0155;
                case EKeyword.IsAlarmNode:
                    operand = this._gmm.Key_IsAlarmNode(next, out link2);
                    goto Label_0155;
                case EKeyword.IsNodeCycleValueAlarm:
                    operand = this._gmm.Key_IsNodeCycleValueAlarm(next, out link2);
                    goto Label_0155;
                case EKeyword.DoubleNaN:
                    operand = this._gmm.Key_DoubleNaN(next, out link2);
                    goto Label_0155;
                case EKeyword.Invoke:
                    operand = this._gmm.Key_Invoke(next, out link2);
                    goto Label_0155;
            }
            goto Label_0155;
        }

        private IOperand MathEvaluate(TOKENLink startLink, TOKENLink endLink)
        {
            TOKENLink next = this._toolBox.InfixToPostfix(startLink, endLink);
            TOKENLink link2 = null;
            IToken token = null;
            while(next.Next != null)
            {
                IOperand operand;
                bool flag;
                DateTime tValue;
                DateTime time2;
                double num;
                bool flag2;
                Operand<int> tag1;
                Operand<int> tag2;
                Operand<DateTime> operand5;
                next = next.Next;
                if(next.Token.Type != ETokenType.token_operator)
                {
                    goto Label_0C48;
                }
                link2 = null;
                token = null;
                EOperatorType type = ((TOKEN<Operator>)next.Token).Tag.Type;
                switch(type)
                {
                    case EOperatorType.Plus:
                    case EOperatorType.Minus:
                        if((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Dstring) && (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Dstring))
                        {
                            goto Label_0283;
                        }
                        if(type != EOperatorType.Plus)
                        {
                            goto Label_021B;
                        }
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<string>(EDataType.Dstring, ((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.ToString() + ((TOKEN<IOperand>)next.Prev.Token).Tag.ToString()), next.Token.Index);
                        goto Label_0B39;

                    case EOperatorType.Multiply:
                        if((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Ddouble) && (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Ddouble))
                        {
                            goto Label_0549;
                        }
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value) * Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value)), next.Token.Index);
                        goto Label_0B39;

                    case EOperatorType.Divide:
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value) / Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value)), next.Token.Index);
                        goto Label_0B39;

                    case EOperatorType.Mod:
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value) % Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value)), next.Token.Index);
                        goto Label_0B39;

                    case EOperatorType.Positive:
                    case EOperatorType.Negative:
                        operand = ((TOKEN<IOperand>)next.Prev.Token).Tag;
                        if(type != EOperatorType.Negative)
                        {
                            goto Label_014D;
                        }
                        if(operand.Type != EDataType.Dint)
                        {
                            break;
                        }
                        tag1 = (Operand<int>)operand;
                        token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<int>(EDataType.Dint, -tag1.TValue), next.Token.Index);
                        goto Label_0B39;

                    case EOperatorType.LessThan:
                    case EOperatorType.GreaterThan:
                    case EOperatorType.LessEqual:
                    case EOperatorType.GreaterEqual:
                        flag = false;
                        if(((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Ddatetime)
                        {
                            goto Label_0762;
                        }
                        operand5 = (Operand<DateTime>)((TOKEN<IOperand>)next.Prev.Prev.Token).Tag;
                        tValue = operand5.TValue;
                        operand5 = (Operand<DateTime>)((TOKEN<IOperand>)next.Prev.Token).Tag;
                        time2 = operand5.TValue;
                        switch(type)
                        {
                            case EOperatorType.LessThan:
                                goto Label_0728;

                            case EOperatorType.GreaterThan:
                                goto Label_0735;

                            case EOperatorType.Equal:
                            case EOperatorType.NotEqual:
                                goto Label_0800;

                            case EOperatorType.LessEqual:
                                goto Label_074F;

                            case EOperatorType.GreaterEqual:
                                goto Label_0742;
                        }
                        goto Label_0800;

                    case EOperatorType.Equal:
                    case EOperatorType.NotEqual:
                        flag2 = false;
                        if((((TOKEN<IOperand>)next.Prev.Token).Tag.Type != EDataType.Dstring) || (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type != EDataType.Dstring))
                        {
                            goto Label_0913;
                        }
                        if(type != EOperatorType.Equal)
                        {
                            goto Label_08C8;
                        }
                        flag2 = ((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.ToString().Equals(((TOKEN<IOperand>)next.Prev.Token).Tag.ToString());
                        goto Label_0B16;

                    default:
                        goto Label_0B39;
                }
                if(operand.Type == EDataType.Ddouble)
                {
                    Operand<double> operand4 = (Operand<double>)operand;
                    token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, -operand4.TValue), next.Token.Index);
                }
                goto Label_0B39;
                Label_014D:
                token = next.Prev.Token;
                goto Label_0B39;
                Label_021B:
                token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<string>(EDataType.Dstring, ((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.ToString().Replace(((TOKEN<IOperand>)next.Prev.Token).Tag.ToString(), "")), next.Token.Index);
                goto Label_0B39;
                Label_0283:
                if((((TOKEN<IOperand>)next.Prev.Token).Tag.Type == EDataType.Ddouble) || (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type == EDataType.Ddouble))
                {
                    token = type == EOperatorType.Plus
                        ? new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value) + Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value)), next.Token.Index)
                        : (IToken)new TOKEN<IOperand>(ETokenType.token_operand, new Operand<double>(EDataType.Ddouble, Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value) - Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value)), next.Token.Index);
                }
                else if(type == EOperatorType.Plus)
                {
                    tag1 = (Operand<int>)((TOKEN<IOperand>)next.Prev.Prev.Token).Tag;
                    tag2 = (Operand<int>)((TOKEN<IOperand>)next.Prev.Token).Tag;
                    token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<int>(EDataType.Dint, tag1.TValue + tag2.TValue), next.Token.Index);
                }
                else
                {
                    tag1 = (Operand<int>)((TOKEN<IOperand>)next.Prev.Prev.Token).Tag;
                    tag2 = (Operand<int>)((TOKEN<IOperand>)next.Prev.Token).Tag;
                    token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<int>(EDataType.Dint, tag1.TValue - tag2.TValue), next.Token.Index);
                }
                goto Label_0B39;
                Label_0549:
                tag1 = (Operand<int>)((TOKEN<IOperand>)next.Prev.Prev.Token).Tag;
                tag2 = (Operand<int>)((TOKEN<IOperand>)next.Prev.Token).Tag;
                token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<int>(EDataType.Dint, tag1.TValue * tag2.TValue), next.Token.Index);
                goto Label_0B39;
                Label_0728:
                flag = tValue < time2;
                goto Label_0800;
                Label_0735:
                flag = tValue > time2;
                goto Label_0800;
                Label_0742:
                flag = tValue >= time2;
                goto Label_0800;
                Label_074F:
                flag = tValue <= time2;
                goto Label_0800;
                Label_0762:
                num = Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value);
                double num2 = Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value);
                switch(type)
                {
                    case EOperatorType.LessThan:
                        flag = num < num2;
                        goto Label_0800;

                    case EOperatorType.GreaterThan:
                        flag = num > num2;
                        goto Label_0800;

                    case EOperatorType.Equal:
                    case EOperatorType.NotEqual:
                        goto Label_0800;

                    case EOperatorType.LessEqual:
                        flag = num <= num2;
                        goto Label_0800;

                    case EOperatorType.GreaterEqual:
                        flag = num >= num2;
                        goto Label_0800;
                }
                Label_0800:
                token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<bool>(EDataType.Dbool, flag), next.Token.Index);
                goto Label_0B39;
                Label_08C8:
                flag2 = !((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.ToString().Equals(((TOKEN<IOperand>)next.Prev.Token).Tag.ToString());
                goto Label_0B16;
                Label_0913:
                if((((TOKEN<IOperand>)next.Prev.Token).Tag.Type == EDataType.Ddatetime) && (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type == EDataType.Ddatetime))
                {
                    operand5 = (Operand<DateTime>)((TOKEN<IOperand>)next.Prev.Prev.Token).Tag;
                    tValue = operand5.TValue;
                    operand5 = (Operand<DateTime>)((TOKEN<IOperand>)next.Prev.Token).Tag;
                    time2 = operand5.TValue;
                    flag2 = type == EOperatorType.Equal ? tValue == time2 : tValue != time2;
                }
                else if((((TOKEN<IOperand>)next.Prev.Token).Tag.Type == EDataType.Dbool) && (((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Type == EDataType.Dbool))
                {
                    Operand<bool> operand6 = (Operand<bool>)((TOKEN<IOperand>)next.Prev.Prev.Token).Tag;
                    bool flag3 = operand6.TValue;
                    operand6 = (Operand<bool>)((TOKEN<IOperand>)next.Prev.Token).Tag;
                    bool flag4 = operand6.TValue;
                    flag2 = type == EOperatorType.Equal ? flag3 == flag4 : flag3 != flag4;
                }
                else
                {
                    num = Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Prev.Token).Tag.Value);
                    num2 = Convert.ToDouble(((TOKEN<IOperand>)next.Prev.Token).Tag.Value);
                    flag2 = type == EOperatorType.Equal ? num == num2 : num != num2;
                }
                Label_0B16:
                token = new TOKEN<IOperand>(ETokenType.token_operand, new Operand<bool>(EDataType.Dbool, flag2), next.Token.Index);
                Label_0B39:
                if(token != null)
                {
                    link2 = new TOKENLink(token);
                    link2.Next = next.Next;
                    if(next.Next != null)
                    {
                        next.Next.Prev = link2;
                    }
                    if(((TOKEN<Operator>)next.Token).Tag.Dimension == 1)
                    {
                        if(next.Prev.Prev != null)
                        {
                            link2.Prev = next.Prev.Prev;
                            next.Prev.Prev.Next = link2;
                        }
                    }
                    else if((((TOKEN<Operator>)next.Token).Tag.Dimension == 2) && (next.Prev.Prev.Prev != null))
                    {
                        link2.Prev = next.Prev.Prev.Prev;
                        next.Prev.Prev.Prev.Next = link2;
                    }
                    next = link2;
                }
                Label_0C48:;
            }
            return ((TOKEN<IOperand>)next.Token).Tag;
        }
    }
}

