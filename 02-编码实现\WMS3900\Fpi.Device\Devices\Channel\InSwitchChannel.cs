﻿//==================================================================================================
//类名：     InSwitchDataChannel   
//创建人:    曹旭
//创建时间:  2012-9-26 15:13:37
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using System;

namespace Fpi.Devices.Channel
{
    public class InSwitchChannel : SwitchChannel
    {
        public InSwitchChannel()
        {
            this.ChannelType = eDeviceChannelType.InSwitch;
        }

        public override void SetInState()
        {
            if(this.StateNode == null)
            {
                return;
            }

            bool stateValue = StateNode.IsOftenClose ? Convert.ToBoolean(this.ChannelValue) : !Convert.ToBoolean(this.ChannelValue);
            this.StateNode.SetValue(stateValue);
        }
    }
}
