<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>552, 7</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>取消(&amp;C)</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>469, 7</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>确定(&amp;E)</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBox1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>634, 2</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pnlFunc.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="pnlFunc.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 415</value>
  </data>
  <data name="pnlFunc.Size" type="System.Drawing.Size, System.Drawing">
    <value>634, 37</value>
  </data>
  <data name="pnlFunc.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Name" xml:space="preserve">
    <value>pnlFunc</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlFunc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlFunc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="panel4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="btnDelGroup.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelGroup.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelGroup.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnDelGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>545, 7</value>
  </data>
  <data name="btnDelGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelGroup.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelGroup.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelGroup.Name" xml:space="preserve">
    <value>btnDelGroup</value>
  </data>
  <data name="&gt;&gt;btnDelGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelGroup.Parent" xml:space="preserve">
    <value>panel4</value>
  </data>
  <data name="&gt;&gt;btnDelGroup.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddGroup.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddGroup.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddGroup.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAddGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>379, 7</value>
  </data>
  <data name="btnAddGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddGroup.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddGroup.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddGroup.Name" xml:space="preserve">
    <value>btnAddGroup</value>
  </data>
  <data name="&gt;&gt;btnAddGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddGroup.Parent" xml:space="preserve">
    <value>panel4</value>
  </data>
  <data name="&gt;&gt;btnAddGroup.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnUpdateGroup.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnUpdateGroup.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnUpdateGroup.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnUpdateGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>462, 7</value>
  </data>
  <data name="btnUpdateGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnUpdateGroup.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnUpdateGroup.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnUpdateGroup.Name" xml:space="preserve">
    <value>btnUpdateGroup</value>
  </data>
  <data name="&gt;&gt;btnUpdateGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUpdateGroup.Parent" xml:space="preserve">
    <value>panel4</value>
  </data>
  <data name="&gt;&gt;btnUpdateGroup.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel4.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 350</value>
  </data>
  <data name="panel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 37</value>
  </data>
  <data name="panel4.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;panel4.Name" xml:space="preserve">
    <value>panel4</value>
  </data>
  <data name="&gt;&gt;panel4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel4.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;panel4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lsvGroup.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="columnHeader9.Text" xml:space="preserve">
    <value>报警组编号</value>
  </data>
  <data name="columnHeader9.Width" type="System.Int32, mscorlib">
    <value>163</value>
  </data>
  <data name="columnHeader10.Text" xml:space="preserve">
    <value>报警组名称</value>
  </data>
  <data name="columnHeader10.Width" type="System.Int32, mscorlib">
    <value>238</value>
  </data>
  <data name="lsvGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="lsvGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 343</value>
  </data>
  <data name="lsvGroup.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;lsvGroup.Name" xml:space="preserve">
    <value>lsvGroup</value>
  </data>
  <data name="&gt;&gt;lsvGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lsvGroup.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;lsvGroup.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabPage4.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage4.Size" type="System.Drawing.Size, System.Drawing">
    <value>626, 389</value>
  </data>
  <data name="tabPage4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tabPage4.Text" xml:space="preserve">
    <value>报警组配置</value>
  </data>
  <data name="&gt;&gt;tabPage4.Name" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;tabPage4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage4.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnSaveCodeByGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>483, 5</value>
  </data>
  <data name="btnSaveCodeByGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 23</value>
  </data>
  <data name="btnSaveCodeByGroup.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="btnSaveCodeByGroup.Text" xml:space="preserve">
    <value>保存当前报警码配置</value>
  </data>
  <data name="&gt;&gt;btnSaveCodeByGroup.Name" xml:space="preserve">
    <value>btnSaveCodeByGroup</value>
  </data>
  <data name="&gt;&gt;btnSaveCodeByGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnSaveCodeByGroup.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;btnSaveCodeByGroup.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lblAlarmGroup.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblAlarmGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 10</value>
  </data>
  <data name="lblAlarmGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 12</value>
  </data>
  <data name="lblAlarmGroup.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lblAlarmGroup.Text" xml:space="preserve">
    <value>报警组选择:</value>
  </data>
  <data name="&gt;&gt;lblAlarmGroup.Name" xml:space="preserve">
    <value>lblAlarmGroup</value>
  </data>
  <data name="&gt;&gt;lblAlarmGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblAlarmGroup.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;lblAlarmGroup.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cboAlarmGroups.Location" type="System.Drawing.Point, System.Drawing">
    <value>79, 6</value>
  </data>
  <data name="cboAlarmGroups.Size" type="System.Drawing.Size, System.Drawing">
    <value>401, 20</value>
  </data>
  <data name="cboAlarmGroups.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;cboAlarmGroups.Name" xml:space="preserve">
    <value>cboAlarmGroups</value>
  </data>
  <data name="&gt;&gt;cboAlarmGroups.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cboAlarmGroups.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;cboAlarmGroups.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="btnDelCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelCode.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelCode.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnDelCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>545, 7</value>
  </data>
  <data name="btnDelCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelCode.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelCode.Name" xml:space="preserve">
    <value>btnDelCode</value>
  </data>
  <data name="&gt;&gt;btnDelCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelCode.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;btnDelCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddCode.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>379, 7</value>
  </data>
  <data name="btnAddCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddCode.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddCode.Name" xml:space="preserve">
    <value>btnAddCode</value>
  </data>
  <data name="&gt;&gt;btnAddCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddCode.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;btnAddCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnUpdateCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnUpdateCode.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnUpdateCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>462, 7</value>
  </data>
  <data name="btnUpdateCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnUpdateCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnUpdateCode.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnUpdateCode.Name" xml:space="preserve">
    <value>btnUpdateCode</value>
  </data>
  <data name="&gt;&gt;btnUpdateCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUpdateCode.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;btnUpdateCode.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 353</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 37</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lsvCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="columnHeader7.Text" xml:space="preserve">
    <value>报警码</value>
  </data>
  <data name="columnHeader5.Text" xml:space="preserve">
    <value>报警名称</value>
  </data>
  <data name="columnHeader5.Width" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="columnHeader4.Text" xml:space="preserve">
    <value>报警等级</value>
  </data>
  <data name="columnHeader4.Width" type="System.Int32, mscorlib">
    <value>83</value>
  </data>
  <data name="columnHeader8.Text" xml:space="preserve">
    <value>重复判定时间(s)</value>
  </data>
  <data name="columnHeader8.Width" type="System.Int32, mscorlib">
    <value>116</value>
  </data>
  <data name="columnHeader6.Text" xml:space="preserve">
    <value>报警描述</value>
  </data>
  <data name="columnHeader6.Width" type="System.Int32, mscorlib">
    <value>257</value>
  </data>
  <data name="lsvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 32</value>
  </data>
  <data name="lsvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 317</value>
  </data>
  <data name="lsvCode.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;lsvCode.Name" xml:space="preserve">
    <value>lsvCode</value>
  </data>
  <data name="&gt;&gt;lsvCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lsvCode.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;lsvCode.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tabPage2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage2.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="tabPage2.Size" type="System.Drawing.Size, System.Drawing">
    <value>626, 392</value>
  </data>
  <data name="tabPage2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tabPage2.Text" xml:space="preserve">
    <value>报警码配置</value>
  </data>
  <data name="&gt;&gt;tabPage2.Name" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;tabPage2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage2.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="panel2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="btnDelGrade.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelGrade.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelGrade.Location" type="System.Drawing.Point, System.Drawing">
    <value>545, 7</value>
  </data>
  <data name="btnDelGrade.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelGrade.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelGrade.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelGrade.Name" xml:space="preserve">
    <value>btnDelGrade</value>
  </data>
  <data name="&gt;&gt;btnDelGrade.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelGrade.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;btnDelGrade.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddGrade.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddGrade.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddGrade.Location" type="System.Drawing.Point, System.Drawing">
    <value>379, 7</value>
  </data>
  <data name="btnAddGrade.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddGrade.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddGrade.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddGrade.Name" xml:space="preserve">
    <value>btnAddGrade</value>
  </data>
  <data name="&gt;&gt;btnAddGrade.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddGrade.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;btnAddGrade.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnUpdateGrade.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnUpdateGrade.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnUpdateGrade.Location" type="System.Drawing.Point, System.Drawing">
    <value>462, 7</value>
  </data>
  <data name="btnUpdateGrade.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnUpdateGrade.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnUpdateGrade.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnUpdateGrade.Name" xml:space="preserve">
    <value>btnUpdateGrade</value>
  </data>
  <data name="&gt;&gt;btnUpdateGrade.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUpdateGrade.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;btnUpdateGrade.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 353</value>
  </data>
  <data name="panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 37</value>
  </data>
  <data name="panel2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;panel2.Name" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel2.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;panel2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lsvGrade.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="columnHeader1.Text" xml:space="preserve">
    <value>报警等级编号</value>
  </data>
  <data name="columnHeader1.Width" type="System.Int32, mscorlib">
    <value>200</value>
  </data>
  <data name="columnHeader2.Text" xml:space="preserve">
    <value>报警等级命名</value>
  </data>
  <data name="columnHeader2.Width" type="System.Int32, mscorlib">
    <value>200</value>
  </data>
  <data name="columnHeader3.Text" xml:space="preserve">
    <value>报警等级值</value>
  </data>
  <data name="columnHeader3.Width" type="System.Int32, mscorlib">
    <value>214</value>
  </data>
  <data name="lsvGrade.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="lsvGrade.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 346</value>
  </data>
  <data name="lsvGrade.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lsvGrade.Name" xml:space="preserve">
    <value>lsvGrade</value>
  </data>
  <data name="&gt;&gt;lsvGrade.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lsvGrade.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;lsvGrade.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabPage1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage1.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 3, 3, 3</value>
  </data>
  <data name="tabPage1.Size" type="System.Drawing.Size, System.Drawing">
    <value>626, 392</value>
  </data>
  <data name="tabPage1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tabPage1.Text" xml:space="preserve">
    <value>报警等级配置</value>
  </data>
  <data name="&gt;&gt;tabPage1.Name" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;tabPage1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage1.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="btnDelSource.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDelSource.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnDelSource.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnDelSource.Location" type="System.Drawing.Point, System.Drawing">
    <value>545, 7</value>
  </data>
  <data name="btnDelSource.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnDelSource.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnDelSource.Text" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="&gt;&gt;btnDelSource.Name" xml:space="preserve">
    <value>btnDelSource</value>
  </data>
  <data name="&gt;&gt;btnDelSource.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnDelSource.Parent" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;btnDelSource.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddSource.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddSource.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnAddSource.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAddSource.Location" type="System.Drawing.Point, System.Drawing">
    <value>379, 7</value>
  </data>
  <data name="btnAddSource.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnAddSource.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnAddSource.Text" xml:space="preserve">
    <value>新增</value>
  </data>
  <data name="&gt;&gt;btnAddSource.Name" xml:space="preserve">
    <value>btnAddSource</value>
  </data>
  <data name="&gt;&gt;btnAddSource.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAddSource.Parent" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;btnAddSource.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnEditSource.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnEditSource.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="btnEditSource.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEditSource.Location" type="System.Drawing.Point, System.Drawing">
    <value>462, 7</value>
  </data>
  <data name="btnEditSource.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btnEditSource.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnEditSource.Text" xml:space="preserve">
    <value>修改</value>
  </data>
  <data name="&gt;&gt;btnEditSource.Name" xml:space="preserve">
    <value>btnEditSource</value>
  </data>
  <data name="&gt;&gt;btnEditSource.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEditSource.Parent" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;btnEditSource.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 353</value>
  </data>
  <data name="panel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 37</value>
  </data>
  <data name="panel3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;panel3.Name" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;panel3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel3.Parent" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;panel3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lsvSource.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="columnHeader11.Text" xml:space="preserve">
    <value>报警源编号</value>
  </data>
  <data name="columnHeader11.Width" type="System.Int32, mscorlib">
    <value>163</value>
  </data>
  <data name="columnHeader12.Text" xml:space="preserve">
    <value>报警源名称</value>
  </data>
  <data name="columnHeader12.Width" type="System.Int32, mscorlib">
    <value>238</value>
  </data>
  <data name="lsvSource.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="lsvSource.Size" type="System.Drawing.Size, System.Drawing">
    <value>620, 346</value>
  </data>
  <data name="lsvSource.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;lsvSource.Name" xml:space="preserve">
    <value>lsvSource</value>
  </data>
  <data name="&gt;&gt;lsvSource.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lsvSource.Parent" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;lsvSource.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabPage3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage3.Size" type="System.Drawing.Size, System.Drawing">
    <value>626, 392</value>
  </data>
  <data name="tabPage3.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tabPage3.Text" xml:space="preserve">
    <value>报警源配置</value>
  </data>
  <data name="&gt;&gt;tabPage3.Name" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;tabPage3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage3.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage3.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tabControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="tabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>634, 415</value>
  </data>
  <data name="tabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;tabControl1.Name" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>634, 452</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAb3+PYCAoMP8/R09AAAAAAAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA/29a
        PzAAAAAAAAAAAAAAAAAAAAAAAAAAAHCAkP8wuPD/EBgg/z9HT0AAAAAAAAAAAAAAAABvWj8wcFhA//Do
        4P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAABvf49QcICQ/zC48P8gKED/P0dPQAAAAABvWj8wcFhA//Dw
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG9/j1BwgJD/MLjw/zA4UP9fT09gcFhA///4
        8P+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb3+PUHCAkP9AqND/cFhA////
        //+woJD/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABvb29wcFhA////
        //+woJD/P0dPQAAAAAAAAAAAj29fEI93X0CAWFD/j3dfQAAAAAAAAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/MLjw/2BgcP+Ph3+gAAAAAH9fT1CAaFD/8PDw/5CAcP8AAAAAAAAAAAAAAABvWj8wcFhA////
        //+woJD/b3+PUHCAkP9woKD/kIBw/5BwYP+AYFD/kHhf8LCQgP+vn4+Qn4h/cKCAcP+AaFD/kHBg////
        //+woJD/AAAAAAAAAACfl4+goJCA//Dw8P/g4ND/0MjA/493X+CvmH9wr5+PILCgkP/AsKD/wLCg/8Cw
        oP+QgHD/AAAAAAAAAAAAAAAAr5+PQMCgkP///////v7+4PDg4P+wj3/AAAAAAAAAAACwoJD/////IL+v
        nzDAsKD/oIBw/wAAAAAAAAAAAAAAAI9/b1CgiHD///f/8PDg4PDAoJDwr5+PMAAAAAAAAAAAAAAAAAAA
        AAD/9/9A0Liw/8CooP8AAAAAj3dfII93X+CQcGD/sKeg8MCooODAn4+wr5+PMAAAAAAAAAAAAAAAAAAA
        AAAAAAAAsKCQ/7CgkP+vn49QAAAAAMCooP/AoJD/0LCg/8CwoP+vn49QAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAA//8AAP//AADHxwAAw4cAAMEPAADgHwAA8D8AAPgwAADwEAAA4AAAAAMAAAAHAwAABwMAAMQH
        AADEHwAA//8AAA==
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>报警配置</value>
  </data>
  <data name="&gt;&gt;columnHeader9.Name" xml:space="preserve">
    <value>columnHeader9</value>
  </data>
  <data name="&gt;&gt;columnHeader9.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader10.Name" xml:space="preserve">
    <value>columnHeader10</value>
  </data>
  <data name="&gt;&gt;columnHeader10.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader7.Name" xml:space="preserve">
    <value>columnHeader7</value>
  </data>
  <data name="&gt;&gt;columnHeader7.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Name" xml:space="preserve">
    <value>columnHeader5</value>
  </data>
  <data name="&gt;&gt;columnHeader5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Name" xml:space="preserve">
    <value>columnHeader4</value>
  </data>
  <data name="&gt;&gt;columnHeader4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader8.Name" xml:space="preserve">
    <value>columnHeader8</value>
  </data>
  <data name="&gt;&gt;columnHeader8.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Name" xml:space="preserve">
    <value>columnHeader6</value>
  </data>
  <data name="&gt;&gt;columnHeader6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Name" xml:space="preserve">
    <value>columnHeader1</value>
  </data>
  <data name="&gt;&gt;columnHeader1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Name" xml:space="preserve">
    <value>columnHeader2</value>
  </data>
  <data name="&gt;&gt;columnHeader2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Name" xml:space="preserve">
    <value>columnHeader3</value>
  </data>
  <data name="&gt;&gt;columnHeader3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader11.Name" xml:space="preserve">
    <value>columnHeader11</value>
  </data>
  <data name="&gt;&gt;columnHeader11.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeader12.Name" xml:space="preserve">
    <value>columnHeader12</value>
  </data>
  <data name="&gt;&gt;columnHeader12.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormConfigAlarm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>