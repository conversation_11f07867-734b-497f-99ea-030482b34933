using System;
using Fpi.Communication.Ports.FpiPorts;
using Fpi.UI.Common.PC.Configure;
using Fpi.Util.Sundry;
using Fpi.Xml;

namespace Fpi.Communication.Ports.UI.PC
{
    public partial class UC_FpiRouterPort : BaseConfigureView//UserControl
    {
        public UC_FpiRouterPort()
        {
            InitializeComponent();
        }

        protected override void ShowConfig(object obj)
        {
            BaseNode configNode = configObj as BaseNode;
            int address = 0;
            try
            {
                address = StringUtil.ParseByte(configNode.GetPropertyValue(FpiRouterPort.PropertyName_Address, "0"));
            }
            catch(Exception)
            {
                address = 0;
            }

            this.nuAddress.Value = address;
        }

        public override object Save()
        {
            string address = string.Format("0x{0:x2}", (int)this.nuAddress.Value).ToUpper();

            BaseNode configNode = configObj as BaseNode;
            configNode.SetProperty(FpiRouterPort.PropertyName_Address, address);
            return configNode;
        }
    }
}