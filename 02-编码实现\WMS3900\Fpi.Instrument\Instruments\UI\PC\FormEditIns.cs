using System;
using System.Windows.Forms;
using Fpi.Instruments.Properties;

namespace Fpi.Instruments.UI.PC
{
    public partial class FormEditIns : Form
    {
        public FormEditIns()
        {
            InitializeComponent();

            if(InstrumentManager.GetInstance().instrumentTypes != null)
            {
                this.cmbType.Items.AddRange(InstrumentManager.GetInstance().instrumentTypes.GetItems());
            }

            if(InstrumentManager.GetInstance().instruments != null)
            {
                this.cmbRoute.Items.AddRange(InstrumentManager.GetInstance().instruments.GetItems());
            }
        }

        public FormEditIns(Instrument ins)
            : this()
        {
            this.Instrument = ins;
        }

        public Instrument Instrument { get; private set; }

        private void FormEditIns_Load(object sender, EventArgs e)
        {
            if(Instrument == null)
            {
                Instrument = new Instrument();
            }

            this.txtID.Text = Instrument.id;
            this.txtName.Text = Instrument.name;
            this.cmbType.SelectedItem = Instrument.InstrumentType;
            if(Instrument.Router != null)
            {
                this.cmbRoute.SelectedItem = Instrument.Router;
            }
            this.nuAddress.Value = Instrument.address;
            this.txtDesc.Text = Instrument.desc;

        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            }

        }

        private void Save()
        {
            string id = this.txtID.Text;
            string name = this.txtName.Text;
            if(string.IsNullOrEmpty(id) || string.IsNullOrEmpty(name))
            {
                throw new Exception(Resources.InstrumentEmpty);
            }

            Instrument.id = id;
            Instrument.name = name;
            Instrument.address = (int)this.nuAddress.Value;
            Instrument.desc = this.txtDesc.Text;

            if(this.cmbType.SelectedItem != null)
            {
                Instrument.type = (this.cmbType.SelectedItem as InstrumentType).id;
            }

            if(this.cmbRoute.SelectedItem != null)
            {
                Instrument.router = (this.cmbRoute.SelectedItem as Instrument).id;
            }
        }


    }
}