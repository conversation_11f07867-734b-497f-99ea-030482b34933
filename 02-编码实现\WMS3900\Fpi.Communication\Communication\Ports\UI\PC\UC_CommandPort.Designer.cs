﻿namespace Fpi.Communication.Ports.UI.PC
{
    partial class UC_CommandPort
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.lblManager = new System.Windows.Forms.Label();
            this.cmbManager = new System.Windows.Forms.ComboBox();
            this.lblConverter = new System.Windows.Forms.Label();
            this.cmbConverter = new System.Windows.Forms.ComboBox();
            this.SuspendLayout();
            // 
            // lblManager
            // 
            this.lblManager.AutoSize = true;
            this.lblManager.Location = new System.Drawing.Point(27, 27);
            this.lblManager.Name = "lblManager";
            this.lblManager.Size = new System.Drawing.Size(47, 12);
            this.lblManager.TabIndex = 0;
            this.lblManager.Text = "Manager";
            // 
            // cmbManager
            // 
            this.cmbManager.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbManager.DropDownWidth = 400;
            this.cmbManager.FormattingEnabled = true;
            this.cmbManager.Location = new System.Drawing.Point(95, 23);
            this.cmbManager.Name = "cmbManager";
            this.cmbManager.Size = new System.Drawing.Size(298, 20);
            this.cmbManager.TabIndex = 2;
            // 
            // lblConverter
            // 
            this.lblConverter.AutoSize = true;
            this.lblConverter.Location = new System.Drawing.Point(27, 60);
            this.lblConverter.Name = "lblConverter";
            this.lblConverter.Size = new System.Drawing.Size(59, 12);
            this.lblConverter.TabIndex = 0;
            this.lblConverter.Text = "Converter";
            // 
            // cmbConverter
            // 
            this.cmbConverter.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbConverter.DropDownWidth = 400;
            this.cmbConverter.FormattingEnabled = true;
            this.cmbConverter.Location = new System.Drawing.Point(95, 56);
            this.cmbConverter.Name = "cmbConverter";
            this.cmbConverter.Size = new System.Drawing.Size(298, 20);
            this.cmbConverter.TabIndex = 2;
            // 
            // UC_CommandPort
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.cmbConverter);
            this.Controls.Add(this.lblConverter);
            this.Controls.Add(this.cmbManager);
            this.Controls.Add(this.lblManager);
            this.Name = "UC_CommandPort";
            this.Size = new System.Drawing.Size(420, 103);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblManager;
        private System.Windows.Forms.ComboBox cmbManager;
        private System.Windows.Forms.Label lblConverter;
        private System.Windows.Forms.ComboBox cmbConverter;
    }
}
