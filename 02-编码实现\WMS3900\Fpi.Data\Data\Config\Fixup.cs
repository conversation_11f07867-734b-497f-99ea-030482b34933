using System;
using Fpi.Xml;

namespace Fpi.Data.Config
{
    public class Fixup : BaseNode
    {
        public bool Valid = true;
        public double Value;
        public double Range;

        public Fixup()
        {
        }
        public Fixup(double fixedValue)
            : this()
        {
            this.Value = fixedValue;
        }

        public Fixup(double fixedValue, double range)
            : this(fixedValue)
        {
            this.Range = range;
        }

        private readonly Random _rd = new Random();
        public double GetValue()
        {
            double v = Value + (_rd.NextDouble() - 0.5) * 2 * Range;
            return v;
        }
    }
}
