﻿using System.Threading.Tasks;
using Fpi.Xml;

namespace Fpi.Entrance
{
    /// <summary>
    /// 网络门禁基类
    /// </summary>
    public class BaseNETEntrance : IdNameNode
    {
        #region 配置相关变量

        /// <summary>
        /// 用于设备描述信息
        /// </summary>
        public string Description;

        #region 连接门禁

        /// <summary>
        /// 设备IP
        /// </summary>
        public string Ip;

        /// <summary>
        /// 设备端口
        /// </summary>
        public string Port;

        /// <summary>
        /// 用户名
        /// </summary>
        public string User;

        /// <summary>
        /// 密码
        /// </summary>
        public string Pwd;

        #endregion

        #region 人员核验、开关门状态接收设置

        /// <summary>
        /// 是否接收
        /// </summary>
        public bool IsReceiceEnable;

        /// <summary>
        /// 接收端口
        /// </summary>
        public int ReceivePort;

        #endregion

        #endregion

        #region 属性

        /// <summary>
        /// 门禁当前为开启状态
        /// </summary>
        public bool IsDoorOpen { get; set; }

        #endregion

        #region 构造

        public BaseNETEntrance()
        {
            Ip = Ip ?? "";
            Port = Port ?? "";
            User = User ?? "";
            Pwd = Pwd ?? "";
        }

        #endregion

        #region 门禁方法

        #region 初始化

        /// <summary>
        /// 初始化门禁
        /// </summary>
        public virtual void InitEntranceAsync()
        {
        }

        /// <summary>
        /// 释放门禁
        /// </summary>
        public virtual void CleanEntranceAsync()
        {
        }

        #endregion

        /// <summary>
        /// 读取门禁状态
        /// </summary>
        public virtual bool ReadEntranceState()
        {
            return false;
        }

        /// <summary>
        /// 远程开门
        /// </summary>
        public virtual Task<string> OpenDoorAsync()
        {
            return null;
        }

        /// <summary>
        /// 远程关门
        /// </summary>
        public virtual Task<string> CloseDoorAsync()
        {
            return null;
        }

        #endregion
    }
}
