﻿using System;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Windows.Forms;
using Fpi.Camera.UI;
using Fpi.Util.Extensions;
using Fpi.Xml;

namespace Fpi.Camera
{
    /// <summary>
    /// 网络摄像机设备基类
    /// </summary>
    public class BaseNETCamera : IdNameNode
    {
        #region 配置相关变量

        /// <summary>
        /// 用于设备描述信息
        /// </summary>
        public string Description;

        #region 连接摄像机

        /// <summary>
        /// 设备IP
        /// </summary>
        public string Ip;

        /// <summary>
        /// 设备端口
        /// </summary>
        public string Port;

        /// <summary>
        /// 用户名
        /// </summary>
        public string User;

        /// <summary>
        /// 密码
        /// </summary>
        public string Pwd;

        /// <summary>
        /// 是否刻录机
        /// </summary>
        public bool IsDS;

        /// <summary>
        /// 刻录机通道
        /// </summary>
        public int DsPort;

        #endregion

        #region 水印设置

        /// <summary>
        /// 是否启用水印
        /// </summary>
        public bool OSDflag;

        /// <summary>
        /// 水印文字
        /// </summary>
        public string OSDString;

        /// <summary>
        /// 水印位置X
        /// </summary>
        public ushort OSDx;

        /// <summary>
        /// 水印位置Y
        /// </summary>
        public ushort OSDy;

        #endregion

        #region 文件

        /// <summary>
        /// 截图保存路径
        /// </summary>
        public string PicSavePath;

        /// <summary>
        /// 录像保存路径
        /// </summary>
        public string VideoSavePath;

        /// <summary>
        /// 控制操作窗体的位置，在弹出自己的控制窗体的时候通过反射可以吧对应的控制窗弹出
        /// </summary>
        public string ControlTypePath;

        #endregion

        #endregion

        #region 标志变量（不本地存储）

        /// <summary>
        /// 对应显示界面
        /// </summary>
        public PlayerItem PreviewInterface { get; set; }

        /// <summary>
        /// 预览、抓图通道
        /// </summary>
        public int PrevierChannel { get; set; } = 1;

        /// <summary>
        /// 设备登录用户号
        /// </summary>
        public IntPtr m_LoginID { get; set; } = IntPtr.Zero;

        /// <summary>
        /// 预览数据流句柄
        /// </summary>
        public IntPtr m_PreviewRealPlayHandle { get; set; } = IntPtr.Zero;

        /// <summary>
        /// 非预览数据流句柄
        /// </summary>
        public IntPtr m_NoPreviewRealPlayHandle { get; set; } = IntPtr.Zero;

        /// <summary>
        /// 录像中标志
        /// </summary>
        public bool IsRecording { get; set; }

        /// <summary>
        /// 录像文件名
        /// </summary>
        public string SVideoFileName { get; set; }

        #endregion

        #region 构造

        public BaseNETCamera()
        {
            Ip = Ip ?? "";
            Port = Port ?? "";
            User = User ?? "";
            Pwd = Pwd ?? "";

            PicSavePath = "ScreenShot";
            VideoSavePath = "VideoFile";
        }

        #endregion

        #region 摄像机方法

        #region 检查连接状态

        /// <summary>
        /// 检查摄像机通信状态
        /// </summary>
        /// <returns></returns>
        public virtual bool IsConnect()
        {
            try
            {
                // 初始化
                InitSDK();

                // 未登录
                if(IntPtr.Zero == m_LoginID)
                {
                    Login();
                }

                return m_LoginID != IntPtr.Zero;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化摄像机SDK
        /// </summary>
        public virtual void InitSDK()
        {
        }

        /// <summary>
        /// 释放摄像机SDK
        /// </summary>
        public virtual void CleanSDK()
        {
        }

        #endregion

        #region 登录注销

        /// <summary>
        /// 登录
        /// </summary>
        public virtual void Login()
        {
        }

        /// <summary>
        /// 注销
        /// </summary>
        public virtual void Logout()
        {
        }

        #endregion

        #region 视频流相关

        /// <summary>
        /// 开始获取视频流
        /// </summary>
        /// <returns></returns>
        public virtual void StartRealPlay()
        {
        }

        /// <summary>
        /// 停止获取视频流
        /// </summary>
        public virtual void StopRealPlay()
        {
        }

        /// <summary>
        /// 开始获取视频流(不绑定预览界面)
        /// </summary>
        /// <returns></returns>
        public virtual void StartNoPreviewRealPlay()
        {
        }

        /// <summary>
        /// 停止获取视频流(不绑定预览界面)
        /// </summary>
        public virtual void StopNoPreviewRealPlay()
        {
        }

        #endregion

        #region 抓拍

        /// <summary>
        /// 抓拍图像，返回图像值
        /// </summary>
        /// <returns></returns>
        public virtual Image GetScreenShotImage()
        {
            if(ScreenShot(out string picFileName) && File.Exists(picFileName))
            {
                return Image.FromFile(picFileName);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 抓拍图像到默认目录
        /// </summary>
        /// <returns></returns>
        public bool ScreenShot()
        {
            return ScreenShot(out string picFileName);
        }

        /// <summary>
        /// 抓拍图像到默认目录
        /// 年月日分文件夹
        /// </summary>
        /// <returns></returns>
        public virtual bool ScreenShot(out string picFileName)
        {
            var now = DateTime.Now;

            var path = FileExtension.GetAbsolutePath(PicSavePath);

            string savePath = Path.Combine(path, now.ToString("yyyy"), now.ToString("MM"), now.ToString("dd"), name);
            //先初始化图片存放路径
            if(!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            //图片文件名
            picFileName = Path.Combine(savePath, GetPictureFileName(now));

            // 截图失败时，尝试重新截图(第一次会尝试进行修复，确保第二次能成功)
            if(!ScreenShot(picFileName))
            {
                // 重新截图
                return ScreenShot(picFileName);
            }

            return true;
        }

        /// <summary>
        /// 抓拍图像到指定文件
        /// </summary>
        /// <param name="picFileName"></param>
        /// <returns></returns>
        public virtual bool ScreenShot(string picFileName)
        {
            return false;
        }

        #endregion

        #region 录像

        /// <summary>
        /// 开始录像到默认目录
        /// 年月日分文件夹
        /// </summary>
        /// <returns></returns>
        public virtual bool StartRecording()
        {
            var now = DateTime.Now;

            var path = FileExtension.GetAbsolutePath(VideoSavePath);

            string savePath = Path.Combine(path, now.ToString("yyyy"), now.ToString("MM"), now.ToString("dd"), name);
            // 先初始化录像存放路径
            if(!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            //录像保存路径和文件名
            string videoFileName = Path.Combine(savePath, GetVideoFileName(now));
            return StartRecording(videoFileName);
        }

        /// <summary>
        /// 开始录像到指定文件
        /// </summary>
        /// <param name="videoFileName"></param>
        /// <returns></returns>
        public virtual bool StartRecording(string videoFileName)
        {
            return false;
        }

        /// <summary>
        /// 停止录像
        /// </summary>
        /// <returns></returns>
        public virtual bool StopRecording()
        {
            return false;
        }

        #endregion

        #region 云台控制

        /// <summary>
        /// 云台控制
        /// </summary>
        /// <param name="dwPTZCommand">控制方向类型</param>
        /// <param name="dwStop">是否停止(mouseup 表示停止传入1，down传入0)</param>
        /// <param name="dwSpeed">速度</param>
        public virtual void PTZControlWithSpeed(ControlType dwPTZCommand, int dwStop, int dwSpeed)
        {

        }

        #endregion

        #region 时间校准

        /// <summary>
        /// 时间校准
        /// </summary>
        /// <returns></returns>
        public virtual void SetTime()
        {
        }

        #endregion

        #region 巡航控制

        /// <summary>
        /// 开始巡航
        /// </summary>
        /// <returns></returns>
        public virtual void PTZCruiseStart()
        {
        }

        /// <summary>
        /// 结束巡航
        /// </summary>
        /// <returns></returns>
        public virtual void PTZCruiseStop()
        {
        }

        #endregion

        #endregion

        #region 对应显示界面

        /// <summary>
        /// 获取摄像机对应显示界面
        /// </summary>
        /// <returns></returns>
        public PlayerItem GetPreviewInterface()
        {
            return PreviewInterface ?? (PreviewInterface = new PlayerItem(id)
            {
                TabStop = true,
                BorderStyle = BorderStyle.None
            });
        }

        #endregion

        #region 获取文件名

        /// <summary>
        /// 获取录像文件名称
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public virtual string GetVideoFileName(DateTime time)
        {
            //return $"{Ip}_{PrevierChannel}_{time.ToString("yyMMddHHmmss", DateTimeFormatInfo.InvariantInfo)}.mp4";
            return $"{name}_{time.ToString("yyMMddHHmmss", DateTimeFormatInfo.InvariantInfo)}.mp4";
        }

        /// <summary>
        /// 获取截图文件名称
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public virtual string GetPictureFileName(DateTime time)
        {
            //return $"{Ip}_{PrevierChannel}_{time.ToString("yyMMddHHmmss", DateTimeFormatInfo.InvariantInfo)}.jpg";
            return $"{name}_{time.ToString("yyMMddHHmmss", DateTimeFormatInfo.InvariantInfo)}.jpg";
        }

        #endregion

        #region 获取错误日志

        /// <summary>
        /// 获取错误日志
        /// </summary>
        /// <returns></returns>
        public virtual string GetLastError()
        {
            return string.Empty;
        }

        #endregion
    }
}