﻿using System;

namespace Fpi.Devices.Channel
{
    public class A2BRoundProcessor : Processor
    {
        #region — 属性 —
        /// <summary>电流/电压 最小值</summary>
        private double minSignalValue;

        public double MinSignalValue
        {
            get => minSignalValue;
            set => minSignalValue = value;
        }

        /// <summary>电流/电压 最大值</summary>
        private double maxSignalValue;

        public double MaxSignalValue
        {
            get => maxSignalValue;
            set => maxSignalValue = value;
        }

        /// <summary>对应数值 最小值</summary>
        private double minDigitalValue;

        public double MinDigitalValue
        {
            get => minDigitalValue;
            set => minDigitalValue = value;
        }

        /// <summary>对应数值 最大值</summary>
        private double maxDigitalValue;

        public double MaxDigitalValue
        {
            get => maxDigitalValue;
            set => maxDigitalValue = value;
        }

        /// <summary>保留小数位数</summary>
        private int _KeepDigits;

        public int Digits
        {
            get => _KeepDigits;
            set => _KeepDigits = value;
        }

        public int RoundAble { get; set; }

        #endregion

        public A2BRoundProcessor()
        {
            this.ucParamConfig = new ConfigA2BRoundPanel();
        }

        public A2BRoundProcessor(string param)
            : this()
        {
            this.SetParam(param);
        }

        public override void SetParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length == 4)
            {
                double.TryParse(paramStrs[0], out minSignalValue);
                double.TryParse(paramStrs[1], out maxSignalValue);

                double.TryParse(paramStrs[2], out minDigitalValue);
                double.TryParse(paramStrs[3], out maxDigitalValue);
            }

            if(paramStrs.Length == 5)
            {
                double.TryParse(paramStrs[0], out minSignalValue);
                double.TryParse(paramStrs[1], out maxSignalValue);

                double.TryParse(paramStrs[2], out minDigitalValue);
                double.TryParse(paramStrs[3], out maxDigitalValue);

                int.TryParse(paramStrs[4], out _KeepDigits);
                //int.TryParse(paramStrs[5], out _RoundBle);
            }

        }

        /// <summary>
        /// 输入数据处理
        /// </summary>
        /// <param name="inputValue"></param>
        /// <returns></returns>
        public override double InputProcessData(double inputValue)
        {
            double dataProcessed = double.NaN;
            //edit by whp 20170420
            if(inputValue < this.minSignalValue)
            {
                //inputValue = this.minSignalValue;
                return dataProcessed;
            }
            dataProcessed = (this.maxDigitalValue - this.minDigitalValue)
                            / (this.maxSignalValue - this.minSignalValue)
                            * (inputValue - this.minSignalValue)
                            + this.minDigitalValue;
            //=====增加数据处理====20170807 whp
            if(_KeepDigits > 0)
            {
                int TenPow = 1;
                for(int i = 0; i < _KeepDigits; i++)
                {
                    TenPow = TenPow * 10;
                }
                dataProcessed = Math.Truncate(dataProcessed * TenPow) / TenPow;
            }
            else
            {
                return Math.Truncate(dataProcessed);
            }

            return dataProcessed;
        }

        /// <summary>
        /// 输出数据处理
        /// </summary>
        /// <param name="outputValue"></param>
        /// <returns></returns>
        public override double OutputProcessData(double outputValue)
        {
            double dataProcessed = double.NaN;

            dataProcessed = outputValue <= this.minDigitalValue
                ? this.minSignalValue
                : outputValue >= this.maxDigitalValue
                    ? this.maxSignalValue
                    : (this.maxSignalValue - this.minSignalValue)
                                                / (this.maxDigitalValue - this.minDigitalValue)
                                                * (outputValue - this.minDigitalValue)
                                                + this.minSignalValue;

            return dataProcessed;
        }

        public override string ToString()
        {
            return "范围转换处理器[包含小数处理](如：4-20MA、0-5V等)";
        }
    }
}
