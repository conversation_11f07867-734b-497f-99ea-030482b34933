﻿using System;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;
using AForge.Controls;
using Fpi.UI.Common.PC;
using Fpi.UI.PC.DockForms;

namespace Fpi.Camera.UI
{
    /// <summary>
    /// USB摄像机监控主界面
    /// </summary>
    public partial class FrmUSBCameraPreview : BaseMainView
    {
        #region 字段属性

        /// <summary>
        /// 当前选中摄像机
        /// </summary>
        private USBCamera _currentCamera;

        /// <summary>
        /// 预览控件
        /// </summary>
        private VideoSourcePlayer _videoSourcePlayer;

        #endregion

        #region 构造

        /// <summary>
        /// 视频监控主界面
        /// </summary>
        public FrmUSBCameraPreview()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 加载初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmUSBCameraPreview_Load(object sender, EventArgs e)
        {
            BindingCameraListToCombox();
        }

        private void cmbCameraList_SelectedIndexChanged(object sender, EventArgs e)
        {
            var newCamera = cmbCameraList.SelectedItem as USBCamera;
            if(newCamera != _currentCamera)
            {
                StopUIPreview();

                if(_currentCamera != null)
                {
                    //CameraLogHelper.Info($"摄像机[{_currentCamera.Name}]:--断开成功!");
                    logView.Log($"摄像机[{_currentCamera.Name}]:--断开成功!");
                }

                _currentCamera = cmbCameraList.SelectedItem as USBCamera;

                if(_currentCamera != null)
                {
                    StartUIPreview();

                    //CameraLogHelper.Info($"摄像机[{newCamera.Name}]:--连接成功!");
                    logView.Log($"摄像机[{newCamera.Name}]:--连接成功!");
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                // 关闭当前预览
                StopUIPreview();

                USBCameraManager.GetInstance().RefreshCameraList();

                BindingCameraListToCombox();
            }
            catch(Exception ex)
            {
                var info = $"刷新USB摄像机列表出错：{ex.Message}";
                logView.Log(info);
                FpiMessageBox.ShowInfo(info);
            }
        }

        /// <summary>
        /// 退出界面，清理事件绑定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MainPreviewForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                // 关闭当前预览
                StopUIPreview();
            }
            catch(Exception ex)
            {
                var info = $"退出界面清理资源出错：{ex.Message}";
                FpiMessageBox.ShowInfo(info);
            }
        }

        #endregion

        #region 截图/录像

        /// <summary>
        /// 截图
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnScreenShot_Click(object sender, EventArgs e)
        {
            try
            {
                if(_currentCamera == null)
                {
                    throw new Exception("当前无有效摄像机！");
                }

                if(!_currentCamera.IsPreviewing)
                {
                    throw new Exception($"摄像机[{_currentCamera.Name}]当前不处于预览状态！");
                }

                // 默认截图路径
                string savePath = Path.Combine(Application.StartupPath, "ScreenShot", DateTime.Now.ToString("yyyy-MM-dd"));

                //先初始化图片存放路径
                if(!Directory.Exists(savePath))
                {
                    Directory.CreateDirectory(savePath);
                }

                //图片保存路径和文件名
                string picFileName = $"{savePath}\\{_currentCamera.Name}_{DateTime.Now:HH-mm-ss}.jpg";
                _currentCamera.ScreenShot().Save(picFileName, ImageFormat.Jpeg);

                string info = $"摄像机[{_currentCamera.Name}]截图成功，文件：{picFileName}";
                logView.Log(info);
                FpiMessageBox.ShowInfo(info);
            }
            catch(Exception ex)
            {
                var info = $"截图出错：{ex.Message}";
                logView.Log(info);
                FpiMessageBox.ShowInfo(info);
            }
        }

        private string _videoFileName;

        /// <summary>
        /// 录像
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRecord_Click(object sender, EventArgs e)
        {
            try
            {
                if(_currentCamera == null)
                {
                    throw new Exception("当前无有效摄像机！");
                }

                //if(!_currentCamera.IsPreviewing)
                //{
                //    throw new Exception($"摄像机[{_currentCamera.Name}]当前不处于预览状态！");
                //}

                //if(_currentCamera.VideoWriter == null)
                //{
                //    _currentCamera.VideoWriter = new VideoFileWriter();
                //}

                //// 开始录像
                //if(!_currentCamera.VideoWriter.IsOpen)
                //{
                //    // 默认录屏路径
                //    string savePath = Path.Combine(Application.StartupPath, "VideoFile", DateTime.Now.ToString("yyyy-MM-dd"));

                //    //先初始化录屏文件存放路径
                //    if(!Directory.Exists(savePath))
                //    {
                //        Directory.CreateDirectory(savePath);
                //    }

                //    //录像保存路径和文件名
                //    _videoFileName = $"{savePath}\\{_currentCamera.Name}_{DateTime.Now:HH-mm-ss}.mp4";

                //    // 设置视频参数（需与摄像机分辨率匹配）
                //    int width = _currentCamera.VideoSource.VideoResolution.FrameSize.Width;
                //    int height = _currentCamera.VideoSource.VideoResolution.FrameSize.Height;
                //    int frameRate = _currentCamera.VideoSource.VideoResolution.AverageFrameRate;

                //    //if(width > 1280 || frameRate > 30)
                //    //{
                //    //    frameRate = frameRate / 3;
                //    //}
                //    // 高帧率下视频时长被压缩，暂时写死使用低帧率
                //    frameRate = 10;

                //    _currentCamera.VideoWriter.Open(_videoFileName, width, height, frameRate, VideoCodec.MPEG4);

                //    _currentCamera.IsRecording = true;
                //    btnRecord.Text = "停止录像";
                //    pnlCameraList.Enabled = false;

                //    string info = $"摄像机[{_currentCamera.Name}]开始录制录像！";
                //    logView.Log(info);
                //    FpiMessageBox.ShowInfo(info);
                //}
                //// 停止录像
                //else
                //{
                //    _currentCamera.VideoWriter.Close();

                //    btnRecord.Text = "开始录像";
                //    pnlCameraList.Enabled = true;

                //    string info = $"摄像机[{_currentCamera.Name}]保存录像成功，录像文件名：{_videoFileName}";
                //    logView.Log(info);
                //    FpiMessageBox.ShowInfo(info);
                //}
            }
            catch(Exception ex)
            {
                var info = $"录像出错：{ex.Message}";
                logView.Log(info);
                FpiMessageBox.ShowInfo(info);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 摄像机列表绑定到下拉框控件
        /// </summary>
        private void BindingCameraListToCombox()
        {
            cmbCameraList.Items.Clear();

            foreach(var usbCamera in USBCameraManager.GetInstance().CameraList)
            {
                cmbCameraList.Items.Add(usbCamera);
            }

            this.cmbCameraList.SelectedIndexChanged -= cmbCameraList_SelectedIndexChanged;
            this.cmbCameraList.SelectedIndexChanged += cmbCameraList_SelectedIndexChanged;
        }

        /// <summary>
        /// 清理界面预览资源
        /// </summary>
        private void StopUIPreview()
        {
            videoSourcePlayer.SignalToStop();
            videoSourcePlayer.WaitForStop();
            videoSourcePlayer.Stop();
            videoSourcePlayer.VideoSource = null;

            // 关闭当前预览
            if(_currentCamera != null)
            {
                _currentCamera.StopPreview();
            }
        }

        /// <summary>
        /// 加载界面预览资源
        /// </summary>
        private void StartUIPreview()
        {
            _currentCamera.StartPreview();

            videoSourcePlayer.VideoSource = _currentCamera.VideoSource;
            videoSourcePlayer.Start();
        }

        #endregion
    }
}