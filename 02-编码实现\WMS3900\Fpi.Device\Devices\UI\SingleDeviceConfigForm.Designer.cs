﻿namespace Fpi.Devices.UI
{
    partial class SingleDeviceConfigForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SingleDeviceConfigForm));
            this.pnlDeciveConfig = new System.Windows.Forms.Panel();
            this.deviceConfigPanel = new Fpi.Devices.UI.DeviceConfigPanel();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnSure = new System.Windows.Forms.Button();
            this.pnlDeciveConfig.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlDeciveConfig
            // 
            this.pnlDeciveConfig.Controls.Add(this.deviceConfigPanel);
            this.pnlDeciveConfig.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlDeciveConfig.Location = new System.Drawing.Point(0, 0);
            this.pnlDeciveConfig.Name = "pnlDeciveConfig";
            this.pnlDeciveConfig.Size = new System.Drawing.Size(479, 553);
            this.pnlDeciveConfig.TabIndex = 0;
            // 
            // deviceConfigPanel
            // 
            this.deviceConfigPanel.Device = null;
            this.deviceConfigPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.deviceConfigPanel.Location = new System.Drawing.Point(0, 0);
            this.deviceConfigPanel.Name = "deviceConfigPanel";
            this.deviceConfigPanel.Size = new System.Drawing.Size(479, 517);
            this.deviceConfigPanel.TabIndex = 0;
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnCancel.Location = new System.Drawing.Point(395, 555);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 28);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnSure
            // 
            this.btnSure.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSure.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnSure.Location = new System.Drawing.Point(308, 555);
            this.btnSure.Name = "btnSure";
            this.btnSure.Size = new System.Drawing.Size(75, 28);
            this.btnSure.TabIndex = 2;
            this.btnSure.Text = "确定";
            this.btnSure.UseVisualStyleBackColor = true;
            this.btnSure.Click += new System.EventHandler(this.btnSure_Click);
            // 
            // SingleDeviceConfigForm
            // 
            this.AcceptButton = this.btnSure;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(479, 592);
            this.Controls.Add(this.btnSure);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.pnlDeciveConfig);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SingleDeviceConfigForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "设备参数配置";
            this.Load += new System.EventHandler(this.SingleDeviceConfigForm_Load);
            this.pnlDeciveConfig.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel pnlDeciveConfig;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnSure;
        private DeviceConfigPanel deviceConfigPanel;
    }
}