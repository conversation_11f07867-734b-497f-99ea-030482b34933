﻿using System.Data;

namespace Fpi.DB
{
    /// <summary>
    /// 数据库连接创建的接口
    /// </summary>
    public interface IDbFactory
    {
        /// <summary>
        /// 连接字符串
        /// </summary>
        string ConnectString { get; }

        /// <summary>
        /// 建立默认连接
        /// </summary>
        /// <returns>数据库连接</returns>
        IDbConnection CreateConnection();

        /// <summary>
        /// 建立DataAdapter对象
        /// </summary>
        /// <returns>DataAdapter对象</returns>
        IDbDataAdapter CreateDataAdapter();

        /// <summary>
        /// 建立Command的参数
        /// </summary>
        /// <returns>Command的参数</returns>
        IDataParameter CreateDataParameter();

        /// <summary>
        /// 创建数据库
        /// </summary>
        /// <param name="dbName">数据库名称</param>
        /// <returns></returns>
        bool CreateDb(string dbName);
    }
}
