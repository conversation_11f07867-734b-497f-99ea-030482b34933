﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Edit" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Edit.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Remove" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Remove.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Add" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Add.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AlarmInfo" xml:space="preserve">
    <value>时间:{0} 报警:{1}</value>
  </data>
  <data name="AlarmOutDate" xml:space="preserve">
    <value>报警过期</value>
  </data>
  <data name="DeleteAlarmCode" xml:space="preserve">
    <value>确定删除报警码:{0}?</value>
  </data>
  <data name="DeleteAlarmGrade" xml:space="preserve">
    <value>确定删除报警等级:{0}?</value>
  </data>
  <data name="ExistAlarmCode" xml:space="preserve">
    <value>已存在编号为 {0} 的报警码</value>
  </data>
  <data name="ExistAlarmGrade" xml:space="preserve">
    <value>已存在编号为 {0} 的报警等级</value>
  </data>
  <data name="SystemExit" xml:space="preserve">
    <value>系统退出</value>
  </data>
  <data name="SystemPrompt" xml:space="preserve">
    <value>系统提示</value>
  </data>
  <data name="AlarmCodeEmpty" xml:space="preserve">
    <value>报警码不可为空</value>
  </data>
  <data name="AlarmGradeCodeEmpty" xml:space="preserve">
    <value>报警等级编号与命名不可为空</value>
  </data>
  <data name="AlarmGradeEmpty" xml:space="preserve">
    <value>报警等级不可为空</value>
  </data>
  <data name="AlarmNameEmpty" xml:space="preserve">
    <value>报警名称不可为空</value>
  </data>
  <data name="AlarmProcess" xml:space="preserve">
    <value>处置后的报警将转为历史报警，是否确定处置该报警?</value>
  </data>
  <data name="AlarmRemoveTime" xml:space="preserve">
    <value>报警消除时间</value>
  </data>
  <data name="AlarmRefresh" xml:space="preserve">
    <value>已刷新当前报警</value>
  </data>
  <data name="AlarmUpdateTime" xml:space="preserve">
    <value>最近报警时间</value>
  </data>
  <data name="BeginEndTimeError" xml:space="preserve">
    <value>起止时间错误</value>
  </data>
  <data name="FirstAlarmTime" xml:space="preserve">
    <value>首次报警时间</value>
  </data>
  <data name="SelectAlarmCode" xml:space="preserve">
    <value>请选择报警码</value>
  </data>
  <data name="SelectAlarmGrade" xml:space="preserve">
    <value>请选择报警等级</value>
  </data>
  <data name="SelectAlarmSource" xml:space="preserve">
    <value>请输入报警源</value>
  </data>
  <data name="SelectQueryTime" xml:space="preserve">
    <value>请选择查询时间类型</value>
  </data>
  <data name="DeleteAlarmSource" xml:space="preserve">
    <value>确定删除报警源:{0}?</value>
  </data>
  <data name="ExistAlarmSource" xml:space="preserve">
    <value>已存在编号为 {0} 的报警源</value>
  </data>
  <data name="QueryAlarmError" xml:space="preserve">
    <value>查询历史报警出错:{0}</value>
  </data>
  <data name="AlarmSaveToDBError" xml:space="preserve">
    <value>报警信息保存到数据库发生异常</value>
  </data>
  <data name="CreateAlarmDBError" xml:space="preserve">
    <value>创建报警数据库异常</value>
  </data>
  <data name="CreateAlarmTableError" xml:space="preserve">
    <value>创建报警信息数据表异常</value>
  </data>
  <data name="AlarmGradeUsed" xml:space="preserve">
    <value>该报警等级被引用,不能被删除</value>
  </data>
  <data name="AlarmSourceUsed" xml:space="preserve">
    <value>该报警源被引用,不能被删除</value>
  </data>
  <data name="AddAlarmTime" xml:space="preserve">
    <value>报警产生时间</value>
  </data>
  <data name="DeleteAlarmGroup" xml:space="preserve">
    <value>确定删除报警组:{0}?</value>
  </data>
  <data name="ExistAlarmGroup" xml:space="preserve">
    <value>已存在编号为 {0} 的报警组</value>
  </data>
</root>