﻿using System.Threading;
using Fpi.DB.Manager;
using Fpi.Util.Interfaces.Initialize;

namespace Fpi.DB
{
    public class KillSleepConnections : IInitialization
    {
        private static object syncObj = new object();
        private static KillSleepConnections instance = null;
        public static KillSleepConnections GetInstance()
        {
            lock(syncObj)
            {
                if(instance == null)
                {
                    instance = new KillSleepConnections();
                }
            }
            return instance;
        }

        private Timer timerKill = null;

        private void KillFunc(object obj)
        {
            try
            {
                DbAccess.KillSleepingConnections(200);
            }
            catch
            {
            }
        }

        #region IInitialization 成员

        public void Initialize()
        {
            // mysql才需要清理连接
            if(DataBaseManager.GetInstance().DataBaseType == DBType.odbc.ToString() || DataBaseManager.GetInstance().DataBaseType == DBType.mysql.ToString())
            {
                timerKill = new Timer(new TimerCallback(KillFunc), null, Timeout.Infinite, Timeout.Infinite);
                timerKill.Change(60000, 30000);
            }
        }

        #endregion
    }

}
