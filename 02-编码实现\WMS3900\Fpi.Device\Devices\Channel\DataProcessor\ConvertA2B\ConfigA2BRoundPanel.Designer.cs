﻿namespace Fpi.Devices.Channel
{
    partial class ConfigA2BRoundPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.nuMaxDigital = new System.Windows.Forms.NumericUpDown();
            this.nuMinDigital = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.nuMaxSignal = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.nuMinSignal = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.txtRound = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxDigital)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinDigital)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxSignal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinSignal)).BeginInit();
            this.SuspendLayout();
            // 
            // nuMaxDigital
            // 
            this.nuMaxDigital.DecimalPlaces = 2;
            this.nuMaxDigital.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMaxDigital.Location = new System.Drawing.Point(201, 33);
            this.nuMaxDigital.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMaxDigital.Minimum = new decimal(new int[] {
            9999999,
            0,
            0,
            -2147483648});
            this.nuMaxDigital.Name = "nuMaxDigital";
            this.nuMaxDigital.Size = new System.Drawing.Size(62, 21);
            this.nuMaxDigital.TabIndex = 20;
            // 
            // nuMinDigital
            // 
            this.nuMinDigital.DecimalPlaces = 2;
            this.nuMinDigital.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMinDigital.Location = new System.Drawing.Point(67, 33);
            this.nuMinDigital.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMinDigital.Minimum = new decimal(new int[] {
            9999999,
            0,
            0,
            -2147483648});
            this.nuMinDigital.Name = "nuMinDigital";
            this.nuMinDigital.Size = new System.Drawing.Size(67, 21);
            this.nuMinDigital.TabIndex = 21;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label5.Location = new System.Drawing.Point(8, 12);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 15;
            this.label5.Text = "最小信号";
            // 
            // nuMaxSignal
            // 
            this.nuMaxSignal.DecimalPlaces = 2;
            this.nuMaxSignal.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMaxSignal.Location = new System.Drawing.Point(201, 8);
            this.nuMaxSignal.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMaxSignal.Name = "nuMaxSignal";
            this.nuMaxSignal.Size = new System.Drawing.Size(62, 21);
            this.nuMaxSignal.TabIndex = 22;
            this.nuMaxSignal.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label6.Location = new System.Drawing.Point(142, 12);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 12);
            this.label6.TabIndex = 14;
            this.label6.Text = "最大信号";
            // 
            // nuMinSignal
            // 
            this.nuMinSignal.DecimalPlaces = 2;
            this.nuMinSignal.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.nuMinSignal.Location = new System.Drawing.Point(67, 8);
            this.nuMinSignal.Maximum = new decimal(new int[] {
            9999999,
            0,
            0,
            0});
            this.nuMinSignal.Name = "nuMinSignal";
            this.nuMinSignal.Size = new System.Drawing.Size(67, 21);
            this.nuMinSignal.TabIndex = 23;
            this.nuMinSignal.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label7.Location = new System.Drawing.Point(8, 37);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(53, 12);
            this.label7.TabIndex = 17;
            this.label7.Text = "最小数值";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label8.Location = new System.Drawing.Point(142, 37);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(53, 12);
            this.label8.TabIndex = 16;
            this.label8.Text = "最大数值";
            // 
            // txtRound
            // 
            this.txtRound.BackColor = System.Drawing.SystemColors.Window;
            this.txtRound.CanEmpty = false;
            this.txtRound.DigitLength = 0;
            this.txtRound.InputType = Fpi.UI.Common.PC.Controls.TextInputType.UInt;
            this.txtRound.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtRound.IsValidCheck = true;
            this.txtRound.Label = "保留小数位数";
            this.txtRound.Location = new System.Drawing.Point(352, 9);
            this.txtRound.MaxValue = null;
            this.txtRound.MinValue = null;
            this.txtRound.Name = "txtRound";
            this.txtRound.Size = new System.Drawing.Size(33, 21);
            this.txtRound.TabIndex = 24;
            this.txtRound.Text = "1";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.label1.Location = new System.Drawing.Point(269, 12);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 25;
            this.label1.Text = "截取小数位数";
            // 
            // ConfigA2BRoundPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.txtRound);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.nuMaxDigital);
            this.Controls.Add(this.nuMinDigital);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.nuMaxSignal);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.nuMinSignal);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label8);
            this.Name = "ConfigA2BRoundPanel";
            this.Size = new System.Drawing.Size(398, 64);
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxDigital)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinDigital)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMaxSignal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuMinSignal)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.NumericUpDown nuMaxDigital;
        private System.Windows.Forms.NumericUpDown nuMinDigital;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown nuMaxSignal;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown nuMinSignal;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtRound;
        private System.Windows.Forms.Label label1;


    }
}
