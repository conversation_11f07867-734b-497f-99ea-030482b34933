﻿using System.Windows.Forms;

namespace Fpi.Devices.Channel
{
    public partial class ConfigA2BPanel : UserControl, IParamConfig
    {
        public ConfigA2BPanel()
        {
            InitializeComponent();
        }

        #region IParamConfig 成员

        public string SaveConfig()
        {
            return nuMinSignal.Value.ToString() + "," +
                   nuMaxSignal.Value.ToString() + "," +
                   nuMinDigital.Value.ToString() + "," +
                   nuMaxDigital.Value.ToString();
        }

        public bool CheakParam()
        {
            return nuMaxSignal.Value > nuMinSignal.Value &&
                nuMaxDigital.Value > nuMinDigital.Value;
        }

        public void ShowParam(string param)
        {
            string[] paramStrs = param.Split(',');

            if(paramStrs.Length == 4)
            {
                this.nuMinSignal.Value = decimal.Parse(paramStrs[0]);
                this.nuMaxSignal.Value = decimal.Parse(paramStrs[1]);

                this.nuMinDigital.Value = decimal.Parse(paramStrs[2]);
                this.nuMaxDigital.Value = decimal.Parse(paramStrs[3]);
            }
        }

        #endregion
    }
}
