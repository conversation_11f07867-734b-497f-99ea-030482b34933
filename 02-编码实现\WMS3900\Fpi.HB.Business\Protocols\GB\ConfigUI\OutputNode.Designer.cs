﻿namespace Fpi.HB.Business.Protocols.GB.ConfigUI
{
    partial class OutputNode
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ctxMenuVarNode = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.menuUp = new System.Windows.Forms.ToolStripMenuItem();
            this.menuDown = new System.Windows.Forms.ToolStripMenuItem();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.btnAddVarNode = new System.Windows.Forms.Button();
            this.btnRemoveVarNode = new System.Windows.Forms.Button();
            this.lsbAllVarNodes = new System.Windows.Forms.ListBox();
            this.lsvUsedVarNodes = new System.Windows.Forms.ListView();
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.btnAddAllVarNode = new System.Windows.Forms.Button();
            this.btnRemoveAllVarNode = new System.Windows.Forms.Button();
            this.pnlFunc = new System.Windows.Forms.Panel();
            this.label4 = new System.Windows.Forms.Label();
            this.cmbType = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cmbDec = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnSet = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.cmbUnit = new System.Windows.Forms.ComboBox();
            this.txtGB = new System.Windows.Forms.TextBox();
            this.panel2 = new System.Windows.Forms.Panel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.ctxMenuVarNode.SuspendLayout();
            this.pnlFunc.SuspendLayout();
            this.panel2.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // ctxMenuVarNode
            // 
            this.ctxMenuVarNode.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.menuUp,
            this.menuDown});
            this.ctxMenuVarNode.Name = "ctxMenu";
            this.ctxMenuVarNode.Size = new System.Drawing.Size(137, 48);
            // 
            // menuUp
            // 
            this.menuUp.Name = "menuUp";
            this.menuUp.Size = new System.Drawing.Size(136, 22);
            this.menuUp.Text = "向上移动(&U)";
            this.menuUp.Click += new System.EventHandler(this.menuUp_Click);
            // 
            // menuDown
            // 
            this.menuDown.Name = "menuDown";
            this.menuDown.Size = new System.Drawing.Size(136, 22);
            this.menuDown.Text = "向下移动(&D)";
            this.menuDown.Click += new System.EventHandler(this.menuDown_Click);
            // 
            // btnAddVarNode
            // 
            this.btnAddVarNode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAddVarNode.ForeColor = System.Drawing.SystemColors.ControlText;
            this.btnAddVarNode.Location = new System.Drawing.Point(159, 120);
            this.btnAddVarNode.Name = "btnAddVarNode";
            this.btnAddVarNode.Size = new System.Drawing.Size(50, 23);
            this.btnAddVarNode.TabIndex = 3;
            this.btnAddVarNode.Text = ">";
            this.toolTip1.SetToolTip(this.btnAddVarNode, "增加输出项");
            this.btnAddVarNode.UseVisualStyleBackColor = true;
            this.btnAddVarNode.Click += new System.EventHandler(this.btnAddVarNode_Click);
            // 
            // btnRemoveVarNode
            // 
            this.btnRemoveVarNode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRemoveVarNode.ForeColor = System.Drawing.SystemColors.ControlText;
            this.btnRemoveVarNode.Location = new System.Drawing.Point(159, 149);
            this.btnRemoveVarNode.Name = "btnRemoveVarNode";
            this.btnRemoveVarNode.Size = new System.Drawing.Size(50, 23);
            this.btnRemoveVarNode.TabIndex = 3;
            this.btnRemoveVarNode.Text = "<";
            this.toolTip1.SetToolTip(this.btnRemoveVarNode, "移除输出项");
            this.btnRemoveVarNode.UseVisualStyleBackColor = true;
            this.btnRemoveVarNode.Click += new System.EventHandler(this.btnRemoveVarNode_Click);
            // 
            // lsbAllVarNodes
            // 
            this.lsbAllVarNodes.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lsbAllVarNodes.FormattingEnabled = true;
            this.lsbAllVarNodes.HorizontalScrollbar = true;
            this.lsbAllVarNodes.ItemHeight = 12;
            this.lsbAllVarNodes.Location = new System.Drawing.Point(3, 17);
            this.lsbAllVarNodes.Name = "lsbAllVarNodes";
            this.lsbAllVarNodes.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.lsbAllVarNodes.Size = new System.Drawing.Size(135, 361);
            this.lsbAllVarNodes.TabIndex = 1;
            this.toolTip1.SetToolTip(this.lsbAllVarNodes, "污染物列表");
            // 
            // lsvUsedVarNodes
            // 
            this.lsvUsedVarNodes.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader2,
            this.columnHeader4,
            this.columnHeader5,
            this.columnHeader3,
            this.columnHeader1});
            this.lsvUsedVarNodes.ContextMenuStrip = this.ctxMenuVarNode;
            this.lsvUsedVarNodes.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lsvUsedVarNodes.FullRowSelect = true;
            this.lsvUsedVarNodes.GridLines = true;
            this.lsvUsedVarNodes.HeaderStyle = System.Windows.Forms.ColumnHeaderStyle.Nonclickable;
            this.lsvUsedVarNodes.Location = new System.Drawing.Point(3, 17);
            this.lsvUsedVarNodes.MultiSelect = false;
            this.lsvUsedVarNodes.Name = "lsvUsedVarNodes";
            this.lsvUsedVarNodes.Size = new System.Drawing.Size(377, 361);
            this.lsvUsedVarNodes.TabIndex = 2;
            this.toolTip1.SetToolTip(this.lsvUsedVarNodes, "输出污染物列表");
            this.lsvUsedVarNodes.UseCompatibleStateImageBehavior = false;
            this.lsvUsedVarNodes.View = System.Windows.Forms.View.Details;
            this.lsvUsedVarNodes.SelectedIndexChanged += new System.EventHandler(this.lsvUsedVarNodes_SelectedIndexChanged);
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "污染物";
            this.columnHeader2.Width = 124;
            // 
            // columnHeader4
            // 
            this.columnHeader4.Text = "输出编码";
            this.columnHeader4.Width = 65;
            // 
            // columnHeader5
            // 
            this.columnHeader5.Text = "数据类型";
            this.columnHeader5.Width = 70;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "单位";
            this.columnHeader3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.columnHeader3.Width = 62;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "小数位";
            this.columnHeader1.Width = 51;
            // 
            // btnAddAllVarNode
            // 
            this.btnAddAllVarNode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAddAllVarNode.ForeColor = System.Drawing.SystemColors.ControlText;
            this.btnAddAllVarNode.Location = new System.Drawing.Point(159, 187);
            this.btnAddAllVarNode.Name = "btnAddAllVarNode";
            this.btnAddAllVarNode.Size = new System.Drawing.Size(50, 23);
            this.btnAddAllVarNode.TabIndex = 6;
            this.btnAddAllVarNode.Text = ">>";
            this.toolTip1.SetToolTip(this.btnAddAllVarNode, "增加所有输出项");
            this.btnAddAllVarNode.UseVisualStyleBackColor = true;
            this.btnAddAllVarNode.Click += new System.EventHandler(this.btnAddAllVarNode_Click);
            // 
            // btnRemoveAllVarNode
            // 
            this.btnRemoveAllVarNode.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRemoveAllVarNode.ForeColor = System.Drawing.SystemColors.ControlText;
            this.btnRemoveAllVarNode.Location = new System.Drawing.Point(159, 216);
            this.btnRemoveAllVarNode.Name = "btnRemoveAllVarNode";
            this.btnRemoveAllVarNode.Size = new System.Drawing.Size(50, 23);
            this.btnRemoveAllVarNode.TabIndex = 5;
            this.btnRemoveAllVarNode.Text = "<<";
            this.toolTip1.SetToolTip(this.btnRemoveAllVarNode, "移除所有输出项");
            this.btnRemoveAllVarNode.UseVisualStyleBackColor = true;
            this.btnRemoveAllVarNode.Click += new System.EventHandler(this.btnRemoveAllVarNode_Click);
            // 
            // pnlFunc
            // 
            this.pnlFunc.Controls.Add(this.label4);
            this.pnlFunc.Controls.Add(this.cmbType);
            this.pnlFunc.Controls.Add(this.label1);
            this.pnlFunc.Controls.Add(this.cmbDec);
            this.pnlFunc.Controls.Add(this.label3);
            this.pnlFunc.Controls.Add(this.btnSet);
            this.pnlFunc.Controls.Add(this.label2);
            this.pnlFunc.Controls.Add(this.cmbUnit);
            this.pnlFunc.Controls.Add(this.txtGB);
            this.pnlFunc.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlFunc.Enabled = false;
            this.pnlFunc.Location = new System.Drawing.Point(0, 391);
            this.pnlFunc.Name = "pnlFunc";
            this.pnlFunc.Size = new System.Drawing.Size(598, 39);
            this.pnlFunc.TabIndex = 6;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(293, 13);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "单位";
            // 
            // cmbType
            // 
            this.cmbType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbType.FormattingEnabled = true;
            this.cmbType.Items.AddRange(new object[] {
            "实时数据",
            "折算数据"});
            this.cmbType.Location = new System.Drawing.Point(205, 8);
            this.cmbType.Name = "cmbType";
            this.cmbType.Size = new System.Drawing.Size(80, 20);
            this.cmbType.TabIndex = 9;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(414, 13);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 6;
            this.label1.Text = "小数位";
            // 
            // cmbDec
            // 
            this.cmbDec.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDec.FormattingEnabled = true;
            this.cmbDec.Items.AddRange(new object[] {
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10"});
            this.cmbDec.Location = new System.Drawing.Point(461, 8);
            this.cmbDec.Name = "cmbDec";
            this.cmbDec.Size = new System.Drawing.Size(57, 20);
            this.cmbDec.TabIndex = 7;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(146, 13);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "数据类型";
            // 
            // btnSet
            // 
            this.btnSet.Location = new System.Drawing.Point(537, 7);
            this.btnSet.Name = "btnSet";
            this.btnSet.Size = new System.Drawing.Size(51, 23);
            this.btnSet.TabIndex = 5;
            this.btnSet.Text = "修改";
            this.btnSet.UseVisualStyleBackColor = true;
            this.btnSet.Click += new System.EventHandler(this.btnSet_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(23, 13);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "输出编码";
            // 
            // cmbUnit
            // 
            this.cmbUnit.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbUnit.FormattingEnabled = true;
            this.cmbUnit.Location = new System.Drawing.Point(328, 8);
            this.cmbUnit.Name = "cmbUnit";
            this.cmbUnit.Size = new System.Drawing.Size(80, 20);
            this.cmbUnit.TabIndex = 2;
            // 
            // txtGB
            // 
            this.txtGB.Location = new System.Drawing.Point(81, 8);
            this.txtGB.Name = "txtGB";
            this.txtGB.Size = new System.Drawing.Size(59, 21);
            this.txtGB.TabIndex = 1;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.btnAddAllVarNode);
            this.panel2.Controls.Add(this.btnRemoveAllVarNode);
            this.panel2.Controls.Add(this.groupBox2);
            this.panel2.Controls.Add(this.groupBox1);
            this.panel2.Controls.Add(this.btnAddVarNode);
            this.panel2.Controls.Add(this.btnRemoveVarNode);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(0, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(598, 430);
            this.panel2.TabIndex = 7;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.lsvUsedVarNodes);
            this.groupBox2.Location = new System.Drawing.Point(215, 4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(383, 381);
            this.groupBox2.TabIndex = 4;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "输出污染物列表";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.lsbAllVarNodes);
            this.groupBox1.Location = new System.Drawing.Point(12, 4);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(141, 381);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "监测因子";
            // 
            // OutputNode
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pnlFunc);
            this.Controls.Add(this.panel2);
            this.Name = "OutputNode";
            this.Size = new System.Drawing.Size(598, 430);
            this.Load += new System.EventHandler(this.OutputNode_Load);
            this.ctxMenuVarNode.ResumeLayout(false);
            this.pnlFunc.ResumeLayout(false);
            this.pnlFunc.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenuVarNode;
        private System.Windows.Forms.ToolStripMenuItem menuUp;
        private System.Windows.Forms.ToolStripMenuItem menuDown;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Panel pnlFunc;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btnSet;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cmbUnit;
        private System.Windows.Forms.TextBox txtGB;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btnAddVarNode;
        private System.Windows.Forms.Button btnRemoveVarNode;
        private System.Windows.Forms.ListBox lsbAllVarNodes;
        private System.Windows.Forms.ListView lsvUsedVarNodes;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.Button btnAddAllVarNode;
        private System.Windows.Forms.Button btnRemoveAllVarNode;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox cmbType;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cmbDec;




    }
}
