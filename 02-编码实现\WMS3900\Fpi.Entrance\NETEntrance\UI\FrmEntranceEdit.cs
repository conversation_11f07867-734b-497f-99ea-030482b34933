﻿using System;
using System.Net;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Sunny.UI;

namespace Fpi.Entrance
{
    public partial class FrmEntranceEdit : UIForm
    {
        #region 字段属性

        /// 是否是编辑模式
        /// </summary>
        private bool _editModel { get; set; }

        /// <summary>
        /// 对应门禁
        /// </summary>
        public BaseNETEntrance Entrance { get; set; }

        #endregion

        #region 构造

        public FrmEntranceEdit()
        {
            InitializeComponent();
            EnumOperate.BandEnumToCmb(cmbType, typeof(eEntranceType));
            cmbType.SelectedIndex = 0;
        }

        public FrmEntranceEdit(BaseNETEntrance entrance) : this()
        {
            _editModel = true;
            this.Entrance = entrance;
            if(this.Entrance is YSEntrance)
            {
                this.cmbType.SelectedValue = 0;
            }
        }

        #endregion

        #region 事件

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                Entrance.id = txtID.Text;
                Entrance.name = txtName.Text;

                Entrance.Ip = txtIP.Text;
                Entrance.Port = txtPort.Text;
                Entrance.User = txtUser.Text;
                Entrance.Pwd = txtPassWord.Text;
                Entrance.Description = txtDesc.Text;

                Entrance.IsReceiceEnable = SwitchEnable.Active;
                int.TryParse(txtReceivePort.Text, out Entrance.ReceivePort);

                DialogResult = DialogResult.OK;

                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        private void FrmEntranceEdit_Load(object sender, EventArgs e)
        {
            cmbType.Enabled = txtID.Enabled = !_editModel;

            if(Entrance == null)
            {
                Entrance = new YSEntrance();
            }

            LoadConfig();

            this.cmbType.SelectedIndexChanged += this.cmbType_SelectedIndexChanged;
        }

        private void cmbType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(!_editModel)
            {
                switch(cmbType.SelectedValue)
                {
                    case 0:
                        if(Entrance is not YSEntrance)
                        {
                            Entrance = new YSEntrance();
                        }
                        break;
                }

                LoadConfig();
            }
        }

        private void SwitchEnable_ValueChanged(object sender, bool value)
        {
            txtReceivePort.Enabled = value;
        }

        #endregion

        #region 私有方法

        private void LoadConfig()
        {
            if(Entrance != null)
            {
                txtID.Text = Entrance.id;
                txtName.Text = Entrance.name;
                txtIP.Text = Entrance.Ip;
                txtPort.Text = Entrance.Port;
                txtUser.Text = Entrance.User;
                txtPassWord.Text = Entrance.Pwd;
                txtDesc.Text = Entrance.Description;

                txtReceivePort.Text = Entrance.ReceivePort.ToString();
                SwitchEnable.Active = Entrance.IsReceiceEnable;

                txtID.Select();
            }
        }

        /// <summary>
        /// 检查输入参数的合法性
        /// </summary>
        private void Check()
        {
            if(string.IsNullOrEmpty(cmbType.Text))
            {
                throw new Exception("门禁类型不能为空！");
            }
            if(string.IsNullOrEmpty(txtID.Text))
            {
                throw new Exception("门禁ID不能为空！");
            }
            if(string.IsNullOrEmpty(txtName.Text))
            {
                throw new Exception("门禁名称不能为空！");
            }
            if(string.IsNullOrEmpty(txtIP.Text))
            {
                throw new Exception("门禁IP不能为空！");
            }
            if(!IPAddress.TryParse(txtIP.Text, out _))
            {
                throw new Exception("门禁IP格式错误！");
            }
            if(!uint.TryParse(txtPort.Text, out uint port) || port < 1 || port > 65535)
            {
                throw new Exception("门禁端口号不可为空，且端口号应为1-65535范围内的整数！");
            }
            if(string.IsNullOrEmpty(txtUser.Text))
            {
                throw new Exception("用户名不能为空！");
            }
            if(string.IsNullOrEmpty(txtPassWord.Text))
            {
                throw new Exception("密码不能为空！");
            }

            // 新增设备，校验ID是否重复
            if(!_editModel && EntranceManager.GetInstance().GetEntranceById(txtID.Text) != null)
            {
                throw new Exception("此门禁ID已存在，请选择其他ID新建门禁！");
            }
        }

        #endregion  
    }
}