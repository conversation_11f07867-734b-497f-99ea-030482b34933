{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication", "ProjectFileName": "Fpi.Communication.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Communication\\Buses\\HttpWebRequestBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_HttpWebRequestBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_HttpWebRequestBus.Designer.cs"}, {"SourceFile": "Communication\\UI\\PC\\PipeConfig\\FrmPipeManager.cs"}, {"SourceFile": "Communication\\UI\\PC\\PipeConfig\\FrmPipeManager.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Communication\\Buses\\BusHelper.cs"}, {"SourceFile": "Communication\\Buses\\GprsBuses\\GprsUDPServerBus.cs"}, {"SourceFile": "Communication\\Buses\\GprsBuses\\GprsUserInfo.cs"}, {"SourceFile": "Communication\\Buses\\GprsBuses\\HDAPIWapper.cs"}, {"SourceFile": "Communication\\Buses\\TcpServers\\ClientKeyPacket.cs"}, {"SourceFile": "Communication\\Buses\\SPCommBus.cs"}, {"SourceFile": "Communication\\Buses\\TcpClientSocket.cs"}, {"SourceFile": "Communication\\Buses\\CommBus.cs"}, {"SourceFile": "Communication\\Buses\\BusLogHelper.cs"}, {"SourceFile": "Communication\\Buses\\TcpServers\\FpiClientKeyPacket.cs"}, {"SourceFile": "Communication\\Buses\\TcpServers\\NetTcpServer.cs"}, {"SourceFile": "Communication\\Buses\\UdpBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_UdpBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_UdpBus.Designer.cs"}, {"SourceFile": "Communication\\Converter\\BracketDataConverter.cs"}, {"SourceFile": "Communication\\Converter\\DataConverter.cs"}, {"SourceFile": "Communication\\Converter\\IDataConvertable.cs"}, {"SourceFile": "Communication\\Converter\\UnReverseDataConverter.cs"}, {"SourceFile": "Communication\\CQueue.cs"}, {"SourceFile": "Communication\\Crc\\Crc8.cs"}, {"SourceFile": "Communication\\Crc\\Crc16_Old.cs"}, {"SourceFile": "Communication\\Exceptions\\CommunicationParamException.cs"}, {"SourceFile": "Communication\\Interfaces\\IBus.cs"}, {"SourceFile": "Communication\\Buses\\BaseBus.cs"}, {"SourceFile": "Communication\\Buses\\TcpServers\\TcpServer.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_ClientSocketBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_ClientSocketBus.Designer.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_CommBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_CommBus.Designer.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_ServerSocketBus.cs"}, {"SourceFile": "Communication\\Buses\\UI\\PC\\UC_ServerSocketBus.Designer.cs"}, {"SourceFile": "Communication\\Buses\\UsbBus.cs"}, {"SourceFile": "Communication\\ByteArrayWrap.cs"}, {"SourceFile": "Communication\\Commands\\Command.cs"}, {"SourceFile": "Communication\\Commands\\CommandExtendId.cs"}, {"SourceFile": "Communication\\Commands\\CommandPort.cs"}, {"SourceFile": "Communication\\Commands\\CommandResendKey.cs"}, {"SourceFile": "Communication\\Commands\\Config\\SpecCommandManager.cs"}, {"SourceFile": "Communication\\Commands\\Config\\WebCommandManager.cs"}, {"SourceFile": "Communication\\Commands\\SendCommand.cs"}, {"SourceFile": "Communication\\Commands\\RecvCommand.cs"}, {"SourceFile": "Communication\\Crc\\Crc16.cs"}, {"SourceFile": "Communication\\Exceptions\\CommunicationException.cs"}, {"SourceFile": "Communication\\Commands\\Config\\CommandDesc.cs"}, {"SourceFile": "Communication\\Commands\\Config\\CommandExtend.cs"}, {"SourceFile": "Communication\\Commands\\Config\\CommandManager.cs"}, {"SourceFile": "Communication\\Commands\\Config\\Display.cs"}, {"SourceFile": "Communication\\Commands\\Config\\Param.cs"}, {"SourceFile": "Communication\\Commands\\ICommandListener.cs"}, {"SourceFile": "Communication\\Commands\\ParametersData.cs"}, {"SourceFile": "Communication\\Exceptions\\CommandException.cs"}, {"SourceFile": "Communication\\Exceptions\\CrcException.cs"}, {"SourceFile": "Communication\\Exceptions\\DataFormatException.cs"}, {"SourceFile": "Communication\\Exceptions\\DeviceException.cs"}, {"SourceFile": "Communication\\Exceptions\\PortException.cs"}, {"SourceFile": "Communication\\Exceptions\\WebException.cs"}, {"SourceFile": "Communication\\Exceptions\\CommandDelaySetException.cs"}, {"SourceFile": "Communication\\Interfaces\\ISupportPipe.cs"}, {"SourceFile": "Communication\\Config\\PipeLogHelper.cs"}, {"SourceFile": "Communication\\Interfaces\\IExceptionReceivable.cs"}, {"SourceFile": "Communication\\Interfaces\\IPortStatck.cs"}, {"SourceFile": "Communication\\Crc\\CT_CRC.cs"}, {"SourceFile": "Communication\\Ports\\FpiPorts\\GJDBSPort.cs"}, {"SourceFile": "Communication\\Ports\\PortLogHelper.cs"}, {"SourceFile": "Communication\\Ports\\ModBus\\ModBusPort.cs"}, {"SourceFile": "Communication\\Ports\\PortHelper.cs"}, {"SourceFile": "Communication\\Ports\\FpiPorts\\BracketPort.cs"}, {"SourceFile": "Communication\\Ports\\SyncPorts\\ResendKeys\\Fpi485ResendKey.cs"}, {"SourceFile": "Communication\\Ports\\FpiPorts\\FpiRouterPort.cs"}, {"SourceFile": "Communication\\Ports\\FpiPorts\\HJ212Port.cs"}, {"SourceFile": "Communication\\Ports\\FpiPorts\\InsertPort.cs"}, {"SourceFile": "Communication\\Ports\\FpiPorts\\UsbSpecPort.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\GroupingFrame.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\GroupingPort.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\ReceiverSlideWindow.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\ResendThread.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\SenderSlideWindow.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\SendState.cs"}, {"SourceFile": "Communication\\Ports\\Grouping\\SlideWindow.cs"}, {"SourceFile": "Communication\\Interfaces\\IConnector.cs"}, {"SourceFile": "Communication\\Interfaces\\IByteStream.cs"}, {"SourceFile": "Communication\\Interfaces\\IReceivable.cs"}, {"SourceFile": "Communication\\Commands\\CommandException.cs"}, {"SourceFile": "Communication\\Ports\\AsynPorts\\DataNode.cs"}, {"SourceFile": "Communication\\Ports\\AsynPorts\\AsynThread.cs"}, {"SourceFile": "Communication\\Ports\\AsynPorts\\AsynRecvPort.cs"}, {"SourceFile": "Communication\\Ports\\BasePort.cs"}, {"SourceFile": "Communication\\Ports\\BusPort.cs"}, {"SourceFile": "Communication\\Ports\\AsynPorts\\AsynSendPort.cs"}, {"SourceFile": "Communication\\Ports\\CommPorts\\SimplePort.cs"}, {"SourceFile": "Communication\\Interfaces\\IPort.cs"}, {"SourceFile": "Communication\\Interfaces\\IPortOwner.cs"}, {"SourceFile": "Communication\\Ports\\NumberPorts\\NumberData.cs"}, {"SourceFile": "Communication\\Ports\\NumberPorts\\NumberResendKey.cs"}, {"SourceFile": "Communication\\Ports\\NumberPorts\\NumberPort.cs"}, {"SourceFile": "Communication\\Interfaces\\IOvertime.cs"}, {"SourceFile": "Communication\\Ports\\SyncPorts\\TimeoutException.cs"}, {"SourceFile": "Communication\\Ports\\SyncPorts\\ResendKeys\\IResendKey.cs"}, {"SourceFile": "Communication\\Ports\\SyncPorts\\SyncPort.cs"}, {"SourceFile": "Communication\\Ports\\SyncPorts\\SyncSendNode.cs"}, {"SourceFile": "Communication\\Ports\\SyncPorts\\NumberSyncPort.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_GroupingPort.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_GroupingPort.Designer.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_SyncPort.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_SyncPort.Designer.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_FpiRouterPort.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_FpiRouterPort.Designer.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_CommandPort.cs"}, {"SourceFile": "Communication\\Ports\\UI\\PC\\UC_CommandPort.Designer.cs"}, {"SourceFile": "Communication\\Exceptions\\ProtocolException.cs"}, {"SourceFile": "Communication\\Protocols\\Common\\Simple\\SimpleParser.cs"}, {"SourceFile": "Communication\\Protocols\\Common\\Simple\\SimpleProtocol.cs"}, {"SourceFile": "Communication\\Protocols\\Interfaces\\IProtocolComponent.cs"}, {"SourceFile": "Communication\\Protocols\\Interfaces\\IRC_Informer.cs"}, {"SourceFile": "Communication\\Protocols\\Interfaces\\ISender.cs"}, {"SourceFile": "Communication\\Protocols\\ProtocolLogHelper.cs"}, {"SourceFile": "Communication\\Protocols\\Parser.cs"}, {"SourceFile": "Communication\\Protocols\\Protocol.cs"}, {"SourceFile": "Communication\\Protocols\\ProtocolComparer.cs"}, {"SourceFile": "Communication\\Protocols\\ProtocolComponent.cs"}, {"SourceFile": "Communication\\Protocols\\ProtocolDesc.cs"}, {"SourceFile": "Communication\\Protocols\\ProtocolHelper.cs"}, {"SourceFile": "Communication\\Protocols\\ProtocolManager.cs"}, {"SourceFile": "Communication\\Protocols\\Receiver.cs"}, {"SourceFile": "Communication\\Protocols\\RemoteProtocol.cs"}, {"SourceFile": "Communication\\Protocols\\Sender.cs"}, {"SourceFile": "Communication\\StringWrap.cs"}, {"SourceFile": "Communication\\UI\\PC\\CommandForms\\CommandExplore.cs"}, {"SourceFile": "Communication\\UI\\PC\\CommandForms\\CommandExplore.Designer.cs"}, {"SourceFile": "Communication\\UI\\PC\\CommandForms\\CommandPanel.cs"}, {"SourceFile": "Communication\\UI\\PC\\CommandForms\\CommandPanel.Designer.cs"}, {"SourceFile": "Communication\\UI\\PC\\CommandForms\\OneCommandPanel.cs"}, {"SourceFile": "Communication\\UI\\PC\\CommandForms\\OneCommandPanel.Designer.cs"}, {"SourceFile": "Communication\\TimeoutByteStream.cs"}, {"SourceFile": "Communication\\Ports\\Web\\WebPort.cs"}, {"SourceFile": "Communication\\Ports\\Web\\WebRecvCommand.cs"}, {"SourceFile": "Communication\\Ports\\Web\\WebSendCommand.cs"}, {"SourceFile": "Communication\\Config\\Pipe.cs"}, {"SourceFile": "Communication\\Config\\PortManager.cs"}, {"SourceFile": "Communication\\Config\\SendThread.cs"}, {"SourceFile": "Communication\\UI\\PC\\PipeConfig\\FrmPipeEdit.cs"}, {"SourceFile": "Communication\\UI\\PC\\PipeConfig\\FrmPipeEdit.Designer.cs"}, {"SourceFile": "Communication\\UI\\PC\\PipeConfig\\FrmPortEdit.cs"}, {"SourceFile": "Communication\\UI\\PC\\PipeConfig\\FrmPortEdit.Designer.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Instrument\\bin\\Debug\\Fpi.Instrument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Instrument\\bin\\Debug\\Fpi.Instrument.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ServiceProcess.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\WinFormsUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll", "OutputItemRelativePath": "Fpi.Communication.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}