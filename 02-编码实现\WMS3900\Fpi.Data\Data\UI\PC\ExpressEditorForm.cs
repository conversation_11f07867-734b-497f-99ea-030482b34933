﻿using System;
using System.Text;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Data.ExpParser;
using Fpi.UI.Common.PC;

namespace Fpi.Data.UI.PC
{
    /// <summary>
    /// 逻辑表达式编辑界面
    /// </summary>
    public partial class ExpressEditorForm : Form
    {
        private readonly ExpressionParse _ex = new ExpressionParse();

        /// <summary>
        /// 表达式串
        /// </summary>
        public string Expression { get; set; } = string.Empty;

        /// <summary>
        /// 关键字集合
        /// </summary>
        private readonly string[] _functions = { "IF", "CASE", "AND", "OR", "Not", "TRUE", "FALSE", "NowDate", "Len", "ToInt", "ToDouble", "ToDateTime", "ToString", "GetNodeValue", "GetNodeIntValue", "GetNodeBoolValue", "GetAllNodeBoolValueOR", "GetAlarmNodeBoolValue", "DoubleNaN", "IsAlarmNode", "IsNodeCycleValueAlarm", "Invoke" };

        /// <summary>
        /// 关键字描述集合
        /// </summary>
        private readonly string[] _descriptions = {
            "IF(JudgeExpression,FirstExpression,SecondExpression)\r\nJudgeExpression 为 true 返回 FirstExpression 否则 返回 SecondExpression",
            "CASE(JudgeExpression1,ResultExpression1,JudgeExpression2,ResultExpression2,...)\r\nJudgeExpression1 为 true 返回 ResultExpression1 以此类推",
            "AND(JudgeExpression,JudgeExpression,...)\r\n所有 JudgeExpression 表达式为 true 返回 true 否则 返回 false",
            "OR(JudgeExpression,JudgeExpression,...)\r\n一个 JudgeExpression 表达式为 true 返回 true 否则 返回 false",
            "Not(JudgeExpression)\r\nJudgeExpression 表达式为 true 返回 fase 否则 返回 true",
            "TRUE()\r\n返回 true",
            "FALSE()\r\n返回 false",
            "NowDate()\r\n返回 当前时间 datetime类型",
            "Len(Expression)\r\n返回值长度 int类型",
            "ToInt(Expression)\r\n值转换 返回 int类型",
            "ToDouble(Expression)\r\n值转换 返回 double类型",
            "ToDateTime(Expression)\r\n值转换 返回 datetime类型",
            "ToString(Expression)|ToString(Expression,Formatstring)\r\n值转换 返回 string类型" ,
            "GetNodeValue(nodeid,unitid)\r\n获取变量值函数 返回 double类型",
            "GetNodeIntValue(nodeid,unitid)\r\n获取变量值函数 返回 int类型",
            "GetNodeBoolValue(nodeid)\r\n获取变量值函数 返回 bool类型" ,
            "GetAllNodeBoolValueOR(FirstNodeId,SecondNodeId,...) \r\n判断节点(或节点集合)任一因子值为ture，结果为ture 返回 bool类型",
            "GetAlarmNodeBoolValue(AlarmSouceID,NodeIDList)\r\n获取设备报警码，当前报警时 返回 bool类型" ,
            "DoubleNaN(doubleExpression)\r\n判断值是否NaN函数 返回 bool类型",
            "IsAlarmNode(FirstNodeId,SecondNodeId,...)\r\n判断节点(或节点集合)是否存在报警函数 返回 bool类型" ,
            "IsNodeCycleValueAlarm(FirstNodeId,SecondNodeId,...)\r\n判断节点(或节点集合)CycleValue值是否存在报警。任一因子报警，返回ture 返回 bool类型" ,
            "Invoke(MethodName,FirstParam,SencondParam,...)\r\n调用执行指定方法，返回执行方法结果"
        };

        /// <summary>
        /// 关键字默认生成串集合
        /// </summary>
        private readonly string[] _defaultExpress = {
            "If(JudgeExpression,FirstExpression,SecondExpression)",
            "Case(JudgeExpression1,ResultExpression1,JudgeExpression2,ResultExpression2)",
            "And(JudgeExpression,JudgeExpression,...)",
            "Or(JudgeExpression,JudgeExpression,...)",
            "Not(JudgeExpression)",
            "True()",
            "False()",
            "NowDate()",
            "Len(Expression)",
            "ToInt(Expression)",
            "ToDouble(Expression)",
            "ToDateTime(Expression)",
            "ToString(Expression)",
            "GetNodeValue(nodeid,unitid)" ,
            "GetNodeIntValue(nodeid,unitid)" ,
            "GetNodeBoolValue(nodeid)" ,
            "GetAllNodeBoolValueOR(FirstNodeId,SecondNodeId,...)" ,
            "GetAlarmNodeBoolValue(AlarmSouceID,NodeIDList)",
            "DoubleNaN(doubleExpression)" ,
            "IsAlarmNode(FirstNodeId,SecondNodeId,...)" ,
            "IsNodeCycleValueAlarm(FirstNodeId,SecondNodeId,...)" ,
            "Invoke(MethodName,FirstParam,SencondParam,...)"
        };

        /// <summary>
        /// 构造
        /// </summary>
        public ExpressEditorForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ExpressEditorForm_Load(object sender, EventArgs e)
        {
            this.textBox_Expression.Text = Expression;
            this.lbFunction.Items.Clear();
            foreach(string func in _functions)
            {
                this.lbFunction.Items.Add(func);
            }
        }

        /// <summary>
        /// 验证表达式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void button_check_Click(object sender, EventArgs e)
        {
            _ex.Expression = this.textBox_Expression.Text.Trim();
            string mes = "";
            if(_ex.Check(ref mes))
            {
                FpiMessageBox.ShowInfo("验证通过!");
            }
            else
            {
                FpiMessageBox.ShowError("验证失败：" + mes);
            }
        }

        /// <summary>
        /// 双击关键字生成表达式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lbFunction_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if(lbFunction.SelectedIndex > -1 && lbFunction.SelectedIndex < _descriptions.Length)
            {
                if(lbFunction.SelectedItem.ToString() == EKeyword.GetNodeValue.ToString()
                    || lbFunction.SelectedItem.ToString() == EKeyword.GetNodeIntValue.ToString())
                {
                    VarNodeEditorForm nodeForm = new VarNodeEditorForm(eVarType.Value);
                    nodeForm.MultiSelect = false;
                    if(nodeForm.ShowDialog() == DialogResult.OK)
                    {
                        string exp = lbFunction.SelectedItem.ToString() + "(\"" + nodeForm.VarNode.FullNodeId + "\",\"" + nodeForm.VarNode.UnitName + "\")";
                        Clipboard.SetDataObject(exp);
                        textBox_Expression.Paste();
                    }
                }
                else if(lbFunction.SelectedItem.ToString() == EKeyword.GetNodeBoolValue.ToString())
                {
                    VarNodeEditorForm nodeForm = new VarNodeEditorForm(eVarType.State);
                    nodeForm.MultiSelect = false;
                    if(nodeForm.ShowDialog() == DialogResult.OK)
                    {
                        string exp = lbFunction.SelectedItem.ToString() + "(\"" + nodeForm.VarNode.FullNodeId + "\")";
                        Clipboard.SetDataObject(exp);
                        textBox_Expression.Paste();
                    }
                }
                else if(lbFunction.SelectedItem.ToString() == EKeyword.GetAllNodeBoolValueOR.ToString())
                {
                    VarNodeEditorForm nodeForm = new VarNodeEditorForm(eVarType.State);
                    nodeForm.MultiSelect = true;
                    if(nodeForm.ShowDialog() == DialogResult.OK)
                    {
                        if(nodeForm.VarNodes.Count > 0)
                        {
                            StringBuilder sb = new StringBuilder(lbFunction.SelectedItem.ToString());
                            sb.Append("(\"");
                            string spilit = "";
                            foreach(VarNodeInfo var in nodeForm.VarNodes)
                            {
                                sb.Append(spilit);
                                sb.Append(var.FullNodeId);
                                spilit = ",";
                            }
                            sb.Append("\")");
                            Clipboard.SetDataObject(sb.ToString());
                            textBox_Expression.Paste();
                        }
                    }
                }
                else if(lbFunction.SelectedItem.ToString() == EKeyword.GetAlarmNodeBoolValue.ToString())
                {
                    AlarmNodeEditorForm nodeForm = new AlarmNodeEditorForm();
                    if(nodeForm.ShowDialog() == DialogResult.OK)
                    {
                        if(nodeForm.AlarmNodes.Count > 0 && nodeForm.AlarmSouce != null)
                        {
                            string idStr = string.Empty;
                            foreach(var node in nodeForm.AlarmNodes)
                            {
                                idStr += node.id + "|";
                            }
                            idStr = idStr.TrimEnd('|');

                            string exp = $"{lbFunction.SelectedItem}(\"{nodeForm.AlarmSouce.id}\",\"{idStr}\")";
                            Clipboard.SetDataObject(exp);
                            textBox_Expression.Paste();
                        }
                    }
                }
                else if(lbFunction.SelectedItem.ToString() == EKeyword.IsAlarmNode.ToString() || lbFunction.SelectedItem.ToString() == EKeyword.IsNodeCycleValueAlarm.ToString())
                {
                    VarNodeEditorForm nodeForm = new VarNodeEditorForm();
                    nodeForm.MultiSelect = true;
                    if(nodeForm.ShowDialog() == DialogResult.OK)
                    {
                        if(nodeForm.VarNodes.Count > 0)
                        {
                            StringBuilder sb = new StringBuilder(lbFunction.SelectedItem.ToString());
                            sb.Append("(\"");
                            string spilit = "";
                            foreach(VarNodeInfo var in nodeForm.VarNodes)
                            {
                                sb.Append(spilit);
                                sb.Append(var.FullNodeId);
                                spilit = ",";
                            }
                            sb.Append("\")");
                            Clipboard.SetDataObject(sb.ToString());
                            textBox_Expression.Paste();
                        }
                    }
                }
                else if(lbFunction.SelectedItem.ToString() == EKeyword.Invoke.ToString())
                {
                    ServiceEditForm form = new ServiceEditForm();
                    if(form.ShowDialog() == DialogResult.OK)
                    {
                        StringBuilder sb = new StringBuilder(lbFunction.SelectedItem.ToString());
                        sb.Append("(");
                        sb.Append(form.InvokeStr);
                        sb.Append(")");
                        Clipboard.SetDataObject(sb.ToString());
                        textBox_Expression.Paste();
                    }
                }
                else
                {
                    Clipboard.SetDataObject(_defaultExpress[lbFunction.SelectedIndex]);
                    textBox_Expression.Paste();
                }
            }
        }

        /// <summary>
        /// 确定保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOk_Click(object sender, EventArgs e)
        {
            _ex.Expression = this.textBox_Expression.Text.Trim();
            string mes = "";
            if(!_ex.Check(ref mes))
            {
                FpiMessageBox.ShowError("验证失败：" + mes);
                DialogResult = DialogResult.None;

            }
            else
            {
                Expression = this.textBox_Expression.Text;
                DialogResult = DialogResult.OK;
            }
        }

        /// <summary>
        /// 选择关键字
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lbFunction_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.lbDes.Text = lbFunction.SelectedIndex > -1 && lbFunction.SelectedIndex < _descriptions.Length
                ? _descriptions[lbFunction.SelectedIndex]
                : "";
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
