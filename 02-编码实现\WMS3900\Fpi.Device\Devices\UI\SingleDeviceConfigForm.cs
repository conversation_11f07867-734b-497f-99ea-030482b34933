﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Fpi.Devices.Properties;

namespace Fpi.Devices.UI
{
    public partial class SingleDeviceConfigForm : Form
    {
        public SingleDeviceConfigForm()
        {
            InitializeComponent();
        }

        public SingleDeviceConfigForm(Device device, DeviceConfigType curConfigType)
            : this()
        {
            this.deviceConfigPanel.Device = device;
            this.deviceConfigPanel.CurConfigType = curConfigType;
        }

        private void btnSure_Click(object sender, EventArgs e)
        {
            try
            {
                this.deviceConfigPanel.SaveDeviceConfigParam();
                DialogResult = DialogResult.OK;
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            this.Close();
        }

        private void SingleDeviceConfigForm_Load(object sender, EventArgs e)
        {
            this.Size = new Size(this.deviceConfigPanel.Width + 10, this.deviceConfigPanel.Height + 80);
        }
    }
}
