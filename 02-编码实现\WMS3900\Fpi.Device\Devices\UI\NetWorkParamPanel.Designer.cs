﻿namespace Fpi.Devices.UI
{
    partial class NetWorkParamPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.nuPort = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.txtHost = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.nuPort)).BeginInit();
            this.SuspendLayout();
            // 
            // nuPort
            // 
            this.nuPort.Location = new System.Drawing.Point(279, 15);
            this.nuPort.Maximum = new decimal(new int[] {
            65535,
            0,
            0,
            0});
            this.nuPort.Name = "nuPort";
            this.nuPort.Size = new System.Drawing.Size(113, 21);
            this.nuPort.TabIndex = 6;
            this.nuPort.Value = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(244, 18);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(29, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "端口";
            // 
            // txtHost
            // 
            this.txtHost.CanEmpty = false;
            this.txtHost.DigitLength = 2;
            this.txtHost.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtHost.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtHost.IsValidCheck = false;
            this.txtHost.Label = "服务器";
            this.txtHost.Location = new System.Drawing.Point(111, 15);
            this.txtHost.MaxLength = 30;
            this.txtHost.MaxValue = null;
            this.txtHost.MinValue = null;
            this.txtHost.Name = "txtHost";
            this.txtHost.Size = new System.Drawing.Size(111, 21);
            this.txtHost.TabIndex = 5;
            this.txtHost.Text = "127.0.0.1";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(64, 18);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "IP地址";
            // 
            // NetWorkParamPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.nuPort);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.txtHost);
            this.Controls.Add(this.label1);
            this.Name = "NetWorkParamPanel";
            this.Size = new System.Drawing.Size(466, 52);
            this.Load += new System.EventHandler(this.SerialPortParamPanel_Load);
            ((System.ComponentModel.ISupportInitialize)(this.nuPort)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.NumericUpDown nuPort;
        private System.Windows.Forms.Label label2;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtHost;
        private System.Windows.Forms.Label label1;


    }
}
