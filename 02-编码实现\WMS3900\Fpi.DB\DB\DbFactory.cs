﻿using System;
using System.ComponentModel;
using System.Data;
using Fpi.DB.Manager;

namespace Fpi.DB
{
    public enum DBType
    {
        /// <summary>
        /// ODBC
        /// </summary>
        [Description("odbc")]
        odbc,
        /// <summary>
        /// mySql
        /// </summary>
        [Description("mysql")]
        mysql,
        /// <summary>
        /// sqlServer
        /// </summary>
        [Description("mssql")]
        mssql,
        /// <summary>
        /// sqlite
        /// </summary>
        [Description("sqlite")]
        sqlite
    }

    /// <summary>
    /// 创建数据库连接工厂
    /// </summary>
    public sealed class DbFactory
    {
        private static volatile IDbFactory _singleDbFactory = null;
        private static readonly object syncObj = new object();

        public static DBType DBType = DBType.odbc;

        /// <summary>
        /// Constructor
        /// </summary>
        private DbFactory()
        {

        }

        /// <summary>
        /// 获得Factory类的实例
        /// </summary>
        /// <returns>Factory类实例</returns>
        public static IDbFactory GetInstance()
        {
            try
            {
                string strDbType = DataBaseManager.GetInstance().DataBaseType;
                DBType = (DBType)Enum.Parse(typeof(DBType), strDbType, true);
            }
            catch
            {
            }
            if(_singleDbFactory == null)
            {
                lock(syncObj)
                {
                    if(_singleDbFactory == null)
                    {
                        _singleDbFactory = CreateInstance();
                    }
                }
            }
            return _singleDbFactory;
        }

        public static void ReLoad()
        {
            try
            {
                _singleDbFactory = null;
                string strDbType = DataBaseManager.GetInstance().DataBaseType;
                DBType = (DBType)Enum.Parse(typeof(DBType), strDbType, true);
                _singleDbFactory = CreateInstance();
            }
            catch
            {
            }
        }

        public static IDbConnection GetConnection(DBType dbType, string dataBaseConStr)
        {
            IDbConnection dbcon = null;
            IDbFactory dbFactory = null;
            switch(dbType)
            {
                case DBType.mysql:
                    {
                        dbFactory = new MySqlFactory(dataBaseConStr);
                        break;
                    }
                case DBType.odbc:
                    {
                        dbFactory = new OdbcFactory(dataBaseConStr);
                        break;
                    }
                case DBType.mssql:
                    {
                        dbFactory = new MssqlFactory(dataBaseConStr);
                        break;
                    }
                case DBType.sqlite:
                    {
                        dbFactory = new SQLiteFactory(dataBaseConStr);
                        break;
                    }
            }
            dbcon = dbFactory.CreateConnection();
            return dbcon;
        }

        /// <summary>
        /// 建立Factory类实例
        /// </summary>
        /// <returns>Factory类实例</returns>
        private static IDbFactory CreateInstance()
        {
            IDbFactory dbFactory = null;

            switch(DBType)
            {
                case DBType.mysql:
                    {
                        dbFactory = new MySqlFactory();
                        break;
                    }
                case DBType.odbc:
                    {
                        dbFactory = new OdbcFactory();
                        break;
                    }
                case DBType.mssql:
                    {
                        dbFactory = new MssqlFactory();
                        break;
                    }
                case DBType.sqlite:
                    {
                        dbFactory = new SQLiteFactory();
                        break;
                    }

            }
            return dbFactory;
        }
    }

}