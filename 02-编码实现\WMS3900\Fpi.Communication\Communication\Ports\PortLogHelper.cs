using Fpi.Communication.Properties;
using Fpi.Log;
using Fpi.Util.Sundry;
namespace Fpi.Communication.Ports
{
    public class PortLogHelper
    {
        private PortLogHelper()
        {
        }

        private const string MsgType = "PortMessage";

        public static void TracePortMsg(string msg)
        {
            LogUtil.Debug(MsgType, msg);
        }

        public static void TracePortSendMsg(byte[] sendData)
        {
            string strBytes = StringUtil.BytesToString(sendData);
            TracePortSendMsg(strBytes);
        }

        public static void TracePortRecvMsg(byte[] recvData)
        {
            string strBytes = StringUtil.BytesToString(recvData);
            TracePortRecvMsg(strBytes);
        }

        public static void TracePortSendMsg(string sendData)
        {
            LogUtil.Debug(MsgType, Resources.Send + ":" + sendData);
        }

        public static void TracePortRecvMsg(string recvData)
        {
            LogUtil.Debug(MsgType, Resources.Recv + ":" + recvData);
        }

        public static void TracePortSendMsg(object source, string sendData)
        {
            LogUtil.Debug(MsgType, "[" + source.ToString() + "]" + Resources.Send + ":" + sendData);
        }

        public static void TracePortRecvMsg(object source, string recvData)
        {
            LogUtil.Debug(MsgType, "[" + source.ToString() + "]" + Resources.Recv + ":" + recvData);
        }

        public static void TracePortSendMsg(object source, byte[] sendData)
        {
            string strBytes = StringUtil.BytesToString(sendData);
            TracePortSendMsg(source, strBytes);
        }

        public static void TracePortRecvMsg(object source, byte[] recvData)
        {
            string strBytes = StringUtil.BytesToString(recvData);
            TracePortRecvMsg(source, strBytes);
        }
    }
}