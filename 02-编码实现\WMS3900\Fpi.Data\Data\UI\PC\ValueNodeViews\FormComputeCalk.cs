using System;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.Properties;
using Fpi.Util.MathUtil;

namespace Fpi.Data.UI.PC.ValueNodeViews
{
    public partial class FormComputeCalk : Form
    {
        public FormComputeCalk()
        {
            InitializeComponent();
        }
        public FormComputeCalk(CalK calk)
            : this()
        {
            this.Calk = calk;
        }

        public CalK Calk { get; private set; }

        private void FormComputeCalk_Load(object sender, EventArgs e)
        {
            if(Calk == null)
            {
                Calk = new CalK();
            }

            this.txtK.Text = Calk.k.ToString();
            this.txtZ.Text = Calk.z.ToString();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                double a = double.Parse(this.txtRaw.Text);
                double b = double.Parse(this.txtAdjust.Text);

                ListViewItem item = new ListViewItem(new string[] { a.ToString(), b.ToString() });
                this.lvArray.Items.Add(item);
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCompute_Click(object sender, EventArgs e)
        {
            try
            {
                Compute();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Compute()
        {
            if(this.lvArray.Items.Count < 2)
            {
                throw new Exception(Resources.TwoDataCompute);
            }

            int count = this.lvArray.Items.Count;
            double[] x = new double[count];
            double[] y = new double[count];

            int i = 0;
            foreach(ListViewItem item in this.lvArray.Items)
            {
                x[i] = double.Parse(item.SubItems[0].Text);
                y[i] = double.Parse(item.SubItems[1].Text);
                i++;
            }

            double[] rv = MathTool.MLR(1, x, y);
            this.txtK.Text = rv[0].ToString();
            this.txtZ.Text = rv[1].ToString();
        }

        private void lvArray_PreviewKeyDown(object sender, PreviewKeyDownEventArgs e)
        {
            if(e.KeyCode == Keys.Delete)
            {
                DeleteItems();
            }
        }

        private void menuDelete_Click(object sender, EventArgs e)
        {
            DeleteItems();
        }

        private void DeleteItems()
        {
            if(this.lvArray.SelectedIndices.Count > 0)
            {
                int[] all = new int[this.lvArray.SelectedIndices.Count];
                foreach(int index in all)
                {
                    this.lvArray.Items.RemoveAt(index);
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                Save();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message, Resources.SystemPrompt, MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        private void Save()
        {
            Calk.k = double.Parse(this.txtK.Text);
            Calk.z = double.Parse(this.txtZ.Text);
        }

    }
}