﻿//=================================================================================================
//说明：支持连接多个客户端的TCP服务器Bus,要求每个客户端连上来使用相同的协议
//     将每个客户端唯一信息与TcpClient socket绑定，这样对上层只需要一个pipe,
//     发送接收命令到哪个客户端对上层应用透明
//创建人：张永强
//创建时间：2011-7-1
//=================================================================================================

using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using Fpi.Communication.Exceptions;
using Fpi.Communication.Properties;
using Fpi.Util.Reflection;

namespace Fpi.Communication.Buses
{
    /// <summary>
    /// 支持连接多个客户端的TcpServer Bus
    /// 创建人：张永强
    /// 创建时间：2011-6-30
    /// </summary>
    public class NetTcpServer : BaseBus
    {
        private static readonly string PropertyName_Port = "port";
        private static readonly string PropertyName_IP = "host";
        private static readonly string PropertyName_ClientKeyPacket = "clientKeyPacket";
        private string _clientKeyPacketImp;
        private ClientKeyPacket _keyPacket;

        private TcpClientList _tcpClientList;
        private TcpListener _listener;
        private readonly ReadWaitNode _readWaitNode = new ReadWaitNode(false);

        private void StartListening()
        {
            connected = true;
            _listener = new TcpListener(IP, Port);
            bool listening = false;

            while(connected)
            {
                try
                {
                    if(!listening)
                    {
                        _listener.Start();
                        listening = true;
                    }
                    TcpClient clientSocket = _listener.AcceptTcpClient();
                    NetTcpClient tcpclient = new NetTcpClient(clientSocket, this._clientKeyPacketImp, _readWaitNode);
                    _tcpClientList.Add(tcpclient);
                    if(_keyPacket.SendCmd != null)
                    {
                        tcpclient.Write(_keyPacket.SendCmd);
                    }
                    BusLogHelper.TraceBusMsg(string.Format(Resources.AcceptTcpConnect, clientSocket.ToString()));
                }
                catch(Exception ex)
                {
                    BusLogHelper.TraceBusMsg(string.Format(Resources.TcpListenError, ex.Message));
                }
            }
        }
        /// <summary>
        /// 端口
        /// </summary>
        public int Port { get; private set; }
        /// <summary>
        /// IP地址
        /// </summary>
        public IPAddress IP { get; private set; }
        public override string FriendlyName => "TCP 服务端（多客户端）";
        public override void Init(Fpi.Xml.BaseNode config)
        {
            if(config == null)
            {
                throw new CommunicationParamException(Resources.TcpLocalPortNotConfig);
            }

            base.Init(config);
            Port = int.Parse(config.GetPropertyValue(PropertyName_Port));
            IP = IPAddress.Parse(config.GetPropertyValue(PropertyName_IP));
            _clientKeyPacketImp = config.GetPropertyValue(PropertyName_ClientKeyPacket);
            _keyPacket = (ClientKeyPacket)ReflectionHelper.CreateInstance(_clientKeyPacketImp);
            _tcpClientList = new TcpClientList();
        }
        public override bool Write(byte[] buf)
        {
            //根据包找到对应的客户端
            object key = _keyPacket.GetKeyfromData(buf);
            NetTcpClient tcpClient = _tcpClientList[key];
            if(tcpClient != null)
            {
                tcpClient.Write(buf);
                return true;
            }
            else
            {
                return false;
            }
        }

        public NetTcpClient GetNetTcpClient(object key)
        {
            //根据包找到对应的客户端
            //object key = add;
            NetTcpClient tcpClient = _tcpClientList[key];
            return tcpClient;
        }

        public override bool Read(byte[] buf, int count, ref int bytesread)
        {
            _readWaitNode.WaitOne();
            return _readWaitNode.TcpClient.Read(buf, count, ref bytesread);
        }

        public override bool Open()
        {
            new Thread(StartListening).Start();
            return true;
        }

        public override bool Close()
        {
            connected = false;
            if(this._listener != null)
            {
                this._listener.Stop();
            }
            foreach(NetTcpClient client in this._tcpClientList)
            {
                client.Close();
            }
            return true;
        }

    }

    /// <summary>
    /// 与服务器相连的一个客户端
    /// 创建人：张永强
    /// 创建时间：2011-6-30
    /// </summary>
    public class NetTcpClient
    {
        private bool _connected;
        private readonly ReadWaitNode _readWaitNode;
        private readonly ClientKeyPacket _clientKeyPacket;

        public TcpClient ClientSocket { get; }
        private readonly NetworkStream _netStream;

        private const int BUFFERSIZE = 10240;
        private readonly byte[] _dataBuffer = new byte[BUFFERSIZE];
        private readonly byte[] _tempBuffer = new byte[BUFFERSIZE];
        private int _receivedPos = 0;

        public event EventHandler DisConnected;
        /// <summary>
        /// 客户端唯一标识键
        /// </summary>
        public object Key { get; set; }
        /// <summary>
        /// 是否已得到唯一标识
        /// </summary>
        public bool KeyGetted { get; }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="clientKeyPacketImp">解析key的包对象</param>
        /// <param name="stream">客户端流</param>
        /// <param name="readwait">读数据事件</param>
        public NetTcpClient(TcpClient clientsocket, string clientKeyPacketImp, ReadWaitNode readwait)
        {
            _connected = true;
            KeyGetted = false;
            this.ClientSocket = clientsocket;
            this._netStream = clientsocket.GetStream();

            //创建解析键的对象
            _clientKeyPacket = (ClientKeyPacket)ReflectionHelper.CreateInstance(clientKeyPacketImp);
            _clientKeyPacket.Init();

            this._readWaitNode = readwait;

            //开启读数据线程
            Thread readThread = new Thread(ReadThread);
            readThread.Priority = ThreadPriority.AboveNormal;
            readThread.Start();

        }
        /// <summary>
        /// 读数据线程
        /// </summary>
        private void ReadThread()
        {
            while(_connected)
            {
                int readBytes = 0;
                try
                {
                    readBytes = _netStream.Read(_tempBuffer, 0, BUFFERSIZE);
                    if(readBytes == 0)
                    {
                        throw new IOException();
                    }
                }
                catch(IOException)
                {
                    this.Close();
                    if(this.DisConnected != null)
                    {
                        this.DisConnected(this, null);
                    }
                }
                //解析数据从中获取客户端唯一key
                byte[] data = new byte[readBytes];
                Buffer.BlockCopy(_tempBuffer, 0, data, 0, readBytes);
                if(!_clientKeyPacket.KeyGetted)
                {
                    _clientKeyPacket.PutData(data);
                    if(_clientKeyPacket.KeyGetted)
                    {
                        this.Key = this._clientKeyPacket.ClientKey;
                    }
                }

                //将收到数据存入缓冲区
                if(_receivedPos + readBytes >= BUFFERSIZE)
                {
                    _receivedPos = 0;
                }
                lock(this._dataBuffer)
                {
                    Buffer.BlockCopy(_tempBuffer, 0, _dataBuffer, _receivedPos, readBytes);
                }
                _receivedPos += readBytes;
                //触发读事件
                _readWaitNode.TcpClient = this;
                _readWaitNode.Set();
            }
        }
        /// <summary>
        /// 从该客户端读取数据
        /// </summary>
        /// <param name="buf"></param>
        /// <param name="count"></param>
        /// <param name="bytesread"></param>
        public bool Read(byte[] buf, int count, ref int bytesread)
        {
            bytesread = Math.Min(count, _receivedPos);
            lock(this._dataBuffer)
            {
                Buffer.BlockCopy(_dataBuffer, 0, buf, 0, bytesread);
                _receivedPos -= bytesread;
                Buffer.BlockCopy(_dataBuffer, bytesread, _dataBuffer, 0, _receivedPos);
            }
            return true;
        }
        /// <summary>
        /// 向客户端写数据
        /// </summary>
        /// <param name="buffer"></param>
        /// <returns></returns>
        public bool Write(byte[] buffer)
        {
            _netStream.Write(buffer, 0, buffer.Length);
            _netStream.Flush();
            return true;
        }

        /// <summary>
        /// 关闭客户端
        /// </summary>
        public void Close()
        {
            this._connected = false;
            if(this._netStream != null)
            {
                this._netStream.Close();
            }
            if(this.ClientSocket != null)
            {
                this.ClientSocket.Close();
            }
        }

    }

    /// <summary>
    /// 已连接上的客户端列表
    /// 创建人：张永强
    /// 创建时间：2011-6-30
    /// </summary>
    public class TcpClientList : IEnumerable<NetTcpClient>
    {
        private readonly List<NetTcpClient> _clientList = new List<NetTcpClient>();
        public NetTcpClient this[object key]
        {
            get
            {
                foreach(NetTcpClient client in _clientList)
                {
                    if(client.Key != null && client.Key.Equals(key))
                    {
                        return client;
                    }
                }
                return null;
            }
        }
        /// <summary>
        /// 向列表中添加一个客户端
        /// </summary>
        /// <param name="client"></param>
        public void Add(NetTcpClient client)
        {
            client.DisConnected += client_DisConnected;
            _clientList.Add(client);
        }

        private void client_DisConnected(object sender, EventArgs e)
        {
            _clientList.Remove(sender as NetTcpClient);
        }

        #region IEnumerable<OneTcpClient> 成员

        public IEnumerator<NetTcpClient> GetEnumerator()
        {
            return _clientList.GetEnumerator();
        }

        #endregion

        #region IEnumerable 成员

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return _clientList.GetEnumerator();
        }

        #endregion
    }

    /// <summary>
    /// 等待读事件的类：主要封装了NetTcpClient到里面，用来获取触发读事件的客户端
    /// 创建人：张永强
    /// 创建时间：2011-7-1
    /// </summary>
    public class ReadWaitNode
    {
        private readonly AutoResetEvent _readEvent;

        public NetTcpClient TcpClient { get; set; } = null;
        public ReadWaitNode(bool initialState)
        {
            _readEvent = new AutoResetEvent(initialState);
        }
        public bool WaitOne()
        {
            return _readEvent.WaitOne();
        }
        public bool Reset()
        {
            return _readEvent.Reset();
        }
        public bool Set()
        {
            return _readEvent.Set();
        }
    }
}
