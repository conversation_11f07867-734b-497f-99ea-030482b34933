﻿using System;
using System.IO;
using System.Net;
using System.Text;
using Fpi.Log;
using Fpi.Util.Interfaces.Initialize;
using Fpi.Xml;

namespace Fpi.HB.Business
{
    /// <summary>
    /// 备份数据库及配置管理类
    /// </summary>
    public class BackUpDatalManager : BaseNode
    {
        #region 字段属性

        /// <summary>
        /// 是否开启数据库备份
        /// </summary>
        public bool IsDBBackUpValid;

        /// <summary>
        /// 数据库备份路径
        /// </summary>
        public string DbSavePath;

        /// <summary>
        /// 数据库备份文件名
        /// </summary>
        public string DbSaveFileName;

        /// <summary>
        /// 是否开启配置文件备份
        /// </summary>
        public bool IsConfigBackUpValid;

        /// <summary>
        /// 配置文件备份路径
        /// </summary>
        public string XmlSavePath;

        #region 上传参数配置

        /// <summary>
        /// 站点ID
        /// </summary>
        public string StationID;

        /// <summary>
        /// 站点名称
        /// </summary>
        public string StationName;

        /// <summary>
        /// 站点URL
        /// </summary>
        public string StationURL;

        /// <summary>
        /// 是否开启上传
        /// </summary>
        public bool ISOpenUpload;

        /// <summary>
        /// 重发次数
        /// </summary>
        public int RepitTimes;

        /// <summary>
        /// 超时时间
        /// </summary>
        public int OverTime;

        #endregion

        #endregion

        #region 单例

        private BackUpDatalManager()
        {
            loadXml();
        }

        private static readonly object SyncObj = new object();
        private static BackUpDatalManager _instance;
        public static BackUpDatalManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new BackUpDatalManager();
                }
            }
            return _instance;
        }

        #endregion

        #region 公共方法

        #endregion

        #region 公共方法

        public static string HttpUploadFile(string url, string path, string stationid, string stationname)
        {
            HttpWebRequest request = WebRequest.Create(url) as HttpWebRequest;
            request.CookieContainer = new CookieContainer();
            request.AllowAutoRedirect = true;
            request.Method = "POST";
            string boundary = DateTime.Now.Ticks.ToString("X"); // 随机分隔线
            request.ContentType = "multipart/form-data;charset=utf-8;boundary=" + boundary;
            byte[] itemBoundaryBytes = Encoding.UTF8.GetBytes("\r\n--" + boundary + "\r\n");
            byte[] endBoundaryBytes = Encoding.UTF8.GetBytes("\r\n--" + boundary + "--\r\n");
            int pos = path.LastIndexOf("\\");
            string fileName = path.Substring(pos + 1);
            //请求头部信息 

            StringBuilder sbHeader = new StringBuilder(string.Format("Content-Disposition:form-data;name=\"file\";filename=\"{0}\"\r\nContent-Type:application/octet-stream\r\n\r\n", fileName));

            byte[] postHeaderBytes = Encoding.UTF8.GetBytes(sbHeader.ToString());
            FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read);
            byte[] bArr = new byte[fs.Length];
            fs.Read(bArr, 0, bArr.Length);
            fs.Close();
            Stream postStream = request.GetRequestStream();
            postStream.Write(itemBoundaryBytes, 0, itemBoundaryBytes.Length);
            postStream.Write(postHeaderBytes, 0, postHeaderBytes.Length);
            postStream.Write(bArr, 0, bArr.Length);
            postStream.Write(endBoundaryBytes, 0, endBoundaryBytes.Length);
            postStream.Close();
            //发送请求并获取相应回应数据

            HttpWebResponse response = request.GetResponse() as HttpWebResponse;
            Stream instream = response.GetResponseStream();
            StreamReader sr = new StreamReader(instream, Encoding.UTF8);
            string res = sr.ReadToEnd();

            return res;
        }

        #endregion
    }
}
