﻿using System;
using System.IO;
using Fpi.DB.Manager;
using Fpi.Xml;

namespace Fpi.HB.Business.Tasks
{
    /// <summary>
    /// 数据库备份及配置备份任务
    /// </summary>
    public class DbBackupTask : CustomTask
    {
        #region 构造

        public DbBackupTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "数据库备份及配置备份任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "DbBackupTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "数据库备份及配置备份任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "数据库备份及配置备份任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            // 配置文件备份
            if(BackUpDatalManager.GetInstance().IsConfigBackUpValid)
            {
                BackUpConfigFile();
            }

            // 数据库备份
            if(BackUpDatalManager.GetInstance().IsDBBackUpValid)
            {
                BackUpDBFile();
            }
        }

        #endregion

        #region 私有方法

        public static void BackUpDBFile()
        {
            string path = BackUpDatalManager.GetInstance().DbSavePath;
            if(string.IsNullOrEmpty(path))
            {
                throw new Exception("数据库备份路径未设置！");
            }

            if(!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            string filename = BackUpDatalManager.GetInstance().DbSaveFileName;
            if(string.IsNullOrEmpty(filename))
            {
                filename = "bk_";
            }

            string zipfile = Path.Combine(path, filename + DateTime.Now.ToString("yyyyMMddHHmmss") + ".bak");
            FpiDataBase.GetInstance().StartBackup(zipfile);
        }

        public static void BackUpConfigFile()
        {
            try
            {
                string path = BackUpDatalManager.GetInstance().XmlSavePath;
                if(string.IsNullOrEmpty(path))
                {
                    throw new Exception("配置文件备份路径未设置！");
                }

                if(!path.EndsWith(@"\"))
                {
                    path = path + @"\";
                }
                if(!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                string fullpath = Path.Combine(path, $"Config_{DateTime.Now.ToString("yyyyMMddHHmmss")}.zip");
                Util.Compress.CompressUtil.ZipFiles(ConstConfig.XmlPath, fullpath, false);
                FpiDataBase.JudgeHistoryCount(new DirectoryInfo(path), "zip", 3);
                Log.LogUtil.Info("自定义任务", "系统配置文件备份任务执行成功!");
            }
            catch(Exception ex)
            {
                Log.LogUtil.Info("自定义任务", $"系统配置文件备份任务执行出错：{ex.Message}");
            }
        }

        #endregion
    }
}