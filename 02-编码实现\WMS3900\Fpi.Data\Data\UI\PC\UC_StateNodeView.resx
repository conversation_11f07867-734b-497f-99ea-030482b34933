<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="cmbStateNodeType.Location" type="System.Drawing.Point, System.Drawing">
    <value>83, 8</value>
  </data>
  <data name="cmbStateNodeType.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="cmbStateNodeType.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;cmbStateNodeType.Name" xml:space="preserve">
    <value>cmbStateNodeType</value>
  </data>
  <data name="&gt;&gt;cmbStateNodeType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cmbStateNodeType.Parent" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;cmbStateNodeType.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 11</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 12</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>开关量类型</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="panel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="panel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 34</value>
  </data>
  <data name="panel3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;panel3.Name" xml:space="preserve">
    <value>panel3</value>
  </data>
  <data name="&gt;&gt;panel3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel3.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 11</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 12</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>初始状态值</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdbFalse.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdbFalse.Location" type="System.Drawing.Point, System.Drawing">
    <value>138, 9</value>
  </data>
  <data name="rdbFalse.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 16</value>
  </data>
  <data name="rdbFalse.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rdbFalse.Text" xml:space="preserve">
    <value>关</value>
  </data>
  <data name="&gt;&gt;rdbFalse.Name" xml:space="preserve">
    <value>rdbFalse</value>
  </data>
  <data name="&gt;&gt;rdbFalse.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbFalse.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;rdbFalse.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rdbTrue.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdbTrue.Location" type="System.Drawing.Point, System.Drawing">
    <value>90, 9</value>
  </data>
  <data name="rdbTrue.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 16</value>
  </data>
  <data name="rdbTrue.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rdbTrue.Text" xml:space="preserve">
    <value>开</value>
  </data>
  <data name="&gt;&gt;rdbTrue.Name" xml:space="preserve">
    <value>rdbTrue</value>
  </data>
  <data name="&gt;&gt;rdbTrue.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rdbTrue.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;rdbTrue.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 6</value>
  </data>
  <data name="panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>185, 34</value>
  </data>
  <data name="panel2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;panel2.Name" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel2.Parent" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 253</value>
  </data>
  <data name="panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 46</value>
  </data>
  <data name="panel1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;panel1.Name" xml:space="preserve">
    <value>panel1</value>
  </data>
  <data name="&gt;&gt;panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;panel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panel1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlMain.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="pnlMain.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="pnlMain.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 253</value>
  </data>
  <data name="pnlMain.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;pnlMain.Name" xml:space="preserve">
    <value>pnlMain</value>
  </data>
  <data name="&gt;&gt;pnlMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlMain.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlMain.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>424, 299</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>UC_StateNodeView</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>