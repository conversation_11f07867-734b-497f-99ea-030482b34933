﻿using System;
using Fpi.Communication.Interfaces;

namespace Fpi.Communication.Protocols
{
    public class SampleReceiver : Receiver
    {
        #region 字段属性

        /// <summary>
        /// 设备数据接收器收到数据事件
        /// </summary>
        private Action<byte[]> _dataReceived;

        #endregion

        #region 公共（重写）方法

        /// <summary>
        /// 接收到数据
        /// </summary>
        /// <param name="source"></param>
        /// <param name="data"></param>
        protected override void ProcesseReceivedData(object source, IByteStream data)
        {
            //数据上抛到设备
            if(_dataReceived != null)
            {
                try
                {
                    _dataReceived(data.GetBytes());
                }
                catch(Exception)
                {
                    // 
                }
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 把事件处理方法绑定到接收器上
        /// </summary>
        /// <param name="dealMethod"></param>
        public void RegisterReceiverEvent(Action<byte[]> dealMethod)
        {
            //第一次注册
            if(_dataReceived == null)
            {
                _dataReceived += dealMethod;
            }
        }

        #endregion
    }
}
