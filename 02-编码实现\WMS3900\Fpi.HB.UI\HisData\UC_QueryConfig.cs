﻿using System;
using System.Windows.Forms;
using Fpi.Data.Config;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.Xml;

namespace Fpi.HB.UI.HisData
{
    /// <summary>
    /// 单个测量点历史查询选项配置控件
    /// </summary>
    public partial class UC_QueryConfig : UserControl
    {
        #region 字段属性

        private readonly QueryGroup _curMp;

        #endregion

        #region 构造

        /// <summary>
        /// 构造
        /// </summary>
        public UC_QueryConfig()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 构造
        /// </summary>
        public UC_QueryConfig(QueryGroup mp)
        {
            _curMp = mp;
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void QueryConfigUC_Load(object sender, EventArgs e)
        {
            LoadNodes();
        }

        #region 增删

        /// <summary>
        /// 添加查询因子
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            if(this.dgvNodeList.SelectedRows.Count > 0)
            {
                for(int i = this.dgvNodeList.SelectedRows.Count - 1; i >= 0; i--)
                {
                    DataGridViewRow dr = this.dgvNodeList.SelectedRows[i];
                    this.dgvNodeList.Rows.Remove(dr);
                    this.dgvSelctNodeList.Rows.Add(dr);
                }
            }
        }

        /// <summary>
        /// 删除查询因子
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRemove_Click(object sender, EventArgs e)
        {
            if(this.dgvSelctNodeList.SelectedRows.Count > 0)
            {
                for(int i = this.dgvSelctNodeList.SelectedRows.Count - 1; i >= 0; i--)
                {
                    DataGridViewRow dr = this.dgvSelctNodeList.SelectedRows[i];
                    this.dgvSelctNodeList.Rows.Remove(dr);
                    this.dgvNodeList.Rows.Add(dr);
                }
            }
        }
        /// <summary>
        /// 添加全部查询因子
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAddAll_Click(object sender, EventArgs e)
        {
            if(this.dgvNodeList.SelectedRows.Count > 0)
            {
                for(int i = this.dgvNodeList.RowCount - 1; i >= 0; i--)
                {
                    DataGridViewRow dr = this.dgvNodeList.Rows[i];
                    this.dgvNodeList.Rows.Remove(dr);
                    this.dgvSelctNodeList.Rows.Add(dr);
                }
            }
        }
        /// <summary>
        /// 删除全部查询因子
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRmoveAll_Click(object sender, EventArgs e)
        {
            if(this.dgvSelctNodeList.SelectedRows.Count > 0)
            {
                for(int i = this.dgvSelctNodeList.RowCount - 1; i >= 0; i--)
                {
                    DataGridViewRow dr = this.dgvSelctNodeList.Rows[i];
                    this.dgvSelctNodeList.Rows.Remove(dr);
                    this.dgvNodeList.Rows.Add(dr);
                }
            }
        }

        #endregion

        #region 调整排序

        private void menuUp_Click(object sender, EventArgs e)
        {
            if(this.dgvSelctNodeList.SelectedRows.Count > 0)
            {
                DataGridViewRow row1 = this.dgvSelctNodeList.SelectedRows[0];
                if(row1.Index > 0)
                {
                    var index = row1.Index - 1;
                    this.dgvSelctNodeList.Rows.Remove(row1);
                    this.dgvSelctNodeList.Rows.Insert(index, row1);

                    this.dgvSelctNodeList.ClearSelection();
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择要调整顺序的输出项!");
            }
        }

        private void menuDown_Click(object sender, EventArgs e)
        {
            if(this.dgvSelctNodeList.SelectedRows.Count > 0)
            {
                DataGridViewRow row1 = this.dgvSelctNodeList.SelectedRows[0];
                if(row1.Index < this.dgvSelctNodeList.Rows.Count - 1)
                {
                    var index = row1.Index + 1;
                    this.dgvSelctNodeList.Rows.Remove(row1);
                    this.dgvSelctNodeList.Rows.Insert(index, row1);
                }
            }
            else
            {
                FpiMessageBox.ShowInfo("请选择要调整顺序的输出项!");
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载node
        /// </summary>
        private void LoadNodes()
        {
            // 先加载报表部分已经选择项
            if(_curMp?.QueryNodes != null)
            {
                foreach(QueryNode node in _curMp.QueryNodes)
                {
                    ValueNode vn = DataManager.GetInstance().GetValueNodeById(node.id);
                    if(vn != null)
                    {
                        int index = dgvSelctNodeList.Rows.Add();
                        DataGridViewRow dr = dgvSelctNodeList.Rows[index];
                        dr.Tag = vn;
                        dr.Cells[0].Value = vn.FullName;
                        DataGridViewComboBoxCell ddl = (DataGridViewComboBoxCell)dr.Cells[1];
                        if(vn.Units != null && vn.Units.GetCount() > 0)
                        {
                            foreach(Unit unit in vn.Units)
                            {
                                ddl.Items.Add(unit.name);
                            }
                            ddl.Value = ddl.Items[0];
                        }
                        DataGridViewComboBoxCell cmbDec = (DataGridViewComboBoxCell)dr.Cells[2];
                        cmbDec.Value = "2";

                        string unitname = string.Empty;
                        if(vn is ValueNode && !string.IsNullOrEmpty(node.UnitId))
                        {
                            unitname = vn.GetUnit(node.UnitId)?.name;
                            dr.Cells[1].Value = unitname;
                            dr.Cells[2].Value = node.Dec.ToString();
                        }
                    }
                }
            }

            // 再加载左侧部分
            foreach(ValueNode vn in DataManager.GetInstance().GetAllValueNodes())
            {
                // 已选择菜单加载过了，跳过加载
                if(_curMp?.QueryNodes != null && _curMp?.QueryNodes.FindNode(vn.id) != null)
                {
                    continue;
                }

                int index = dgvNodeList.Rows.Add();
                DataGridViewRow dr = dgvNodeList.Rows[index];
                dr.Tag = vn;
                dr.Cells[0].Value = vn.FullName;
                DataGridViewComboBoxCell ddl = (DataGridViewComboBoxCell)dr.Cells[1];
                if(vn.Units != null && vn.Units.GetCount() > 0)
                {
                    foreach(Unit unit in vn.Units)
                    {
                        ddl.Items.Add(unit.name);
                    }
                    ddl.Value = ddl.Items[0];
                }
                DataGridViewComboBoxCell cmbDec = (DataGridViewComboBoxCell)dr.Cells[2];
                cmbDec.Value = "2";
            }
        }

        /// <summary>
        /// 保存配置信息
        /// </summary>
        public void Save()
        {
            if(_curMp.QueryNodes == null)
            {
                _curMp.QueryNodes = new NodeList();
            }
            _curMp.QueryNodes.Clear();
            for(int i = 0; i < this.dgvSelctNodeList.RowCount; i++)
            {
                DataGridViewRow dr = this.dgvSelctNodeList.Rows[i];
                VarNode node = dr.Tag as VarNode;
                string unitid = string.Empty;
                int dec = 2;
                if(node is ValueNode && dr.Cells[1].Value != null)
                {
                    string unitname = dr.Cells[1].Value.ToString();
                    if((node as ValueNode).Units.FindNodeByName(unitname) is Unit unit)
                    {
                        unitid = unit.id;
                    }
                    dec = Convert.ToInt32(dr.Cells[2].Value);
                }
                QueryNode queryNode = new QueryNode();
                queryNode.id = node.id;
                queryNode.name = node.name;
                queryNode.UnitId = unitid;
                queryNode.Dec = dec;
                _curMp.QueryNodes.Add(queryNode);
            }
        }

        #endregion
    }
}
