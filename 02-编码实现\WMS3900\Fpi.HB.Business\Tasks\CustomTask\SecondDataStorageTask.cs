﻿using System;
using Fpi.HB.Business.DB;

namespace Fpi.HB.Business.Tasks
{
    /// <summary>
    /// 秒级数据存储任务
    /// </summary>
    public class SecondDataStorageTask : CustomTask
    {
        #region 构造

        public SecondDataStorageTask()
        {
            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "秒级数据存储任务";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "SecondDataStorageTask";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "秒级数据存储任务";
            }
        }

        #endregion

        #region 方法重写

        public override string ToString()
        {
            if(string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName"))
            {
                return "秒级数据存储任务";
            }
            else
            {
                return this.name;
            }
        }

        public override void DoTask()
        {
            DBHelper.WriteRealDataToDb(DateTime.Now);
        }

        #endregion
    }
}