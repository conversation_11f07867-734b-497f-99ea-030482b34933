﻿//==================================================================================================
//类名：     BaseEquipment   
//创建人:    曹旭
//创建时间:  2012-9-24 14:48:27
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Fpi.Alarm;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Manager;
using Fpi.Data.Config;
using Fpi.Devices.Channel;
using Fpi.Timers;
using Fpi.Xml;
using Timer = Fpi.Timers.Timer;

namespace Fpi.Devices
{
    public class Device : IdNameNode, ITimerAction
    {
        #region 私有或保护成员变量

        /// <summary>
        /// 关联的报警源
        /// </summary>
        protected AlarmSource _alarmSource;

        /// <summary>
        /// 记录与仪表通信异常的次数，超过3次报警
        /// </summary>
        protected int _communicationErrorCount;

        /// <summary>
        /// 设备通信异常报警ID
        /// </summary>
        public string ComErrorAlarmCodeId { get; set; } = "999";

        #endregion

        #region 配置相关变量

        /// <summary>
        /// 设备地址
        /// </summary>
        public string Addr;

        /// <summary>
        /// 用于设备描述信息
        /// </summary>
        public string Description;

        /// <summary>
        /// 设备类型描述，用于设备反控时查找类型
        /// </summary>
        public string TypeDesc;

        /// <summary>
        /// 该设备是否已被系统选中使用
        /// </summary>
        public bool IsUsed;

        /// <summary>
        /// 是否显示默认通信界面
        /// </summary>
        public bool NeedDefaultCommView = true;

        /// <summary>
        /// 协议实现类全名
        /// </summary>
        public string ProtocolImp;

        /// <summary>
        /// 设备通信类型
        /// </summary>
        public string DeviceCommType;

        /// <summary>
        /// 关联报警源ID
        /// </summary>
        public string AlarmSourceId;

        /// <summary>
        /// 报警组编号，由具体设备实现时定义好
        /// </summary>
        public string AlarmGroupId;

        /// <summary>
        /// 设备指令管理（聚光设备特有）
        /// </summary>
        public string CommandManager;

        /// <summary>
        /// 设备定时器，用于数据获取或者其他定时操作
        /// </summary>
        public Timer Timer;

        /// <summary>
        /// 设备数据通道
        /// </summary>
        public NodeList InValueChannels = new NodeList();
        public NodeList OutValueChannels = new NodeList();
        public NodeList InSwitchChannels = new NodeList();
        public NodeList OutSwitchChannels = new NodeList();

        #endregion

        #region 属性

        /// <summary>
        /// 设备状态字段，用于标示设备当前状态，由业务层通过自定义枚举（整形）进行赋值
        /// </summary>
        public int DeviceState { get; set; } = -1;

        /// <summary>
        /// 设备类型
        /// </summary>
        public eDeviceType DeviceType { get; protected set; }

        /// <summary>
        /// 设备特性报警码列表
        /// </summary>
        public Dictionary<string, string> DeviceAlarmList { get; protected set; } = new Dictionary<string, string>();

        #endregion

        #region 公开方法

        public override BaseNode Init(System.Xml.XmlNode node)
        {
            base.Init(node);

            if(!string.IsNullOrEmpty(this.AlarmSourceId))
            {
                try
                {
                    this._alarmSource = AlarmManager.GetInstance().alarmSources.FindNode(this.AlarmSourceId) as AlarmSource;
                }
                catch(Exception ex)
                {
                    //记录初始化失败日志
                }
            }

            return this;
        }

        /// <summary>
        /// 根据设备通道个数，自动重建对应数量的默认通道配置
        /// </summary>
        public void ReBuildDataChannel(int inValueChannelCount, int outValueChannelCount, int inSwitchChannelCount, int outSwitchChannelCount)
        {
            this.InValueChannels.Clear();
            this.OutValueChannels.Clear();
            this.InSwitchChannels.Clear();
            this.OutSwitchChannels.Clear();

            for(int i = 0; i < inValueChannelCount; i++)
            {
                InValueChannel chl = new InValueChannel();
                chl.id = "InValueChannel_" + (i + 1).ToString("D2");
                chl.name = "数值输入通道" + (i + 1).ToString("D2");
                this.InValueChannels.Add(chl);
            }

            for(int i = 0; i < outValueChannelCount; i++)
            {
                OutValueChannel chl = new OutValueChannel();
                chl.id = "OutValueChannel_" + (i + 1).ToString("D2");
                chl.name = "数值输出通道" + (i + 1).ToString("D2");
                this.OutValueChannels.Add(chl);
            }

            for(int i = 0; i < inSwitchChannelCount; i++)
            {
                InSwitchChannel chl = new InSwitchChannel();
                chl.id = "InSwitchChannel_" + (i + 1).ToString("D2");
                chl.name = "开关量输入通道" + (i + 1).ToString("D2");
                this.InSwitchChannels.Add(chl);
            }

            for(int i = 0; i < outSwitchChannelCount; i++)
            {
                OutSwitchChannel chl = new OutSwitchChannel();
                chl.id = "OutSwitchChannel_" + (i + 1).ToString("D2");
                chl.name = "开关量输出通道" + (i + 1).ToString("D2");
                this.OutSwitchChannels.Add(chl);
            }
        }

        /// <summary>
        /// 将通道实时数据设置到对应Node上
        /// </summary>
        protected void SetRealDataToNode()
        {
            foreach(InValueChannel inValueChl in this.InValueChannels)
            {
                inValueChl?.SetRealValue();
            }

            foreach(InSwitchChannel inSwitchChl in this.InSwitchChannels)
            {
                inSwitchChl?.SetInState();
            }
        }

        /// <summary>
        /// 获取设备对应测量因子编码
        /// </summary>
        /// <returns></returns>
        public ValueNode GetDevTargetPolNode()
        {
            foreach(InValueChannel channel in InValueChannels)
            {
                if(channel.ValueNode != null)
                {
                    return channel.ValueNode;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取设备所有测量因子
        /// </summary>
        /// <returns></returns>

        public List<ValueNode> GetDevTargetPolNodeList()
        {
            List<ValueNode> polNodeList = new List<ValueNode>();
            foreach(InValueChannel channel in this.InValueChannels)
            {
                if(channel.ValueNode != null)
                {
                    polNodeList.Add(channel.ValueNode);
                }
            }
            return polNodeList;
        }

        /// <summary>
        /// 更新某一报警码状态
        /// </summary>
        /// <param name="alarmCode"></param>
        /// <param name="state"></param>
        protected void UpdateAlarmState(string alarmCode, bool state)
        {
            if(state)
            {
                AlarmManager.GetInstance().AddAlarm(AlarmSourceId, alarmCode);
            }
            else
            {
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, alarmCode);
            }
        }

        /// <summary>
        /// 设备通信是否异常（通过通信异常报警码判断）
        /// </summary>
        /// <returns></returns>
        public bool IsComStateError()
        {
            return AlarmManager.GetInstance().CheckIsAlarm(this.AlarmSourceId, ComErrorAlarmCodeId);
        }

        /// <summary>
        /// 设备是否有通信异常外的其他报警
        /// </summary>
        /// <returns></returns>
        public bool IsAlarmExceptComError()
        {
            return AlarmManager.GetInstance().GetCurrentAlarms().Any(x => x.AlarmSource.id == AlarmSourceId && x.AlarmCode.id != ComErrorAlarmCodeId);
        }

        /// <summary>
        /// 发送数据到设备
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        protected IByteStream SendToDevice(IByteStream cmd)
        {
            return SendToDevice(this.id, cmd);
        }

        /// <summary>
        /// 发送数据到设备
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cmd"></param>
        /// <returns></returns>
        protected static IByteStream SendToDevice(string id, IByteStream cmd)
        {
            return (IByteStream)PortManager.GetInstance().Send(id, cmd);
        }

        #region 子类根据实际情况重写[通用方法]

        /// <summary>
        /// 用于设置设备自定义报警码
        /// </summary>
        public virtual void SetDeviceAlarmList()
        {
            this.DeviceAlarmList.Clear();
            this.DeviceAlarmList.Add(ComErrorAlarmCodeId, "设备与PC通讯异常");
        }

        /// <summary>
        /// 获取设备报警信息
        /// </summary>
        public virtual void GetDeviceAlarm()
        {
        }

        /// <summary>
        /// 获取设备状态信息
        /// </summary>
        public virtual void GetDeviceState()
        {
        }

        /// <summary>
        /// 获取设备测量数据
        /// </summary>
        public virtual void GetDeviceData()
        {
        }

        /// <summary>
        /// 初始化当前设备状态及参数，常用与软件开启或关闭时设置输出状态
        /// </summary>
        public virtual void InitDeviceState()
        {
        }

        /// <summary>
        /// 用于配置设备状态参数查看界面
        /// </summary>
        public virtual UserControl GetDeviceParamUC()
        {
            return null;
        }

        #endregion

        #region 子类根据实际情况重写[数据输出]
        /// <summary>
        /// 设置所有输出的模拟量
        /// </summary>
        public virtual void WriteSimulates()
        {
        }

        /// <summary>
        /// 设置所有输出的开关量
        /// </summary>
        public virtual void WriteSwitchs()
        {
        }

        /// <summary>
        /// 设置某个模拟量输出
        /// </summary>
        /// <param name="varId">变量id</param>
        public virtual void WriteOneSimulate(string varId)
        {

        }

        /// <summary>
        /// 设置某个开关量输出
        /// </summary>
        /// <param name="varId"></param>
        public virtual void WriteOneSwitch(string varId)
        {
        }

        #endregion

        #endregion

        #region ITimerAction 成员

        public virtual void OnTimer(string instruementId)
        {
            //获取设备数据
            this.GetDeviceData();

            //获取设备状态
            this.GetDeviceState();

            //获取设备报警
            this.GetDeviceAlarm();
        }

        #endregion
    }
}