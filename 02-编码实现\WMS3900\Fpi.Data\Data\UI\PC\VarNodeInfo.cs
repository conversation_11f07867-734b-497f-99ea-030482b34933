﻿//==================================================================================================
//类名：     VarNodeInfo   
//创建人:    hongbing_mao
//创建时间:  06/02/2012 16:26:21
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
using System.ComponentModel;
using Fpi.Util.Serializes.CustomSerializer;

namespace Fpi.Data.UI.PC
{
    /// <summary>
    /// 关联变量信息类
    /// </summary>
    public class VarNodeInfo : BaseSerializableObject
    {
        [SerializableFieldAttribute()]
        private string _fullnodeId;
        /// <summary>
        /// 关联节点ID
        /// </summary>
        public string FullNodeId
        {
            get => _fullnodeId;
            set => _fullnodeId = value;
        }
        [SerializableFieldAttribute()]
        private string _unitName;
        /// <summary>
        /// 显示数据使用单位名称
        /// </summary>
        public string UnitName
        {
            get => _unitName;
            set => _unitName = value;
        }

        /// <summary>
        /// 小数位数
        /// </summary>
        [SerializableFieldAttribute()]
        protected int _decNum = 2;
        /// <summary>
        /// 小数位数
        /// </summary>
        [Description("小数位数")]
        public int DecNum
        {
            get => _decNum;
            set => _decNum = value;
        }

        /// <summary>
        /// 构造
        /// </summary>
        public VarNodeInfo()
        {

        }

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="id"></param>
        /// <param name="unit"></param>
        public VarNodeInfo(string id, string unit)
        {
            _fullnodeId = id;
            _unitName = unit;
        }


        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="id"></param>
        /// <param name="unit"></param>
        /// <param name="decNum"></param>
        public VarNodeInfo(string id, string unit, int decNum)
        {
            _fullnodeId = id;
            _unitName = unit;
            _decNum = decNum;
        }
    }
}
