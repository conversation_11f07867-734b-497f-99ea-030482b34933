﻿//==================================================================================================
//类名：     DBNode   
//创建人:    hongbing_mao
//创建时间:  2012-12-21 16:04:03
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================
namespace Fpi.HB.Business.DB
{
    /// <summary>
    /// 数据库因子关联对象
    /// </summary>
    public class DBNode
    {
        public DBNode(string fullnodeid)
        {
            NodeId = fullnodeid;
        }

        public string NodeId { get; set; }

        public string FullNodeID { get; set; }

        public double Value { get; set; } = double.NaN;

        public double Min { get; set; } = double.NaN;

        public double Max { get; set; } = double.NaN;

        public double Avg { get; set; } = double.NaN;

        public double Cou { get; set; } = double.NaN;
    }
}
