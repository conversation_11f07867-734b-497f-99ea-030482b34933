﻿//==================================================================================================
//类名：     SwitchDataChannel   
//创建人:    曹旭
//创建时间:  2012-9-24 15:19:29
//
//修改人    修改时间    修改后版本              修改内容
//
//
//==================================================================================================

using Fpi.Data.Config;
using Fpi.Xml;

namespace Fpi.Devices.Channel
{
    public class SwitchChannel : DataChannel
    {
        public StateNode StateNode { get; set; } = null;

        public override BaseNode Init(System.Xml.XmlNode node)
        {
            base.Init(node);
            this.StateNode = (StateNode)this.GetVarNode();
            return this;
        }
    }
}
