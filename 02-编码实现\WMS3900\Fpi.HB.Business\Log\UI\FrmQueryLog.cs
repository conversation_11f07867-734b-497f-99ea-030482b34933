﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Fpi.DB;
using Fpi.HB.Business.DB;
using Fpi.Log.Config;
using Fpi.UI.Common.PC;
using Fpi.UI.PC;
using Fpi.UI.PC.DockForms;
using Fpi.Util;
using Fpi.Util.StatusForm;
using Sunny.UI;

namespace Fpi.HB.Business.UI
{
    /// <summary>
    /// 系统日志查询
    /// </summary>
    public partial class FrmQueryLog : BaseWindow
    {
        #region 属性、字段

        /// <summary>
        /// 额外的where语句
        /// </summary>
        private string _addWhereSqlStr = string.Empty;

        /// <summary>
        /// 日志类型ID与名称转换字典
        /// </summary>
        private Dictionary<string, string> _logLevelDic = new Dictionary<string, string>();

        #endregion

        #region 构造

        public FrmQueryLog()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmQueryLog_Load(object sender, EventArgs e)
        {
            InitCombox();
            InitialDateTimePicker();
            SetDataGridViewHead();
        }

        /// <summary>
        /// 开始查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                lblPage.Text = @"?/?";

                pagination.PageChanged -= new UIPagination.OnPageChangeEventHandler(pagination_PageChanged);

                // 清空界面数据
                dgvData.Rows.Clear();

                pagination.TotalCount = 0;

                // 检查查询条件
                Check();

                // 查询数据数量
                QueryDataCount();

                // 刷新页面显示
                FlushView();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"查询数据错误：{ex.Message}");
            }
            finally
            {
                pagination.PageChanged += new UIPagination.OnPageChangeEventHandler(pagination_PageChanged);

                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 导出数据至excel
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExcelExport_Click(object sender, EventArgs e)
        {
            try
            {
                pnlTop.Enabled = false;
                if(dgvData.Rows.Count == 0)
                {
                    throw new Exception("当前无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Application.StartupPath + "\\query\\";
                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName =
                    $"{filePath}日志数据_{dtpStartTime.Value:yyyy-MM-dd HH-mm-ss}_{dtpEndTime.Value:yyyy-MM-dd HH-mm-ss}.xlsx";

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    Stopwatch watch = new Stopwatch();
                    watch.Start();

                    FileExportHelper.SaveDataGridViewToExcelFile(dgvData, saveFileDialog.FileName);

                    watch.Stop();
                    float time = watch.ElapsedMilliseconds / 1000f;
                    MessageNotifier.ShowInfo($"导出操作耗时：{time}秒。");

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }

            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError("数据导出错误:" + ex.Message);
            }
            finally
            {
                // 交出CPU控制权，处理消息队列中的其他消息
                Application.DoEvents();
                pnlTop.Enabled = true;
            }
        }

        /// <summary>
        /// 点击翻页
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="pagingSource"></param>
        /// <param name="curPage">第几页，和界面对应，从1开始，取数据要用pageIndex - 1</param>
        /// <param name="onePageCount">单页数据量，也就是PageSize值</param>
        private void pagination_PageChanged(object sender, object pagingSource, int curPage, int onePageCount)
        {
            try
            {
                this.lblPage.Text = $@"{curPage}/{pagination.PageCount}";

                // 显示等待界面
                UIFormServiceHelper.ShowWaitForm(this, "数据查询中，请稍候...");
                // 线程稍微停一下，否则下面执行很快时等待界面关闭不了。推测是windows消息通信机制问题。
                Thread.Sleep(50);

                // 清空原数据
                dgvData.Rows.Clear();

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this);

                string strSql = $"select * from {DBConfig.LOG_DATA}  where datatime>='{dtpStartTime.Value.ToString(DBConfig.DATETIME_FORMAT)}' and datatime<='{dtpEndTime.Value.ToString(DBConfig.DATETIME_FORMAT)}' {_addWhereSqlStr} order by datatime asc limit {onePageCount} offset {onePageCount * (curPage - 1)}";
                IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql);

                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this);

                if(reader != null)
                {
                    FillDataGridViewData(reader, curPage, onePageCount);
                }
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(this);
            }
        }

        private void FillDataGridViewData(IDataReader reader, int curPage, int onePageCount)
        {
            try
            {
                // 打开进度条界面
                FpiStatusFormService.ShowStatusForm(onePageCount, "数据渲染中，请稍候...");

                // 进度条序号值
                int currentStep = 1;
                // 进度条满值
                int currentPageCount = onePageCount;
                // 若当前是最后一页，则数据不足onePageCount
                if(curPage * onePageCount > pagination.TotalCount)
                {
                    currentPageCount = pagination.TotalCount - (curPage - 1) * onePageCount;
                }

                // 表格中数据序号值
                int rowIndex = (curPage - 1) * onePageCount + 1;

                while(reader.Read())
                {
                    int index = dgvData.Rows.Add();
                    DataGridViewRow dr = dgvData.Rows[index];
                    dr.Cells[0].Value = rowIndex++;

                    dr.Cells[1].Value = Convert.ToDateTime(reader["datatime"]).ToString(DBConfig.DATETIME_FORMAT);

                    var level = Convert.ToString(reader["level"]);
                    if(_logLevelDic.ContainsKey(level))
                    {
                        dr.Cells[2].Value = _logLevelDic[level];
                    }
                    else
                    {
                        dr.Cells[2].Value = level;
                    }

                    dr.Cells[3].Value = Convert.ToString(reader["type"]);

                    string message = Convert.ToString(reader["message"]);
                    dr.Cells[4].Value = message;
                    dr.Cells[4].ToolTipText = message;
                    dr.Cells[4].Style.Alignment = DataGridViewContentAlignment.MiddleLeft;
                }

                FpiStatusFormService.SetDescription($"数据渲染中[{currentStep++}/{currentPageCount}]......");
                FpiStatusFormService.StepIt();
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏进度条界面
                FpiStatusFormService.HideStatusForm();
            }
        }

        #endregion

        #region 私有方法

        #region 数据查询

        /// <summary>
        /// 检查查询条件输入是否合格
        /// </summary>
        private void Check()
        {
            if(dtpStartTime.Value > dtpEndTime.Value)
            {
                throw new Exception("起始时间应不可大于结束时间！");
            }

            if(string.IsNullOrEmpty(txtRecordCount.Text))
            {
                throw new Exception("每页记录数输入不可为空！");
            }

            if(!int.TryParse(txtRecordCount.Text, out int onePageCount))
            {
                throw new Exception("每页记录数输入异常，应为大于1的整数！");
            }

            if(onePageCount < 1)
            {
                throw new Exception("每页记录数值不可小于1！");
            }

            pagination.PageSize = onePageCount;

            var andWhereSql = new StringBuilder();
            // 日志等级过滤
            if(!string.IsNullOrEmpty(cmbLogLevel.Text))
            {
                foreach(KeyValuePair<string, string> item in _logLevelDic)
                {
                    if(item.Value == cmbLogLevel.Text)
                    {
                        andWhereSql.Append($"and level like '%{item.Key}%' ");
                        break;
                    }
                }
            }
            // 日志类型过滤
            if(!string.IsNullOrEmpty(txtLogType.Text))
            {
                andWhereSql.Append($"and type like '%{txtLogType.Text}%' ");
            }
            // 日志内容过滤
            if(!string.IsNullOrEmpty(txtLogMessage.Text))
            {
                andWhereSql.Append($"and message like '%{txtLogMessage.Text}%' ");
            }
            _addWhereSqlStr = andWhereSql.ToString();
        }

        /// <summary>
        /// 更新数据内容
        /// </summary>
        private void QueryDataCount()
        {
            int recordCount = 0;
            try
            {
                string strSql = $"select count(*) from {DBConfig.LOG_DATA} where datatime>='{dtpStartTime.Value.ToString(DBConfig.DATETIME_FORMAT)}' and datatime<='{dtpEndTime.Value.ToString(DBConfig.DATETIME_FORMAT)}' {_addWhereSqlStr} order by datatime asc";
                recordCount = DbAccess.QueryRecordCount(strSql);
            }
            catch
            {
                throw new Exception("数据库连接异常!");
            }

            if(recordCount > 0)
            {
                pagination.TotalCount = recordCount;
            }
            else
            {
                throw new Exception("无当前条件下的查询数据!");
            }
        }

        /// <summary>
        /// 刷新页面显示
        /// </summary>
        private void FlushView()
        {
            if(pagination.ActivePage != 1)
            {
                pagination.ActivePage = 1;
            }
            else
            {
                pagination_PageChanged(null, null, 1, pagination.PageSize);
            }
        }

        #endregion

        #region 控件

        /// <summary>
        /// 初始化下拉控件显示
        /// </summary>
        private void InitCombox()
        {
            cmbLogLevel.Items.Add(string.Empty);
            foreach(LogLevel level in LogManager.GetInstance().LogLevels)
            {
                if(!_logLevelDic.ContainsKey(level.id))
                {
                    _logLevelDic.Add(level.id, level.name);
                    cmbLogLevel.Items.Add(level.name);
                }
            }
        }

        /// <summary>
        /// 初始化表头显示
        /// </summary>
        private void SetDataGridViewHead()
        {
            dgvData.ClearColumns();

            var col = dgvData.AddColumn("序号", "num");
            col.FillWeight = 50;
            col = dgvData.AddColumn("日志产生时间", "datatime");
            col.FillWeight = 100;
            col = dgvData.AddColumn("日志等级", "level");
            col.FillWeight = 50;
            col = dgvData.AddColumn("日志类型", "type");
            col.FillWeight = 150;
            col = dgvData.AddColumn("日志内容", "message");
            col.FillWeight = 600;
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitialDateTimePicker()
        {
            DateTime now = DateTime.Now;
            var startTime = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0);
            dtpStartTime.Value = startTime;
            dtpEndTime.Value = startTime.AddDays(1);
        }

        #endregion

        #endregion
    }
}
