﻿namespace Fpi.Communication.Ports.UI.PC
{
    partial class UC_GroupingPort
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.nuWinSize = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.nuFrmSize = new System.Windows.Forms.NumericUpDown();
            ((System.ComponentModel.ISupportInitialize)(this.nuWinSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuFrmSize)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(35, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "WindowSize";
            // 
            // nuWinSize
            // 
            this.nuWinSize.Location = new System.Drawing.Point(112, 33);
            this.nuWinSize.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nuWinSize.Name = "nuWinSize";
            this.nuWinSize.Size = new System.Drawing.Size(244, 21);
            this.nuWinSize.TabIndex = 1;
            this.nuWinSize.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(35, 71);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "FrameSize";
            // 
            // nuFrmSize
            // 
            this.nuFrmSize.Location = new System.Drawing.Point(112, 67);
            this.nuFrmSize.Maximum = new decimal(new int[] {
            8192,
            0,
            0,
            0});
            this.nuFrmSize.Minimum = new decimal(new int[] {
            128,
            0,
            0,
            0});
            this.nuFrmSize.Name = "nuFrmSize";
            this.nuFrmSize.Size = new System.Drawing.Size(244, 21);
            this.nuFrmSize.TabIndex = 1;
            this.nuFrmSize.Value = new decimal(new int[] {
            128,
            0,
            0,
            0});
            // 
            // UC_GroupingPort
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.nuFrmSize);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.nuWinSize);
            this.Controls.Add(this.label1);
            this.Name = "UC_GroupingPort";
            this.Size = new System.Drawing.Size(391, 120);
            ((System.ComponentModel.ISupportInitialize)(this.nuWinSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuFrmSize)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown nuWinSize;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown nuFrmSize;
    }
}
