﻿namespace Fpi.Data.ExpParser
{
    public enum EKeyword
    {
        IF,
        CASE,
        AND,
        OR,
        NOT,
        TRUE,
        FALSE,
        ToString,
        ToDateTime,
        ToInt,
        ToDouble,
        Len,
        NowDate,
        GetNodeValue,
        GetNodeIntValue,
        GetNodeBoolValue,
        GetAllNodeBoolValueOR,
        GetAlarmNodeBoolValue,
        DoubleNaN,
        IsAlarmNode,
        IsNodeCycleValueAlarm,
        Invoke
    }
}

