using System;
using Fpi.Data.Interfaces;
using Fpi.Util.Reflection;

namespace Fpi.Data.Config
{
    public delegate void ValueChange<PERSON>andler(VarNode varNode, object newValue);
    public delegate void AlarmNotifyHandler(VarNode varNode, object value);


    public class DataHelper
    {
        static public Type[] GetNodeTypes()
        {
            Type[] types = ReflectionHelper.GetChildTypes(typeof(VarNode));
            Type[] res = new Type[types.Length + 1];
            res[0] = typeof(VarNode);
            Array.Copy(types, 0, res, 1, types.Length);
            return res;
        }
        static public INodeConfigView GetConfigView(Type nodeType)
        {
            VarNode var = ReflectionHelper.CreateInstance(nodeType) as VarNode;
            return var.GetConfigView();
        }
    }
}
