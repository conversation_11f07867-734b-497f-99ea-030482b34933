﻿namespace Fpi.Communication.UI.PC.PipeConfig
{
    partial class FrmPipeEdit
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmPipeEdit));
            this.pnlFunc = new System.Windows.Forms.Panel();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.pnlMain = new System.Windows.Forms.Panel();
            this.chkReconnect = new System.Windows.Forms.CheckBox();
            this.chkLst = new System.Windows.Forms.CheckedListBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txtId = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnPort = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.btnProtocol = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.btnBus = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtName = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.panel2 = new System.Windows.Forms.Panel();
            this.chkValid = new System.Windows.Forms.CheckBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.cmbBus = new Sunny.UI.UIComboBox();
            this.cmbProtocol = new Sunny.UI.UIComboBox();
            this.pnlFunc.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.pnlMain.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlFunc
            // 
            this.pnlFunc.Controls.Add(this.btnOK);
            this.pnlFunc.Controls.Add(this.btnCancel);
            resources.ApplyResources(this.pnlFunc, "pnlFunc");
            this.pnlFunc.Name = "pnlFunc";
            // 
            // btnOK
            // 
            resources.ApplyResources(this.btnOK, "btnOK");
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Name = "btnOK";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            resources.ApplyResources(this.btnCancel, "btnCancel");
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.panel1);
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.pnlMain);
            this.panel1.Controls.Add(this.panel2);
            resources.ApplyResources(this.panel1, "panel1");
            this.panel1.Name = "panel1";
            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.cmbProtocol);
            this.pnlMain.Controls.Add(this.cmbBus);
            this.pnlMain.Controls.Add(this.chkReconnect);
            this.pnlMain.Controls.Add(this.chkLst);
            this.pnlMain.Controls.Add(this.label1);
            this.pnlMain.Controls.Add(this.txtId);
            this.pnlMain.Controls.Add(this.label3);
            this.pnlMain.Controls.Add(this.btnPort);
            this.pnlMain.Controls.Add(this.label2);
            this.pnlMain.Controls.Add(this.btnProtocol);
            this.pnlMain.Controls.Add(this.label4);
            this.pnlMain.Controls.Add(this.btnBus);
            this.pnlMain.Controls.Add(this.label5);
            this.pnlMain.Controls.Add(this.txtName);
            resources.ApplyResources(this.pnlMain, "pnlMain");
            this.pnlMain.Name = "pnlMain";
            // 
            // chkReconnect
            // 
            resources.ApplyResources(this.chkReconnect, "chkReconnect");
            this.chkReconnect.Checked = true;
            this.chkReconnect.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkReconnect.Name = "chkReconnect";
            this.chkReconnect.UseVisualStyleBackColor = true;
            // 
            // chkLst
            // 
            this.chkLst.CheckOnClick = true;
            this.chkLst.FormattingEnabled = true;
            resources.ApplyResources(this.chkLst, "chkLst");
            this.chkLst.Name = "chkLst";
            this.chkLst.Sorted = true;
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // txtId
            // 
            this.txtId.CanEmpty = true;
            this.txtId.DigitLength = 2;
            this.txtId.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtId.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtId.IsValidCheck = false;
            resources.ApplyResources(this.txtId, "txtId");
            this.txtId.MaxValue = null;
            this.txtId.MinValue = null;
            this.txtId.Name = "txtId";
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // btnPort
            // 
            resources.ApplyResources(this.btnPort, "btnPort");
            this.btnPort.Image = global::Fpi.Communication.Properties.Resources.Edit;
            this.btnPort.Name = "btnPort";
            this.toolTip1.SetToolTip(this.btnPort, resources.GetString("btnPort.ToolTip"));
            this.btnPort.UseVisualStyleBackColor = true;
            this.btnPort.Click += new System.EventHandler(this.btnPort_Click);
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // btnProtocol
            // 
            resources.ApplyResources(this.btnProtocol, "btnProtocol");
            this.btnProtocol.Image = global::Fpi.Communication.Properties.Resources.Modify;
            this.btnProtocol.Name = "btnProtocol";
            this.toolTip1.SetToolTip(this.btnProtocol, resources.GetString("btnProtocol.ToolTip"));
            this.btnProtocol.UseVisualStyleBackColor = true;
            this.btnProtocol.Click += new System.EventHandler(this.btnProtocol_Click);
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // btnBus
            // 
            resources.ApplyResources(this.btnBus, "btnBus");
            this.btnBus.Image = global::Fpi.Communication.Properties.Resources.Modify;
            this.btnBus.Name = "btnBus";
            this.toolTip1.SetToolTip(this.btnBus, resources.GetString("btnBus.ToolTip"));
            this.btnBus.UseVisualStyleBackColor = true;
            this.btnBus.Click += new System.EventHandler(this.btnBus_Click);
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // txtName
            // 
            this.txtName.CanEmpty = true;
            this.txtName.DigitLength = 2;
            this.txtName.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtName.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtName.IsValidCheck = false;
            resources.ApplyResources(this.txtName, "txtName");
            this.txtName.MaxValue = null;
            this.txtName.MinValue = null;
            this.txtName.Name = "txtName";
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.chkValid);
            resources.ApplyResources(this.panel2, "panel2");
            this.panel2.ForeColor = System.Drawing.Color.DarkBlue;
            this.panel2.Name = "panel2";
            // 
            // chkValid
            // 
            resources.ApplyResources(this.chkValid, "chkValid");
            this.chkValid.Checked = true;
            this.chkValid.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkValid.ForeColor = System.Drawing.SystemColors.Highlight;
            this.chkValid.Name = "chkValid";
            this.chkValid.UseVisualStyleBackColor = true;
            this.chkValid.CheckedChanged += new System.EventHandler(this.chkValid_CheckedChanged);
            // 
            // cmbBus
            // 
            this.cmbBus.DataSource = null;
            this.cmbBus.DropDownAutoWidth = true;
            this.cmbBus.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbBus.FillColor = System.Drawing.Color.White;
            resources.ApplyResources(this.cmbBus, "cmbBus");
            this.cmbBus.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbBus.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbBus.Name = "cmbBus";
            this.cmbBus.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(130)))), ((int)(((byte)(135)))), ((int)(((byte)(144)))));
            this.cmbBus.SymbolSize = 24;
            this.cmbBus.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbBus.Watermark = "";
            this.cmbBus.SelectedIndexChanged += new System.EventHandler(this.cmbBus_SelectedIndexChanged);
            // 
            // cmbProtocol
            // 
            this.cmbProtocol.DataSource = null;
            this.cmbProtocol.DropDownAutoWidth = true;
            this.cmbProtocol.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbProtocol.FillColor = System.Drawing.Color.White;
            resources.ApplyResources(this.cmbProtocol, "cmbProtocol");
            this.cmbProtocol.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbProtocol.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbProtocol.Name = "cmbProtocol";
            this.cmbProtocol.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(130)))), ((int)(((byte)(135)))), ((int)(((byte)(144)))));
            this.cmbProtocol.SymbolSize = 24;
            this.cmbProtocol.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbProtocol.Watermark = "";
            this.cmbProtocol.SelectedIndexChanged += new System.EventHandler(this.cmbProtocol_SelectedIndexChanged);
            // 
            // FrmPipeEdit
            // 
            this.AcceptButton = this.btnOK;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.pnlFunc);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmPipeEdit";
            this.ShowInTaskbar = false;
            this.Load += new System.EventHandler(this.FormPipeEdit_Load);
            this.pnlFunc.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.pnlMain.ResumeLayout(false);
            this.pnlMain.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel pnlFunc;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Panel panel1;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtId;
        private System.Windows.Forms.Label label1;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtName;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Button btnBus;
        private System.Windows.Forms.Button btnProtocol;
        private System.Windows.Forms.Button btnPort;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.CheckBox chkValid;
        private System.Windows.Forms.CheckedListBox chkLst;
        private System.Windows.Forms.CheckBox chkReconnect;
        private Sunny.UI.UIComboBox cmbBus;
        private Sunny.UI.UIComboBox cmbProtocol;
    }
}