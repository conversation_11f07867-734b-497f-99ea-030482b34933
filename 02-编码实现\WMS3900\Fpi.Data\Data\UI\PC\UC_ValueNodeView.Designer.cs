﻿namespace Fpi.Data.UI.PC
{
    partial class UC_ValueNodeView
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(UC_ValueNodeView));
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.pageBase = new System.Windows.Forms.TabPage();
            this.pageLocal = new System.Windows.Forms.TabPage();
            this.label7 = new System.Windows.Forms.Label();
            this.txtQualiyClass = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.btnFormula = new System.Windows.Forms.Button();
            this.cmbLimit = new System.Windows.Forms.ComboBox();
            this.cmbScope = new System.Windows.Forms.ComboBox();
            this.nuSmooth = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.txtFormula = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.pageUnit = new System.Windows.Forms.TabPage();
            this.lvUnit = new System.Windows.Forms.ListView();
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.panel8 = new System.Windows.Forms.Panel();
            this.cmbSelfUnit = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnAddUnit = new System.Windows.Forms.Button();
            this.pageScope = new System.Windows.Forms.TabPage();
            this.lvScope = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.panel9 = new System.Windows.Forms.Panel();
            this.btnDelScope = new System.Windows.Forms.Button();
            this.btnAddScope = new System.Windows.Forms.Button();
            this.btnUpdateScope = new System.Windows.Forms.Button();
            this.pageLimit = new System.Windows.Forms.TabPage();
            this.lvLimit = new System.Windows.Forms.ListView();
            this.columnHeader9 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.panel10 = new System.Windows.Forms.Panel();
            this.btnDelLimit = new System.Windows.Forms.Button();
            this.btnAddLimit = new System.Windows.Forms.Button();
            this.btnUpdateLimit = new System.Windows.Forms.Button();
            this.pageFixed = new System.Windows.Forms.TabPage();
            this.label12 = new System.Windows.Forms.Label();
            this.gbFixed = new System.Windows.Forms.GroupBox();
            this.nuFixedValue = new System.Windows.Forms.NumericUpDown();
            this.nuFixedRange = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.chkFixed = new System.Windows.Forms.CheckBox();
            this.pageIndice = new System.Windows.Forms.TabPage();
            this.lvIndice = new System.Windows.Forms.ListView();
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.pnlIndice = new System.Windows.Forms.Panel();
            this.btnDelIndice = new System.Windows.Forms.Button();
            this.btnAddIndice = new System.Windows.Forms.Button();
            this.btnEditIndice = new System.Windows.Forms.Button();
            this.txtDetectionLimit = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.txtGBCode = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.txtMolwt = new Fpi.UI.Common.PC.Controls.FpiTextBox();
            this.tabControl1.SuspendLayout();
            this.pageLocal.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuSmooth)).BeginInit();
            this.pageUnit.SuspendLayout();
            this.panel8.SuspendLayout();
            this.pageScope.SuspendLayout();
            this.panel9.SuspendLayout();
            this.pageLimit.SuspendLayout();
            this.panel10.SuspendLayout();
            this.pageFixed.SuspendLayout();
            this.gbFixed.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuFixedValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuFixedRange)).BeginInit();
            this.pageIndice.SuspendLayout();
            this.pnlIndice.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.pageBase);
            this.tabControl1.Controls.Add(this.pageLocal);
            this.tabControl1.Controls.Add(this.pageUnit);
            this.tabControl1.Controls.Add(this.pageScope);
            this.tabControl1.Controls.Add(this.pageLimit);
            this.tabControl1.Controls.Add(this.pageFixed);
            this.tabControl1.Controls.Add(this.pageIndice);
            resources.ApplyResources(this.tabControl1, "tabControl1");
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            // 
            // pageBase
            // 
            resources.ApplyResources(this.pageBase, "pageBase");
            this.pageBase.Name = "pageBase";
            this.pageBase.UseVisualStyleBackColor = true;
            // 
            // pageLocal
            // 
            this.pageLocal.Controls.Add(this.txtDetectionLimit);
            this.pageLocal.Controls.Add(this.label7);
            this.pageLocal.Controls.Add(this.txtQualiyClass);
            this.pageLocal.Controls.Add(this.label4);
            this.pageLocal.Controls.Add(this.txtGBCode);
            this.pageLocal.Controls.Add(this.txtMolwt);
            this.pageLocal.Controls.Add(this.label2);
            this.pageLocal.Controls.Add(this.label1);
            this.pageLocal.Controls.Add(this.btnFormula);
            this.pageLocal.Controls.Add(this.cmbLimit);
            this.pageLocal.Controls.Add(this.cmbScope);
            this.pageLocal.Controls.Add(this.nuSmooth);
            this.pageLocal.Controls.Add(this.label9);
            this.pageLocal.Controls.Add(this.label8);
            this.pageLocal.Controls.Add(this.txtFormula);
            this.pageLocal.Controls.Add(this.label5);
            this.pageLocal.Controls.Add(this.label6);
            resources.ApplyResources(this.pageLocal, "pageLocal");
            this.pageLocal.Name = "pageLocal";
            this.pageLocal.UseVisualStyleBackColor = true;
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // txtQualiyClass
            // 
            resources.ApplyResources(this.txtQualiyClass, "txtQualiyClass");
            this.txtQualiyClass.Name = "txtQualiyClass";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // btnFormula
            // 
            resources.ApplyResources(this.btnFormula, "btnFormula");
            this.btnFormula.Name = "btnFormula";
            this.btnFormula.UseVisualStyleBackColor = true;
            this.btnFormula.Click += new System.EventHandler(this.btnFormula_Click);
            // 
            // cmbLimit
            // 
            this.cmbLimit.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbLimit.FormattingEnabled = true;
            resources.ApplyResources(this.cmbLimit, "cmbLimit");
            this.cmbLimit.Name = "cmbLimit";
            // 
            // cmbScope
            // 
            this.cmbScope.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbScope.FormattingEnabled = true;
            resources.ApplyResources(this.cmbScope, "cmbScope");
            this.cmbScope.Name = "cmbScope";
            // 
            // nuSmooth
            // 
            resources.ApplyResources(this.nuSmooth, "nuSmooth");
            this.nuSmooth.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.nuSmooth.Name = "nuSmooth";
            this.nuSmooth.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label9
            // 
            resources.ApplyResources(this.label9, "label9");
            this.label9.Name = "label9";
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // txtFormula
            // 
            resources.ApplyResources(this.txtFormula, "txtFormula");
            this.txtFormula.Name = "txtFormula";
            this.txtFormula.TextChanged += new System.EventHandler(this.txtFormula_TextChanged);
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // pageUnit
            // 
            this.pageUnit.Controls.Add(this.lvUnit);
            this.pageUnit.Controls.Add(this.panel8);
            resources.ApplyResources(this.pageUnit, "pageUnit");
            this.pageUnit.Name = "pageUnit";
            this.pageUnit.UseVisualStyleBackColor = true;
            this.pageUnit.Leave += new System.EventHandler(this.pageUnit_Leave);
            // 
            // lvUnit
            // 
            this.lvUnit.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader6});
            this.lvUnit.FullRowSelect = true;
            resources.ApplyResources(this.lvUnit, "lvUnit");
            this.lvUnit.MultiSelect = false;
            this.lvUnit.Name = "lvUnit";
            this.lvUnit.UseCompatibleStateImageBehavior = false;
            this.lvUnit.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader6
            // 
            resources.ApplyResources(this.columnHeader6, "columnHeader6");
            // 
            // panel8
            // 
            this.panel8.Controls.Add(this.cmbSelfUnit);
            this.panel8.Controls.Add(this.label3);
            this.panel8.Controls.Add(this.btnAddUnit);
            resources.ApplyResources(this.panel8, "panel8");
            this.panel8.Name = "panel8";
            // 
            // cmbSelfUnit
            // 
            this.cmbSelfUnit.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbSelfUnit.FormattingEnabled = true;
            resources.ApplyResources(this.cmbSelfUnit, "cmbSelfUnit");
            this.cmbSelfUnit.Name = "cmbSelfUnit";
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // btnAddUnit
            // 
            resources.ApplyResources(this.btnAddUnit, "btnAddUnit");
            this.btnAddUnit.Image = global::Fpi.Properties.Resources.Add;
            this.btnAddUnit.Name = "btnAddUnit";
            this.btnAddUnit.UseVisualStyleBackColor = true;
            this.btnAddUnit.Click += new System.EventHandler(this.btnAddUnit_Click);
            // 
            // pageScope
            // 
            this.pageScope.Controls.Add(this.lvScope);
            this.pageScope.Controls.Add(this.panel9);
            resources.ApplyResources(this.pageScope, "pageScope");
            this.pageScope.Name = "pageScope";
            this.pageScope.UseVisualStyleBackColor = true;
            this.pageScope.Leave += new System.EventHandler(this.pageScope_Leave);
            // 
            // lvScope
            // 
            resources.ApplyResources(this.lvScope, "lvScope");
            this.lvScope.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3,
            this.columnHeader4});
            this.lvScope.FullRowSelect = true;
            this.lvScope.Name = "lvScope";
            this.lvScope.UseCompatibleStateImageBehavior = false;
            this.lvScope.View = System.Windows.Forms.View.Details;
            this.lvScope.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvScope_MouseDoubleClick);
            // 
            // columnHeader1
            // 
            resources.ApplyResources(this.columnHeader1, "columnHeader1");
            // 
            // columnHeader2
            // 
            resources.ApplyResources(this.columnHeader2, "columnHeader2");
            // 
            // columnHeader3
            // 
            resources.ApplyResources(this.columnHeader3, "columnHeader3");
            // 
            // columnHeader4
            // 
            resources.ApplyResources(this.columnHeader4, "columnHeader4");
            // 
            // panel9
            // 
            this.panel9.Controls.Add(this.btnDelScope);
            this.panel9.Controls.Add(this.btnAddScope);
            this.panel9.Controls.Add(this.btnUpdateScope);
            resources.ApplyResources(this.panel9, "panel9");
            this.panel9.Name = "panel9";
            // 
            // btnDelScope
            // 
            resources.ApplyResources(this.btnDelScope, "btnDelScope");
            this.btnDelScope.Image = global::Fpi.Properties.Resources.Remove;
            this.btnDelScope.Name = "btnDelScope";
            this.btnDelScope.UseVisualStyleBackColor = true;
            this.btnDelScope.Click += new System.EventHandler(this.btnDelScope_Click);
            // 
            // btnAddScope
            // 
            resources.ApplyResources(this.btnAddScope, "btnAddScope");
            this.btnAddScope.Image = global::Fpi.Properties.Resources.Add;
            this.btnAddScope.Name = "btnAddScope";
            this.btnAddScope.UseVisualStyleBackColor = true;
            this.btnAddScope.Click += new System.EventHandler(this.btnAddScope_Click);
            // 
            // btnUpdateScope
            // 
            resources.ApplyResources(this.btnUpdateScope, "btnUpdateScope");
            this.btnUpdateScope.Image = global::Fpi.Properties.Resources.Edit;
            this.btnUpdateScope.Name = "btnUpdateScope";
            this.btnUpdateScope.UseVisualStyleBackColor = true;
            this.btnUpdateScope.Click += new System.EventHandler(this.btnUpdateScope_Click);
            // 
            // pageLimit
            // 
            this.pageLimit.Controls.Add(this.lvLimit);
            this.pageLimit.Controls.Add(this.panel10);
            resources.ApplyResources(this.pageLimit, "pageLimit");
            this.pageLimit.Name = "pageLimit";
            this.pageLimit.UseVisualStyleBackColor = true;
            this.pageLimit.Leave += new System.EventHandler(this.pageLimit_Leave);
            // 
            // lvLimit
            // 
            this.lvLimit.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader9,
            this.columnHeader10,
            this.columnHeader11,
            this.columnHeader12});
            resources.ApplyResources(this.lvLimit, "lvLimit");
            this.lvLimit.FullRowSelect = true;
            this.lvLimit.Name = "lvLimit";
            this.lvLimit.UseCompatibleStateImageBehavior = false;
            this.lvLimit.View = System.Windows.Forms.View.Details;
            this.lvLimit.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lvLimit_MouseDoubleClick);
            // 
            // columnHeader9
            // 
            resources.ApplyResources(this.columnHeader9, "columnHeader9");
            // 
            // columnHeader10
            // 
            resources.ApplyResources(this.columnHeader10, "columnHeader10");
            // 
            // columnHeader11
            // 
            resources.ApplyResources(this.columnHeader11, "columnHeader11");
            // 
            // columnHeader12
            // 
            resources.ApplyResources(this.columnHeader12, "columnHeader12");
            // 
            // panel10
            // 
            this.panel10.Controls.Add(this.btnDelLimit);
            this.panel10.Controls.Add(this.btnAddLimit);
            this.panel10.Controls.Add(this.btnUpdateLimit);
            resources.ApplyResources(this.panel10, "panel10");
            this.panel10.Name = "panel10";
            // 
            // btnDelLimit
            // 
            resources.ApplyResources(this.btnDelLimit, "btnDelLimit");
            this.btnDelLimit.Image = global::Fpi.Properties.Resources.Remove;
            this.btnDelLimit.Name = "btnDelLimit";
            this.btnDelLimit.UseVisualStyleBackColor = true;
            this.btnDelLimit.Click += new System.EventHandler(this.btnDelLimit_Click);
            // 
            // btnAddLimit
            // 
            resources.ApplyResources(this.btnAddLimit, "btnAddLimit");
            this.btnAddLimit.Image = global::Fpi.Properties.Resources.Add;
            this.btnAddLimit.Name = "btnAddLimit";
            this.btnAddLimit.UseVisualStyleBackColor = true;
            this.btnAddLimit.Click += new System.EventHandler(this.btnAddLimit_Click);
            // 
            // btnUpdateLimit
            // 
            resources.ApplyResources(this.btnUpdateLimit, "btnUpdateLimit");
            this.btnUpdateLimit.Image = global::Fpi.Properties.Resources.Edit;
            this.btnUpdateLimit.Name = "btnUpdateLimit";
            this.btnUpdateLimit.UseVisualStyleBackColor = true;
            this.btnUpdateLimit.Click += new System.EventHandler(this.btnUpdateLimit_Click);
            // 
            // pageFixed
            // 
            this.pageFixed.Controls.Add(this.label12);
            this.pageFixed.Controls.Add(this.gbFixed);
            this.pageFixed.Controls.Add(this.chkFixed);
            resources.ApplyResources(this.pageFixed, "pageFixed");
            this.pageFixed.Name = "pageFixed";
            this.pageFixed.UseVisualStyleBackColor = true;
            // 
            // label12
            // 
            resources.ApplyResources(this.label12, "label12");
            this.label12.Name = "label12";
            // 
            // gbFixed
            // 
            this.gbFixed.Controls.Add(this.nuFixedValue);
            this.gbFixed.Controls.Add(this.nuFixedRange);
            this.gbFixed.Controls.Add(this.label11);
            this.gbFixed.Controls.Add(this.label10);
            resources.ApplyResources(this.gbFixed, "gbFixed");
            this.gbFixed.Name = "gbFixed";
            this.gbFixed.TabStop = false;
            // 
            // nuFixedValue
            // 
            this.nuFixedValue.DecimalPlaces = 2;
            resources.ApplyResources(this.nuFixedValue, "nuFixedValue");
            this.nuFixedValue.Maximum = new decimal(new int[] {
            999999,
            0,
            0,
            0});
            this.nuFixedValue.Minimum = new decimal(new int[] {
            999999,
            0,
            0,
            -2147483648});
            this.nuFixedValue.Name = "nuFixedValue";
            // 
            // nuFixedRange
            // 
            this.nuFixedRange.DecimalPlaces = 2;
            resources.ApplyResources(this.nuFixedRange, "nuFixedRange");
            this.nuFixedRange.Maximum = new decimal(new int[] {
            999999,
            0,
            0,
            0});
            this.nuFixedRange.Name = "nuFixedRange";
            // 
            // label11
            // 
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // label10
            // 
            resources.ApplyResources(this.label10, "label10");
            this.label10.Name = "label10";
            // 
            // chkFixed
            // 
            resources.ApplyResources(this.chkFixed, "chkFixed");
            this.chkFixed.ForeColor = System.Drawing.Color.Blue;
            this.chkFixed.Name = "chkFixed";
            this.chkFixed.UseVisualStyleBackColor = true;
            this.chkFixed.CheckedChanged += new System.EventHandler(this.chkFixed_CheckedChanged);
            // 
            // pageIndice
            // 
            this.pageIndice.Controls.Add(this.lvIndice);
            this.pageIndice.Controls.Add(this.pnlIndice);
            resources.ApplyResources(this.pageIndice, "pageIndice");
            this.pageIndice.Name = "pageIndice";
            this.pageIndice.UseVisualStyleBackColor = true;
            // 
            // lvIndice
            // 
            this.lvIndice.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader5,
            this.columnHeader7,
            this.columnHeader8,
            this.columnHeader13});
            resources.ApplyResources(this.lvIndice, "lvIndice");
            this.lvIndice.FullRowSelect = true;
            this.lvIndice.Name = "lvIndice";
            this.lvIndice.UseCompatibleStateImageBehavior = false;
            this.lvIndice.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader5
            // 
            resources.ApplyResources(this.columnHeader5, "columnHeader5");
            // 
            // columnHeader7
            // 
            resources.ApplyResources(this.columnHeader7, "columnHeader7");
            // 
            // columnHeader8
            // 
            resources.ApplyResources(this.columnHeader8, "columnHeader8");
            // 
            // columnHeader13
            // 
            resources.ApplyResources(this.columnHeader13, "columnHeader13");
            // 
            // pnlIndice
            // 
            this.pnlIndice.Controls.Add(this.btnDelIndice);
            this.pnlIndice.Controls.Add(this.btnAddIndice);
            this.pnlIndice.Controls.Add(this.btnEditIndice);
            resources.ApplyResources(this.pnlIndice, "pnlIndice");
            this.pnlIndice.Name = "pnlIndice";
            // 
            // btnDelIndice
            // 
            resources.ApplyResources(this.btnDelIndice, "btnDelIndice");
            this.btnDelIndice.Image = global::Fpi.Properties.Resources.Remove;
            this.btnDelIndice.Name = "btnDelIndice";
            this.btnDelIndice.UseVisualStyleBackColor = true;
            // 
            // btnAddIndice
            // 
            resources.ApplyResources(this.btnAddIndice, "btnAddIndice");
            this.btnAddIndice.Image = global::Fpi.Properties.Resources.Add;
            this.btnAddIndice.Name = "btnAddIndice";
            this.btnAddIndice.UseVisualStyleBackColor = true;
            // 
            // btnEditIndice
            // 
            resources.ApplyResources(this.btnEditIndice, "btnEditIndice");
            this.btnEditIndice.Image = global::Fpi.Properties.Resources.Edit;
            this.btnEditIndice.Name = "btnEditIndice";
            this.btnEditIndice.UseVisualStyleBackColor = true;
            // 
            // txtDetectionLimit
            // 
            this.txtDetectionLimit.CanEmpty = true;
            this.txtDetectionLimit.DigitLength = 6;
            this.txtDetectionLimit.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtDetectionLimit.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtDetectionLimit.IsValidCheck = false;
            resources.ApplyResources(this.txtDetectionLimit, "txtDetectionLimit");
            this.txtDetectionLimit.MaxValue = null;
            this.txtDetectionLimit.MinValue = null;
            this.txtDetectionLimit.Name = "txtDetectionLimit";
            // 
            // txtGBCode
            // 
            this.txtGBCode.CanEmpty = true;
            this.txtGBCode.DigitLength = 3;
            this.txtGBCode.InputType = Fpi.UI.Common.PC.Controls.TextInputType.String;
            this.txtGBCode.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtGBCode.IsValidCheck = false;
            resources.ApplyResources(this.txtGBCode, "txtGBCode");
            this.txtGBCode.MaxValue = null;
            this.txtGBCode.MinValue = null;
            this.txtGBCode.Name = "txtGBCode";
            // 
            // txtMolwt
            // 
            this.txtMolwt.CanEmpty = true;
            this.txtMolwt.DigitLength = 6;
            this.txtMolwt.InputType = Fpi.UI.Common.PC.Controls.TextInputType.Float;
            this.txtMolwt.InvalidBackColor = System.Drawing.Color.Gold;
            this.txtMolwt.IsValidCheck = false;
            resources.ApplyResources(this.txtMolwt, "txtMolwt");
            this.txtMolwt.MaxValue = null;
            this.txtMolwt.MinValue = null;
            this.txtMolwt.Name = "txtMolwt";
            // 
            // UC_ValueNodeView
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tabControl1);
            this.Name = "UC_ValueNodeView";
            this.tabControl1.ResumeLayout(false);
            this.pageLocal.ResumeLayout(false);
            this.pageLocal.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuSmooth)).EndInit();
            this.pageUnit.ResumeLayout(false);
            this.panel8.ResumeLayout(false);
            this.panel8.PerformLayout();
            this.pageScope.ResumeLayout(false);
            this.panel9.ResumeLayout(false);
            this.pageLimit.ResumeLayout(false);
            this.panel10.ResumeLayout(false);
            this.pageFixed.ResumeLayout(false);
            this.pageFixed.PerformLayout();
            this.gbFixed.ResumeLayout(false);
            this.gbFixed.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuFixedValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuFixedRange)).EndInit();
            this.pageIndice.ResumeLayout(false);
            this.pnlIndice.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage pageBase;
        private System.Windows.Forms.TabPage pageUnit;
        private System.Windows.Forms.Panel panel8;
        private System.Windows.Forms.Button btnAddUnit;
        private System.Windows.Forms.TabPage pageScope;
        private System.Windows.Forms.Panel panel9;
        private System.Windows.Forms.Button btnDelScope;
        private System.Windows.Forms.Button btnAddScope;
        private System.Windows.Forms.Button btnUpdateScope;
        private System.Windows.Forms.ListView lvScope;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.TabPage pageLimit;
        private System.Windows.Forms.Panel panel10;
        private System.Windows.Forms.Button btnDelLimit;
        private System.Windows.Forms.Button btnAddLimit;
        private System.Windows.Forms.Button btnUpdateLimit;
        private System.Windows.Forms.TabPage pageFixed;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.GroupBox gbFixed;
        private System.Windows.Forms.NumericUpDown nuFixedValue;
        private System.Windows.Forms.NumericUpDown nuFixedRange;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.CheckBox chkFixed;
        private System.Windows.Forms.ListView lvUnit;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ListView lvLimit;
        private System.Windows.Forms.ColumnHeader columnHeader9;
        private System.Windows.Forms.ColumnHeader columnHeader10;
        private System.Windows.Forms.ColumnHeader columnHeader11;
        private System.Windows.Forms.ColumnHeader columnHeader12;
        private System.Windows.Forms.TabPage pageLocal;
        private System.Windows.Forms.ComboBox cmbLimit;
        private System.Windows.Forms.ComboBox cmbScope;
        private System.Windows.Forms.NumericUpDown nuSmooth;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox txtFormula;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button btnFormula;
        private System.Windows.Forms.Label label1;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtGBCode;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtMolwt;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cmbSelfUnit;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtQualiyClass;
        private System.Windows.Forms.TabPage pageIndice;
        private System.Windows.Forms.ListView lvIndice;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader7;
        private System.Windows.Forms.ColumnHeader columnHeader8;
        private System.Windows.Forms.ColumnHeader columnHeader13;
        private System.Windows.Forms.Panel pnlIndice;
        private System.Windows.Forms.Button btnDelIndice;
        private System.Windows.Forms.Button btnAddIndice;
        private System.Windows.Forms.Button btnEditIndice;
        private Fpi.UI.Common.PC.Controls.FpiTextBox txtDetectionLimit;
        private System.Windows.Forms.Label label7;

    }
}
