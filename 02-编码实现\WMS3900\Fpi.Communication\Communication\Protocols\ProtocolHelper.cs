using System;
using System.Runtime.InteropServices;
using Fpi.Communication.Exceptions;
using Fpi.Communication.Manager;
using Fpi.Xml;

namespace Fpi.Communication.Protocols
{
    public static class ProtocolHelper
    {
        #region Call Win Api

        [DllImport("Kernel32.dll")]
        public static extern uint SetLocalTime(ref SYSTEMTIME lpSystemTime);

        public static void CorrectSystemTime(DateTime dateTime)
        {
            TimeSpan ts = dateTime - DateTime.Now;
            if(Math.Abs(ts.TotalMinutes) < 0.5)
            {
                return;
            }
            SYSTEMTIME sysTime = new SYSTEMTIME();
            sysTime.wYear = (ushort)dateTime.Year;
            sysTime.wMonth = (ushort)dateTime.Month;
            sysTime.wDay = (ushort)dateTime.Day;
            sysTime.wHour = (ushort)dateTime.Hour;
            sysTime.wMinute = (ushort)dateTime.Minute;
            sysTime.wSecond = (ushort)dateTime.Second;
            sysTime.wMilliseconds = (ushort)dateTime.Millisecond;
            sysTime.wDayOfWeek = (ushort)dateTime.DayOfWeek;
            SetLocalTime(ref sysTime);
        }

        #endregion

        #region Sender Timer Control

        public static void StartSenderTimer(Protocol owner)
        {
            Sender sender = owner.Sender;
            if(sender != null)
            {
                sender.StartTimer();
            }
        }

        public static void StopSenderTimer(Protocol owner)
        {
            Sender sender = owner.Sender;
            if(sender != null)
            {
                sender.StopTimer();
            }
        }

        public static void SendRealData(Protocol owner)
        {
            Sender sender = owner.Sender;
            if(sender != null)
            {
                sender.SendData(owner.CurrentPipe);
            }
        }

        #endregion

        #region Get protocol property

        public static int GetSenderInterval(Pipe pipe)
        {
            string str = GetConfigValue(pipe, Protocol.PropertyName_SenderInterval);
            int rv;
            int.TryParse(str, out rv);
            return rv / 1000;
        }

        public static void SetSenderInterval(Pipe pipe, int interval)
        {
            Property prop = GetConfig(pipe, Protocol.PropertyName_SenderInterval);
            prop.value = (interval * 1000).ToString();
            PortManager.GetInstance().Save();
        }

        public static string GetConfigValue(Pipe pipe, string propertyId)
        {
            Property prop = GetConfig(pipe, propertyId);
            return prop.value;
        }

        public static Property GetConfig(Pipe pipe, string propertyId)
        {
            Property protocolConfig = GetProtoclConfig(pipe);
            Property prop = protocolConfig.GetProperty(propertyId);
            if(prop == null)
            {
                prop = new Property(propertyId, propertyId);
                protocolConfig.AddProperty(prop);
            }
            return prop;
        }

        public static Property GetProtoclConfig(Pipe pipe)
        {
            if(pipe == null)
            {
                throw new ProtocolException("CurrentPipe is null:" + pipe.name);
            }
            Property prop = pipe.GetProtocolProperty();
            return prop;
        }

        #endregion
    }

    public struct SYSTEMTIME
    {
        public ushort wYear;
        public ushort wMonth;
        public ushort wDayOfWeek;
        public ushort wDay;
        public ushort wHour;
        public ushort wMinute;
        public ushort wSecond;
        public ushort wMilliseconds;
    }
}