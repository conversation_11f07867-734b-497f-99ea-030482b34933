﻿namespace Fpi.Alarm.UI.PC
{
    partial class FrmQueryHistoryAlarm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && ( components != null ))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle10 = new System.Windows.Forms.DataGridViewCellStyle();
            this.pnlTop = new Sunny.UI.UIPanel();
            this.uiLabel6 = new Sunny.UI.UILabel();
            this.uiLabel7 = new Sunny.UI.UILabel();
            this.lblPage = new Sunny.UI.UILabel();
            this.txtRecordCount = new Sunny.UI.UITextBox();
            this.dtpEndTime = new Sunny.UI.UIDatetimePicker();
            this.cmbDateType = new Sunny.UI.UIComboBox();
            this.cmbGrade = new Sunny.UI.UIComboBox();
            this.cmbSource = new Sunny.UI.UIComboBox();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.btnExcelExport = new Sunny.UI.UIButton();
            this.dtpStartTime = new Sunny.UI.UIDatetimePicker();
            this.btnQuery = new Sunny.UI.UIButton();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.dgvAlarmInfo = new Sunny.UI.UIDataGridView();
            this.pagination = new Sunny.UI.UIPagination();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.pnlTop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvAlarmInfo)).BeginInit();
            this.SuspendLayout();
            // 
            // pnlTop
            // 
            this.pnlTop.Controls.Add(this.uiLabel6);
            this.pnlTop.Controls.Add(this.uiLabel7);
            this.pnlTop.Controls.Add(this.lblPage);
            this.pnlTop.Controls.Add(this.txtRecordCount);
            this.pnlTop.Controls.Add(this.dtpEndTime);
            this.pnlTop.Controls.Add(this.cmbDateType);
            this.pnlTop.Controls.Add(this.cmbGrade);
            this.pnlTop.Controls.Add(this.cmbSource);
            this.pnlTop.Controls.Add(this.uiLabel5);
            this.pnlTop.Controls.Add(this.uiLabel4);
            this.pnlTop.Controls.Add(this.uiLabel3);
            this.pnlTop.Controls.Add(this.btnExcelExport);
            this.pnlTop.Controls.Add(this.dtpStartTime);
            this.pnlTop.Controls.Add(this.btnQuery);
            this.pnlTop.Controls.Add(this.uiLabel2);
            this.pnlTop.Controls.Add(this.uiLabel1);
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(216)))), ((int)(((byte)(225)))), ((int)(((byte)(238)))));
            this.pnlTop.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.pnlTop.Location = new System.Drawing.Point(0, 0);
            this.pnlTop.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTop.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlTop.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.pnlTop.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlTop.Size = new System.Drawing.Size(984, 89);
            this.pnlTop.TabIndex = 0;
            this.pnlTop.Text = null;
            this.pnlTop.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiLabel6
            // 
            this.uiLabel6.AutoSize = true;
            this.uiLabel6.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel6.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel6.Location = new System.Drawing.Point(593, 55);
            this.uiLabel6.Name = "uiLabel6";
            this.uiLabel6.Size = new System.Drawing.Size(90, 21);
            this.uiLabel6.TabIndex = 11;
            this.uiLabel6.Text = "每页记录数";
            this.uiLabel6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel7
            // 
            this.uiLabel7.AutoSize = true;
            this.uiLabel7.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel7.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel7.Location = new System.Drawing.Point(764, 55);
            this.uiLabel7.Name = "uiLabel7";
            this.uiLabel7.Size = new System.Drawing.Size(46, 21);
            this.uiLabel7.TabIndex = 12;
            this.uiLabel7.Text = "页码:";
            this.uiLabel7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // lblPage
            // 
            this.lblPage.AutoSize = true;
            this.lblPage.BackColor = System.Drawing.Color.Transparent;
            this.lblPage.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblPage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblPage.Location = new System.Drawing.Point(805, 55);
            this.lblPage.Name = "lblPage";
            this.lblPage.Size = new System.Drawing.Size(33, 21);
            this.lblPage.TabIndex = 13;
            this.lblPage.Text = "?/?";
            this.lblPage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtRecordCount
            // 
            this.txtRecordCount.ButtonSymbol = 61761;
            this.txtRecordCount.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtRecordCount.DoubleValue = 27D;
            this.txtRecordCount.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.txtRecordCount.IntValue = 27;
            this.txtRecordCount.Location = new System.Drawing.Point(683, 51);
            this.txtRecordCount.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtRecordCount.Maximum = 65534D;
            this.txtRecordCount.Minimum = 1D;
            this.txtRecordCount.MinimumSize = new System.Drawing.Size(1, 1);
            this.txtRecordCount.Name = "txtRecordCount";
            this.txtRecordCount.Padding = new System.Windows.Forms.Padding(5);
            this.txtRecordCount.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.txtRecordCount.ShowText = false;
            this.txtRecordCount.Size = new System.Drawing.Size(50, 29);
            this.txtRecordCount.TabIndex = 8;
            this.txtRecordCount.Text = "27";
            this.txtRecordCount.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.txtRecordCount.Watermark = "";
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dtpEndTime.FillColor = System.Drawing.Color.White;
            this.dtpEndTime.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dtpEndTime.Location = new System.Drawing.Point(376, 51);
            this.dtpEndTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpEndTime.MaxLength = 19;
            this.dtpEndTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpEndTime.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dtpEndTime.Size = new System.Drawing.Size(205, 29);
            this.dtpEndTime.SymbolDropDown = 61555;
            this.dtpEndTime.SymbolNormal = 61555;
            this.dtpEndTime.SymbolSize = 24;
            this.dtpEndTime.TabIndex = 6;
            this.dtpEndTime.Text = "2021-01-25 10:00:03";
            this.dtpEndTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpEndTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpEndTime.Watermark = "";
            // 
            // cmbDateType
            // 
            this.cmbDateType.DataSource = null;
            this.cmbDateType.DropDownAutoWidth = true;
            this.cmbDateType.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbDateType.FillColor = System.Drawing.Color.White;
            this.cmbDateType.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.cmbDateType.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbDateType.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbDateType.Location = new System.Drawing.Point(559, 11);
            this.cmbDateType.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbDateType.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbDateType.MultiLanguageSupport = false;
            this.cmbDateType.Name = "cmbDateType";
            this.cmbDateType.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbDateType.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.cmbDateType.Size = new System.Drawing.Size(124, 29);
            this.cmbDateType.SymbolSize = 24;
            this.cmbDateType.TabIndex = 4;
            this.cmbDateType.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbDateType.Watermark = "";
            // 
            // cmbGrade
            // 
            this.cmbGrade.DataSource = null;
            this.cmbGrade.DropDownAutoWidth = true;
            this.cmbGrade.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbGrade.FillColor = System.Drawing.Color.White;
            this.cmbGrade.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.cmbGrade.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbGrade.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbGrade.Location = new System.Drawing.Point(376, 11);
            this.cmbGrade.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbGrade.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbGrade.MultiLanguageSupport = false;
            this.cmbGrade.Name = "cmbGrade";
            this.cmbGrade.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbGrade.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.cmbGrade.Size = new System.Drawing.Size(94, 29);
            this.cmbGrade.SymbolSize = 24;
            this.cmbGrade.TabIndex = 3;
            this.cmbGrade.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbGrade.Watermark = "";
            // 
            // cmbSource
            // 
            this.cmbSource.DataSource = null;
            this.cmbSource.DropDownAutoWidth = true;
            this.cmbSource.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbSource.FillColor = System.Drawing.Color.White;
            this.cmbSource.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.cmbSource.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbSource.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbSource.Location = new System.Drawing.Point(85, 11);
            this.cmbSource.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbSource.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbSource.MultiLanguageSupport = false;
            this.cmbSource.Name = "cmbSource";
            this.cmbSource.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbSource.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.cmbSource.Size = new System.Drawing.Size(205, 29);
            this.cmbSource.SymbolSize = 24;
            this.cmbSource.TabIndex = 2;
            this.cmbSource.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbSource.Watermark = "";
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(483, 15);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(74, 21);
            this.uiLabel5.TabIndex = 9;
            this.uiLabel5.Text = "时间类型";
            this.uiLabel5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel4
            // 
            this.uiLabel4.AutoSize = true;
            this.uiLabel4.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel4.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel4.Location = new System.Drawing.Point(303, 15);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(74, 21);
            this.uiLabel4.TabIndex = 8;
            this.uiLabel4.Text = "报警等级";
            this.uiLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(28, 15);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(58, 21);
            this.uiLabel3.TabIndex = 7;
            this.uiLabel3.Text = "报警源";
            this.uiLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnExcelExport
            // 
            this.btnExcelExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExcelExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExcelExport.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnExcelExport.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnExcelExport.Location = new System.Drawing.Point(884, 9);
            this.btnExcelExport.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnExcelExport.Name = "btnExcelExport";
            this.btnExcelExport.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnExcelExport.Size = new System.Drawing.Size(92, 32);
            this.btnExcelExport.TabIndex = 1;
            this.btnExcelExport.Text = "导出";
            this.btnExcelExport.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExcelExport.Click += new System.EventHandler(this.btnExcelExport_Click);
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dtpStartTime.FillColor = System.Drawing.Color.White;
            this.dtpStartTime.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dtpStartTime.Location = new System.Drawing.Point(85, 51);
            this.dtpStartTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpStartTime.MaxLength = 19;
            this.dtpStartTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpStartTime.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dtpStartTime.Size = new System.Drawing.Size(205, 29);
            this.dtpStartTime.SymbolDropDown = 61555;
            this.dtpStartTime.SymbolNormal = 61555;
            this.dtpStartTime.SymbolSize = 24;
            this.dtpStartTime.TabIndex = 5;
            this.dtpStartTime.Text = "2021-01-25 10:00:03";
            this.dtpStartTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpStartTime.Value = new System.DateTime(2021, 1, 25, 10, 0, 3, 579);
            this.dtpStartTime.Watermark = "";
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQuery.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnQuery.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnQuery.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btnQuery.Location = new System.Drawing.Point(781, 9);
            this.btnQuery.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.btnQuery.Size = new System.Drawing.Size(92, 32);
            this.btnQuery.TabIndex = 0;
            this.btnQuery.Text = "查询";
            this.btnQuery.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(303, 55);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(74, 21);
            this.uiLabel2.TabIndex = 11;
            this.uiLabel2.Text = "结束时间";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(12, 55);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(74, 21);
            this.uiLabel1.TabIndex = 10;
            this.uiLabel1.Text = "起始时间";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // dgvAlarmInfo
            // 
            this.dgvAlarmInfo.AllowUserToAddRows = false;
            this.dgvAlarmInfo.AllowUserToDeleteRows = false;
            this.dgvAlarmInfo.AllowUserToOrderColumns = true;
            this.dgvAlarmInfo.AllowUserToResizeRows = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvAlarmInfo.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle6;
            this.dgvAlarmInfo.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvAlarmInfo.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(216)))), ((int)(((byte)(225)))), ((int)(((byte)(238)))));
            this.dgvAlarmInfo.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvAlarmInfo.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText;
            this.dgvAlarmInfo.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle7.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvAlarmInfo.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgvAlarmInfo.ColumnHeadersHeight = 32;
            this.dgvAlarmInfo.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle8.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvAlarmInfo.DefaultCellStyle = dataGridViewCellStyle8;
            this.dgvAlarmInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvAlarmInfo.EnableHeadersVisualStyles = false;
            this.dgvAlarmInfo.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvAlarmInfo.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dgvAlarmInfo.Location = new System.Drawing.Point(0, 89);
            this.dgvAlarmInfo.Name = "dgvAlarmInfo";
            this.dgvAlarmInfo.ReadOnly = true;
            this.dgvAlarmInfo.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            dataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle9.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle9.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle9.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle9.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvAlarmInfo.RowHeadersDefaultCellStyle = dataGridViewCellStyle9;
            this.dgvAlarmInfo.RowHeadersVisible = false;
            dataGridViewCellStyle10.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle10.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle10.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle10.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvAlarmInfo.RowsDefaultCellStyle = dataGridViewCellStyle10;
            this.dgvAlarmInfo.RowTemplate.Height = 29;
            this.dgvAlarmInfo.ScrollBarColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dgvAlarmInfo.ScrollBarRectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.dgvAlarmInfo.ScrollBarStyleInherited = false;
            this.dgvAlarmInfo.SelectedIndex = -1;
            this.dgvAlarmInfo.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvAlarmInfo.Size = new System.Drawing.Size(984, 558);
            this.dgvAlarmInfo.Style = Sunny.UI.UIStyle.Custom;
            this.dgvAlarmInfo.TabIndex = 0;
            // 
            // pagination
            // 
            this.pagination.ButtonFillColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.pagination.ButtonFillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(128)))), ((int)(((byte)(204)))));
            this.pagination.ButtonInterval = 5;
            this.pagination.ButtonStyleInherited = false;
            this.pagination.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pagination.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(216)))), ((int)(((byte)(225)))), ((int)(((byte)(238)))));
            this.pagination.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pagination.Location = new System.Drawing.Point(0, 647);
            this.pagination.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pagination.MinimumSize = new System.Drawing.Size(1, 1);
            this.pagination.Name = "pagination";
            this.pagination.PagerCount = 13;
            this.pagination.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pagination.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(69)))), ((int)(((byte)(134)))));
            this.pagination.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Top;
            this.pagination.ShowText = false;
            this.pagination.Size = new System.Drawing.Size(984, 34);
            this.pagination.Style = Sunny.UI.UIStyle.Custom;
            this.pagination.TabIndex = 0;
            this.pagination.Text = null;
            this.pagination.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.pagination.TotalCount = 0;
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.Filter = "excel文件|*.xlsx";
            this.saveFileDialog.Title = "报警数据导出";
            // 
            // FrmQueryHistoryAlarm
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(984, 681);
            this.Controls.Add(this.dgvAlarmInfo);
            this.Controls.Add(this.pagination);
            this.Controls.Add(this.pnlTop);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmQueryHistoryAlarm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "历史报警查询";
            this.Load += new System.EventHandler(this.FrmQueryHistoryAlarm_Load);
            this.pnlTop.ResumeLayout(false);
            this.pnlTop.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvAlarmInfo)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIPanel pnlTop;
        private Sunny.UI.UIDatetimePicker dtpEndTime;
        private Sunny.UI.UIDatetimePicker dtpStartTime;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIButton btnQuery;
        private Sunny.UI.UIDataGridView dgvAlarmInfo;
        private Sunny.UI.UIButton btnExcelExport;
        private Sunny.UI.UITextBox txtRecordCount;
        private Sunny.UI.UILabel lblPage;
        private Sunny.UI.UILabel uiLabel7;
        private Sunny.UI.UILabel uiLabel6;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        private Sunny.UI.UIComboBox cmbSource;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UIComboBox cmbDateType;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UIComboBox cmbGrade;
        private Sunny.UI.UIPagination pagination;
    }
}